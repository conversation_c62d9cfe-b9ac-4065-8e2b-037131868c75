import{bE as t,__tla as k}from"./index-Daqg4PFz.js";let s,c,e,u,m,o,l,i,n,p,d,w,y,g,_,f,b,x,h,j=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{m=async a=>await t.get({url:"/crm/customer/page",params:a}),o=async a=>await t.get({url:"/crm/customer/put-pool-remind-page",params:a}),n=async()=>await t.get({url:"/crm/customer/put-pool-remind-count"}),i=async()=>await t.get({url:"/crm/customer/today-contact-count"}),p=async()=>await t.get({url:"/crm/customer/follow-count"}),l=async a=>await t.get({url:"/crm/customer/get?id="+a}),e=async a=>await t.post({url:"/crm/customer/create",data:a}),c=async a=>await t.put({url:"/crm/customer/update",data:a}),h=async(a,r)=>await t.put({url:"/crm/customer/update-deal-status",params:{id:a,dealStatus:r}}),g=async a=>await t.delete({url:"/crm/customer/delete?id="+a}),_=async a=>await t.download({url:"/crm/customer/export-excel",params:a}),y=()=>t.download({url:"/crm/customer/get-import-template"}),d=async a=>await t.upload({url:"/crm/customer/import",data:a}),s=async()=>await t.get({url:"/crm/customer/simple-list"}),x=async a=>await t.put({url:"/crm/customer/transfer",data:a}),w=async(a,r)=>await t.put({url:"/crm/customer/lock",data:{id:a,lockStatus:r}}),b=async a=>await t.put({url:"/crm/customer/receive",params:{ids:a.join(",")}}),u=async(a,r)=>await t.put({url:"/crm/customer/distribute",data:{ids:a,ownerUserId:r}}),f=async a=>await t.put({url:`/crm/customer/put-pool?id=${a}`})});export{j as __tla,s as a,c as b,e as c,u as d,m as e,o as f,l as g,i as h,n as i,p as j,d as k,w as l,y as m,g as n,_ as o,f as p,b as r,x as t,h as u};
