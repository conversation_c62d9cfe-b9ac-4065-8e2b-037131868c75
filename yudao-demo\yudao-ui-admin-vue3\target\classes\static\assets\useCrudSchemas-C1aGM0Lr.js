import{n as I,f as A,cK as P,eg as C,cw as D,V as H,D as M,h as g,__tla as x}from"./index-Daqg4PFz.js";import{e as _,b as K,f as N}from"./tree-BMqZf9_I.js";import{_ as V,__tla as j}from"./DictTag-BDZzHcIz.js";let E,k=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{let y,T,w,F,v,S;({t:y}=I()),E=l=>{const o=A({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),a=T(l,o);o.searchSchema=a||[];const s=w(l);o.tableColumns=s||[];const t=F(l,o);o.formSchema=t;const r=v(l);return o.detailSchema=r,{allSchemas:o}},T=(l,o)=>{const a=[],s=[];_(l,t=>{var r,p,e,m;if(t!=null&&t.isSearch||(r=t.search)!=null&&r.show){let u=((p=t==null?void 0:t.search)==null?void 0:p.component)||"Input";const f=[];let h={};if(t.dictType){const n={label:"\u5168\u90E8",value:""};f.push(n),P(t.dictType).forEach(c=>{f.push(c)}),h={options:f},(e=t.search)!=null&&e.component||(u="Select")}const d=C({component:u,...t.search,field:t.field,label:((m=t.search)==null?void 0:m.label)||t.label},{componentProps:h});d.api&&s.push(async()=>{var c;const n=await d.api();if(n){const i=D(o.searchSchema,b=>b.field===d.field);i!==-1&&(o.searchSchema[i].componentProps.options=S(n,(c=d.componentProps.optionsAlias)==null?void 0:c.labelField))}}),delete d.show,a.push(d)}});for(const t of s)t();return a},w=l=>{const o=K(l,{conversion:a=>{var s;if((a==null?void 0:a.isTable)!==!1&&((s=a==null?void 0:a.table)==null?void 0:s.show)!==!1)return!a.formatter&&a.dictType&&(a.formatter=(t,r,p)=>g(V,{type:a.dictType,value:p})),{...a.table,...a}}});return N(o,a=>(a.children===void 0&&delete a.children,!!a.field))},F=(l,o)=>{const a=[],s=[];_(l,t=>{var r,p,e,m,u;if((t==null?void 0:t.isForm)!==!1&&((r=t==null?void 0:t.form)==null?void 0:r.show)!==!1){let f=((p=t==null?void 0:t.form)==null?void 0:p.component)||"Input",h="";(e=t.form)!=null&&e.value?h=(m=t.form)==null?void 0:m.value:f==="InputNumber"&&(h=0);let d={};if(t.dictType){const c=[];t.dictClass&&t.dictClass==="number"?H(t.dictType).forEach(i=>{c.push(i)}):t.dictClass&&t.dictClass==="boolean"?M(t.dictType).forEach(i=>{c.push(i)}):P(t.dictType).forEach(i=>{c.push(i)}),d={options:c},t.form&&t.form.component||(f="Select")}const n=C({component:f,value:h,...t.form,field:t.field,label:((u=t.form)==null?void 0:u.label)||t.label},{componentProps:d});n.api&&s.push(async()=>{var i;const c=await n.api();if(c){const b=D(o.formSchema,Y=>Y.field===n.field);b!==-1&&(o.formSchema[b].componentProps.options=S(c,(i=n.componentProps.optionsAlias)==null?void 0:i.labelField))}}),delete n.show,a.push(n)}});for(const t of s)t();return a},v=l=>{const o=[];return _(l,a=>{var s,t,r,p,e;if((a==null?void 0:a.isDetail)!==!1&&((s=a.detail)==null?void 0:s.show)!==!1){const m={...a.detail,field:a.field,label:((t=a.detail)==null?void 0:t.label)||a.label};a.dictType&&(m.dictType=a.dictType),((r=a.detail)!=null&&r.dateFormat||a.formatter=="formatDate")&&(m.dateFormat=(p=a==null?void 0:a.detail)!=null&&p.dateFormat?(e=a==null?void 0:a.detail)==null?void 0:e.dateFormat:"YYYY-MM-DD HH:mm:ss"),delete m.show,o.push(m)}}),o},S=(l,o)=>l==null?void 0:l.map(a=>(o?a.labelField=y(a.labelField):a.label=y(a.label),a))});export{k as __tla,E as u};
