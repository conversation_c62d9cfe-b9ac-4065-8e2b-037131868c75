import{r as T,C,at as F,be as b,d as c,bg as _,o as a,c as r,l as g,a as t,bj as L,a9 as h,a0 as m,bh as w,bk as P,F as d,k as x,aW as S,i as W,ao as $,aX as X,bi as q,bl as z,__tla as A}from"./index-Daqg4PFz.js";let E,D=Promise.all([(()=>{try{return A}catch{}})()]).then(async()=>{const N=b({animated:{type:Boolean,default:!1},count:{type:Number,default:1},rows:{type:Number,default:3},loading:{type:Boolean,default:!0},throttle:{type:Number}}),j=b({variant:{type:String,values:["circle","rect","h1","h3","text","caption","p","image","button"],default:"text"}}),B=c({name:"ElSkeletonItem"});var i=w(c({...B,props:j,setup(v){const u=_("skeleton");return(o,s)=>(a(),r("div",{class:m([t(u).e("item"),t(u).e(o.variant)])},[o.variant==="image"?(a(),g(t(L),{key:0})):h("v-if",!0)],2))}}),[["__file","skeleton-item.vue"]]);const I=c({name:"ElSkeleton"});E=q(w(c({...I,props:N,setup(v,{expose:u}){const o=v,s=_("skeleton"),k=((e,p=0)=>{if(p===0)return e;const l=T(!1);let n=0;const y=()=>{n&&clearTimeout(n),n=window.setTimeout(()=>{l.value=e.value},p)};return C(y),F(()=>e.value,f=>{f?y():l.value=f}),l})(P(o,"loading"),o.throttle);return u({uiLoading:k}),(e,p)=>t(k)?(a(),r("div",$({key:0,class:[t(s).b(),t(s).is("animated",e.animated)]},e.$attrs),[(a(!0),r(d,null,x(e.count,l=>(a(),r(d,{key:l},[e.loading?S(e.$slots,"template",{key:l},()=>[W(i,{class:m(t(s).is("first")),variant:"p"},null,8,["class"]),(a(!0),r(d,null,x(e.rows,n=>(a(),g(i,{key:n,class:m([t(s).e("paragraph"),t(s).is("last",n===e.rows&&e.rows>1)]),variant:"p"},null,8,["class"]))),128))]):h("v-if",!0)],64))),128))],16)):S(e.$slots,"default",X($({key:1},e.$attrs)))}}),[["__file","skeleton.vue"]]),{SkeletonItem:i}),z(i)});export{E,D as __tla};
