import{bg as ae,at as q,az as w,cC as D,cc as F,K as B,c7 as H,b,d as I,J as G,as as se,c3 as ce,cD as J,c9 as oe,c6 as ne,b7 as re,cE as de,r as Q,f as P,C as ue,h as T,bh as ie,__tla as he}from"./index-Daqg4PFz.js";let W,pe=Promise.all([(()=>{try{return he}catch{}})()]).then(async()=>{const X=I({extends:G,setup(t,o){const r=G.setup(t,o);delete r.selectOptionClick;const p=se().proxy;return w(()=>{r.select.states.cachedOptions.get(p.value)||r.select.onOptionCreate(p)}),r},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function L(t){return t||t===0}function z(t){return Array.isArray(t)&&t.length}function K(t){return Array.isArray(t)?t:L(t)?[t]:[]}function O(t,o,r,p,n){for(let d=0;d<t.length;d++){const f=t[d];if(o(f,d,t,n))return p?p(f,d,t,n):f;{const A=r(f);if(z(A)){const V=O(A,o,r,p,f);if(V)return V}}}}function R(t,o,r,p){for(let n=0;n<t.length;n++){const d=t[n];o(d,n,t,p);const f=r(d);z(f)&&R(f,o,r,d)}}var Y=I({props:{data:{type:Array,default:()=>[]}},setup(t){const o=re(de);return q(()=>t.data,()=>{var r;t.data.forEach(n=>{o.states.cachedOptions.has(n.value)||o.states.cachedOptions.set(n.value,n)});const p=((r=o.selectRef)==null?void 0:r.querySelectorAll("input"))||[];Array.from(p).includes(document.activeElement)||o.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const Z=I({name:"ElTreeSelect",inheritAttrs:!1,props:{...B.props,...J.props,cacheData:{type:Array,default:()=>[]}},setup(t,o){const{slots:r,expose:p}=o,n=Q(),d=Q(),f=b(()=>t.nodeKey||t.valueKey||"value"),A=((e,{attrs:C,emit:M},{select:g,tree:m,key:v})=>{const S=ae("tree-select");return q(()=>e.data,()=>{e.filterable&&w(()=>{var u,a;(a=m.value)==null||a.filter((u=g.value)==null?void 0:u.states.inputValue)})},{flush:"post"}),{...D(F(e),Object.keys(B.props)),...C,"onUpdate:modelValue":u=>M(H,u),valueKey:v,popperClass:b(()=>{const u=[S.e("popper")];return e.popperClass&&u.push(e.popperClass),u.join(" ")}),filterMethod:(u="")=>{var a;e.filterMethod?e.filterMethod(u):e.remoteMethod?e.remoteMethod(u):(a=m.value)==null||a.filter(u)}}})(t,o,{select:n,tree:d,key:f}),{cacheOptions:V,...ee}=((e,{attrs:C,slots:M,emit:g},{select:m,tree:v,key:S})=>{q(()=>e.modelValue,()=>{e.showCheckbox&&w(()=>{const l=v.value;l&&!ce(l.getCheckedKeys(),K(e.modelValue))&&l.setCheckedKeys(K(e.modelValue))})},{immediate:!0,deep:!0});const u=b(()=>({value:S.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props})),a=(l,s)=>{var c;const i=u.value[l];return ne(i)?i(s,(c=v.value)==null?void 0:c.getNode(a("value",s))):s[i]},$=K(e.modelValue).map(l=>O(e.data||[],s=>a("value",s)===l,s=>a("children",s),(s,c,i,y)=>y&&a("value",y))).filter(l=>L(l)),te=b(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const l=[];return R(e.data.concat(e.cacheData),s=>{const c=a("value",s);l.push({value:c,currentLabel:a("label",s),isDisabled:a("disabled",s)})},s=>a("children",s)),l});return{...D(F(e),Object.keys(J.props)),...C,nodeKey:S,expandOnClickNode:b(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:b(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat($):$),renderContent:(l,{node:s,data:c,store:i})=>l(X,{value:a("value",c),label:a("label",c),disabled:a("disabled",c)},e.renderContent?()=>e.renderContent(l,{node:s,data:c,store:i}):M.default?()=>M.default({node:s,data:c,store:i}):void 0),filterNodeMethod:(l,s,c)=>e.filterNodeMethod?e.filterNodeMethod(l,s,c):!l||new RegExp(oe(l),"i").test(a("label",s)||""),onNodeClick:(l,s,c)=>{var i,y,N,x;if((i=C.onNodeClick)==null||i.call(C,l,s,c),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!s.isLeaf)e.expandOnClickNode&&c.proxy.handleExpandIconClick();else if(!a("disabled",l)){const _=(y=m.value)==null?void 0:y.states.options.get(a("value",l));(N=m.value)==null||N.handleOptionSelect(_)}(x=m.value)==null||x.focus()}},onCheck:(l,s)=>{var c;if(!e.showCheckbox)return;const i=a("value",l),y={};R([v.value.store.root],h=>y[h.key]=h,h=>h.childNodes);const N=s.checkedKeys,x=e.multiple?K(e.modelValue).filter(h=>!(h in y)&&!N.includes(h)):[],_=x.concat(N);if(e.checkStrictly)g(H,e.multiple?_:_.includes(i)?i:void 0);else if(e.multiple)g(H,x.concat(v.value.getCheckedKeys(!0)));else{const h=O([l],k=>!z(a("children",k))&&!a("disabled",k),k=>a("children",k)),j=h?a("value",h):void 0,le=L(e.modelValue)&&!!O([l],k=>a("value",k)===e.modelValue,k=>a("children",k));g(H,j===e.modelValue||le?void 0:j)}w(()=>{var h;const j=K(e.modelValue);v.value.setCheckedKeys(j),(h=C.onCheck)==null||h.call(C,l,{checkedKeys:v.value.getCheckedKeys(),checkedNodes:v.value.getCheckedNodes(),halfCheckedKeys:v.value.getHalfCheckedKeys(),halfCheckedNodes:v.value.getHalfCheckedNodes()})}),(c=m.value)==null||c.focus()},cacheOptions:te}})(t,o,{select:n,tree:d,key:f}),U=P({});return p(U),ue(()=>{Object.assign(U,{...D(d.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...D(n.value,["focus","blur"])})}),()=>T(B,P({...A,ref:e=>n.value=e}),{...r,default:()=>[T(Y,{data:V.value}),T(J,P({...ee,ref:e=>d.value=e}))]})}});var E=ie(Z,[["__file","tree-select.vue"]]);E.install=t=>{t.component(E.name,E)},W=E});export{W as E,pe as __tla};
