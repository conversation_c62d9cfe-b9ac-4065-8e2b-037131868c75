import{bE as t,__tla as a}from"./index-Daqg4PFz.js";let e,r=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{e={getFunnelSummary:s=>t.get({url:"/crm/statistics-funnel/get-funnel-summary",params:s}),getBusinessSummaryByEndStatus:s=>t.get({url:"/crm/statistics-funnel/get-business-summary-by-end-status",params:s}),getBusinessSummaryByDate:s=>t.get({url:"/crm/statistics-funnel/get-business-summary-by-date",params:s}),getBusinessInversionRateSummaryByDate:s=>t.get({url:"/crm/statistics-funnel/get-business-inversion-rate-summary-by-date",params:s}),getBusinessPageByDate:s=>t.get({url:"/crm/statistics-funnel/get-business-page-by-date",params:s})}});export{e as S,r as __tla};
