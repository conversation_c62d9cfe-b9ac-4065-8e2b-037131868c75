import{d as o,o as p,c as i,g as a,av as r,_ as s,__tla as l}from"./index-Daqg4PFz.js";let t,n=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{t=s(o({name:"Divider",__name:"index",props:{property:{type:Object,required:!0}},setup:d=>(e,y)=>(p(),i("div",{class:"flex items-center",style:r({height:e.property.height+"px"})},[a("div",{class:"w-full",style:r({borderTopStyle:e.property.borderType,borderTopColor:e.property.lineColor,borderTopWidth:`${e.property.lineWidth}px`,margin:e.property.paddingType==="none"?"0":"0px 16px"})},null,4)],4))}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/Divider/index.vue"]])});export{n as __tla,t as default};
