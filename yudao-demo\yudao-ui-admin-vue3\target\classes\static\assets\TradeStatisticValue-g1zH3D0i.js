import{_ as g,__tla as v}from"./CountTo-Dat_y5oU.js";import{d as y,p as t,o as m,c as b,g as a,t as u,l as w,w as S,i as s,a9 as T,a0 as h,a as l,aM as r,j,x as V,aO as M,_ as O,__tla as k}from"./index-Daqg4PFz.js";let f,J=Promise.all([(()=>{try{return v}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{let i,n,c,o,p;i={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},n={class:"flex items-center justify-between text-gray-500"},c={class:"mb-4 text-3xl"},o={class:"flex flex-row gap-1 text-sm"},p=a("span",{class:"text-gray-500"},"\u73AF\u6BD4",-1),f=O(y({name:"TradeStatisticValue",__name:"TradeStatisticValue",props:{tooltip:t.string.def(""),title:t.string.def(""),prefix:t.string.def(""),value:t.number.def(0),decimals:t.number.def(0),percent:t.oneOfType([Number,String]).def(0)},setup:e=>(N,P)=>{const d=V,_=M,x=g;return m(),b("div",i,[a("div",n,[a("span",null,u(e.title),1),e.tooltip?(m(),w(_,{key:0,content:e.tooltip,placement:"top-start"},{default:S(()=>[s(d,{icon:"ep:warning"})]),_:1},8,["content"])):T("",!0)]),a("div",c,[s(x,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),a("div",o,[p,a("span",{class:h(l(r)(e.percent)>0?"text-red-500":"text-green-500")},[j(u(Math.abs(l(r)(e.percent)))+"% ",1),s(d,{icon:l(r)(e.percent)>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)])])}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/statistics/trade/components/TradeStatisticValue.vue"]])});export{J as __tla,f as default};
