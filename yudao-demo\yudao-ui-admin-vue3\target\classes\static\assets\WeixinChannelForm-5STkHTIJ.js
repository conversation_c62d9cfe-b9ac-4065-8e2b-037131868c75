import{d as j,n as B,I as E,r as f,o as c,c as _,i as a,w as t,a as i,j as d,H as G,l as K,F as H,k as Z,cK as Q,G as X,t as Y,a9 as U,z as $,Z as ee,L as ae,am as le,an as te,x as ie,N as oe,bD as re,O as ne,R as pe,_ as de,__tla as se}from"./index-Daqg4PFz.js";import{_ as ue,__tla as ce}from"./Dialog-BjBBVYCI.js";import{C as me}from"./constants-WoCEnNvc.js";import{g as fe,c as ge,u as ye,__tla as _e}from"./index-Dl3EQuP9.js";let q,ve=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return _e}catch{}})()]).then(async()=>{let V,w;V={key:0},w={key:1},q=de(j({name:"WeixinChannelForm",__name:"WeixinChannelForm",emits:["success"],setup(be,{expose:A,emit:F}){const{t:C}=B(),g=E(),s=f(!1),x=f(""),u=f(!1),l=f({appId:"",code:"",status:void 0,feeRate:void 0,remark:"",config:{appId:"",mchId:"",apiVersion:"",mchKey:"",keyContent:"",privateKeyContent:"",privateCertContent:"",apiV3Key:""}}),P={feeRate:[{required:!0,message:"\u8BF7\u8F93\u5165\u6E20\u9053\u8D39\u7387",trigger:"blur"}],status:[{required:!0,message:"\u6E20\u9053\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],"config.mchId":[{required:!0,message:"\u8BF7\u4F20\u5165\u5546\u6237\u53F7",trigger:"blur"}],"config.appId":[{required:!0,message:"\u8BF7\u8F93\u5165\u516C\u4F17\u53F7APPID",trigger:"blur"}],"config.apiVersion":[{required:!0,message:"API\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],"config.mchKey":[{required:!0,message:"\u8BF7\u8F93\u5165\u5546\u6237\u5BC6\u94A5",trigger:"blur"}],"config.keyContent":[{required:!0,message:"\u8BF7\u4E0A\u4F20 apiclient_cert.p12 \u8BC1\u4E66",trigger:"blur"}],"config.privateKeyContent":[{required:!0,message:"\u8BF7\u4E0A\u4F20 apiclient_key.pem \u8BC1\u4E66",trigger:"blur"}],"config.privateCertContent":[{required:!0,message:"\u8BF7\u4E0A\u4F20 apiclient_cert.pem\u8BC1 \u4E66",trigger:"blur"}],"config.apiV3Key":[{required:!0,message:"\u8BF7\u4E0A\u4F20 api V3 \u5BC6\u94A5\u503C",trigger:"blur"}]},y=f();A({open:async(n,e)=>{s.value=!0,u.value=!0,N(n,e);try{const r=await fe(n,e);r&&r.id&&(l.value=r,l.value.config=JSON.parse(r.config)),x.value=l.value.id?"\u7F16\u8F91\u652F\u4ED8\u6E20\u9053":"\u521B\u5EFA\u652F\u4ED8\u6E20\u9053"}finally{u.value=!1}}});const z=F,S=async()=>{if(y&&await y.value.validate()){u.value=!0;try{const n={...l.value};n.config=JSON.stringify(l.value.config),n.id?(await ye(n),g.success(C("common.updateSuccess"))):(await ge(n),g.success(C("common.createSuccess"))),s.value=!1,z("success")}finally{u.value=!1}}},N=(n,e)=>{var r;l.value={appId:n,code:e,status:me.ENABLE,feeRate:void 0,remark:"",config:{appId:"",mchId:"",apiVersion:"",mchKey:"",keyContent:"",privateKeyContent:"",privateCertContent:"",apiV3Key:""}},(r=y.value)==null||r.resetFields()},I=(n,e)=>{if("."+n.name.split(".")[1]!==e){debugger;return g.error('\u8BF7\u4E0A\u4F20\u6307\u5B9A\u683C\u5F0F"'+e+'"\u6587\u4EF6'),!1}let r=n.size/1024/1024<2;return r||g.error("\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7 2MB"),r},D=n=>{I(n,".p12")},R=n=>{I(n,".pem")},O=n=>{const e=new FileReader;e.onload=r=>{l.value.config.privateKeyContent=r.target.result},e.readAsText(n.file)},T=n=>{const e=new FileReader;e.onload=r=>{l.value.config.privateCertContent=r.target.result},e.readAsText(n.file)},J=n=>{const e=new FileReader;e.onload=r=>{l.value.config.keyContent=r.target.result.split(",")[1]},e.readAsDataURL(n.file)};return(n,e)=>{const r=ee,p=ae,v=le,k=te,b=ie,m=oe,h=re,L=ne,M=ue,W=pe;return c(),_("div",null,[a(M,{modelValue:i(s),"onUpdate:modelValue":e[12]||(e[12]=o=>$(s)?s.value=o:null),title:i(x),onClose:n.close,width:"800px"},{footer:t(()=>[a(m,{disabled:i(u),type:"primary",onClick:S},{default:t(()=>[d("\u786E \u5B9A")]),_:1},8,["disabled"]),a(m,{onClick:e[11]||(e[11]=o=>s.value=!1)},{default:t(()=>[d("\u53D6 \u6D88")]),_:1})]),default:t(()=>[G((c(),K(L,{ref_key:"formRef",ref:y,model:i(l),rules:P,"label-width":"120px"},{default:t(()=>[a(p,{"label-width":"180px",label:"\u6E20\u9053\u8D39\u7387",prop:"feeRate"},{default:t(()=>[a(r,{modelValue:i(l).feeRate,"onUpdate:modelValue":e[0]||(e[0]=o=>i(l).feeRate=o),placeholder:"\u8BF7\u8F93\u5165\u6E20\u9053\u8D39\u7387",clearable:"",style:{width:"100%"}},{append:t(()=>[d("%")]),_:1},8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"\u5FAE\u4FE1 APPID",prop:"config.appId"},{default:t(()=>[a(r,{modelValue:i(l).config.appId,"onUpdate:modelValue":e[1]||(e[1]=o=>i(l).config.appId=o),placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1 APPID",clearable:"",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"\u5546\u6237\u53F7",prop:"config.mchId"},{default:t(()=>[a(r,{modelValue:i(l).config.mchId,"onUpdate:modelValue":e[2]||(e[2]=o=>i(l).config.mchId=o),style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"\u6E20\u9053\u72B6\u6001",prop:"status"},{default:t(()=>[a(k,{modelValue:i(l).status,"onUpdate:modelValue":e[3]||(e[3]=o=>i(l).status=o)},{default:t(()=>[(c(!0),_(H,null,Z(i(Q)(i(X).COMMON_STATUS),o=>(c(),K(v,{key:parseInt(o.value),label:parseInt(o.value)},{default:t(()=>[d(Y(o.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"API \u7248\u672C",prop:"config.apiVersion"},{default:t(()=>[a(k,{modelValue:i(l).config.apiVersion,"onUpdate:modelValue":e[4]||(e[4]=o=>i(l).config.apiVersion=o)},{default:t(()=>[a(v,{label:"v2"},{default:t(()=>[d("v2")]),_:1}),a(v,{label:"v3"},{default:t(()=>[d("v3")]),_:1})]),_:1},8,["modelValue"])]),_:1}),i(l).config.apiVersion==="v2"?(c(),_("div",V,[a(p,{"label-width":"180px",label:"\u5546\u6237\u5BC6\u94A5",prop:"config.mchKey"},{default:t(()=>[a(r,{modelValue:i(l).config.mchKey,"onUpdate:modelValue":e[5]||(e[5]=o=>i(l).config.mchKey=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u6237\u5BC6\u94A5",clearable:"",style:{width:"100%"},type:"textarea",autosize:{minRows:8,maxRows:8}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"apiclient_cert.p12 \u8BC1\u4E66",prop:"config.keyContent"},{default:t(()=>[a(r,{modelValue:i(l).config.keyContent,"onUpdate:modelValue":e[6]||(e[6]=o=>i(l).config.keyContent=o),type:"textarea",placeholder:"\u8BF7\u4E0A\u4F20 apiclient_cert.p12 \u8BC1\u4E66",readonly:"",autosize:{minRows:8,maxRows:8},style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:""},{default:t(()=>[a(h,{limit:1,accept:".p12",action:"","before-upload":D,"http-request":J},{default:t(()=>[a(m,{type:"primary"},{default:t(()=>[a(b,{icon:"ep:upload",class:"mr-5px"}),d(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:1})]),_:1})])):U("",!0),i(l).config.apiVersion==="v3"?(c(),_("div",w,[a(p,{"label-width":"180px",label:"API V3 \u5BC6\u94A5",prop:"config.apiV3Key"},{default:t(()=>[a(r,{modelValue:i(l).config.apiV3Key,"onUpdate:modelValue":e[7]||(e[7]=o=>i(l).config.apiV3Key=o),placeholder:"\u8BF7\u8F93\u5165 API V3 \u5BC6\u94A5",clearable:"",style:{width:"100%"},type:"textarea",autosize:{minRows:8,maxRows:8}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"apiclient_key.pem \u8BC1\u4E66",prop:"config.privateKeyContent"},{default:t(()=>[a(r,{modelValue:i(l).config.privateKeyContent,"onUpdate:modelValue":e[8]||(e[8]=o=>i(l).config.privateKeyContent=o),type:"textarea",placeholder:"\u8BF7\u4E0A\u4F20 apiclient_key.pem \u8BC1\u4E66",readonly:"",autosize:{minRows:8,maxRows:8},style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"",prop:"privateKeyContentFile"},{default:t(()=>[a(h,{ref:"privateKeyContentFile",limit:1,accept:".pem",action:"","before-upload":R,"http-request":O},{default:t(()=>[a(m,{type:"primary"},{default:t(()=>[a(b,{icon:"ep:upload",class:"mr-5px"}),d(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:1},512)]),_:1}),a(p,{"label-width":"180px",label:"apiclient_cert.pem\u8BC1\u4E66",prop:"config.privateCertContent"},{default:t(()=>[a(r,{modelValue:i(l).config.privateCertContent,"onUpdate:modelValue":e[9]||(e[9]=o=>i(l).config.privateCertContent=o),type:"textarea",placeholder:"\u8BF7\u4E0A\u4F20apiclient_cert.pem\u8BC1\u4E66",readonly:"",autosize:{minRows:8,maxRows:8},style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(p,{"label-width":"180px",label:"",prop:"privateCertContentFile"},{default:t(()=>[a(h,{ref:"privateCertContentFile",limit:1,accept:".pem",action:"","before-upload":R,"http-request":T},{default:t(()=>[a(m,{type:"primary"},{default:t(()=>[a(b,{icon:"ep:upload",class:"mr-5px"}),d(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:1},512)]),_:1})])):U("",!0),a(p,{"label-width":"180px",label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[a(r,{modelValue:i(l).remark,"onUpdate:modelValue":e[10]||(e[10]=o=>i(l).remark=o),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[W,i(u)]])]),_:1},8,["modelValue","title","onClose"])])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/pay/app/components/channel/WeixinChannelForm.vue"]])});export{ve as __tla,q as default};
