import{d as ca,I as pa,n as ma,u as _a,r as y,bd as fa,C as ya,T as wa,o as u,c as D,i as a,w as e,a as l,U as ha,j as d,H as b,l as p,F as L,k as va,g as w,t as k,aG as N,aK as ba,Z as ka,L as ga,ci as Ca,M as xa,x as Va,N as Sa,O as Ua,A as Ea,B as Ta,E as Ia,s as Pa,P as Da,aO as La,cj as Na,ax as Aa,Q as Ya,R as Ba,_ as Ra,__tla as $a}from"./index-Daqg4PFz.js";import{_ as ja,__tla as za}from"./index-BBLwwrga.js";import{E as Fa,__tla as Ha}from"./el-image-Bn34T02c.js";import{_ as Ka,__tla as Ma}from"./ContentWrap-DZg14iby.js";import{_ as Oa,__tla as Ja}from"./index-CmwFi8Xl.js";import{d as Za,__tla as qa}from"./formatTime-BCfRGyrF.js";import{h as Ga,d as Qa,t as Wa}from"./tree-BMqZf9_I.js";import{d as C}from"./constants-WoCEnNvc.js";import{d as Xa}from"./download--D_IyRio.js";import{d as ae,a as ee,e as J,f as te,h as le,__tla as ne}from"./spu-zkQh6zUd.js";import{g as se,__tla as re}from"./category-D3voy_BE.js";import{__tla as oe}from"./index-CS70nJJ8.js";import{__tla as ue}from"./el-card-Dvjjuipo.js";let Z,ie=Promise.all([(()=>{try{return $a}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{let A,Y;A={class:"flex"},Y={class:"ml-4 overflow-hidden"},Z=Ra(ca({name:"ProductSpu",__name:"index",setup(de){const h=pa(),{t:q}=ma(),{push:B}=_a(),E=y(!1),T=y(!1),R=y(0),$=y([]),j=y([{name:"\u51FA\u552E\u4E2D",type:0,count:0},{name:"\u4ED3\u5E93\u4E2D",type:1,count:0},{name:"\u5DF2\u552E\u7F44",type:2,count:0},{name:"\u8B66\u6212\u5E93\u5B58",type:3,count:0},{name:"\u56DE\u6536\u7AD9",type:4,count:0}]),r=y({pageNo:1,pageSize:10,tabType:0,name:"",categoryId:void 0,createTime:void 0}),z=y(),f=async()=>{E.value=!0;try{const s=await ae(r.value);$.value=s.list,R.value=s.total}finally{E.value=!1}},G=s=>{r.value.tabType=s.paneName,f()},x=async()=>{const s=await ee();for(let n in s)j.value[Number(n)].count=s[n]},F=async(s,n)=>{try{const V=n===C.RECYCLE.status?"\u52A0\u5165\u5230\u56DE\u6536\u7AD9":"\u6062\u590D\u5230\u4ED3\u5E93";await h.confirm(`\u786E\u8BA4\u8981"${s.name}"${V}\u5417\uFF1F`),await J({id:s.id,status:n}),h.success(V+"\u6210\u529F"),await x(),await f()}catch{}},I=()=>{f()},Q=()=>{z.value.resetFields(),I()},H=s=>{B(typeof s!="number"?{name:"ProductSpuAdd"}:{name:"ProductSpuEdit",params:{id:s}})},W=async()=>{try{await h.exportConfirm(),T.value=!0;const s=await le(r);Xa.excel(s,"\u5546\u54C1\u5217\u8868.xls")}catch{}finally{T.value=!1}},P=y();return fa(()=>{f()}),ya(async()=>{await x(),await f();const s=await se({});P.value=Ga(s,"id","parentId")}),(s,n)=>{const V=Oa,X=ka,m=ga,aa=Ca,ea=xa,S=Va,_=Sa,K=Ua,M=Ka,ta=Ea,la=Ta,v=Ia,U=Pa,c=Da,na=Fa,sa=La,ra=Na,oa=Aa,ua=Ya,ia=ja,g=wa("hasPermi"),da=Ba;return u(),D(L,null,[a(V,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1 SPU \u4E0E SKU",url:"https://doc.iocoder.cn/mall/product-spu-sku/"}),a(M,null,{default:e(()=>[a(K,{ref_key:"queryFormRef",ref:z,inline:!0,model:l(r),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(m,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:e(()=>[a(X,{modelValue:l(r).name,"onUpdate:modelValue":n[0]||(n[0]=t=>l(r).name=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",onKeyup:ha(I,["enter"])},null,8,["modelValue"])]),_:1}),a(m,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:e(()=>[a(aa,{modelValue:l(r).categoryId,"onUpdate:modelValue":n[1]||(n[1]=t=>l(r).categoryId=t),options:l(P),props:l(Qa),class:"w-1/1",clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","options","props"])]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:e(()=>[a(ea,{modelValue:l(r).createTime,"onUpdate:modelValue":n[2]||(n[2]=t=>l(r).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:e(()=>[a(_,{onClick:I},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:search"}),d(" \u641C\u7D22 ")]),_:1}),a(_,{onClick:Q},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:refresh"}),d(" \u91CD\u7F6E ")]),_:1}),b((u(),p(_,{plain:"",type:"primary",onClick:n[3]||(n[3]=t=>H(void 0))},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:plus"}),d(" \u65B0\u589E ")]),_:1})),[[g,["product:spu:create"]]]),b((u(),p(_,{loading:l(T),plain:"",type:"success",onClick:W},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:download"}),d(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[g,["product:spu:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(M,null,{default:e(()=>[a(la,{modelValue:l(r).tabType,"onUpdate:modelValue":n[4]||(n[4]=t=>l(r).tabType=t),onTabClick:G},{default:e(()=>[(u(!0),D(L,null,va(l(j),t=>(u(),p(ta,{key:t.type,label:t.name+"("+t.count+")",name:t.type},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),b((u(),p(ua,{data:l($)},{default:e(()=>[a(c,{type:"expand"},{default:e(({row:t})=>[a(K,{class:"spu-table-expand","label-position":"left"},{default:e(()=>[a(U,null,{default:e(()=>[a(v,{span:24},{default:e(()=>[a(U,null,{default:e(()=>[a(v,{span:8},{default:e(()=>[a(m,{label:"\u5546\u54C1\u5206\u7C7B:"},{default:e(()=>{return[w("span",null,k((i=t.categoryId,Wa(P.value,i))),1)];var i}),_:2},1024)]),_:2},1024),a(v,{span:8},{default:e(()=>[a(m,{label:"\u5E02\u573A\u4EF7:"},{default:e(()=>[w("span",null,k(l(N)(t.marketPrice)),1)]),_:2},1024)]),_:2},1024),a(v,{span:8},{default:e(()=>[a(m,{label:"\u6210\u672C\u4EF7:"},{default:e(()=>[w("span",null,k(l(N)(t.costPrice)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024),a(U,null,{default:e(()=>[a(v,{span:24},{default:e(()=>[a(U,null,{default:e(()=>[a(v,{span:8},{default:e(()=>[a(m,{label:"\u6D4F\u89C8\u91CF:"},{default:e(()=>[w("span",null,k(t.browseCount),1)]),_:2},1024)]),_:2},1024),a(v,{span:8},{default:e(()=>[a(m,{label:"\u865A\u62DF\u9500\u91CF:"},{default:e(()=>[w("span",null,k(t.virtualSalesCount),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:1}),a(c,{label:"\u5546\u54C1\u7F16\u53F7","min-width":"140",prop:"id"}),a(c,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"300"},{default:e(({row:t})=>[w("div",A,[a(na,{fit:"cover",src:t.picUrl,class:"flex-none w-50px h-50px",onClick:i=>{return o=t.picUrl,void ba({urlList:[o]});var o}},null,8,["src","onClick"]),w("div",Y,[a(sa,{effect:"dark",content:t.name,placement:"top"},{default:e(()=>[w("div",null,k(t.name),1)]),_:2},1032,["content"])])])]),_:1}),a(c,{align:"center",label:"\u4EF7\u683C","min-width":"160",prop:"price"},{default:e(({row:t})=>[d(" \xA5 "+k(l(N)(t.price)),1)]),_:1}),a(c,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),a(c,{align:"center",label:"\u5E93\u5B58","min-width":"90",prop:"stock"}),a(c,{align:"center",label:"\u6392\u5E8F","min-width":"70",prop:"sort"}),a(c,{align:"center",label:"\u9500\u552E\u72B6\u6001","min-width":"80"},{default:e(({row:t})=>[t.status>=0?(u(),p(ra,{key:0,modelValue:t.status,"onUpdate:modelValue":i=>t.status=i,"active-value":1,"inactive-value":0,"active-text":"\u4E0A\u67B6","inactive-text":"\u4E0B\u67B6","inline-prompt":"",onChange:i=>(async o=>{try{const O=o.status?"\u4E0A\u67B6":"\u4E0B\u67B6";await h.confirm(`\u786E\u8BA4\u8981${O}"${o.name}"\u5417\uFF1F`),await J({id:o.id,status:o.status}),h.success(O+"\u6210\u529F"),await x(),await f()}catch{o.status=o.status===C.DISABLE.status?C.ENABLE.status:C.DISABLE.status}})(t)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(u(),p(oa,{key:1,type:"info"},{default:e(()=>[d("\u56DE\u6536\u7AD9")]),_:1}))]),_:1}),a(c,{formatter:l(Za),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(c,{align:"center",fixed:"right",label:"\u64CD\u4F5C","min-width":"200"},{default:e(({row:t})=>[a(_,{link:"",type:"primary",onClick:i=>{return o=t.id,void B({name:"ProductSpuDetail",params:{id:o}});var o}},{default:e(()=>[d(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"]),b((u(),p(_,{link:"",type:"primary",onClick:i=>H(t.id)},{default:e(()=>[d(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]]),l(r).tabType===4?(u(),D(L,{key:0},[b((u(),p(_,{link:"",type:"danger",onClick:i=>(async o=>{try{await h.delConfirm(),await te(o),h.success(q("common.delSuccess")),await x(),await f()}catch{}})(t.id)},{default:e(()=>[d(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["product:spu:delete"]]]),b((u(),p(_,{link:"",type:"primary",onClick:i=>F(t,l(C).DISABLE.status)},{default:e(()=>[d(" \u6062\u590D ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]])],64)):b((u(),p(_,{key:1,link:"",type:"danger",onClick:i=>F(t,l(C).RECYCLE.status)},{default:e(()=>[d(" \u56DE\u6536 ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]])]),_:1})]),_:1},8,["data"])),[[da,l(E)]]),a(ia,{limit:l(r).pageSize,"onUpdate:limit":n[5]||(n[5]=t=>l(r).pageSize=t),page:l(r).pageNo,"onUpdate:page":n[6]||(n[6]=t=>l(r).pageNo=t),total:l(R),onPagination:f},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__scopeId","data-v-b78c7ed6"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/product/spu/index.vue"]])});export{ie as __tla,Z as default};
