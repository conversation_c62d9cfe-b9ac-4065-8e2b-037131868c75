import{_ as q,__tla as z}from"./ComponentContainerProperty-S0tn7S7r.js";import{d as P,o as s,l as i,w as l,i as a,a as o,j as _,a9 as F,c as J,F as L,x as N,aN as A,aO as B,an as I,L as Q,am as S,cj as W,ck as G,cq as H,cs as K,O as M,_ as R,__tla as T}from"./index-Daqg4PFz.js";import{_ as X,__tla as Y}from"./index-DJKCzxE6.js";import{_ as Z,__tla as $}from"./index-DMPh3Ayy.js";import{E as aa,__tla as la}from"./el-card-Dvjjuipo.js";import{E as ea,__tla as ta}from"./el-text-vv1naHK-.js";import{u as oa,__tla as ra}from"./util-BXiX1W-V.js";import{__tla as ma}from"./index-D5jdnmIf.js";import"./color-BN7ZL7BD.js";import{__tla as ua}from"./vuedraggable.umd-BozBW0_1.js";import{__tla as _a}from"./AppLinkSelectDialog-D0bg80Di.js";import{__tla as da}from"./Dialog-BjBBVYCI.js";import{__tla as sa}from"./ProductCategorySelect-DXgDK0XS.js";import{__tla as pa}from"./el-tree-select-BKcJcOKx.js";import"./tree-BMqZf9_I.js";import{__tla as ia}from"./category-D3voy_BE.js";import{__tla as na}from"./Qrcode-CIHNtQVl.js";import{__tla as ca}from"./IFrame-DOdFY0xB.js";import{__tla as fa}from"./el-collapse-item-CUcELNOM.js";let h,ya=Promise.all([(()=>{try{return z}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return fa}catch{}})()]).then(async()=>{h=R(P({name:"CarouselProperty",__name:"property",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(b,{emit:U}){const x=b,w=U,{formData:t}=oa(x.modelValue,w);return(Va,r)=>{const n=N,c=A,f=B,p=I,m=Q,d=S,v=W,g=G,k=ea,y=aa,V=H,D=K,E=Z,j=X,C=M,O=q;return s(),i(O,{modelValue:o(t).style,"onUpdate:modelValue":r[5]||(r[5]=e=>o(t).style=e)},{default:l(()=>[a(C,{"label-width":"80px",model:o(t)},{default:l(()=>[a(y,{header:"\u6837\u5F0F\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[a(m,{label:"\u6837\u5F0F",prop:"type"},{default:l(()=>[a(p,{modelValue:o(t).type,"onUpdate:modelValue":r[0]||(r[0]=e=>o(t).type=e)},{default:l(()=>[a(f,{class:"item",content:"\u9ED8\u8BA4",placement:"bottom"},{default:l(()=>[a(c,{label:"default"},{default:l(()=>[a(n,{icon:"system-uicons:carousel"})]),_:1})]),_:1}),a(f,{class:"item",content:"\u5361\u7247",placement:"bottom"},{default:l(()=>[a(c,{label:"card"},{default:l(()=>[a(n,{icon:"ic:round-view-carousel"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u6307\u793A\u5668",prop:"indicator"},{default:l(()=>[a(p,{modelValue:o(t).indicator,"onUpdate:modelValue":r[1]||(r[1]=e=>o(t).indicator=e)},{default:l(()=>[a(d,{label:"dot"},{default:l(()=>[_("\u5C0F\u5706\u70B9")]),_:1}),a(d,{label:"number"},{default:l(()=>[_("\u6570\u5B57")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u662F\u5426\u8F6E\u64AD",prop:"autoplay"},{default:l(()=>[a(v,{modelValue:o(t).autoplay,"onUpdate:modelValue":r[2]||(r[2]=e=>o(t).autoplay=e)},null,8,["modelValue"])]),_:1}),o(t).autoplay?(s(),i(m,{key:0,label:"\u64AD\u653E\u95F4\u9694",prop:"interval"},{default:l(()=>[a(g,{modelValue:o(t).interval,"onUpdate:modelValue":r[3]||(r[3]=e=>o(t).interval=e),max:10,min:.5,step:.5,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"]),a(k,{type:"info"},{default:l(()=>[_("\u5355\u4F4D\uFF1A\u79D2")]),_:1})]),_:1})):F("",!0)]),_:1}),a(y,{header:"\u5185\u5BB9\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[a(j,{modelValue:o(t).items,"onUpdate:modelValue":r[4]||(r[4]=e=>o(t).items=e),"empty-item":{type:"img"}},{default:l(({element:e})=>[a(m,{label:"\u7C7B\u578B",prop:"type",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(p,{modelValue:e.type,"onUpdate:modelValue":u=>e.type=u},{default:l(()=>[a(d,{label:"img"},{default:l(()=>[_("\u56FE\u7247")]),_:1}),a(d,{label:"video"},{default:l(()=>[_("\u89C6\u9891")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.type==="img"?(s(),i(m,{key:0,label:"\u56FE\u7247",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(V,{modelValue:e.imgUrl,"onUpdate:modelValue":u=>e.imgUrl=u,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):(s(),J(L,{key:1},[a(m,{label:"\u5C01\u9762",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(V,{modelValue:e.imgUrl,"onUpdate:modelValue":u=>e.imgUrl=u,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),a(m,{label:"\u89C6\u9891",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(D,{modelValue:e.videoUrl,"onUpdate:modelValue":u=>e.videoUrl=u,"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)),a(m,{label:"\u94FE\u63A5",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(E,{modelValue:e.url,"onUpdate:modelValue":u=>e.url=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/Carousel/property.vue"]])});export{ya as __tla,h as default};
