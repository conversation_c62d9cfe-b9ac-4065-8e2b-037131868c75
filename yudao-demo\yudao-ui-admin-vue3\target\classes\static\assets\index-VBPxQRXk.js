import{d as le,I as te,n as re,r as n,f as oe,C as se,ay as ue,T as ie,o as s,c as b,i as a,w as r,a as l,U as Y,F as g,k as S,l as i,V as de,G as E,j as c,H as m,e5 as ne,dV as ce,Z as pe,L as me,J as _e,K as fe,M as ve,x as ye,N as ke,O as he,P as we,Q as be,R as ge,_ as xe,__tla as Ve}from"./index-Daqg4PFz.js";import{_ as Ce,__tla as Se}from"./index-BBLwwrga.js";import{_ as Ue,__tla as Te}from"./DictTag-BDZzHcIz.js";import{_ as Ie,__tla as Pe}from"./ContentWrap-DZg14iby.js";import{_ as Ne,__tla as Me}from"./index-CmwFi8Xl.js";import{b as De,__tla as We}from"./formatTime-BCfRGyrF.js";import{d as Re}from"./download--D_IyRio.js";import{S as qe,a as U,__tla as ze}from"./StockMoveForm-L0DJEK7K.js";import{P as Ae,__tla as He}from"./index-BdaXniMm.js";import{W as Ke,__tla as Le}from"./index-b9NHryvG.js";import{__tla as Ye}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Ee}from"./el-card-Dvjjuipo.js";import{__tla as Fe}from"./Dialog-BjBBVYCI.js";import{__tla as Je}from"./StockMoveItemForm-BCGj7Zqy.js";import{__tla as je}from"./index-BUJ03bwx.js";let F,Ge=Promise.all([(()=>{try{return Ve}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return je}catch{}})()]).then(async()=>{F=xe(le({name:"ErpStockMove",__name:"index",setup(Oe){const k=te(),{t:J}=re(),T=n(!0),N=n([]),M=n(0),o=oe({pageNo:1,pageSize:10,no:void 0,productId:void 0,fromWarehouseId:void 0,moveTime:[],status:void 0,remark:void 0,creator:void 0}),D=n(),I=n(!1),W=n([]),R=n([]),q=n([]),v=async()=>{T.value=!0;try{const u=await U.getStockMovePage(o);N.value=u.list,M.value=u.total}finally{T.value=!1}},x=()=>{o.pageNo=1,v()},j=()=>{D.value.resetFields(),x()},z=n(),P=(u,t)=>{z.value.open(u,t)},A=async u=>{try{await k.delConfirm(),await U.deleteStockMove(u),k.success(J("common.delSuccess")),await v(),h.value=h.value.filter(t=>!u.includes(t.id))}catch{}},H=async(u,t)=>{try{await k.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u8C03\u5EA6\u5355\u5417\uFF1F`),await U.updateStockMoveStatus(u,t),k.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},G=async()=>{try{await k.exportConfirm(),I.value=!0;const u=await U.exportStockMove(o);Re.excel(u,"\u5E93\u5B58\u8C03\u5EA6\u5355.xls")}catch{}finally{I.value=!1}},h=n([]),O=u=>{h.value=u};return se(async()=>{await v(),W.value=await Ae.getProductSimpleList(),R.value=await Ke.getWarehouseSimpleList(),q.value=await ue()}),(u,t)=>{const Q=Ne,K=pe,_=me,V=_e,C=fe,Z=ve,w=ye,d=ke,$=he,L=Ie,p=we,B=Ue,X=be,ee=Ce,f=ie("hasPermi"),ae=ge;return s(),b(g,null,[a(Q,{title:"\u3010\u5E93\u5B58\u3011\u5E93\u5B58\u8C03\u62E8\u3001\u5E93\u5B58\u76D8\u70B9",url:"https://doc.iocoder.cn/erp/stock-move-check/"}),a(L,null,{default:r(()=>[a($,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:D,inline:!0,"label-width":"68px"},{default:r(()=>[a(_,{label:"\u8C03\u5EA6\u5355\u53F7",prop:"no"},{default:r(()=>[a(K,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u8C03\u5EA6\u5355\u53F7",clearable:"",onKeyup:Y(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(C,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(s(!0),b(g,null,S(l(W),e=>(s(),i(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u8C03\u5EA6\u65F6\u95F4",prop:"moveTime"},{default:r(()=>[a(Z,{modelValue:l(o).moveTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).moveTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(_,{label:"\u4ED3\u5E93",prop:"fromWarehouseId"},{default:r(()=>[a(C,{modelValue:l(o).fromWarehouseId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).fromWarehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(s(!0),b(g,null,S(l(R),e=>(s(),i(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(C,{modelValue:l(o).creator,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(s(!0),b(g,null,S(l(q),e=>(s(),i(V,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[a(C,{modelValue:l(o).status,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(s(!0),b(g,null,S(l(de)(l(E).ERP_AUDIT_STATUS),e=>(s(),i(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(K,{modelValue:l(o).remark,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:Y(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,null,{default:r(()=>[a(d,{onClick:x},{default:r(()=>[a(w,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),a(d,{onClick:j},{default:r(()=>[a(w,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1}),m((s(),i(d,{type:"primary",plain:"",onClick:t[7]||(t[7]=e=>P("create"))},{default:r(()=>[a(w,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[f,["erp:stock-move:create"]]]),m((s(),i(d,{type:"success",plain:"",onClick:G,loading:l(I)},{default:r(()=>[a(w,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[f,["erp:stock-move:export"]]]),m((s(),i(d,{type:"danger",plain:"",onClick:t[8]||(t[8]=e=>A(l(h).map(y=>y.id))),disabled:l(h).length===0},{default:r(()=>[a(w,{icon:"ep:delete",class:"mr-5px"}),c(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(L,null,{default:r(()=>[m((s(),i(X,{data:l(N),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:O},{default:r(()=>[a(p,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(p,{"min-width":"180",label:"\u8C03\u5EA6\u5355\u53F7",align:"center",prop:"no"}),a(p,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(p,{label:"\u8C03\u5EA6\u65F6\u95F4",align:"center",prop:"moveTime",formatter:l(De),width:"120px"},null,8,["formatter"]),a(p,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(p,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(ne)},null,8,["formatter"]),a(p,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(ce)},null,8,["formatter"]),a(p,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(B,{type:l(E).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(p,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[m((s(),i(d,{link:"",onClick:y=>P("detail",e.row.id)},{default:r(()=>[c(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:query"]]]),m((s(),i(d,{link:"",type:"primary",onClick:y=>P("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[f,["erp:stock-move:update"]]]),e.row.status===10?m((s(),i(d,{key:0,link:"",type:"primary",onClick:y=>H(e.row.id,20)},{default:r(()=>[c(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:update-status"]]]):m((s(),i(d,{key:1,link:"",type:"danger",onClick:y=>H(e.row.id,10)},{default:r(()=>[c(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:update-status"]]]),m((s(),i(d,{link:"",type:"danger",onClick:y=>A([e.row.id])},{default:r(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["data"])),[[ae,l(T)]]),a(ee,{total:l(M),page:l(o).pageNo,"onUpdate:page":t[9]||(t[9]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[10]||(t[10]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(qe,{ref_key:"formRef",ref:z,onSuccess:v},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/stock/move/index.vue"]])});export{Ge as __tla,F as default};
