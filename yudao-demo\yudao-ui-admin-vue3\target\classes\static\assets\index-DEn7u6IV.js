import{_,__tla as l}from"./index-CmwFi8Xl.js";import{d as s,o as c,c as o,i as t,F as m,_ as e,__tla as i}from"./index-Daqg4PFz.js";import n,{__tla as u}from"./ProductSummary-BcOiwSj_.js";import p,{__tla as y}from"./ProductRank-CBF5Dcgq.js";import{__tla as f}from"./el-card-Dvjjuipo.js";import{__tla as h}from"./el-skeleton-item-BmS2F7Yy.js";import{__tla as d}from"./Echart-C33-KcLZ.js";import{__tla as v}from"./index-CfQWqgvA.js";import{__tla as x}from"./formatTime-BCfRGyrF.js";import{__tla as P}from"./product-4bIjnpY4.js";import{__tla as w}from"./index-CvS2v6KM.js";import{__tla as F}from"./CountTo-Dat_y5oU.js";import"./download--D_IyRio.js";import{__tla as J}from"./CardTitle-BD5ZuvK3.js";import{__tla as S}from"./index-BBLwwrga.js";import{__tla as U}from"./index-CS70nJJ8.js";import{__tla as b}from"./el-image-Bn34T02c.js";let r,g=Promise.all([(()=>{try{return l}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return u}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return v}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{r=e(s({name:"ProductStatistics",__name:"index",setup:j=>(k,q)=>{const a=_;return c(),o(m,null,[t(a,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),t(n),t(p,{class:"mt-16px"})],64)}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/statistics/product/index.vue"]])});export{g as __tla,r as default};
