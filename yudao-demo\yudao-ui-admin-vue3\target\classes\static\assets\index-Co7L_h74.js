import{bE as Y,d as $,I as ee,n as ae,r as u,f as le,bd as te,C as re,T as oe,o as p,c as b,i as e,w as r,a as l,F as y,k as T,l as n,V as se,G as O,U as pe,j as h,H as V,e5 as F,J as ue,K as ie,L as ne,Z as ce,M as de,x as _e,N as me,O as fe,P as be,Q as ye,R as he,_ as ve,__tla as we}from"./index-Daqg4PFz.js";import{_ as ge,__tla as xe}from"./index-BBLwwrga.js";import{_ as ke,__tla as Te}from"./DictTag-BDZzHcIz.js";import{_ as Ve,__tla as Ne}from"./ContentWrap-DZg14iby.js";import{_ as ze,__tla as Ce}from"./index-CmwFi8Xl.js";import{d as Ie,__tla as Pe}from"./formatTime-BCfRGyrF.js";import{d as Ue}from"./download--D_IyRio.js";import{P as Re,__tla as Ee}from"./index-BdaXniMm.js";import{W as Se,__tla as De}from"./index-b9NHryvG.js";import{__tla as Ye}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Oe}from"./el-card-Dvjjuipo.js";let H,Fe=Promise.all([(()=>{try{return we}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Oe}catch{}})()]).then(async()=>{let N,z;N=async d=>await Y.get({url:"/erp/stock-record/page",params:d}),z=async d=>await Y.download({url:"/erp/stock-record/export-excel",params:d}),H=ve($({name:"ErpStockRecord",__name:"index",setup(d){const K=ee();ae();const v=u(!0),C=u([]),I=u(0),t=le({pageNo:1,pageSize:10,productId:void 0,warehouseId:void 0,bizType:void 0,bizNo:void 0,createTime:[]}),P=u(),w=u(!1),U=u([]),R=u([]),_=async()=>{v.value=!0;try{const c=await N(t);C.value=c.list,I.value=c.total}finally{v.value=!1}},g=()=>{t.pageNo=1,_()},L=()=>{P.value.resetFields(),g()},M=u(),Z=async()=>{try{await K.exportConfirm(),w.value=!0;const c=await z(t);Ue.excel(c,"\u4EA7\u54C1\u5E93\u5B58\u660E\u7EC6.xls")}catch{}finally{w.value=!1}};return te(()=>{_()}),re(async()=>{await _(),U.value=await Re.getProductSimpleList(),R.value=await Se.getWarehouseSimpleList()}),(c,o)=>{const W=ze,x=ue,k=ie,i=ne,B=ce,J=de,m=_e,f=me,j=fe,E=Ve,s=be,q=ke,G=ye,Q=ge,S=oe("hasPermi"),A=he;return p(),b(y,null,[e(W,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),e(E,null,{default:r(()=>[e(j,{class:"-mb-15px",model:l(t),ref_key:"queryFormRef",ref:P,inline:!0,"label-width":"68px"},{default:r(()=>[e(i,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[e(k,{modelValue:l(t).productId,"onUpdate:modelValue":o[0]||(o[0]=a=>l(t).productId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(p(!0),b(y,null,T(l(U),a=>(p(),n(x,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[e(k,{modelValue:l(t).warehouseId,"onUpdate:modelValue":o[1]||(o[1]=a=>l(t).warehouseId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(p(!0),b(y,null,T(l(R),a=>(p(),n(x,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"\u7C7B\u578B",prop:"bizType"},{default:r(()=>[e(k,{modelValue:l(t).bizType,"onUpdate:modelValue":o[2]||(o[2]=a=>l(t).bizType=a),placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:r(()=>[(p(!0),b(y,null,T(l(se)(l(O).ERP_STOCK_RECORD_BIZ_TYPE),a=>(p(),n(x,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"\u4E1A\u52A1\u5355\u53F7",prop:"bizNo"},{default:r(()=>[e(B,{modelValue:l(t).bizNo,"onUpdate:modelValue":o[3]||(o[3]=a=>l(t).bizNo=a),placeholder:"\u8BF7\u8F93\u5165\u4E1A\u52A1\u5355\u53F7",clearable:"",onKeyup:pe(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(J,{modelValue:l(t).createTime,"onUpdate:modelValue":o[4]||(o[4]=a=>l(t).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),e(i,null,{default:r(()=>[e(f,{onClick:g},{default:r(()=>[e(m,{icon:"ep:search",class:"mr-5px"}),h(" \u641C\u7D22")]),_:1}),e(f,{onClick:L},{default:r(()=>[e(m,{icon:"ep:refresh",class:"mr-5px"}),h(" \u91CD\u7F6E")]),_:1}),V((p(),n(f,{type:"primary",plain:"",onClick:o[5]||(o[5]=a=>{return D="create",void M.value.open(D,X);var D,X})},{default:r(()=>[e(m,{icon:"ep:plus",class:"mr-5px"}),h(" \u65B0\u589E ")]),_:1})),[[S,["erp:stock-record:create"]]]),V((p(),n(f,{type:"success",plain:"",onClick:Z,loading:l(w)},{default:r(()=>[e(m,{icon:"ep:download",class:"mr-5px"}),h(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[S,["erp:stock-record:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,null,{default:r(()=>[V((p(),n(G,{data:l(C),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(s,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName"}),e(s,{label:"\u4EA7\u54C1\u5206\u7C7B",align:"center",prop:"categoryName"}),e(s,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unitName"}),e(s,{label:"\u4ED3\u5E93\u7F16\u53F7",align:"center",prop:"warehouseName"}),e(s,{label:"\u7C7B\u578B",align:"center",prop:"bizType","min-width":"100"},{default:r(a=>[e(q,{type:l(O).ERP_STOCK_RECORD_BIZ_TYPE,value:a.row.bizType},null,8,["type","value"])]),_:1}),e(s,{label:"\u51FA\u5165\u5E93\u5355\u53F7",align:"center",prop:"bizNo",width:"200"}),e(s,{label:"\u51FA\u5165\u5E93\u65E5\u671F",align:"center",prop:"createTime",formatter:l(Ie),width:"180px"},null,8,["formatter"]),e(s,{label:"\u51FA\u5165\u5E93\u6570\u91CF",align:"center",prop:"count",formatter:l(F)},null,8,["formatter"]),e(s,{label:"\u5E93\u5B58\u91CF",align:"center",prop:"totalCount",formatter:l(F)},null,8,["formatter"]),e(s,{label:"\u64CD\u4F5C\u4EBA",align:"center",prop:"creatorName"})]),_:1},8,["data"])),[[A,l(v)]]),e(Q,{total:l(I),page:l(t).pageNo,"onUpdate:page":o[6]||(o[6]=a=>l(t).pageNo=a),limit:l(t).pageSize,"onUpdate:limit":o[7]||(o[7]=a=>l(t).pageSize=a),onPagination:_},null,8,["total","page","limit"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/stock/record/index.vue"]])});export{Fe as __tla,H as default};
