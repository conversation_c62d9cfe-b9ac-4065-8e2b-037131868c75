import{d as B,r,at as b,C as j,o as s,l as C,w as P,g as y,av as l,a,c as p,F as U,k as z,i as g,a9 as f,a0 as E,t as R,b3 as L,_ as S,__tla as q}from"./index-Daqg4PFz.js";import{E as D,__tla as F}from"./el-image-Bn34T02c.js";import{g as G,__tla as J}from"./spu-zkQh6zUd.js";import{g as O,__tla as W}from"./seckillActivity-DX1pM3ss.js";let k,A=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return W}catch{}})()]).then(async()=>{let m,v;m={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},v=B({name:"PromotionS<PERSON>ill",__name:"index",props:{property:{type:Object,required:!0}},setup($){const o=$,i=r([]);b(()=>o.property.activityId,async()=>{if(!o.property.activityId)return;const e=await O(o.property.activityId);e!=null&&e.spuId&&(i.value=[await G(e.spuId)])},{immediate:!0,deep:!0});const c=r(375),_=r(),t=r(2),x=r("100%"),d=r("0"),h=r("");return b(()=>[o.property,c,i.value.length],()=>{t.value=o.property.layoutType==="oneCol"?1:3;const e=(c.value-o.property.space*(t.value-1))/t.value;d.value=t.value===2?"64px":`${e}px`,h.value=`repeat(${t.value}, auto)`,x.value="100%"},{immediate:!0,deep:!0}),j(()=>{var e,u;c.value=((u=(e=_.value)==null?void 0:e.wrapRef)==null?void 0:u.offsetWidth)||375}),(e,u)=>{const w=D,T=L;return s(),C(T,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:_},{default:P(()=>[y("div",{class:"grid overflow-x-auto",style:l({gridGap:`${e.property.space}px`,gridTemplateColumns:a(h),width:a(x)})},[(s(!0),p(U,null,z(a(i),(n,I)=>(s(),p("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:l({borderTopLeftRadius:`${e.property.borderRadiusTop}px`,borderTopRightRadius:`${e.property.borderRadiusTop}px`,borderBottomLeftRadius:`${e.property.borderRadiusBottom}px`,borderBottomRightRadius:`${e.property.borderRadiusBottom}px`}),key:I},[e.property.badge.show?(s(),p("div",m,[g(w,{fit:"cover",src:e.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):f("",!0),g(w,{fit:"cover",src:n.picUrl,style:l({width:a(d),height:a(d)})},null,8,["src","style"]),y("div",{class:E(["flex flex-col gap-8px p-8px box-border",{"w-[calc(100%-64px)]":a(t)===2,"w-full":a(t)===3}])},[e.property.fields.name.show?(s(),p("div",{key:0,class:"truncate text-12px",style:l({color:e.property.fields.name.color})},R(n.name),5)):f("",!0),y("div",null,[e.property.fields.price.show?(s(),p("span",{key:0,class:"text-12px",style:l({color:e.property.fields.price.color})}," \uFFE5"+R(n.price),5)):f("",!0)])],2)],4))),128))],4)]),_:1},512)}}}),k=S(v,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/PromotionSeckill/index.vue"]])});export{A as __tla,k as default};
