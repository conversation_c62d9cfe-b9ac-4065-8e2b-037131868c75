import{d as C,n as J,I as S,r as o,o as i,l as c,w as u,i as r,a as l,j as p,H as z,c as H,F as N,k as D,z as K,Z as L,L as O,J as P,K as Z,O as q,N as B,R as E,_ as G,__tla as M}from"./index-Daqg4PFz.js";import{_ as Q,__tla as T}from"./Dialog-BjBBVYCI.js";import{c as W,d as X,__tla as Y}from"./index-DPIAYXsS.js";import{g as $,__tla as ee}from"./index-BCA1igdc.js";let V,ae=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return ee}catch{}})()]).then(async()=>{V=G(C({name:"SystemUserAssignRoleForm",__name:"UserAssignRoleForm",emits:["success"],setup(le,{expose:k,emit:b}){const{t:I}=J(),U=S(),d=o(!1),n=o(!1),e=o({id:-1,nickname:"",username:"",roleIds:[]}),m=o(),v=o([]);k({open:async t=>{d.value=!0,F(),e.value.id=t.id,e.value.username=t.username,e.value.nickname=t.nickname,n.value=!0;try{e.value.roleIds=await W(t.id)}finally{n.value=!1}v.value=await $()}});const h=b,w=async()=>{if(m&&await m.value.validate()){n.value=!0;try{await X({userId:e.value.id,roleIds:e.value.roleIds}),U.success(I("common.updateSuccess")),d.value=!1,h("success",!0)}finally{n.value=!1}}},F=()=>{var t;e.value={id:-1,nickname:"",username:"",roleIds:[]},(t=m.value)==null||t.resetFields()};return(t,s)=>{const f=L,_=O,R=P,g=Z,x=q,y=B,A=Q,j=E;return i(),c(A,{modelValue:l(d),"onUpdate:modelValue":s[4]||(s[4]=a=>K(d)?d.value=a:null),title:"\u5206\u914D\u89D2\u8272"},{footer:u(()=>[r(y,{disabled:l(n),type:"primary",onClick:w},{default:u(()=>[p("\u786E \u5B9A")]),_:1},8,["disabled"]),r(y,{onClick:s[3]||(s[3]=a=>d.value=!1)},{default:u(()=>[p("\u53D6 \u6D88")]),_:1})]),default:u(()=>[z((i(),c(x,{ref_key:"formRef",ref:m,model:l(e),"label-width":"80px"},{default:u(()=>[r(_,{label:"\u7528\u6237\u540D\u79F0"},{default:u(()=>[r(f,{modelValue:l(e).username,"onUpdate:modelValue":s[0]||(s[0]=a=>l(e).username=a),disabled:!0},null,8,["modelValue"])]),_:1}),r(_,{label:"\u7528\u6237\u6635\u79F0"},{default:u(()=>[r(f,{modelValue:l(e).nickname,"onUpdate:modelValue":s[1]||(s[1]=a=>l(e).nickname=a),disabled:!0},null,8,["modelValue"])]),_:1}),r(_,{label:"\u89D2\u8272"},{default:u(()=>[r(g,{modelValue:l(e).roleIds,"onUpdate:modelValue":s[2]||(s[2]=a=>l(e).roleIds=a),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272"},{default:u(()=>[(i(!0),H(N,null,D(l(v),a=>(i(),c(R,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[j,l(n)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/system/user/UserAssignRoleForm.vue"]])});export{ae as __tla,V as default};
