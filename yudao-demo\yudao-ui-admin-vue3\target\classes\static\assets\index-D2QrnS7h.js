import{d as o,r as s,at as l,o as p,c as _,a as c,_ as m,__tla as d}from"./index-Daqg4PFz.js";import{g as u,__tla as y}from"./index-Co7K9Yc8.js";let i,v=Promise.all([(()=>{try{return d}catch{}})(),(()=>{try{return y}catch{}})()]).then(async()=>{let e;e=["innerHTML"],i=m(o({name:"PromotionArticle",__name:"index",props:{property:{type:Object,required:!0}},setup(n){const t=n,a=s();return l(()=>t.property.id,async()=>{t.property.id&&(a.value=await u(t.property.id))},{immediate:!0}),(f,h)=>{var r;return p(),_("div",{class:"min-h-30px",innerHTML:(r=c(a))==null?void 0:r.content},null,8,e)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/PromotionArticle/index.vue"]])});export{v as __tla,i as default};
