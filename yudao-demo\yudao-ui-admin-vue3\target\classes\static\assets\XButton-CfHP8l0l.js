import{d as p,p as n,b as f,o as a,l as s,w as _,ao as m,a as I,bz as y,N as b,a9 as c,j as k,t as g,x,_ as C,__tla as v}from"./index-Daqg4PFz.js";let i,B=Promise.all([(()=>{try{return v}catch{}})()]).then(async()=>{i=C(p({name:"XButton",__name:"XButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def(""),link:n.bool.def(!1),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(e){const r=e,d=f(()=>{const l=["title","preIcon","postIcon","onClick"],t={...y(),...r};for(const o in t)l.indexOf(o)!==-1&&delete t[o];return t});return(l,t)=>{const o=x,u=b;return a(),s(u,m(I(d),{onClick:e.onClick}),{default:_(()=>[e.preIcon?(a(),s(o,{key:0,icon:e.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),k(" "+g(e.title?e.title:"")+" ",1),e.postIcon?(a(),s(o,{key:1,icon:e.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-440d118d"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/XButton/src/XButton.vue"]])});export{i as _,B as __tla};
