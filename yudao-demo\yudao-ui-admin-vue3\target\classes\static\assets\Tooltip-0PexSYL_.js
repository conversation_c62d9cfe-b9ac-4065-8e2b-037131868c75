import{d as o,p as e,o as i,c as p,g as r,t as c,i as a,w as u,F as _,x as m,aO as d,_ as f,__tla as g}from"./index-Daqg4PFz.js";let s,v=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{s=f(o({name:"Tooltip",__name:"Tooltip",props:{title:e.string.def(""),message:e.string.def(""),icon:e.string.def("ep:question-filled")},setup:t=>(x,y)=>{const n=m,l=d;return i(),p(_,null,[r("span",null,c(t.title),1),a(l,{content:t.message,placement:"top"},{default:u(()=>[a(n,{icon:t.icon,class:"relative top-1px ml-1px"},null,8,["icon"])]),_:1},8,["content"])],64)}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Tooltip/src/Tooltip.vue"]])});export{s as _,v as __tla};
