import{d as a,o as r,l as s,_ as o,__tla as l}from"./index-Daqg4PFz.js";import{E as n,__tla as _}from"./el-image-Bn34T02c.js";let e,i=Promise.all([(()=>{try{return l}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{e=o(a({name:"UserWallet",__name:"index",props:{property:{type:Object,required:!0}},setup:p=>(c,m)=>{const t=n;return r(),s(t,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/walletCardStyle.png"})}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/UserWallet/index.vue"]])});export{i as __tla,e as default};
