import{d as c,o as x,c as d,g as e,i as s,w as f,x as u,_,__tla as m}from"./index-Daqg4PFz.js";import{E as y,__tla as b}from"./el-avatar-DpVhY4zL.js";let n,v=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{let t,a,r,p,i;t={class:"flex flex-col"},a={class:"flex items-center justify-between p-x-18px p-y-24px"},r={class:"flex flex-1 items-center gap-16px"},p=e("span",{class:"text-18px font-bold"},"\u828B\u9053\u6E90\u7801",-1),i=e("div",{class:"flex items-center justify-between justify-between bg-white p-x-20px p-y-8px text-12px"},[e("span",{class:"color-#ff690d"},"\u70B9\u51FB\u7ED1\u5B9A\u624B\u673A\u53F7"),e("span",{class:"rounded-26px bg-#ff6100 p-x-8px p-y-5px color-white"},"\u53BB\u7ED1\u5B9A")],-1),n=_(c({name:"UserCard",__name:"index",props:{property:{type:Object,required:!0}},setup:w=>(g,h)=>{const l=u,o=y;return x(),d("div",t,[e("div",a,[e("div",r,[s(o,{size:60},{default:f(()=>[s(l,{icon:"ep:avatar",size:60})]),_:1}),p]),s(l,{icon:"tdesign:qrcode",size:20})]),i])}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/UserCard/index.vue"]])});export{v as __tla,n as default};
