import{_ as o,__tla as u}from"./ContentWrap-DZg14iby.js";import{_ as i,__tla as m}from"./IFrame-DOdFY0xB.js";import{_ as d,__tla as f}from"./index-CmwFi8Xl.js";import{d as p,r as _,C as h,o as y,c as g,i as t,w as v,a as w,F as x,_ as b,__tla as C}from"./index-Daqg4PFz.js";import{b as F,__tla as I}from"./index-Cz8k7H0s.js";import{__tla as J}from"./el-card-Dvjjuipo.js";let s,P=Promise.all([(()=>{try{return u}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{s=b(p({name:"InfraSwagger",__name:"index",setup(S){const l=_(!0),r=_("http://localhost:48080/doc.html");return h(async()=>{try{const a=await F("url.swagger");a&&a.length>0&&(r.value=a)}finally{l.value=!1}}),(a,U)=>{const e=d,n=i,c=o;return y(),g(x,null,[t(e,{title:"\u63A5\u53E3\u6587\u6863",url:"https://doc.iocoder.cn/api-doc/"}),t(c,null,{default:v(()=>[t(n,{src:w(r)},null,8,["src"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/swagger/index.vue"]])});export{P as __tla,s as default};
