import{d,o as t,c as s,g as o,F as b,k as m,i,w as v,av as n,t as g,x as f,_ as x,__tla as k}from"./index-Daqg4PFz.js";import{E as h,__tla as I}from"./el-image-Bn34T02c.js";let y,T=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{let l,c,p;l={class:"tab-bar"},c={class:"h-full w-full flex items-center justify-center"},p=d({name:"TabBar",__name:"index",props:{property:{type:Object,required:!0}},setup:U=>(r,j)=>{const u=f,_=h;return t(),s("div",l,[o("div",{class:"tab-bar-bg",style:n({background:r.property.style.bgType==="color"?r.property.style.bgColor:`url(${r.property.style.bgImg})`,backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},[(t(!0),s(b,null,m(r.property.items,(a,e)=>(t(),s("div",{key:e,class:"tab-bar-item"},[i(_,{src:e===0?a.activeIconUrl:a.iconUrl},{error:v(()=>[o("div",c,[i(u,{icon:"ep:picture"})])]),_:2},1032,["src"]),o("span",{style:n({color:e===0?r.property.style.activeColor:r.property.style.color})},g(a.text),5)]))),128))],4)])}}),y=x(p,[["__scopeId","data-v-12586d52"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/TabBar/index.vue"]])});export{T as __tla,y as default};
