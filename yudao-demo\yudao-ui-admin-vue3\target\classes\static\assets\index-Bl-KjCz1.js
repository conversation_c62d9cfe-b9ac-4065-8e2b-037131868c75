import{d as J,I as K,n as Q,r as p,f as Z,C as B,T as W,o as i,c as X,i as a,w as t,a as e,U as Y,j as _,H as d,l as m,dV as b,G as $,F as aa,Z as ea,L as ta,x as ra,N as la,O as oa,P as ca,Q as na,R as pa,_ as sa,__tla as ia}from"./index-Daqg4PFz.js";import{_ as _a,__tla as ua}from"./index-BBLwwrga.js";import{_ as da,__tla as ma}from"./DictTag-BDZzHcIz.js";import{_ as fa,__tla as ya}from"./ContentWrap-DZg14iby.js";import{E as ga,__tla as ha}from"./el-tree-select-BKcJcOKx.js";import{_ as wa,__tla as xa}from"./index-CmwFi8Xl.js";import{d as va,__tla as ba}from"./formatTime-BCfRGyrF.js";import{d as Ca}from"./download--D_IyRio.js";import{P as C,__tla as Pa}from"./index-BdaXniMm.js";import{P as ka,__tla as Na}from"./index-5n3H8eW8.js";import Sa,{__tla as Ua}from"./ProductForm-55B0Y-SW.js";import{h as Va,d as Ia}from"./tree-BMqZf9_I.js";import{__tla as Ra}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Ta}from"./el-card-Dvjjuipo.js";import{__tla as za}from"./Dialog-BjBBVYCI.js";import{__tla as Fa}from"./index-CqLcja70.js";import"./constants-WoCEnNvc.js";let R,La=Promise.all([(()=>{try{return ia}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Fa}catch{}})()]).then(async()=>{R=sa(J({name:"ErpProduct",__name:"index",setup(Ma){const g=K(),{t:T}=Q(),h=p(!0),P=p([]),k=p(0),l=Z({pageNo:1,pageSize:10,name:void 0,categoryId:void 0}),N=p(),w=p(!1),S=p([]),u=async()=>{h.value=!0;try{const n=await C.getProductPage(l);P.value=n.list,k.value=n.total}finally{h.value=!1}},x=()=>{l.pageNo=1,u()},z=()=>{N.value.resetFields(),x()},U=p(),V=(n,o)=>{U.value.open(n,o)},F=async()=>{try{await g.exportConfirm(),w.value=!0;const n=await C.exportProduct(l);Ca.excel(n,"\u4EA7\u54C1.xls")}catch{}finally{w.value=!1}};return B(async()=>{await u();const n=await ka.getProductCategorySimpleList();S.value=Va(n,"id","parentId")}),(n,o)=>{const L=wa,M=ea,v=ta,O=ga,f=ra,s=la,E=oa,I=fa,c=ca,j=da,q=na,A=_a,y=W("hasPermi"),D=pa;return i(),X(aa,null,[a(L,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u4FE1\u606F\u3001\u5206\u7C7B\u3001\u5355\u4F4D",url:"https://doc.iocoder.cn/erp/product/"}),a(I,null,{default:t(()=>[a(E,{class:"-mb-15px",model:e(l),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[a(v,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[a(M,{modelValue:e(l).name,"onUpdate:modelValue":o[0]||(o[0]=r=>e(l).name=r),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:Y(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,{label:"\u5206\u7C7B",prop:"categoryId"},{default:t(()=>[a(O,{modelValue:e(l).categoryId,"onUpdate:modelValue":o[1]||(o[1]=r=>e(l).categoryId=r),data:e(S),props:e(Ia),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B",class:"!w-240px"},null,8,["modelValue","data","props"])]),_:1}),a(v,null,{default:t(()=>[a(s,{onClick:x},{default:t(()=>[a(f,{icon:"ep:search",class:"mr-5px"}),_(" \u641C\u7D22")]),_:1}),a(s,{onClick:z},{default:t(()=>[a(f,{icon:"ep:refresh",class:"mr-5px"}),_(" \u91CD\u7F6E")]),_:1}),d((i(),m(s,{type:"primary",plain:"",onClick:o[2]||(o[2]=r=>V("create"))},{default:t(()=>[a(f,{icon:"ep:plus",class:"mr-5px"}),_(" \u65B0\u589E ")]),_:1})),[[y,["erp:product:create"]]]),d((i(),m(s,{type:"success",plain:"",onClick:F,loading:e(w)},{default:t(()=>[a(f,{icon:"ep:download",class:"mr-5px"}),_(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(I,null,{default:t(()=>[d((i(),m(q,{data:e(P),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(c,{label:"\u6761\u7801",align:"center",prop:"barCode"}),a(c,{label:"\u540D\u79F0",align:"center",prop:"name"}),a(c,{label:"\u89C4\u683C",align:"center",prop:"standard"}),a(c,{label:"\u5206\u7C7B",align:"center",prop:"categoryName"}),a(c,{label:"\u5355\u4F4D",align:"center",prop:"unitName"}),a(c,{label:"\u91C7\u8D2D\u4EF7\u683C",align:"center",prop:"purchasePrice",formatter:e(b)},null,8,["formatter"]),a(c,{label:"\u9500\u552E\u4EF7\u683C",align:"center",prop:"salePrice",formatter:e(b)},null,8,["formatter"]),a(c,{label:"\u6700\u4F4E\u4EF7\u683C",align:"center",prop:"minPrice",formatter:e(b)},null,8,["formatter"]),a(c,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(r=>[a(j,{type:e($).COMMON_STATUS,value:r.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(va),width:"180px"},null,8,["formatter"]),a(c,{label:"\u64CD\u4F5C",align:"center",width:"110"},{default:t(r=>[d((i(),m(s,{link:"",type:"primary",onClick:G=>V("update",r.row.id)},{default:t(()=>[_(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["erp:product:update"]]]),d((i(),m(s,{link:"",type:"danger",onClick:G=>(async H=>{try{await g.delConfirm(),await C.deleteProduct(H),g.success(T("common.delSuccess")),await u()}catch{}})(r.row.id)},{default:t(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[D,e(h)]]),a(A,{total:e(k),page:e(l).pageNo,"onUpdate:page":o[3]||(o[3]=r=>e(l).pageNo=r),limit:e(l).pageSize,"onUpdate:limit":o[4]||(o[4]=r=>e(l).pageSize=r),onPagination:u},null,8,["total","page","limit"])]),_:1}),a(Sa,{ref_key:"formRef",ref:U,onSuccess:u},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/product/product/index.vue"]])});export{La as __tla,R as default};
