import{d as m,r as _,C as y,o as l,c as f,i as e,w as p,a as r,H as d,l as h,a9 as k,F as v,R as w,_ as g,__tla as x}from"./index-Daqg4PFz.js";import{_ as b,__tla as j}from"./ContentWrap-DZg14iby.js";import{_ as C,__tla as F}from"./IFrame-DOdFY0xB.js";import{_ as H,__tla as I}from"./index-CmwFi8Xl.js";import{b as J,__tla as P}from"./index-Cz8k7H0s.js";import{__tla as R}from"./el-card-Dvjjuipo.js";let n,S=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{n=g(m({name:"InfraSkyWalking",__name:"index",setup(U){const t=_(!0),s=_("http://skywalking.shop.iocoder.cn");return y(async()=>{try{const a=await J("url.skywalking");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,W)=>{const o=H,c=C,i=b,u=w;return l(),f(v,null,[e(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(i,null,{default:p(()=>[r(t)?k("",!0):d((l(),h(c,{key:0,src:r(s)},null,8,["src"])),[[u,r(t)]])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/skywalking/index.vue"]])});export{S as __tla,n as default};
