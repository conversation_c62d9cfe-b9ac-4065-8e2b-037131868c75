import{d as Z,ec as B,n as W,I as X,r as _,f as Y,C as $,T as aa,o as n,c as M,i as a,w as e,a as t,U as ea,F as g,k as la,V as ta,G as N,l as p,j as u,H as m,a9 as ra,az as sa,ed as E,Z as oa,L as na,J as ua,K as ca,x as ia,N as _a,O as pa,P as ma,Q as da,R as fa,_ as ya,__tla as ha}from"./index-Daqg4PFz.js";import{_ as wa,__tla as va}from"./DictTag-BDZzHcIz.js";import{_ as ka,__tla as ba}from"./ContentWrap-DZg14iby.js";import{_ as Ca,__tla as xa}from"./index-CmwFi8Xl.js";import{h as Sa}from"./tree-BMqZf9_I.js";import{b as Ua,d as Ra,__tla as Va}from"./index-C-XtJZJa.js";import Oa,{__tla as Ta}from"./MenuForm-s8AAZWbI.js";import"./color-BN7ZL7BD.js";import{__tla as Fa}from"./el-card-Dvjjuipo.js";import{__tla as Ma}from"./Dialog-BjBBVYCI.js";import{__tla as ga}from"./Tooltip-0PexSYL_.js";import{__tla as Na}from"./index-CS70nJJ8.js";import{__tla as Ea}from"./el-tree-select-BKcJcOKx.js";import"./constants-WoCEnNvc.js";let L,La=Promise.all([(()=>{try{return ha}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ea}catch{}})()]).then(async()=>{L=ya(Z({name:"SystemMenu",__name:"index",setup(Pa){const{wsCache:U}=B(),{t:P}=W(),w=X(),v=_(!0),R=_([]),c=Y({name:void 0,status:void 0}),V=_(),k=_(!1),b=_(!0),d=async()=>{v.value=!0;try{const f=await Ua(c);R.value=Sa(f)}finally{v.value=!1}},C=()=>{d()},A=()=>{V.value.resetFields(),C()},O=_(),x=(f,r,y)=>{O.value.open(f,r,y)},I=()=>{b.value=!1,k.value=!k.value,sa(()=>{b.value=!0})},J=async()=>{try{await w.confirm("\u5373\u5C06\u66F4\u65B0\u7F13\u5B58\u5237\u65B0\u6D4F\u89C8\u5668\uFF01","\u5237\u65B0\u83DC\u5355\u7F13\u5B58"),U.delete(E.USER),U.delete(E.ROLE_ROUTERS),location.reload()}catch{}};return $(()=>{d()}),(f,r)=>{const y=Ca,K=oa,S=na,j=ua,q=ca,i=ia,s=_a,z=pa,T=ka,o=ma,D=wa,G=da,h=aa("hasPermi"),H=fa;return n(),M(g,null,[a(y,{title:"\u529F\u80FD\u6743\u9650",url:"https://doc.iocoder.cn/resource-permission"}),a(y,{title:"\u83DC\u5355\u8DEF\u7531",url:"https://doc.iocoder.cn/vue3/route/"}),a(T,null,{default:e(()=>[a(z,{ref_key:"queryFormRef",ref:V,inline:!0,model:t(c),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(S,{label:"\u83DC\u5355\u540D\u79F0",prop:"name"},{default:e(()=>[a(K,{modelValue:t(c).name,"onUpdate:modelValue":r[0]||(r[0]=l=>t(c).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u83DC\u5355\u540D\u79F0",onKeyup:ea(C,["enter"])},null,8,["modelValue"])]),_:1}),a(S,{label:"\u72B6\u6001",prop:"status"},{default:e(()=>[a(q,{modelValue:t(c).status,"onUpdate:modelValue":r[1]||(r[1]=l=>t(c).status=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u83DC\u5355\u72B6\u6001"},{default:e(()=>[(n(!0),M(g,null,la(t(ta)(t(N).COMMON_STATUS),l=>(n(),p(j,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(S,null,{default:e(()=>[a(s,{onClick:C},{default:e(()=>[a(i,{class:"mr-5px",icon:"ep:search"}),u(" \u641C\u7D22 ")]),_:1}),a(s,{onClick:A},{default:e(()=>[a(i,{class:"mr-5px",icon:"ep:refresh"}),u(" \u91CD\u7F6E ")]),_:1}),m((n(),p(s,{plain:"",type:"primary",onClick:r[2]||(r[2]=l=>x("create"))},{default:e(()=>[a(i,{class:"mr-5px",icon:"ep:plus"}),u(" \u65B0\u589E ")]),_:1})),[[h,["system:menu:create"]]]),a(s,{plain:"",type:"danger",onClick:I},{default:e(()=>[a(i,{class:"mr-5px",icon:"ep:sort"}),u(" \u5C55\u5F00/\u6298\u53E0 ")]),_:1}),a(s,{plain:"",onClick:J},{default:e(()=>[a(i,{class:"mr-5px",icon:"ep:refresh"}),u(" \u5237\u65B0\u83DC\u5355\u7F13\u5B58 ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(T,null,{default:e(()=>[t(b)?m((n(),p(G,{key:0,data:t(R),"default-expand-all":t(k),"row-key":"id"},{default:e(()=>[a(o,{"show-overflow-tooltip":!0,label:"\u83DC\u5355\u540D\u79F0",prop:"name",width:"250"}),a(o,{align:"center",label:"\u56FE\u6807",prop:"icon",width:"100"},{default:e(l=>[a(i,{icon:l.row.icon},null,8,["icon"])]),_:1}),a(o,{label:"\u6392\u5E8F",prop:"sort",width:"60"}),a(o,{"show-overflow-tooltip":!0,label:"\u6743\u9650\u6807\u8BC6",prop:"permission"}),a(o,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u8DEF\u5F84",prop:"component"}),a(o,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u540D\u79F0",prop:"componentName"}),a(o,{label:"\u72B6\u6001",prop:"status",width:"80"},{default:e(l=>[a(D,{type:t(N).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(o,{align:"center",label:"\u64CD\u4F5C"},{default:e(l=>[m((n(),p(s,{link:"",type:"primary",onClick:F=>x("update",l.row.id)},{default:e(()=>[u(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[h,["system:menu:update"]]]),m((n(),p(s,{link:"",type:"primary",onClick:F=>x("create",void 0,l.row.id)},{default:e(()=>[u(" \u65B0\u589E ")]),_:2},1032,["onClick"])),[[h,["system:menu:create"]]]),m((n(),p(s,{link:"",type:"danger",onClick:F=>(async Q=>{try{await w.delConfirm(),await Ra(Q),w.success(P("common.delSuccess")),await d()}catch{}})(l.row.id)},{default:e(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["system:menu:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[H,t(v)]]):ra("",!0)]),_:1}),a(Oa,{ref_key:"formRef",ref:O,onSuccess:d},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/system/menu/index.vue"]])});export{La as __tla,L as default};
