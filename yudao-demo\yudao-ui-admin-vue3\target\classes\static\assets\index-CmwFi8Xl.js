import{d as o,p as t,o as l,l as i,w as c,cL as _,g as u,t as p,_ as d,__tla as m}from"./index-Daqg4PFz.js";let a,y=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{a=d(o({name:"DocAlert",__name:"index",props:{title:t.string,url:t.string},setup(e){const s=e,n=()=>{window.open(s.url)};return(v,f)=>{const r=_;return l(),i(r,{key:0,type:"success","show-icon":""},{title:c(()=>[u("div",{onClick:n},p("\u3010"+e.title+"\u3011\u6587\u6863\u5730\u5740\uFF1A"+e.url),1)]),_:1})}}}),[["__scopeId","data-v-457f964f"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DocAlert/index.vue"]])});export{a as _,y as __tla};
