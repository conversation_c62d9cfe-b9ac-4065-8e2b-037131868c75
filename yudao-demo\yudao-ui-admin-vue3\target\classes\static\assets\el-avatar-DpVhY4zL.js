import{be as q,bn as w,bo as p,bp as x,bf as A,d as b,bg as B,r as F,b as n,bq as N,br as P,at as V,o as c,c as v,av as f,a as l,l as y,w as W,b1 as $,bs as C,aW as D,a0 as G,bh as H,bi as I,__tla as J}from"./index-Daqg4PFz.js";let m,K=Promise.all([(()=>{try{return J}catch{}})()]).then(async()=>{const _=q({size:{type:[Number,String],values:w,default:"",validator:s=>p(s)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:x},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:A(String),default:"cover"}}),d={error:s=>s instanceof Event},S=["src","alt","srcset"],g=b({name:"ElAvatar"});m=I(H(b({...g,props:_,emits:d,setup(s,{emit:h}){const e=s,t=B("avatar"),i=F(!1),z=n(()=>{const{size:a,icon:o,shape:u}=e,r=[t.b()];return N(a)&&r.push(t.m(a)),o&&r.push(t.m("icon")),u&&r.push(t.m(u)),r}),E=n(()=>{const{size:a}=e;return p(a)?t.cssVarBlock({size:P(a)||""}):void 0}),k=n(()=>({objectFit:e.fit}));function j(a){i.value=!0,h("error",a)}return V(()=>e.src,()=>i.value=!1),(a,o)=>(c(),v("span",{class:G(l(z)),style:f(l(E))},[!a.src&&!a.srcSet||i.value?a.icon?(c(),y(l(C),{key:1},{default:W(()=>[(c(),y($(a.icon)))]),_:1})):D(a.$slots,"default",{key:2}):(c(),v("img",{key:0,src:a.src,alt:a.alt,srcset:a.srcSet,style:f(l(k)),onError:j},null,44,S))],6))}}),[["__file","avatar.vue"]]))});export{m as E,K as __tla};
