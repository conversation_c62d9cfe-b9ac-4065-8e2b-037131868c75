import{d as o,o as m,c as p,i as v,w as y,g as e,t as c,v as g,_ as f,__tla as h}from"./index-Daqg4PFz.js";let d,U=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{let r,s,t,i,l,u;r={class:"avue-card__body",style:{padding:"10px","background-color":"#fff","border-radius":"5px"}},s={class:"avue-card__avatar"},t=["src"],i={class:"avue-card__detail"},l={class:"avue-card__title",style:{"margin-bottom":"unset"}},u={class:"avue-card__info",style:{height:"unset"}},d=f(o({name:"WxMusic",__name:"main",props:{title:{required:!1,type:String},description:{required:!1,type:String},musicUrl:{required:!1,type:String},hqMusicUrl:{required:!1,type:String},thumbMediaUrl:{required:!0,type:String}},setup:(a,{expose:n})=>(n({musicUrl:a.musicUrl}),(q,b)=>{const _=g;return m(),p("div",null,[v(_,{type:"success",underline:!1,target:"_blank",href:a.hqMusicUrl?a.hqMusicUrl:a.musicUrl},{default:y(()=>[e("div",r,[e("div",s,[e("img",{src:a.thumbMediaUrl,alt:""},null,8,t)]),e("div",i,[e("div",l,c(a.title),1),e("div",u,c(a.description),1)])])]),_:1},8,["href"])])})}),[["__scopeId","data-v-334246f2"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-music/main.vue"]])});export{U as __tla,d as default};
