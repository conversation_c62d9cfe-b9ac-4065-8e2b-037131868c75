import{d as p,b as m,o as i,l as _,a as c,z as y,Z as V,_ as v,__tla as x}from"./index-Daqg4PFz.js";let u,T=Promise.all([(()=>{try{return x}catch{}})()]).then(async()=>{u=v(p({__name:"TabText",props:{modelValue:{type:[String,null],required:!1}},emits:["update:modelValue","input"],setup(s,{emit:o}){const n=s,t=o,e=m({get:()=>n.modelValue,set:a=>{t("update:modelValue",a),t("input",a)}});return(a,l)=>{const r=V;return i(),_(r,{type:"textarea",rows:5,placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",modelValue:c(e),"onUpdate:modelValue":l[0]||(l[0]=d=>y(e)?e.value=d:null)},null,8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-reply/components/TabText.vue"]])});export{T as __tla,u as default};
