import{d as J,f as M,e as T,r as u,b as j,at as A,C as B,ay as H,o as h,c as V,i as e,w as l,a as r,F as S,k as K,l as L,j as x,z as N,M as O,L as W,J as G,K as Q,x as X,N as Z,O as ee,A as ae,B as te,E as re,_ as le,__tla as se}from"./index-Daqg4PFz.js";import{_ as oe,__tla as ue}from"./ContentWrap-DZg14iby.js";import{E as ne,__tla as ce}from"./el-tree-select-BKcJcOKx.js";import{g as _e,__tla as de}from"./index-D-Abj-9W.js";import{f as me,c as ie,__tla as pe}from"./formatTime-BCfRGyrF.js";import{h as D,d as fe}from"./tree-BMqZf9_I.js";import ye,{__tla as he}from"./ContractCountPerformance-BxLLSQd2.js";import ve,{__tla as be}from"./ContractPricePerformance-CtDJQhZE.js";import Ce,{__tla as Pe}from"./ReceivablePricePerformance-D0xUK-Cu.js";import{__tla as ge}from"./el-card-Dvjjuipo.js";import{__tla as Ie}from"./el-skeleton-item-BmS2F7Yy.js";import{__tla as ke}from"./Echart-C33-KcLZ.js";import{__tla as we}from"./performance-BWmJ_CQR.js";let q,Ve=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{let v;v=J({name:"CrmStatisticsCustomer",__name:"index",setup(Se){const t=M({deptId:T().getUser.deptId,userId:void 0,times:[me(ie(new Date(new Date().getTime()-6048e5)))]}),b=u(),C=u([]),P=u([]),R=j(()=>t.deptId?P.value.filter(i=>i.deptId===t.deptId):[]),d=u("ContractCountPerformance"),g=u(),I=u(),k=u(),f=()=>{var n,o,c,p,_,m;const i=parseInt(t.times[0]),a=new Date(i,0,1,0,0,0);switch(t.times[0]=`${a.getFullYear()}-${String(a.getMonth()+1).padStart(2,"0")}-${String(a.getDate()).padStart(2,"0")} ${String(a.getHours()).padStart(2,"0")}:${String(a.getMinutes()).padStart(2,"0")}:${String(a.getSeconds()).padStart(2,"0")}`,d.value){case"ContractCountPerformance":(o=(n=g.value)==null?void 0:n.loadData)==null||o.call(n);break;case"ContractPricePerformance":(p=(c=I.value)==null?void 0:c.loadData)==null||p.call(c);break;case"ReceivablePricePerformance":(m=(_=k.value)==null?void 0:_.loadData)==null||m.call(_)}};A(d,()=>{f()});const U=()=>{b.value.resetFields(),f()};return B(async()=>{C.value=D(await _e()),P.value=D(await H())}),(i,a)=>{const n=O,o=W,c=ne,p=G,_=Q,m=X,w=Z,Y=ee,$=oe,y=ae,F=te,z=re;return h(),V(S,null,[e($,null,{default:l(()=>[e(Y,{class:"-mb-15px",model:r(t),ref_key:"queryFormRef",ref:b,inline:!0,"label-width":"68px"},{default:l(()=>[e(o,{label:"\u9009\u62E9\u5E74\u4EFD",prop:"orderDate"},{default:l(()=>[e(n,{modelValue:r(t).times[0],"onUpdate:modelValue":a[0]||(a[0]=s=>r(t).times[0]=s),class:"!w-240px",type:"year","value-format":"YYYY","default-time":[new Date().getFullYear()]},null,8,["modelValue","default-time"])]),_:1}),e(o,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:l(()=>[e(c,{modelValue:r(t).deptId,"onUpdate:modelValue":a[1]||(a[1]=s=>r(t).deptId=s),class:"!w-240px",data:r(C),props:r(fe),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:a[2]||(a[2]=s=>r(t).userId=void 0)},null,8,["modelValue","data","props"])]),_:1}),e(o,{label:"\u5458\u5DE5",prop:"userId"},{default:l(()=>[e(_,{modelValue:r(t).userId,"onUpdate:modelValue":a[3]||(a[3]=s=>r(t).userId=s),class:"!w-240px",placeholder:"\u5458\u5DE5",clearable:""},{default:l(()=>[(h(!0),V(S,null,K(r(R),(s,E)=>(h(),L(p,{label:s.nickname,value:s.id,key:E},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,null,{default:l(()=>[e(w,{onClick:f},{default:l(()=>[e(m,{icon:"ep:search",class:"mr-5px"}),x(" \u641C\u7D22 ")]),_:1}),e(w,{onClick:U},{default:l(()=>[e(m,{icon:"ep:refresh",class:"mr-5px"}),x(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(z,null,{default:l(()=>[e(F,{modelValue:r(d),"onUpdate:modelValue":a[4]||(a[4]=s=>N(d)?d.value=s:null)},{default:l(()=>[e(y,{label:"\u5458\u5DE5\u5408\u540C\u6570\u91CF\u7EDF\u8BA1",name:"ContractCountPerformance",lazy:""},{default:l(()=>[e(ye,{"query-params":r(t),ref_key:"ContractCountPerformanceRef",ref:g},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u5408\u540C\u91D1\u989D\u7EDF\u8BA1",name:"ContractPricePerformance",lazy:""},{default:l(()=>[e(ve,{"query-params":r(t),ref_key:"ContractPricePerformanceRef",ref:I},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u56DE\u6B3E\u91D1\u989D\u7EDF\u8BA1",name:"ReceivablePricePerformance",lazy:""},{default:l(()=>[e(Ce,{"query-params":r(t),ref_key:"ReceivablePricePerformanceRef",ref:k},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}}),q=le(v,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/statistics/performance/index.vue"]])});export{Ve as __tla,q as default};
