import{d as a,o as r,l as o,_ as s,__tla as n}from"./index-Daqg4PFz.js";import{E as p,__tla as _}from"./el-image-Bn34T02c.js";let e,i=Promise.all([(()=>{try{return n}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{e=s(a({name:"UserCoupon",__name:"index",props:{property:{type:Object,required:!0}},setup:c=>(l,m)=>{const t=p;return r(),o(t,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/couponCardStyle.png"})}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/UserCoupon/index.vue"]])});export{i as __tla,e as default};
