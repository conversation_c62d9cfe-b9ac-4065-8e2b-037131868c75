import{bE as a,__tla as _}from"./index-Daqg4PFz.js";import{f as r,__tla as p}from"./formatTime-BCfRGyrF.js";let e,i,l,c,m,o,d,g=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{let s;l=()=>a.get({url:"/statistics/trade/summary"}),c=t=>a.get({url:"/statistics/trade/analyse",params:s(t)}),m=t=>a.get({url:"/statistics/trade/list",params:s(t)}),o=t=>a.download({url:"/statistics/trade/export-excel",params:s(t)}),d=async()=>await a.get({url:"/statistics/trade/order-count"}),i=async()=>await a.get({url:"/statistics/trade/order-comparison"}),e=(t,n,u)=>a.get({url:"/statistics/trade/order-count-trend",params:{type:t,beginTime:r(n),endTime:r(u)}}),s=t=>({times:[r(t.times[0]),r(t.times[1])]})});export{g as __tla,e as a,i as b,l as c,c as d,m as e,o as f,d as g};
