import{d as y,o as r,c as s,g as e,av as t,i as l,t as n,F as u,k as h,H as _,a8 as m,x as v,_ as b,__tla as g}from"./index-Daqg4PFz.js";let i,x=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{let o;o={class:"right"},i=b(y({name:"SearchBar",__name:"index",props:{property:{type:Object,required:!0}},setup:f=>(a,k)=>{const p=v;return r(),s("div",{class:"search-bar",style:t({color:a.property.textColor})},[e("div",{class:"inner",style:t({height:`${a.property.height}px`,background:a.property.backgroundColor,borderRadius:`${a.property.borderRadius}px`})},[e("div",{class:"placeholder",style:t({justifyContent:a.property.placeholderPosition})},[l(p,{icon:"ep:search"}),e("span",null,n(a.property.placeholder||"\u641C\u7D22\u5546\u54C1"),1)],4),e("div",o,[(r(!0),s(u,null,h(a.property.hotKeywords,(c,d)=>(r(),s("span",{key:d},n(c),1))),128)),_(l(p,{icon:"ant-design:scan-outlined"},null,512),[[m,a.property.showScan]])])],4)],4)}}),[["__scopeId","data-v-89c9e102"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/SearchBar/index.vue"]])});export{x as __tla,i as default};
