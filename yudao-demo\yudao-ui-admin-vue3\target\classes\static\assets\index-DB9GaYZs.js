import{d as y,r as f,o as l,c as i,k as v,av as b,a as g,i as n,w as h,g as p,t as w,F as k,x as P,_ as j,__tla as C}from"./index-Daqg4PFz.js";import{E,__tla as U}from"./el-image-Bn34T02c.js";let c,q=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let t,a,r,s;t=["onClick"],a={class:"h-full w-full flex items-center justify-center"},r={class:"absolute right-1 top-1 text-12px"},s=y({name:"Popover",__name:"index",props:{property:{type:Object,required:!0}},setup(z){const o=f(0);return(u,B)=>{const d=P,m=E;return l(!0),i(k,null,v(u.property.list,(_,e)=>(l(),i("div",{key:e,class:"absolute bottom-50% right-50% h-454px w-292px border-1px border-gray border-rounded-4px border-solid bg-white p-1px",style:b({zIndex:100+e+(g(o)===e?100:0),marginRight:-146-20*e+"px",marginBottom:-227-20*e+"px"}),onClick:D=>(x=>{o.value=x})(e)},[n(m,{src:_.imgUrl,fit:"contain",class:"h-full w-full"},{error:h(()=>[p("div",a,[n(d,{icon:"ep:picture"})])]),_:2},1032,["src"]),p("div",r,w(e+1),1)],12,t))),128)}}}),c=j(s,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/Popover/index.vue"]])});export{q as __tla,c as default};
