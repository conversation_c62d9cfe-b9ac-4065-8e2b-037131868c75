import{d as S,I as T,r as o,o as m,l as c,w as r,i as u,a as l,j as p,H as q,c as j,F as N,k as z,z as H,ay as K,J as L,K as O,L as P,Z,O as A,N as B,R as D,_ as E,__tla as G}from"./index-Daqg4PFz.js";import{_ as M,__tla as Q}from"./Dialog-BjBBVYCI.js";import{s as W,__tla as X}from"./index-CYOuQA7P.js";let b,Y=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{b=E(S({name:"TaskSignCreateForm",__name:"TaskSignCreateForm",emits:["success"],setup($,{expose:g,emit:h}){const k=T(),d=o(!1),i=o(!1),s=o({id:"",userIds:[],type:"",reason:""}),w=o({userIds:[{required:!0,message:"\u52A0\u7B7E\u5904\u7406\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u52A0\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),n=o(),f=o([]);g({open:async t=>{d.value=!0,I(),s.value.id=t,f.value=await K()}});const V=h,y=async t=>{if(n&&await n.value.validate()){i.value=!0,s.value.type=t;try{await W(s.value),k.success("\u52A0\u7B7E\u6210\u529F"),d.value=!1,V("success")}finally{i.value=!1}}},I=()=>{var t;s.value={id:"",userIds:[],type:"",reason:""},(t=n.value)==null||t.resetFields()};return(t,e)=>{const C=L,F=O,v=P,x=Z,U=A,_=B,J=M,R=D;return m(),c(J,{modelValue:l(d),"onUpdate:modelValue":e[5]||(e[5]=a=>H(d)?d.value=a:null),title:"\u52A0\u7B7E",width:"500"},{footer:r(()=>[u(_,{disabled:l(i),type:"primary",onClick:e[2]||(e[2]=a=>y("before"))},{default:r(()=>[p(" \u5411\u524D\u52A0\u7B7E ")]),_:1},8,["disabled"]),u(_,{disabled:l(i),type:"primary",onClick:e[3]||(e[3]=a=>y("after"))},{default:r(()=>[p(" \u5411\u540E\u52A0\u7B7E ")]),_:1},8,["disabled"]),u(_,{onClick:e[4]||(e[4]=a=>d.value=!1)},{default:r(()=>[p("\u53D6 \u6D88")]),_:1})]),default:r(()=>[q((m(),c(U,{ref_key:"formRef",ref:n,model:l(s),rules:l(w),"label-width":"110px"},{default:r(()=>[u(v,{label:"\u52A0\u7B7E\u5904\u7406\u4EBA",prop:"userIds"},{default:r(()=>[u(F,{modelValue:l(s).userIds,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).userIds=a),multiple:"",clearable:"",style:{width:"100%"}},{default:r(()=>[(m(!0),j(N,null,z(l(f),a=>(m(),c(C,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(v,{label:"\u52A0\u7B7E\u7406\u7531",prop:"reason"},{default:r(()=>[u(x,{modelValue:l(s).reason,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).reason=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u52A0\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,l(i)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/dialog/TaskSignCreateForm.vue"]])});export{Y as __tla,b as default};
