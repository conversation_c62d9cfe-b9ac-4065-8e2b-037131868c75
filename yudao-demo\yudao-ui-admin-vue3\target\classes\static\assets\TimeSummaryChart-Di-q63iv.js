import{d as _,p as s,f as c,at as d,o as p,l as h,w as r,i,a as o,_ as y,__tla as f}from"./index-Daqg4PFz.js";import{E as x,__tla as g}from"./el-card-Dvjjuipo.js";import{_ as v,__tla as b}from"./Echart-C33-KcLZ.js";import{C as w,__tla as A}from"./CardTitle-BD5ZuvK3.js";let l,C=Promise.all([(()=>{try{return f}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{l=y(_({name:"MemberStatisticsCard",__name:"TimeSummaryChart",props:{title:s.string.def("").isRequired,value:s.object.isRequired},setup(n){const a=n,t=c({dataset:{dimensions:["time","price"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u91D1\u989D",type:"line",smooth:!0,areaStyle:{}}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:a.title}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}});return d(()=>a.value,e=>{e&&t.dataset&&t.dataset.source&&(t.dataset.source=e)}),(e,S)=>{const m=v,u=x;return p(),h(u,{shadow:"never"},{header:r(()=>[i(o(w),{title:a.title},null,8,["title"])]),default:r(()=>[i(m,{height:300,options:o(t)},null,8,["options"])]),_:1})}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/home/<USER>/TimeSummaryChart.vue"]])});export{C as __tla,l as default};
