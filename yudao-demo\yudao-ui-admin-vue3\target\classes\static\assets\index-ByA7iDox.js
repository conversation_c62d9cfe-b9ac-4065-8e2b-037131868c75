import{d as t,o as a,l as s,_ as o,__tla as n}from"./index-Daqg4PFz.js";import{E as _,__tla as i}from"./el-image-Bn34T02c.js";let e,p=Promise.all([(()=>{try{return n}catch{}})(),(()=>{try{return i}catch{}})()]).then(async()=>{e=o(t({name:"UserOrder",__name:"index",props:{property:{type:Object,required:!0}},setup:c=>(d,l)=>{const r=_;return a(),s(r,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/orderCardStyle.png"})}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/UserOrder/index.vue"]])});export{p as __tla,e as default};
