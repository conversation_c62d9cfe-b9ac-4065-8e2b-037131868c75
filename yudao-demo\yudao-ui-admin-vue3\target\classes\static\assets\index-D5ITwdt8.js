import{d as v,r as f,o as a,c as e,i as r,w as n,F as b,k,l as w,t as c,a as j,a9 as C,x as E,_ as U,__tla as q}from"./index-Daqg4PFz.js";import{E as D,a as F,__tla as J}from"./el-carousel-item-g4NI8jnR.js";import{E as O,__tla as P}from"./el-image-Bn34T02c.js";let y,z=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{let s,o,i,l;s={key:0,class:"h-250px flex items-center justify-center bg-gray-3"},o={key:1,class:"relative"},i={key:0,class:"absolute bottom-10px right-10px rounded-xl bg-black p-x-8px p-y-2px text-10px text-white opacity-40"},l=v({name:"Carousel",__name:"index",props:{property:{type:Object,required:!0}},setup(A){const p=f(0),u=t=>{p.value=t+1};return(t,B)=>{const _=E,d=O,m=D,x=F;return t.property.items.length===0?(a(),e("div",s,[r(_,{icon:"tdesign:image",class:"text-gray-8 text-120px!"})])):(a(),e("div",o,[r(x,{height:"174px",type:t.property.type==="card"?"card":"",autoplay:t.property.autoplay,interval:1e3*t.property.interval,"indicator-position":t.property.indicator==="number"?"none":void 0,onChange:u},{default:n(()=>[(a(!0),e(b,null,k(t.property.items,(h,g)=>(a(),w(m,{key:g},{default:n(()=>[r(d,{class:"h-full w-full",src:h.imgUrl},null,8,["src"])]),_:2},1024))),128))]),_:1},8,["type","autoplay","interval","indicator-position"]),t.property.indicator==="number"?(a(),e("div",i,c(j(p))+" / "+c(t.property.items.length),1)):C("",!0)]))}}}),y=U(l,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/Carousel/index.vue"]])});export{z as __tla,y as default};
