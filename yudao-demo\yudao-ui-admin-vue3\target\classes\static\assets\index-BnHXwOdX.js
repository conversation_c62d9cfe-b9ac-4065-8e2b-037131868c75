import{d as Y,u as z,r as s,f as B,C as F,o as x,c as H,i as e,w as r,a,U as q,j as i,H as A,l as K,G as R,t as j,F as G,Z as J,L,M as O,x as Q,N as Z,O as W,P as E,Q as X,R as $,_ as ee,__tla as ae}from"./index-Daqg4PFz.js";import{_ as te,__tla as le}from"./index-BBLwwrga.js";import{_ as re,__tla as ne}from"./DictTag-BDZzHcIz.js";import{_ as oe,__tla as se}from"./ContentWrap-DZg14iby.js";import{_ as ie,__tla as pe}from"./index-CmwFi8Xl.js";import{d as f,a as ce,__tla as me}from"./formatTime-BCfRGyrF.js";import{i as _e,__tla as ue}from"./index-CYOuQA7P.js";import{__tla as de}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as fe}from"./el-card-Dvjjuipo.js";let T,he=Promise.all([(()=>{try{return ae}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return fe}catch{}})()]).then(async()=>{T=ee(Y({name:"BpmManagerTask",__name:"index",setup(ge){const{push:k}=z(),p=s(!0),h=s(0),g=s([]),n=B({pageNo:1,pageSize:10,name:"",createTime:[]}),w=s(),c=async()=>{p.value=!0;try{const _=await _e(n);g.value=_.list,h.value=_.total}finally{p.value=!1}},m=()=>{n.pageNo=1,c()},U=()=>{w.value.resetFields(),m()};return F(()=>{c()}),(_,o)=>{const M=ie,I=J,u=L,N=O,y=Q,d=Z,P=W,b=oe,t=E,S=re,V=X,C=te,D=$;return x(),H(G,null,[e(M,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),e(b,null,{default:r(()=>[e(P,{ref_key:"queryFormRef",ref:w,inline:!0,model:a(n),class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(u,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:r(()=>[e(I,{modelValue:a(n).name,"onUpdate:modelValue":o[0]||(o[0]=l=>a(n).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:q(m,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(N,{modelValue:a(n).createTime,"onUpdate:modelValue":o[1]||(o[1]=l=>a(n).createTime=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:r(()=>[e(d,{onClick:m},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:search"}),i(" \u641C\u7D22 ")]),_:1}),e(d,{onClick:U},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:refresh"}),i(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(b,null,{default:r(()=>[A((x(),K(V,{data:a(g)},{default:r(()=>[e(t,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(t,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(t,{formatter:a(f),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(t,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(t,{formatter:a(f),align:"center",label:"\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(t,{formatter:a(f),align:"center",label:"\u4EFB\u52A1\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),e(t,{align:"center",label:"\u5BA1\u6279\u4EBA",prop:"assigneeUser.nickname",width:"100"}),e(t,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:r(l=>[e(S,{type:a(R).BPM_TASK_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(t,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"180"}),e(t,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:r(l=>[i(j(a(ce)(l.row.durationInMillis)),1)]),_:1}),e(t,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(t,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(t,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:r(l=>[e(d,{link:"",type:"primary",onClick:we=>{return v=l.row,void k({name:"BpmProcessInstanceDetail",query:{id:v.processInstance.id}});var v}},{default:r(()=>[i("\u5386\u53F2")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[D,a(p)]]),e(C,{limit:a(n).pageSize,"onUpdate:limit":o[2]||(o[2]=l=>a(n).pageSize=l),page:a(n).pageNo,"onUpdate:page":o[3]||(o[3]=l=>a(n).pageNo=l),total:a(h),onPagination:c},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/task/manager/index.vue"]])});export{he as __tla,T as default};
