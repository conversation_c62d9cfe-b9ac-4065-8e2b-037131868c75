import{be as U,dC as K,dD as Q,d as C,cI as V,dE as X,b as y,r as w,bg as Y,bW as Z,dF as ee,br as ae,o as l,l as te,i as r,w as f,H as se,a as e,dG as le,dH as oe,g as F,ao as re,b2 as de,a0 as s,c as d,aW as u,t as ie,a9 as o,bs as ne,dI as ce,a8 as fe,a3 as ue,dJ as be,bh as pe,bi as ve,__tla as ye}from"./index-Daqg4PFz.js";let E,he=Promise.all([(()=>{try{return ye}catch{}})()]).then(async()=>{const L=U({...K,direction:{type:String,default:"rtl",values:["ltr","rtl","ttb","btt"]},size:{type:[String,Number],default:"30%"},withHeader:{type:Boolean,default:!0},modalFade:{type:Boolean,default:!0},headerAriaLevel:{type:String,default:"2"}}),R=Q,g=["aria-label","aria-labelledby","aria-describedby"],$=["id","aria-level"],x=["aria-label"],z=["id"],B=C({name:"ElDrawer",inheritAttrs:!1});E=ve(pe(C({...B,props:L,emits:R,setup(I,{expose:S}){const i=I,T=V();X({scope:"el-drawer",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/drawer.html#slots"},y(()=>!!T.title));const b=w(),h=w(),t=Y("drawer"),{t:D}=Z(),{afterEnter:m,afterLeave:k,beforeLeave:P,visible:p,rendered:H,titleId:v,bodyId:_,zIndex:q,onModalClick:W,onOpenAutoFocus:O,onCloseAutoFocus:j,onFocusoutPrevented:G,onCloseRequested:J,handleClose:n}=ee(i,b),M=y(()=>i.direction==="rtl"||i.direction==="ltr"),A=y(()=>ae(i.size));return S({handleClose:n,afterEnter:m,afterLeave:k}),(a,c)=>(l(),te(be,{to:"body",disabled:!a.appendToBody},[r(ue,{name:e(t).b("fade"),onAfterEnter:e(m),onAfterLeave:e(k),onBeforeLeave:e(P),persisted:""},{default:f(()=>[se(r(e(le),{mask:a.modal,"overlay-class":a.modalClass,"z-index":e(q),onClick:e(W)},{default:f(()=>[r(e(oe),{loop:"",trapped:e(p),"focus-trap-el":b.value,"focus-start-el":h.value,onFocusAfterTrapped:e(O),onFocusAfterReleased:e(j),onFocusoutPrevented:e(G),onReleaseRequested:e(J)},{default:f(()=>[F("div",re({ref_key:"drawerRef",ref:b,"aria-modal":"true","aria-label":a.title||void 0,"aria-labelledby":a.title?void 0:e(v),"aria-describedby":e(_)},a.$attrs,{class:[e(t).b(),a.direction,e(p)&&"open"],style:e(M)?"width: "+e(A):"height: "+e(A),role:"dialog",onClick:c[1]||(c[1]=de(()=>{},["stop"]))}),[F("span",{ref_key:"focusStartRef",ref:h,class:s(e(t).e("sr-focus")),tabindex:"-1"},null,2),a.withHeader?(l(),d("header",{key:0,class:s(e(t).e("header"))},[a.$slots.title?u(a.$slots,"title",{key:1},()=>[o(" DEPRECATED SLOT ")]):u(a.$slots,"header",{key:0,close:e(n),titleId:e(v),titleClass:e(t).e("title")},()=>[a.$slots.title?o("v-if",!0):(l(),d("span",{key:0,id:e(v),role:"heading","aria-level":a.headerAriaLevel,class:s(e(t).e("title"))},ie(a.title),11,$))]),a.showClose?(l(),d("button",{key:2,"aria-label":e(D)("el.drawer.close"),class:s(e(t).e("close-btn")),type:"button",onClick:c[0]||(c[0]=(...N)=>e(n)&&e(n)(...N))},[r(e(ne),{class:s(e(t).e("close"))},{default:f(()=>[r(e(ce))]),_:1},8,["class"])],10,x)):o("v-if",!0)],2)):o("v-if",!0),e(H)?(l(),d("div",{key:1,id:e(_),class:s(e(t).e("body"))},[u(a.$slots,"default")],10,z)):o("v-if",!0),a.$slots.footer?(l(),d("div",{key:2,class:s(e(t).e("footer"))},[u(a.$slots,"footer")],2)):o("v-if",!0)],16,g)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])]),_:3},8,["mask","overlay-class","z-index","onClick"]),[[fe,e(p)]])]),_:3},8,["name","onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}}),[["__file","drawer.vue"]]))});export{E,he as __tla};
