import{d as P,r as i,u as N,S as V,C as j,o,c as D,H as M,a,l as w,w as r,j as S,a9 as T,i as t,F,I as H,N as U,A as W,B as J,E as Q,R as q,_ as G,__tla as K}from"./index-Daqg4PFz.js";import{g as O,_ as X,__tla as Y}from"./index-YDxIZBTH.js";import{u as Z,__tla as $}from"./tagsView-CrrEoR03.js";import{b as aa,__tla as ta}from"./index-BC06Brp1.js";import ra,{__tla as _a}from"./ReceivablePlanDetailsHeader-CkxBQff4.js";import la,{__tla as ea}from"./ReceivablePlanDetailsInfo-CO5k_qfU.js";import ia,{__tla as sa}from"./PermissionList-BFo34k9h.js";import{B as R,__tla as ca}from"./index-BWsMQsUV.js";import na,{__tla as oa}from"./ReceivablePlanForm-DmTaec_y.js";import{__tla as ua}from"./el-timeline-item-DLMaR2h1.js";import{__tla as ma}from"./formatTime-BCfRGyrF.js";import{__tla as fa}from"./ContentWrap-DZg14iby.js";import{__tla as pa}from"./el-card-Dvjjuipo.js";import{__tla as ya}from"./el-descriptions-item-Bucl-KSp.js";import{__tla as da}from"./el-text-vv1naHK-.js";import{__tla as ha}from"./el-collapse-item-CUcELNOM.js";import{__tla as va}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as ba}from"./PermissionForm-CVzor7tm.js";import{__tla as wa}from"./Dialog-BjBBVYCI.js";import{__tla as Ra}from"./index-CCPyMtv-.js";import{__tla as Ca}from"./index-BYuPmJ1X.js";let C,Ea=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ca}catch{}})()]).then(async()=>{C=G(P({name:"CrmReceivablePlanDetail",__name:"index",setup(za){const E=H(),u=i(0),s=i(!0),_=i({}),m=i(),f=async l=>{s.value=!0;try{_.value=await aa(l),await z(l)}finally{s.value=!1}},p=i(),y=i([]),z=async l=>{if(!l)return;const e=await O({bizType:R.CRM_RECEIVABLE_PLAN,bizId:l});y.value=e.list},{delView:A}=Z(),{currentRoute:L}=N(),d=()=>{A(a(L))},{params:h}=V();return j(async()=>{if(!h.id)return E.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u56DE\u6B3E\u8BA1\u5212\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void d();u.value=h.id,await f(u.value)}),(l,e)=>{const g=U,c=W,k=X,x=J,B=Q,I=q;return o(),D(F,null,[M((o(),w(ra,{"receivable-plan":a(_)},{default:r(()=>{var n;return[(n=a(m))!=null&&n.validateWrite?(o(),w(g,{key:0,onClick:e[0]||(e[0]=Aa=>{return v="update",b=a(_).id,void p.value.open(v,b);var v,b})},{default:r(()=>[S(" \u7F16\u8F91 ")]),_:1})):T("",!0)]}),_:1},8,["receivable-plan"])),[[I,a(s)]]),t(B,null,{default:r(()=>[t(x,null,{default:r(()=>[t(c,{label:"\u8BE6\u7EC6\u8D44\u6599"},{default:r(()=>[t(la,{"receivable-plan":a(_)},null,8,["receivable-plan"])]),_:1}),t(c,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:r(()=>[t(k,{"log-list":a(y)},null,8,["log-list"])]),_:1}),t(c,{label:"\u56E2\u961F\u6210\u5458"},{default:r(()=>[t(ia,{ref_key:"permissionListRef",ref:m,"biz-id":a(_).id,"biz-type":a(R).CRM_RECEIVABLE_PLAN,"show-action":!0,onQuitTeam:d},null,8,["biz-id","biz-type"])]),_:1})]),_:1})]),_:1}),t(na,{ref_key:"formRef",ref:p,onSuccess:e[1]||(e[1]=n=>f(a(_).id))},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/receivable/plan/detail/index.vue"]])});export{Ea as __tla,C as default};
