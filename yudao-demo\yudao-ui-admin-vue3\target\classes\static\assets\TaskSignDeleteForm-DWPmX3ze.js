import{d as z,I as B,r as u,o as m,l as c,w as l,i as n,a,j as f,H,c as J,F as L,k as O,t as p,z as P,aB as Z,aN as A,an as E,L as G,Z as K,O as M,N as Q,R as W,_ as X,__tla as Y}from"./index-Daqg4PFz.js";import{_ as $,__tla as ee}from"./Dialog-BjBBVYCI.js";import{e as ae,f as le,__tla as se}from"./index-CYOuQA7P.js";let U,re=Promise.all([(()=>{try{return Y}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return se}catch{}})()]).then(async()=>{U=X(z({name:"TaskSignDeleteForm",__name:"TaskSignDeleteForm",emits:["success"],setup(te,{expose:V,emit:F}){const v=B(),t=u(!1),o=u(!1),r=u({id:"",reason:""}),N=u({id:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u51CF\u7B7E\u4EFB\u52A1",trigger:"change"}],reason:[{required:!0,message:"\u51CF\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=u(),_=u([]);V({open:async i=>{if(_.value=await ae(i),Z(_.value))return v.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u51CF\u7B7E\u7684\u4EFB\u52A1"),!1;t.value=!0,R()}});const x=F,D=async()=>{if(d&&await d.value.validate()){o.value=!0;try{await le(r.value),v.success("\u51CF\u7B7E\u6210\u529F"),t.value=!1,x("success")}finally{o.value=!1}}},R=()=>{var i;r.value={id:"",reason:""},(i=d.value)==null||i.resetFields()};return(i,s)=>{const S=A,T=E,g=G,j=K,q=M,y=Q,C=$,I=W;return m(),c(C,{modelValue:a(t),"onUpdate:modelValue":s[3]||(s[3]=e=>P(t)?t.value=e:null),title:"\u51CF\u7B7E",width:"500"},{footer:l(()=>[n(y,{disabled:a(o),type:"primary",onClick:D},{default:l(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),n(y,{onClick:s[2]||(s[2]=e=>t.value=!1)},{default:l(()=>[f("\u53D6 \u6D88")]),_:1})]),default:l(()=>[H((m(),c(q,{ref_key:"formRef",ref:d,model:a(r),rules:a(N),"label-width":"110px"},{default:l(()=>[n(g,{label:"\u51CF\u7B7E\u4EFB\u52A1",prop:"id"},{default:l(()=>[n(T,{modelValue:a(r).id,"onUpdate:modelValue":s[0]||(s[0]=e=>a(r).id=e)},{default:l(()=>[(m(!0),J(L,null,O(a(_),e=>(m(),c(S,{key:e.id,label:e.id},{default:l(()=>{var b,k,w,h;return[f(p(e.name)+" ("+p(((b=e.assigneeUser)==null?void 0:b.deptName)||((k=e.ownerUser)==null?void 0:k.deptName))+" - "+p(((w=e.assigneeUser)==null?void 0:w.nickname)||((h=e.ownerUser)==null?void 0:h.nickname))+") ",1)]}),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(g,{label:"\u51CF\u7B7E\u7406\u7531",prop:"reason"},{default:l(()=>[n(j,{modelValue:a(r).reason,"onUpdate:modelValue":s[1]||(s[1]=e=>a(r).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u51CF\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[I,a(o)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/dialog/TaskSignDeleteForm.vue"]])});export{re as __tla,U as default};
