import{d as R,n as S,I as j,r as i,f as z,o as V,l as b,w as u,i as o,a,j as k,H as N,z as H,Z as J,L,O,N as Z,R as A,_ as B,__tla as D}from"./index-Daqg4PFz.js";import{_ as E,__tla as G}from"./Dialog-BjBBVYCI.js";import{e as K,c as M,u as Q,__tla as T}from"./property-Dsd0TI7Q.js";let g,W=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return T}catch{}})()]).then(async()=>{g=B(R({name:"ProductPropertyValueForm",__name:"ValueForm",emits:["success"],setup(X,{expose:h,emit:w}){const{t:p}=S(),_=j(),s=i(!1),v=i(""),d=i(!1),y=i(""),l=i({id:void 0,propertyId:void 0,name:"",remark:""}),I=z({propertyId:[{required:!0,message:"\u5C5E\u6027\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=i();h({open:async(r,e,m)=>{if(s.value=!0,v.value=p("action."+r),y.value=r,F(),l.value.propertyId=e,m){d.value=!0;try{l.value=await K(m)}finally{d.value=!1}}}});const U=w,x=async()=>{if(n&&await n.value.validate()){d.value=!0;try{const r=l.value;y.value==="create"?(await M(r),_.success(p("common.createSuccess"))):(await Q(r),_.success(p("common.updateSuccess"))),s.value=!1,U("success")}finally{d.value=!1}}},F=()=>{var r;l.value={id:void 0,propertyId:void 0,name:"",remark:""},(r=n.value)==null||r.resetFields()};return(r,e)=>{const m=J,c=L,P=O,f=Z,q=E,C=A;return V(),b(q,{modelValue:a(s),"onUpdate:modelValue":e[4]||(e[4]=t=>H(s)?s.value=t:null),title:a(v)},{footer:u(()=>[o(f,{disabled:a(d),type:"primary",onClick:x},{default:u(()=>[k("\u786E \u5B9A")]),_:1},8,["disabled"]),o(f,{onClick:e[3]||(e[3]=t=>s.value=!1)},{default:u(()=>[k("\u53D6 \u6D88")]),_:1})]),default:u(()=>[N((V(),b(P,{ref_key:"formRef",ref:n,model:a(l),rules:a(I),"label-width":"80px"},{default:u(()=>[o(c,{label:"\u5C5E\u6027\u7F16\u53F7",prop:"category"},{default:u(()=>[o(m,{modelValue:a(l).propertyId,"onUpdate:modelValue":e[0]||(e[0]=t=>a(l).propertyId=t),disabled:""},null,8,["modelValue"])]),_:1}),o(c,{label:"\u540D\u79F0",prop:"name"},{default:u(()=>[o(m,{modelValue:a(l).name,"onUpdate:modelValue":e[1]||(e[1]=t=>a(l).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(c,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[o(m,{modelValue:a(l).remark,"onUpdate:modelValue":e[2]||(e[2]=t=>a(l).remark=t),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,a(d)]])]),_:1},8,["modelValue","title"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/product/property/value/ValueForm.vue"]])});export{W as __tla,g as default};
