import{n as o,__tla as s}from"./index-Daqg4PFz.js";let m,c=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{let a;({t:a}=o()),m=()=>({required:r=>({required:!0,message:r||a("common.required")}),lengthRange:r=>{const{min:n,max:e,message:t}=r;return{min:n,max:e,message:t||a("common.lengthRange",{min:n,max:e})}},notSpace:r=>({validator:(n,e,t)=>{(e==null?void 0:e.indexOf(" "))!==-1?t(new Error(r||a("common.notSpace"))):t()}}),notSpecialCharacters:r=>({validator:(n,e,t)=>{/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi.test(e)?t(new Error(r||a("common.notSpecialCharacters"))):t()}})})});export{c as __tla,m as u};
