import{_ as E,r as J,o as l,c as r,g as i,i as P,w as Ae,a as C,z as Be,ap as io,a5 as to,a6 as so,aq as lo,ar as ao,as as ro,b as L,C as co,at as O,au as po,T as fo,av as go,t as w,F as U,H as Ee,aw as ze,a9 as v,j as Co,a0 as Re,k as uo,l as qe,d as No,__tla as vo}from"./index-Daqg4PFz.js";let He,ho=Promise.all([(()=>{try{return vo}catch{}})()]).then(async()=>{let y,z,R,q,H,G,K,Q,x,_,$,j,F,X,Y,Z,ee,oe,ne,ie,de,te,se,le,ae,re,ce,pe,fe,ge,Ce,ue,Ne,ve,he,me,ye,ke,be,we,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,xe,_e,<PERSON>,<PERSON>,<PERSON>,Je,Oe,$e;y=e=>(to("data-v-1b1bdc79"),e=e(),so(),e),z={class:"add-node-btn-box"},R={class:"add-node-btn"},q={class:"add-node-popover-body"},H=[y(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE8EF")],-1)),y(()=>i("p",null,"\u5BA1\u6279\u4EBA",-1))],G=[y(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE93B")],-1)),y(()=>i("p",null,"\u6284\u9001\u4EBA",-1))],K=[y(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE9BE")],-1)),y(()=>i("p",null,"\u6761\u4EF6\u5206\u652F",-1))],Q=y(()=>i("button",{class:"btn",type:"button"},[i("span",{class:"iconfont"},"\uE95B")],-1)),x=E({__name:"addNode",props:{childNodeP:{type:Object,default:()=>({})}},emits:["update:childNodeP"],setup(e,{emit:c}){let a=e,o=c,s=J(!1);const u=h=>{var p;s.value=!1,h!=4?(h==1?p={nodeName:"\u5BA1\u6838\u4EBA",error:!0,type:1,settype:1,selectMode:0,selectRange:0,directorLevel:1,examineMode:1,noHanderAction:1,examineEndDirectorLevel:0,childNode:a.childNodeP,nodeUserList:[]}:h==2&&(p={nodeName:"\u6284\u9001\u4EBA",type:2,ccSelfSelectFlag:1,childNode:a.childNodeP,nodeUserList:[]}),o("update:childNodeP",p)):o("update:childNodeP",{nodeName:"\u8DEF\u7531",type:4,childNode:null,conditionNodes:[{nodeName:"\u6761\u4EF61",error:!0,type:3,priorityLevel:1,conditionList:[],nodeUserList:[],childNode:a.childNodeP},{nodeName:"\u6761\u4EF62",type:3,priorityLevel:2,conditionList:[],nodeUserList:[],childNode:null}]})};return(h,p)=>{const k=io;return l(),r("div",z,[i("div",R,[P(k,{placement:"right-start",modelValue:C(s),"onUpdate:modelValue":p[3]||(p[3]=f=>Be(s)?s.value=f:s=f),width:"auto"},{reference:Ae(()=>[Q]),default:Ae(()=>[i("div",q,[i("a",{class:"add-node-popover-item approver",onClick:p[0]||(p[0]=f=>u(1))},[...H]),i("a",{class:"add-node-popover-item notifier",onClick:p[1]||(p[1]=f=>u(2))},[...G]),i("a",{class:"add-node-popover-item condition",onClick:p[2]||(p[2]=f=>u(4))},[...K])])]),_:1},8,["modelValue"])])])}}},[["__scopeId","data-v-1b1bdc79"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/SimpleProcessDesigner/src/addNode.vue"]]),_=e=>{if(e)return e.map(c=>c.name).toString()},$=e=>{if(e.settype==1){if(e.nodeUserList.length==1)return e.nodeUserList[0].name;if(e.nodeUserList.length>1){if(e.examineMode==1)return _(e.nodeUserList);if(e.examineMode==2)return e.nodeUserList.length+"\u4EBA\u4F1A\u7B7E"}}else if(e.settype==2){const c=e.directorLevel==1?"\u76F4\u63A5\u4E3B\u7BA1":"\u7B2C"+e.directorLevel+"\u7EA7\u4E3B\u7BA1";if(e.examineMode==1)return c;if(e.examineMode==2)return c+"\u4F1A\u7B7E"}else{if(e.settype==4)return e.selectRange==1?"\u53D1\u8D77\u4EBA\u81EA\u9009":e.nodeUserList.length>0?e.selectRange==2?"\u53D1\u8D77\u4EBA\u81EA\u9009":"\u53D1\u8D77\u4EBA\u4ECE"+e.nodeUserList[0].name+"\u4E2D\u81EA\u9009":"";if(e.settype==5)return"\u53D1\u8D77\u4EBA\u81EA\u5DF1";if(e.settype==7)return"\u4ECE\u76F4\u63A5\u4E3B\u7BA1\u5230\u901A\u8BAF\u5F55\u4E2D\u7EA7\u522B\u6700\u9AD8\u7684\u7B2C"+e.examineEndDirectorLevel+"\u4E2A\u5C42\u7EA7\u4E3B\u7BA1"}},j=e=>e.nodeUserList.length!=0?_(e.nodeUserList):e.ccSelfSelectFlag==1?"\u53D1\u8D77\u4EBA\u81EA\u9009":void 0,F=(e,c)=>{const{conditionList:a,nodeUserList:o}=e.conditionNodes[c];if(a.length==0)return c==e.conditionNodes.length-1&&e.conditionNodes[0].conditionList.length!=0?"\u5176\u4ED6\u6761\u4EF6\u8FDB\u5165\u6B64\u6D41\u7A0B":"\u8BF7\u8BBE\u7F6E\u6761\u4EF6";{let s="";for(let u=0;u<a.length;u++){const{columnId:h,columnType:p,showType:k,showName:f,optType:m,zdy1:b,opt1:V,zdy2:D,opt2:I,fixedDownBoxValue:M}=a[u];h==0&&o.length!=0&&(s+="\u53D1\u8D77\u4EBA\u5C5E\u4E8E\uFF1A",s+=o.map(W=>W.name).join("\u6216")+" \u5E76\u4E14 "),p=="String"&&k=="3"&&b&&(s+=f+"\u5C5E\u4E8E\uFF1A"+X(b,JSON.parse(M))+" \u5E76\u4E14 "),p=="Double"&&(m!=6&&b?s+=`${f} ${["","<",">","\u2264","=","\u2265"][m]} ${b} \u5E76\u4E14 `:m==6&&b&&D&&(s+=`${b} ${V} ${f} ${I} ${D} \u5E76\u4E14 `))}return s?s.substring(0,s.length-4):"\u8BF7\u8BBE\u7F6E\u6761\u4EF6"}},X=(e,c)=>{const a=[],o=e.split(",");for(const s in c)o.map(u=>{u==s&&a.push(c[s].value)});return a.join("\u6216")},Y=["87, 106, 149","255, 148, 62","50, 150, 250"],Z=["\u53D1\u8D77\u4EBA","\u5BA1\u6838\u4EBA","\u6284\u9001\u4EBA"],ee=lo("simpleWorkflow",{state:()=>({tableId:"",isTried:!1,promoterDrawer:!1,flowPermission1:{},approverDrawer:!1,approverConfig1:{},copyerDrawer:!1,copyerConfig1:{},conditionDrawer:!1,conditionsConfig1:{conditionNodes:[]}}),actions:{setTableId(e){this.tableId=e},setIsTried(e){this.isTried=e},setPromoter(e){this.promoterDrawer=e},setFlowPermission(e){this.flowPermission1=e},setApprover(e){this.approverDrawer=e},setApproverConfig(e){this.approverConfig1=e},setCopyer(e){this.copyerDrawer=e},setCopyerConfig(e){this.copyerConfig1=e},setCondition(e){this.conditionDrawer=e},setConditionsConfig(e){this.conditionsConfig1=e}}}),oe=()=>ee(ao),ne={key:0,class:"node-wrap"},ie={key:0},de={class:"iconfont"},te=["placeholder"],se={class:"text"},le={key:0,class:"placeholder"},ae=i("i",{class:"anticon anticon-right arrow"},null,-1),re={key:0,class:"error_tip"},ce=[i("i",{class:"anticon anticon-exclamation-circle"},null,-1)],pe={key:1,class:"branch-wrap"},fe={class:"branch-box-wrap"},ge={class:"branch-box"},Ce={class:"condition-node"},ue={class:"condition-node-box"},Ne=["onClick"],ve={class:"title-wrapper"},he=["onBlur","onUpdate:modelValue"],me=["onClick"],ye=["onClick"],ke=["onClick"],be=["onClick"],we=["onClick"],Le={key:2,class:"error_tip"},Pe=[i("i",{class:"anticon anticon-exclamation-circle"},null,-1)],Ue=i("div",{class:"top-left-cover-line"},null,-1),xe=i("div",{class:"bottom-left-cover-line"},null,-1),_e=i("div",{class:"top-right-cover-line"},null,-1),De=i("div",{class:"bottom-right-cover-line"},null,-1),Se={__name:"nodeWrap",props:{nodeConfig:{type:Object,default:()=>({})},flowPermission:{type:Object,default:()=>[]}},emits:["update:flowPermission","update:nodeConfig"],setup(e,{emit:c}){let a=ro().uid,o=e,s=L(()=>Z[o.nodeConfig.type]),u=L(()=>o.nodeConfig.type==0?_(o.flowPermission)||"\u6240\u6709\u4EBA":o.nodeConfig.type==1?$(o.nodeConfig):j(o.nodeConfig)),h=J([]),p=J(!1);const k=()=>{for(var n=0;n<o.nodeConfig.conditionNodes.length;n++)o.nodeConfig.conditionNodes[n].error=F(o.nodeConfig,n)=="\u8BF7\u8BBE\u7F6E\u6761\u4EF6"&&n!=o.nodeConfig.conditionNodes.length-1};co(()=>{o.nodeConfig.type==1?o.nodeConfig.error=!$(o.nodeConfig):o.nodeConfig.type==2?o.nodeConfig.error=!j(o.nodeConfig):o.nodeConfig.type==4&&k()});let f=c,m=oe(),{setPromoter:b,setApprover:V,setCopyer:D,setCondition:I,setFlowPermission:M,setApproverConfig:W,setCopyerConfig:Ge,setConditionsConfig:Ke}=m,S=L(()=>m.isTried),Qe=L(()=>m.flowPermission1),Xe=L(()=>m.approverConfig1),Ye=L(()=>m.copyerConfig1),Ze=L(()=>m.conditionsConfig1);O(Qe,n=>{n.flag&&n.id===a&&f("update:flowPermission",n.value)}),O(Xe,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)}),O(Ye,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)}),O(Ze,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)});const je=n=>{n||n===0?h.value[n]=!0:p.value=!0},Fe=n=>{n||n===0?(h.value[n]=!1,o.nodeConfig.conditionNodes[n].nodeName=o.nodeConfig.conditionNodes[n].nodeName||"\u6761\u4EF6"):(p.value=!1,o.nodeConfig.nodeName=o.nodeConfig.nodeName||s)},eo=()=>{f("update:nodeConfig",o.nodeConfig.childNode)},oo=()=>{let n=o.nodeConfig.conditionNodes.length+1;o.nodeConfig.conditionNodes.push({nodeName:"\u6761\u4EF6"+n,type:3,priorityLevel:n,conditionList:[],nodeUserList:[],childNode:null}),k(),f("update:nodeConfig",o.nodeConfig)},Ve=(n,d)=>{n.childNode?Ve(n.childNode,d):n.childNode=d},A=n=>{var{type:d}=o.nodeConfig;d==0?(b(!0),M({value:o.flowPermission,flag:!1,id:a})):d==1?(V(!0),W({value:{...JSON.parse(JSON.stringify(o.nodeConfig)),settype:o.nodeConfig.settype?o.nodeConfig.settype:1},flag:!1,id:a})):d==2?(D(!0),Ge({value:JSON.parse(JSON.stringify(o.nodeConfig)),flag:!1,id:a})):(I(!0),Ke({value:JSON.parse(JSON.stringify(o.nodeConfig)),priorityLevel:n,flag:!1,id:a}))},Ie=(n,d=1)=>{o.nodeConfig.conditionNodes[n]=o.nodeConfig.conditionNodes.splice(n+d,1,o.nodeConfig.conditionNodes[n])[0],o.nodeConfig.conditionNodes.map((T,B)=>{T.priorityLevel=B+1}),k(),f("update:nodeConfig",o.nodeConfig)};return(n,d)=>{const T=po("nodeWrap",!0),B=fo("focus");return l(),r(U,null,[e.nodeConfig.type<3?(l(),r("div",ne,[i("div",{class:Re(["node-wrap-box",(e.nodeConfig.type==0?"start-node ":"")+(C(S)&&e.nodeConfig.error?"active error":"")])},[i("div",{class:"title",style:go(`background: rgb(${C(Y)[e.nodeConfig.type]});`)},[e.nodeConfig.type==0?(l(),r("span",ie,w(e.nodeConfig.nodeName),1)):(l(),r(U,{key:1},[i("span",de,w(e.nodeConfig.type==1?"\uE8EF":"\uE93B"),1),C(p)?Ee((l(),r("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:d[0]||(d[0]=t=>Fe()),onFocus:d[1]||(d[1]=t=>t.currentTarget.select()),"onUpdate:modelValue":d[2]||(d[2]=t=>e.nodeConfig.nodeName=t),placeholder:C(s)},null,40,te)),[[B],[ze,e.nodeConfig.nodeName]]):(l(),r("span",{key:1,class:"editable-title",onClick:d[3]||(d[3]=t=>je())},w(e.nodeConfig.nodeName),1)),i("i",{class:"anticon anticon-close close",onClick:eo})],64))],4),i("div",{class:"content",onClick:A},[i("div",se,[C(u)?v("",!0):(l(),r("span",le,"\u8BF7\u9009\u62E9"+w(C(s)),1)),Co(" "+w(C(u)),1)]),ae]),C(S)&&e.nodeConfig.error?(l(),r("div",re,[...ce])):v("",!0)],2),P(x,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":d[4]||(d[4]=t=>e.nodeConfig.childNode=t)},null,8,["childNodeP"])])):v("",!0),e.nodeConfig.type==4?(l(),r("div",pe,[i("div",fe,[i("div",ge,[i("button",{class:"add-branch",onClick:oo},"\u6DFB\u52A0\u6761\u4EF6"),(l(!0),r(U,null,uo(e.nodeConfig.conditionNodes,(t,N)=>(l(),r("div",{class:"col-box",key:N},[i("div",Ce,[i("div",ue,[i("div",{class:Re(["auto-judge",C(S)&&t.error?"error active":""])},[N!=0?(l(),r("div",{key:0,class:"sort-left",onClick:g=>Ie(N,-1)},"<",8,Ne)):v("",!0),i("div",ve,[C(h)[N]?Ee((l(),r("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:g=>Fe(N),onFocus:d[5]||(d[5]=g=>g.currentTarget.select()),"onUpdate:modelValue":g=>t.nodeName=g},null,40,he)),[[ze,t.nodeName]]):(l(),r("span",{key:1,class:"editable-title",onClick:g=>je(N)},w(t.nodeName),9,me)),i("span",{class:"priority-title",onClick:g=>A(t.priorityLevel)},"\u4F18\u5148\u7EA7"+w(t.priorityLevel),9,ye),i("i",{class:"anticon anticon-close close",onClick:g=>(no=>{o.nodeConfig.conditionNodes.splice(no,1),o.nodeConfig.conditionNodes.map((Me,We)=>{Me.priorityLevel=We+1,Me.nodeName=`\u6761\u4EF6${We+1}`}),k(),f("update:nodeConfig",o.nodeConfig),o.nodeConfig.conditionNodes.length==1&&(o.nodeConfig.childNode&&(o.nodeConfig.conditionNodes[0].childNode?Ve(o.nodeConfig.conditionNodes[0].childNode,o.nodeConfig.childNode):o.nodeConfig.conditionNodes[0].childNode=o.nodeConfig.childNode),f("update:nodeConfig",o.nodeConfig.conditionNodes[0].childNode))})(N)},null,8,ke)]),N!=e.nodeConfig.conditionNodes.length-1?(l(),r("div",{key:1,class:"sort-right",onClick:g=>Ie(N)},">",8,be)):v("",!0),i("div",{class:"content",onClick:g=>A(t.priorityLevel)},w(C(F)(e.nodeConfig,N)),9,we),C(S)&&t.error?(l(),r("div",Le,[...Pe])):v("",!0)],2),P(x,{childNodeP:t.childNode,"onUpdate:childNodeP":g=>t.childNode=g},null,8,["childNodeP","onUpdate:childNodeP"])])]),t.childNode?(l(),qe(T,{key:0,nodeConfig:t.childNode,"onUpdate:nodeConfig":g=>t.childNode=g},null,8,["nodeConfig","onUpdate:nodeConfig"])):v("",!0),N==0?(l(),r(U,{key:1},[Ue,xe],64)):v("",!0),N==e.nodeConfig.conditionNodes.length-1?(l(),r(U,{key:2},[_e,De],64)):v("",!0)]))),128))]),P(x,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":d[6]||(d[6]=t=>e.nodeConfig.childNode=t)},null,8,["childNodeP"])])])):v("",!0),e.nodeConfig.childNode?(l(),qe(T,{key:2,nodeConfig:e.nodeConfig.childNode,"onUpdate:nodeConfig":d[7]||(d[7]=t=>e.nodeConfig.childNode=t)},null,8,["nodeConfig"])):v("",!0)],64)}}},Te=E(Se,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/SimpleProcessDesigner/src/nodeWrap.vue"]]),Je={class:"dingflow-design"},Oe={class:"box-scale"},$e=i("div",{class:"end-node"},[i("div",{class:"end-node-circle"}),i("div",{class:"end-node-text"},"\u6D41\u7A0B\u7ED3\u675F")],-1),He=E(No({name:"SimpleWorkflowDesignEditor",__name:"index",setup(e){let c=J({nodeName:"\u53D1\u8D77\u4EBA",type:0,id:"root",formPerms:{},nodeUserList:[],childNode:{}});return(a,o)=>(l(),r("div",null,[i("section",Je,[i("div",Oe,[P(Te,{nodeConfig:C(c),"onUpdate:nodeConfig":o[0]||(o[0]=s=>Be(c)?c.value=s:c=s)},null,8,["nodeConfig"]),$e])])]))}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/simpleWorkflow/index.vue"]])});export{ho as __tla,He as default};
