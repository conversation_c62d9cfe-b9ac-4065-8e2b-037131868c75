import{d as y,o as t,c as a,F as _,k as m,av as s,t as l,a9 as x,l as b,g as i,_ as f,__tla as g}from"./index-Daqg4PFz.js";import{E as h,__tla as v}from"./el-image-Bn34T02c.js";let c,k=Promise.all([(()=>{try{return g}catch{}})(),(()=>{try{return v}catch{}})()]).then(async()=>{let r,o;r={class:"flex flex-row flex-wrap"},o=y({name:"MenuGrid",__name:"index",props:{property:{type:Object,required:!0}},setup:w=>(p,C)=>{const d=h;return t(),a("div",r,[(t(!0),a(_,null,m(p.property.list,(e,u)=>{var n;return t(),a("div",{key:u,class:"relative flex flex-col items-center p-b-14px p-t-20px",style:s({width:1/p.property.column*100+"%"})},[(n=e.badge)!=null&&n.show?(t(),a("span",{key:0,class:"absolute left-50% top-10px z-1 h-20px rounded-50% p-x-6px text-center text-12px leading-20px",style:s({color:e.badge.textColor,backgroundColor:e.badge.bgColor})},l(e.badge.text),5)):x("",!0),e.iconUrl?(t(),b(d,{key:1,class:"h-28px w-28px",src:e.iconUrl},null,8,["src"])):x("",!0),i("span",{class:"m-t-8px h-16px text-12px leading-16px",style:s({color:e.titleColor})},l(e.title),5),i("span",{class:"m-t-6px h-12px text-10px leading-12px",style:s({color:e.subtitleColor})},l(e.subtitle),5)],4)}),128))])}}),c=f(o,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/MenuGrid/index.vue"]])});export{k as __tla,c as default};
