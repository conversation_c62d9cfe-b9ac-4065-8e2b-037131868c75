import{du as o,r as v,C as f,__tla as b}from"./index-Daqg4PFz.js";import{g as h,__tla as w}from"./dict.type-BqDb60NG.js";let s,x=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return w}catch{}})()]).then(async()=>{let p,d;p=(r,m,n)=>n.map(a=>(a.field==="formCreate$required"?a.title=r("props.required")||a.title:a.field&&a.field!=="_optionType"&&(a.title=r("components."+m+"."+a.field)||a.title),a)),d=[{type:"switch",field:"multiple",title:"\u662F\u5426\u591A\u9009"},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528"},{type:"switch",field:"clearable",title:"\u662F\u5426\u53EF\u4EE5\u6E05\u7A7A\u9009\u9879"},{type:"switch",field:"collapseTags",title:"\u591A\u9009\u65F6\u662F\u5426\u5C06\u9009\u4E2D\u503C\u6309\u6587\u5B57\u7684\u5F62\u5F0F\u5C55\u793A"},{type:"inputNumber",field:"multipleLimit",title:"\u591A\u9009\u65F6\u7528\u6237\u6700\u591A\u53EF\u4EE5\u9009\u62E9\u7684\u9879\u76EE\u6570\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",props:{min:0}},{type:"input",field:"autocomplete",title:"autocomplete \u5C5E\u6027"},{type:"input",field:"placeholder",title:"\u5360\u4F4D\u7B26"},{type:"switch",field:"filterable",title:"\u662F\u5426\u53EF\u641C\u7D22"},{type:"switch",field:"allowCreate",title:"\u662F\u5426\u5141\u8BB8\u7528\u6237\u521B\u5EFA\u65B0\u6761\u76EE"},{type:"input",field:"noMatchText",title:"\u641C\u7D22\u6761\u4EF6\u65E0\u5339\u914D\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"switch",field:"remote",title:"\u5176\u4E2D\u7684\u9009\u9879\u662F\u5426\u4ECE\u670D\u52A1\u5668\u8FDC\u7A0B\u52A0\u8F7D"},{type:"Struct",field:"remoteMethod",title:"\u81EA\u5B9A\u4E49\u8FDC\u7A0B\u641C\u7D22\u65B9\u6CD5"},{type:"input",field:"noDataText",title:"\u9009\u9879\u4E3A\u7A7A\u65F6\u663E\u793A\u7684\u6587\u5B57"},{type:"switch",field:"reserveKeyword",title:"\u591A\u9009\u4E14\u53EF\u641C\u7D22\u65F6\uFF0C\u662F\u5426\u5728\u9009\u4E2D\u4E00\u4E2A\u9009\u9879\u540E\u4FDD\u7559\u5F53\u524D\u7684\u641C\u7D22\u5173\u952E\u8BCD"},{type:"switch",field:"defaultFirstOption",title:"\u5728\u8F93\u5165\u6846\u6309\u4E0B\u56DE\u8F66\uFF0C\u9009\u62E9\u7B2C\u4E00\u4E2A\u5339\u914D\u9879"},{type:"switch",field:"popperAppendToBody",title:"\u662F\u5426\u5C06\u5F39\u51FA\u6846\u63D2\u5165\u81F3 body \u5143\u7D20",value:!0},{type:"switch",field:"automaticDropdown",title:"\u5BF9\u4E8E\u4E0D\u53EF\u641C\u7D22\u7684 Select\uFF0C\u662F\u5426\u5728\u8F93\u5165\u6846\u83B7\u5F97\u7126\u70B9\u540E\u81EA\u52A8\u5F39\u51FA\u9009\u9879\u83DC\u5355"}],s=r=>{const m=(()=>{const t="Editor";return{icon:"icon-editor",label:"\u5BCC\u6587\u672C",name:t,rule:()=>({type:t,field:o(),title:"\u5BCC\u6587\u672C",info:"",$required:!1}),props:(e,{t:i})=>p(i,t+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},{type:"input",field:"height",title:"\u9AD8\u5EA6"},{type:"switch",field:"readonly",title:"\u662F\u5426\u53EA\u8BFB"}])}})(),n=(()=>{const t="\u6587\u4EF6\u4E0A\u4F20",e="UploadFile";return{icon:"icon-upload",label:t,name:e,rule:()=>({type:e,field:o(),title:t,info:"",$required:!1}),props:(i,{t:l})=>p(l,e+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},{type:"select",field:"fileType",title:"\u6587\u4EF6\u7C7B\u578B",value:["doc","xls","ppt","txt","pdf"],options:[{label:"doc",value:"doc"},{label:"xls",value:"xls"},{label:"ppt",value:"ppt"},{label:"txt",value:"txt"},{label:"pdf",value:"pdf"}],props:{multiple:!0}},{type:"switch",field:"autoUpload",title:"\u662F\u5426\u5728\u9009\u53D6\u6587\u4EF6\u540E\u7ACB\u5373\u8FDB\u884C\u4E0A\u4F20",value:!0},{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"switch",field:"isShowTip",title:"\u662F\u5426\u663E\u793A\u63D0\u793A",value:!0},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"switch",field:"disabled",title:"\u662F\u5426\u7981\u7528",value:!1}])}})(),a=(()=>{const t="\u5355\u56FE\u4E0A\u4F20",e="UploadImg";return{icon:"icon-upload",label:t,name:e,rule:()=>({type:e,field:o(),title:t,info:"",$required:!1}),props:(i,{t:l})=>p(l,e+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"},{type:"switch",field:"disabled",title:"\u662F\u5426\u663E\u793A\u5220\u9664\u6309\u94AE",value:!0},{type:"switch",field:"showBtnText",title:"\u662F\u5426\u663E\u793A\u6309\u94AE\u6587\u5B57",value:!0}])}})(),g=(()=>{const t="\u591A\u56FE\u4E0A\u4F20",e="UploadImgs";return{icon:"icon-upload",label:t,name:e,rule:()=>({type:e,field:o(),title:t,info:"",$required:!1}),props:(i,{t:l})=>p(l,e+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},{type:"switch",field:"drag",title:"\u62D6\u62FD\u4E0A\u4F20",value:!1},{type:"select",field:"fileType",title:"\u56FE\u7247\u7C7B\u578B\u9650\u5236",value:["image/jpeg","image/png","image/gif"],options:[{label:"image/apng",value:"image/apng"},{label:"image/bmp",value:"image/bmp"},{label:"image/gif",value:"image/gif"},{label:"image/jpeg",value:"image/jpeg"},{label:"image/pjpeg",value:"image/pjpeg"},{label:"image/svg+xml",value:"image/svg+xml"},{label:"image/tiff",value:"image/tiff"},{label:"image/webp",value:"image/webp"},{label:"image/x-icon",value:"image/x-icon"}],props:{multiple:!0}},{type:"inputNumber",field:"fileSize",title:"\u5927\u5C0F\u9650\u5236(MB)",value:5,props:{min:0}},{type:"inputNumber",field:"limit",title:"\u6570\u91CF\u9650\u5236",value:5,props:{min:0}},{type:"input",field:"height",title:"\u7EC4\u4EF6\u9AD8\u5EA6",value:"150px"},{type:"input",field:"width",title:"\u7EC4\u4EF6\u5BBD\u5EA6",value:"150px"},{type:"input",field:"borderradius",title:"\u7EC4\u4EF6\u8FB9\u6846\u5706\u89D2",value:"8px"}])}})(),c=(()=>{const t="\u5B57\u5178\u9009\u62E9\u5668",e="DictSelect",i=v([]);return f(async()=>{const l=await h();l&&l.length!==0&&(i.value=(l==null?void 0:l.map(u=>({label:u.name,value:u.type})))??[])}),{icon:"icon-select",label:t,name:e,rule:()=>({type:e,field:o(),title:t,info:"",$required:!1}),props:(l,{t:u})=>p(u,e+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},{type:"select",field:"dictType",title:"\u5B57\u5178\u7C7B\u578B",value:"",options:i.value},{type:"select",field:"valueType",title:"\u5B57\u5178\u503C\u7C7B\u578B",value:"str",options:[{label:"\u6570\u5B57",value:"int"},{label:"\u5B57\u7B26\u4E32",value:"str"},{label:"\u5E03\u5C14\u503C",value:"bool"}]},...d])}})(),y=(()=>{const t="\u7528\u6237\u9009\u62E9\u5668",e="UserSelect";return{icon:"icon-select",label:t,name:e,rule:()=>({type:e,field:o(),title:t,info:"",$required:!1}),props:(i,{t:l})=>p(l,e+".props",[{type:"Required",field:"formCreate$required",title:"\u662F\u5426\u5FC5\u586B"},...d])}})();f(()=>{var t,e;(t=r.value)==null||t.removeMenuItem("upload"),(e=r.value)==null||e.removeMenuItem("fc-editor"),[m,n,a,g,c,y].forEach(i=>{var l,u;(l=r.value)==null||l.addComponent(i),(u=r.value)==null||u.appendMenuItem("main",{icon:i.icon,name:i.name,label:i.label})})})}});export{x as __tla,s as u};
