import{d as v,f,r as h,C as p,o as t,l as c,w as y,c as g,F as w,k as x,a as i,J as V,K as b,_ as k,__tla as C}from"./index-Daqg4PFz.js";import{f as J,__tla as U}from"./index-CtIY6rl-.js";let d,A=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{d=k(v({name:"WxAccountSelect",__name:"main",emits:["change"],setup(F,{emit:o}){const a=f({id:-1,name:""}),e=h([]),u=o,r=m=>{const l=e.value.find(s=>s.id===m);a.id&&(a.name=l?l.name:"",u("change",a.id,a.name))};return p(()=>{(async()=>(e.value=await J(),e.value.length>0&&(a.id=e.value[0].id,a.id&&(a.name=e.value[0].name,u("change",a.id,a.name)))))()}),(m,l)=>{const s=V,_=b;return t(),c(_,{modelValue:i(a).id,"onUpdate:modelValue":l[0]||(l[0]=n=>i(a).id=n),placeholder:"\u8BF7\u9009\u62E9\u516C\u4F17\u53F7",class:"!w-240px",onChange:r},{default:y(()=>[(t(!0),g(w,null,x(i(e),n=>(t(),c(s,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-account-select/main.vue"]])});export{A as __tla,d as default};
