import{d as A,r as N,eh as F,f as v,aG as w,C as H,o as m,c as J,i as a,g as Q,w as r,H as d,l as p,a as l,F as V,e1 as X,V as U,G as E,E as Y,s as j,P as B,Q as q,R as D,_ as K,__tla as O}from"./index-Daqg4PFz.js";import{E as W,__tla as Z}from"./el-card-Dvjjuipo.js";import{e as aa,_ as ta,__tla as ea}from"./Echart-C33-KcLZ.js";import{_ as ra,__tla as la}from"./index-CmwFi8Xl.js";import{d as sa,e as oa,f as ia,b as na,__tla as ua}from"./member-CJ2a9zFW.js";import{S as g,__tla as ca}from"./index-CvS2v6KM.js";import{c as ma}from"./china-aeAnb323.js";import{f as _a,__tla as da}from"./formatter-CcSwhdjG.js";import pa,{__tla as fa}from"./MemberFunnelCard-CE8ZmZkn.js";import ha,{__tla as ba}from"./MemberTerminalCard-DJIGSybZ.js";import{C as S,__tla as ga}from"./CardTitle-BD5ZuvK3.js";import{__tla as ya}from"./formatTime-BCfRGyrF.js";import{__tla as va}from"./CountTo-Dat_y5oU.js";import{__tla as wa}from"./index-CfQWqgvA.js";let $,xa=Promise.all([(()=>{try{return O}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{let x;x={class:"flex flex-col"},$=K(A({name:"MemberStatistics",__name:"index",setup(Ca){var P;const c=N(!0),f=N(),y=F();(P=aa)==null||P.registerMap("china",ma);const R=v({tooltip:{trigger:"item",confine:!0,formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right"},roseType:"area",series:[{name:"\u4F1A\u5458\u7EC8\u7AEF",type:"pie",label:{show:!1},labelLine:{show:!1},data:[]}]}),C=v({tooltip:{trigger:"item",confine:!0,formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right"},roseType:"area",series:[{name:"\u4F1A\u5458\u6027\u522B",type:"pie",label:{show:!1},labelLine:{show:!1},data:[]}]}),b=v({tooltip:{trigger:"item",formatter:e=>{var o,s,t,i,u;return`${((o=e==null?void 0:e.data)==null?void 0:o.areaName)||(e==null?void 0:e.name)}<br/>
\u4F1A\u5458\u6570\u91CF\uFF1A${((s=e==null?void 0:e.data)==null?void 0:s.userCount)||0}<br/>
\u8BA2\u5355\u521B\u5EFA\u6570\u91CF\uFF1A${((t=e==null?void 0:e.data)==null?void 0:t.orderCreateUserCount)||0}<br/>
\u8BA2\u5355\u652F\u4ED8\u6570\u91CF\uFF1A${((i=e==null?void 0:e.data)==null?void 0:i.orderPayUserCount)||0}<br/>
\u8BA2\u5355\u652F\u4ED8\u91D1\u989D\uFF1A${w(((u=e==null?void 0:e.data)==null?void 0:u.orderPayPrice)||0)}`}},visualMap:{text:["\u9AD8","\u4F4E"],realtime:!1,calculable:!0,top:"middle",inRange:{color:["#fff","#3b82f6"]}},series:[{name:"\u4F1A\u5458\u5730\u57DF\u5206\u5E03",type:"map",map:"china",roam:!1,selectedMode:!1,data:[]}]}),T=async()=>{f.value=await sa()},L=async()=>{const e=await oa();y.value=e.map(t=>({...t,areaName:X(t.areaName)}));let o=0,s=0;b.series[0].data=y.value.map(t=>(o=Math.min(o,t.orderPayUserCount||0),s=Math.max(s,t.orderPayUserCount||0),{...t,name:t.areaName,value:t.orderPayUserCount||0})),b.visualMap.min=o,b.visualMap.max=s},k=async()=>{const e=await ia(),o=U(E.SYSTEM_USER_SEX);o.push({label:"\u672A\u77E5",value:null}),C.series[0].data=o.map(s=>{var i;const t=(i=e.find(u=>u.sex===s.value))==null?void 0:i.userCount;return{name:s.label,value:t||0}})},G=async()=>{const e=await na(),o=U(E.TERMINAL);o.push({label:"\u672A\u77E5",value:null}),R.series[0].data=o.map(s=>{var i;const t=(i=e.find(u=>u.terminal===s.value))==null?void 0:i.userCount;return{name:s.label,value:t||0}})};return H(async()=>{c.value=!0,await Promise.all([T(),G(),L(),k()]),c.value=!1}),(e,o)=>{const s=ra,t=Y,i=j,u=ta,h=B,I=q,M=W,_=D;return m(),J(V,null,[a(s,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),Q("div",x,[a(i,{gutter:16,class:"summary"},{default:r(()=>[d((m(),p(t,{sm:6,xs:12},{default:r(()=>{var n;return[a(g,{value:((n=l(f))==null?void 0:n.userCount)||0,icon:"fa-solid:users","icon-bg-color":"text-blue-500","icon-color":"bg-blue-100",title:"\u7D2F\u8BA1\u4F1A\u5458\u6570"},null,8,["value"])]}),_:1})),[[_,l(c)]]),d((m(),p(t,{sm:6,xs:12},{default:r(()=>{var n;return[a(g,{value:((n=l(f))==null?void 0:n.rechargeUserCount)||0,icon:"fa-solid:user","icon-bg-color":"text-purple-500","icon-color":"bg-purple-100",title:"\u7D2F\u8BA1\u5145\u503C\u4EBA\u6570"},null,8,["value"])]}),_:1})),[[_,l(c)]]),d((m(),p(t,{sm:6,xs:12},{default:r(()=>{var n;return[a(g,{decimals:2,value:l(w)(((n=l(f))==null?void 0:n.rechargePrice)||0),icon:"fa-solid:money-check-alt","icon-bg-color":"text-yellow-500","icon-color":"bg-yellow-100",prefix:"\uFFE5",title:"\u7D2F\u8BA1\u5145\u503C\u91D1\u989D"},null,8,["value"])]}),_:1})),[[_,l(c)]]),d((m(),p(t,{sm:6,xs:12},{default:r(()=>{var n;return[a(g,{decimals:2,value:l(w)(((n=l(f))==null?void 0:n.expensePrice)||0),icon:"fa-solid:yen-sign","icon-bg-color":"text-green-500","icon-color":"bg-green-100",prefix:"\uFFE5",title:"\u7D2F\u8BA1\u6D88\u8D39\u91D1\u989D"},null,8,["value"])]}),_:1})),[[_,l(c)]])]),_:1}),a(i,{gutter:16,class:"mb-4"},{default:r(()=>[a(t,{md:18,sm:24},{default:r(()=>[a(pa)]),_:1}),a(t,{md:6,sm:24},{default:r(()=>[a(ha)]),_:1})]),_:1}),a(i,{gutter:16},{default:r(()=>[a(t,{md:18,sm:24},{default:r(()=>[a(M,{shadow:"never"},{header:r(()=>[a(l(S),{title:"\u4F1A\u5458\u5730\u57DF\u5206\u5E03"})]),default:r(()=>[d((m(),p(i,null,{default:r(()=>[a(t,{span:10},{default:r(()=>[a(u,{height:300,options:l(b)},null,8,["options"])]),_:1}),a(t,{span:14},{default:r(()=>[a(I,{data:l(y),height:300},{default:r(()=>[a(h,{"sort-method":(n,z)=>n.areaName.localeCompare(z.areaName,"zh-CN"),align:"center",label:"\u7701\u4EFD","min-width":"80",prop:"areaName","show-overflow-tooltip":"",sortable:""},null,8,["sort-method"]),a(h,{align:"center",label:"\u4F1A\u5458\u6570\u91CF","min-width":"105",prop:"userCount",sortable:""}),a(h,{align:"center",label:"\u8BA2\u5355\u521B\u5EFA\u6570\u91CF","min-width":"135",prop:"orderCreateUserCount",sortable:""}),a(h,{align:"center",label:"\u8BA2\u5355\u652F\u4ED8\u6570\u91CF","min-width":"135",prop:"orderPayUserCount",sortable:""}),a(h,{formatter:l(_a),align:"center",label:"\u8BA2\u5355\u652F\u4ED8\u91D1\u989D","min-width":"135",prop:"orderPayPrice",sortable:""},null,8,["formatter"])]),_:1},8,["data"])]),_:1})]),_:1})),[[_,l(c)]])]),_:1})]),_:1}),a(t,{md:6,sm:24},{default:r(()=>[d((m(),p(M,{shadow:"never"},{header:r(()=>[a(l(S),{title:"\u4F1A\u5458\u6027\u522B\u6BD4\u4F8B"})]),default:r(()=>[a(u,{height:300,options:l(C)},null,8,["options"])]),_:1})),[[_,l(c)]])]),_:1})]),_:1})])],64)}}}),[["__scopeId","data-v-c0f9ec90"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/statistics/member/index.vue"]])});export{xa as __tla,$ as default};
