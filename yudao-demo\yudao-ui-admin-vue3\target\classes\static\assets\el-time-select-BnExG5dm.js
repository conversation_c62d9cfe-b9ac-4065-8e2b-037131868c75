import{be as C,bP as E,bf as S,cF as P,bN as U,d as _,cx as x,cG as w,bg as A,r as G,cH as K,bW as W,b as i,o as m,l as b,w as h,a as c,bs as q,a0 as D,b1 as J,a9 as L,c as Q,F as R,k as X,K as I,bh as Y,__tla as Z}from"./index-Daqg4PFz.js";let $,ee=Promise.all([(()=>{try{return Z}catch{}})()]).then(async()=>{const T=C({format:{type:String,default:"HH:mm"},modelValue:String,disabled:Boolean,editable:{type:Boolean,default:!0},effect:{type:String,default:"light"},clearable:{type:Boolean,default:!0},size:E,placeholder:String,start:{type:String,default:"09:00"},end:{type:String,default:"18:00"},step:{type:String,default:"00:30"},minTime:String,maxTime:String,name:String,prefixIcon:{type:S([String,Object]),default:()=>P},clearIcon:{type:S([String,Object]),default:()=>U}}),u=n=>{const o=(n||"").split(":");if(o.length>=2){let t=Number.parseInt(o[0],10);const r=Number.parseInt(o[1],10),s=n.toUpperCase();return s.includes("AM")&&t===12?t=0:s.includes("PM")&&t!==12&&(t+=12),{hours:t,minutes:r}}return null},v=(n,o)=>{const t=u(n);if(!t)return-1;const r=u(o);if(!r)return-1;const s=t.minutes+60*t.hours,f=r.minutes+60*r.hours;return s===f?0:s>f?1:-1},g=n=>`${n}`.padStart(2,"0"),d=n=>`${g(n.hours)}:${g(n.minutes)}`,V=(n,o)=>{const t=u(n);if(!t)return"";const r=u(o);if(!r)return"";const s={hours:t.hours,minutes:t.minutes};return s.minutes+=r.minutes,s.hours+=r.hours,s.hours+=Math.floor(s.minutes/60),s.minutes=s.minutes%60,d(s)},z=_({name:"ElTimeSelect"});var p=Y(_({...z,props:T,emits:["change","blur","focus","update:modelValue"],setup(n,{expose:o}){const t=n;x.extend(w);const{Option:r}=I,s=A("input"),f=G(),H=K(),{lang:k}=W(),B=i(()=>t.modelValue),F=i(()=>{const e=u(t.start);return e?d(e):null}),y=i(()=>{const e=u(t.end);return e?d(e):null}),M=i(()=>{const e=u(t.step);return e?d(e):null}),N=i(()=>{const e=u(t.minTime||"");return e?d(e):null}),O=i(()=>{const e=u(t.maxTime||"");return e?d(e):null}),j=i(()=>{const e=[];if(t.start&&t.end&&t.step){let l,a=F.value;for(;a&&y.value&&v(a,y.value)<=0;)l=x(a,"HH:mm").locale(k.value).format(t.format),e.push({value:l,disabled:v(a,N.value||"-1:-1")<=0||v(a,O.value||"100:100")>=0}),a=V(a,M.value)}return e});return o({blur:()=>{var e,l;(l=(e=f.value)==null?void 0:e.blur)==null||l.call(e)},focus:()=>{var e,l;(l=(e=f.value)==null?void 0:e.focus)==null||l.call(e)}}),(e,l)=>(m(),b(c(I),{ref_key:"select",ref:f,"model-value":c(B),disabled:c(H),clearable:e.clearable,"clear-icon":e.clearIcon,size:e.size,effect:e.effect,placeholder:e.placeholder,"default-first-option":"",filterable:e.editable,"onUpdate:modelValue":l[0]||(l[0]=a=>e.$emit("update:modelValue",a)),onChange:l[1]||(l[1]=a=>e.$emit("change",a)),onBlur:l[2]||(l[2]=a=>e.$emit("blur",a)),onFocus:l[3]||(l[3]=a=>e.$emit("focus",a))},{prefix:h(()=>[e.prefixIcon?(m(),b(c(q),{key:0,class:D(c(s).e("prefix-icon"))},{default:h(()=>[(m(),b(J(e.prefixIcon)))]),_:1},8,["class"])):L("v-if",!0)]),default:h(()=>[(m(!0),Q(R,null,X(c(j),a=>(m(),b(c(r),{key:a.value,label:a.value,value:a.value,disabled:a.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["model-value","disabled","clearable","clear-icon","size","effect","placeholder","filterable"]))}}),[["__file","time-select.vue"]]);p.install=n=>{n.component(p.name,p)},$=p});export{$ as E,ee as __tla};
