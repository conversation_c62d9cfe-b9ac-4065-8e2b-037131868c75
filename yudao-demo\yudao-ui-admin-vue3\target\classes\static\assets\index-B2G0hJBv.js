import{d as H,f as J,e as j,r as m,b as B,at as G,C as K,ay as O,o as p,c as y,i as a,w as t,a as e,F as h,k as g,V as P,G as W,l as C,j as q,z as Q,M as X,L as Z,J as $,K as aa,x as ea,N as la,O as ta,A as ra,B as sa,E as ua,_ as na,__tla as _a}from"./index-Daqg4PFz.js";import{_ as oa,__tla as ma}from"./ContentWrap-DZg14iby.js";import{E as da,__tla as ca}from"./el-tree-select-BKcJcOKx.js";import{g as ia,__tla as pa}from"./index-D-Abj-9W.js";import{f as S,c as fa,e as ya,g as ha,__tla as va}from"./formatTime-BCfRGyrF.js";import{h as U,d as ba}from"./tree-BMqZf9_I.js";import Ia,{__tla as Va}from"./FunnelBusiness-BYRffIF4.js";import wa,{__tla as Ra}from"./BusinessSummary-Cpif4hCp.js";import ka,{__tla as Da}from"./BusinessInversionRateSummary-BlXOoC09.js";import{__tla as xa}from"./el-card-Dvjjuipo.js";import{__tla as ga}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as Ca}from"./el-skeleton-item-BmS2F7Yy.js";import{__tla as qa}from"./Echart-C33-KcLZ.js";import{__tla as Sa}from"./funnel-zdvqbTKQ.js";import{__tla as Ua}from"./index-BBLwwrga.js";import{__tla as za}from"./index-CS70nJJ8.js";let z,Ea=Promise.all([(()=>{try{return _a}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return za}catch{}})()]).then(async()=>{let v;v=H({name:"CrmStatisticsFunnel",__name:"index",setup(Fa){const r=J({interval:2,deptId:j().getUser.deptId,userId:void 0,times:[S(fa(new Date(new Date().getTime()-6048e5))),S(ya(new Date(new Date().getTime()-864e5)))]}),b=m(),I=m([]),V=m([]),E=B(()=>r.deptId?V.value.filter(_=>_.deptId===r.deptId):[]),c=m("funnelRef"),w=m(),R=m(),k=m(),n=()=>{var _,s,d,u,o,i;switch(c.value){case"funnelRef":(s=(_=w.value)==null?void 0:_.loadData)==null||s.call(_);break;case"businessSummaryRef":(u=(d=R.value)==null?void 0:d.loadData)==null||u.call(d);break;case"businessInversionRateSummaryRef":(i=(o=k.value)==null?void 0:o.loadData)==null||i.call(o)}};G(c,()=>{n()});const F=()=>{b.value.resetFields(),n()};return K(async()=>{I.value=U(await ia()),V.value=U(await O())}),(_,s)=>{const d=X,u=Z,o=$,i=aa,T=da,D=ea,x=la,M=ta,Y=oa,f=ra,A=sa,L=ua;return p(),y(h,null,[a(Y,null,{default:t(()=>[a(M,{ref_key:"queryFormRef",ref:b,inline:!0,model:e(r),class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(u,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:t(()=>[a(d,{modelValue:e(r).times,"onUpdate:modelValue":s[0]||(s[0]=l=>e(r).times=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:e(ha),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss",onChange:n},null,8,["modelValue","default-time","shortcuts"])]),_:1}),a(u,{label:"\u65F6\u95F4\u95F4\u9694",prop:"interval"},{default:t(()=>[a(i,{modelValue:e(r).interval,"onUpdate:modelValue":s[1]||(s[1]=l=>e(r).interval=l),class:"!w-240px",placeholder:"\u95F4\u9694\u7C7B\u578B",onChange:n},{default:t(()=>[(p(!0),y(h,null,g(e(P)(e(W).DATE_INTERVAL),l=>(p(),C(o,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:t(()=>[a(T,{modelValue:e(r).deptId,"onUpdate:modelValue":s[2]||(s[2]=l=>e(r).deptId=l),data:e(I),props:e(ba),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:s[3]||(s[3]=l=>(e(r).userId=void 0,n()))},null,8,["modelValue","data","props"])]),_:1}),a(u,{label:"\u5458\u5DE5",prop:"userId"},{default:t(()=>[a(i,{modelValue:e(r).userId,"onUpdate:modelValue":s[4]||(s[4]=l=>e(r).userId=l),class:"!w-240px",clearable:"",placeholder:"\u5458\u5DE5",onChange:n},{default:t(()=>[(p(!0),y(h,null,g(e(E),(l,N)=>(p(),C(o,{key:N,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,null,{default:t(()=>[a(x,{onClick:n},{default:t(()=>[a(D,{class:"mr-5px",icon:"ep:search"}),q(" \u67E5\u8BE2 ")]),_:1}),a(x,{onClick:F},{default:t(()=>[a(D,{class:"mr-5px",icon:"ep:refresh"}),q(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(L,null,{default:t(()=>[a(A,{modelValue:e(c),"onUpdate:modelValue":s[5]||(s[5]=l=>Q(c)?c.value=l:null)},{default:t(()=>[a(f,{label:"\u9500\u552E\u6F0F\u6597\u5206\u6790",lazy:"",name:"funnelRef"},{default:t(()=>[a(Ia,{ref_key:"funnelRef",ref:w,"query-params":e(r)},null,8,["query-params"])]),_:1}),a(f,{label:"\u65B0\u589E\u5546\u673A\u5206\u6790",lazy:"",name:"businessSummaryRef"},{default:t(()=>[a(wa,{ref_key:"businessSummaryRef",ref:R,"query-params":e(r)},null,8,["query-params"])]),_:1}),a(f,{label:"\u5546\u673A\u8F6C\u5316\u7387\u5206\u6790",lazy:"",name:"businessInversionRateSummaryRef"},{default:t(()=>[a(ka,{ref_key:"businessInversionRateSummaryRef",ref:k,"query-params":e(r)},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}}),z=na(v,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/statistics/funnel/index.vue"]])});export{Ea as __tla,z as default};
