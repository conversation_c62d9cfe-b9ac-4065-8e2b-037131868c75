import{bE as r,__tla as g}from"./index-Daqg4PFz.js";let e,a,p,u,l,o,d,s,c,y,i=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{a=t=>r.post({url:"/product/property/create",data:t}),o=t=>r.put({url:"/product/property/update",data:t}),c=t=>r.delete({url:`/product/property/delete?id=${t}`}),d=t=>r.get({url:`/product/property/get?id=${t}`}),s=t=>r.get({url:"/product/property/page",params:t}),e=t=>r.get({url:"/product/property/value/page",params:t}),l=t=>r.get({url:`/product/property/value/get?id=${t}`}),p=t=>r.post({url:"/product/property/value/create",data:t}),y=t=>r.put({url:"/product/property/value/update",data:t}),u=t=>r.delete({url:`/product/property/value/delete?id=${t}`})});export{i as __tla,e as a,a as b,p as c,u as d,l as e,o as f,d as g,s as h,c as i,y as u};
