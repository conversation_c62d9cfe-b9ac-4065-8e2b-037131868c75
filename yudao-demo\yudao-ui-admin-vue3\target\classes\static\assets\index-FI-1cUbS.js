import{d as m,r as _,C as p,o as l,c as y,i as e,w as d,a as r,H as f,l as h,a9 as v,F as x,R as w,_ as b,__tla as g}from"./index-Daqg4PFz.js";import{_ as j,__tla as k}from"./ContentWrap-DZg14iby.js";import{_ as A,__tla as C}from"./IFrame-DOdFY0xB.js";import{_ as F,__tla as H}from"./index-CmwFi8Xl.js";import{b as I,__tla as J}from"./index-Cz8k7H0s.js";import{__tla as P}from"./el-card-Dvjjuipo.js";let n,R=Promise.all([(()=>{try{return g}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{n=b(m({name:"InfraAdminServer",__name:"index",setup(S){const t=_(!0),s=_("http://localhost:48080/admin/applications");return p(async()=>{try{const a=await I("url.spring-boot-admin");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,U)=>{const o=F,i=A,c=j,u=w;return l(),y(x,null,[e(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(c,null,{default:d(()=>[r(t)?v("",!0):f((l(),h(i,{key:0,src:r(s)},null,8,["src"])),[[u,r(t)]])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/server/index.vue"]])});export{R as __tla,n as default};
