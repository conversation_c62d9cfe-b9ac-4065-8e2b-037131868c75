import{d as v,I as h,b7 as I,r as U,f as j,o as w,l as x,w as r,g as F,aW as S,i as q,j as D,a as t,N as E,bD as H,_ as J,__tla as N}from"./index-Daqg4PFz.js";import{b as P,a as W,U as $,H as k,__tla as z}from"./upload-Bcl_Ww_H.js";import{U as i,__tla as A}from"./useUpload-DvwaTvLo.js";let d,B=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{let l;l={class:"el-upload__tip",style:{"margin-left":"5px"}},d=J(v({__name:"UploadFile",props:{type:{type:String,required:!0}},emits:["uploaded"],setup(n,{emit:u}){const e=h(),p=n,_=I("accountId"),o=U([]),c=u,s=j({type:i.Image,title:"",introduction:"",accountId:_}),m=p.type===i.Image?P:W,f=a=>{if(a.code!==0)return e.alertError("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;o.value=[],s.title="",s.introduction="",e.notifySuccess("\u4E0A\u4F20\u6210\u529F"),c("uploaded")},y=a=>e.error("\u4E0A\u4F20\u5931\u8D25: "+a.message);return(a,C)=>{const b=E,g=H;return w(),x(g,{action:t($),headers:t(k),multiple:"",limit:1,"file-list":t(o),data:t(s),"on-error":y,"before-upload":t(m),"on-success":f},{tip:r(()=>[F("span",l,[S(a.$slots,"default",{},void 0,!0)])]),default:r(()=>[q(b,{type:"primary",plain:""},{default:r(()=>[D(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:3},8,["action","headers","file-list","data","before-upload"])}}}),[["__scopeId","data-v-9db5701b"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/material/components/UploadFile.vue"]])});export{B as __tla,d as default};
