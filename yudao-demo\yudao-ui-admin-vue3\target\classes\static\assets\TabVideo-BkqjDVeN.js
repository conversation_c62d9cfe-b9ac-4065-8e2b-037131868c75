import{d as T,I as q,dY as A,b as B,r as y,f as C,o as h,c as M,i as a,w as r,a as l,l as P,a9 as D,j as v,z as E,Z as N,s as O,x as Q,N as S,cJ as W,E as Y,bD as Z,_ as F,__tla as G}from"./index-Daqg4PFz.js";import H,{__tla as K}from"./main-tYLRPXX5.js";import{W as L,__tla as R}from"./main-BFIJAzpS.js";import{u as X,U as $,__tla as tt}from"./useUpload-DvwaTvLo.js";import{__tla as at}from"./index-BBLwwrga.js";import{__tla as et}from"./index-CS70nJJ8.js";import{__tla as lt}from"./main-CZAPo5JB.js";import{__tla as rt}from"./el-image-Bn34T02c.js";import{__tla as ot}from"./main-D2WNvJUY.js";import{__tla as ut}from"./index-C7JnLY69.js";import{__tla as it}from"./index-DC2RezQi.js";import{__tla as st}from"./formatTime-BCfRGyrF.js";let V,_t=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return st}catch{}})()]).then(async()=>{V=F(T({__name:"TabVideo",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(b,{emit:I}){const g=q(),w={Authorization:"Bearer "+A()},U=b,k=I,e=B({get:()=>U.modelValue,set:t=>k("update:modelValue",t)}),i=y(!1),n=y([]),s=C({accountId:e.value.accountId,type:"video",title:"",introduction:""}),x=t=>X($.Video,10)(t),j=t=>{if(t.code!==0)return g.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;n.value=[],s.title="",s.introduction="",c(t.data)},c=t=>{i.value=!1,e.value.mediaId=t.mediaId,e.value.url=t.url,e.value.name=t.name,t.title&&(e.value.title=t.title||""),t.introduction&&(e.value.description=t.introduction||"")};return(t,o)=>{const m=N,_=O,p=Q,f=S,z=W,d=Y,J=Z;return h(),M("div",null,[a(_,null,{default:r(()=>[a(m,{modelValue:l(e).title,"onUpdate:modelValue":o[0]||(o[0]=u=>l(e).title=u),class:"input-margin-bottom",placeholder:"\u8BF7\u8F93\u5165\u6807\u9898"},null,8,["modelValue"]),a(m,{class:"input-margin-bottom",modelValue:l(e).description,"onUpdate:modelValue":o[1]||(o[1]=u=>l(e).description=u),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0"},null,8,["modelValue"]),a(_,{class:"ope-row",justify:"center"},{default:r(()=>[l(e).url?(h(),P(l(H),{key:0,url:l(e).url},null,8,["url"])):D("",!0)]),_:1}),a(d,null,{default:r(()=>[a(_,{style:{"text-align":"center"},align:"middle"},{default:r(()=>[a(d,{span:12},{default:r(()=>[a(f,{type:"success",onClick:o[2]||(o[2]=u=>i.value=!0)},{default:r(()=>[v(" \u7D20\u6750\u5E93\u9009\u62E9 "),a(p,{icon:"ep:circle-check"})]),_:1}),a(z,{title:"\u9009\u62E9\u89C6\u9891",modelValue:l(i),"onUpdate:modelValue":o[3]||(o[3]=u=>E(i)?i.value=u:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:r(()=>[a(l(L),{type:"video","account-id":l(e).accountId,onSelectMaterial:c},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(d,{span:12},{default:r(()=>[a(J,{action:"http://localhost:48080/admin-api/mp/material/upload-temporary",headers:w,multiple:"",limit:1,"file-list":l(n),data:l(s),"before-upload":x,"on-success":j},{default:r(()=>[a(f,{type:"primary"},{default:r(()=>[v("\u65B0\u5EFA\u89C6\u9891 "),a(p,{icon:"ep:upload"})]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-bf7e1f5e"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-reply/components/TabVideo.vue"]])});export{_t as __tla,V as default};
