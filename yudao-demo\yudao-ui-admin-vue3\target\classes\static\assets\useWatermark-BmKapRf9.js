const a=Symbol("watermark-dom");function c(l=document.body){let i=()=>{};const d=a.toString(),r=()=>{const t=document.getElementById(d);t&&l&&l.removeChild(t),window.removeEventListener("resize",i)},s=t=>{r();const o=document.createElement("canvas");o.width=300,o.height=240;const n=o.getContext("2d");n&&(n.rotate(-20*Math.PI/120),n.font="15px Vedana",n.fillStyle="rgba(0, 0, 0, 0.15)",n.textAlign="left",n.textBaseline="middle",n.fillText(t,o.width/20,o.height));const e=document.createElement("div");return e.id=d,e.style.pointerEvents="none",e.style.top="0px",e.style.left="0px",e.style.position="absolute",e.style.zIndex="100000000",e.style.width=document.documentElement.clientWidth+"px",e.style.height=document.documentElement.clientHeight+"px",e.style.background="url("+o.toDataURL("image/png")+") left top repeat",l&&l.appendChild(e),d};return{setWatermark:function(t){s(t),i=()=>{s(t)},window.addEventListener("resize",i)},clear:r}}export{c as u};
