import{d as oe,u as ue,r as i,f as de,at as se,C as ie,T as pe,o as u,c as p,i as l,w as t,a,F as n,k as y,V as C,G as V,l as d,dR as ne,a9 as T,U as _e,H as g,a8 as ce,j as h,g as me,J as ye,K as ve,L as fe,M as be,Z as he,x as ke,N as Ve,O as Ue,Q as we,R as Ce,_ as Te,__tla as ge}from"./index-Daqg4PFz.js";import{_ as xe,__tla as Ee}from"./index-BBLwwrga.js";import{E as Pe,a as Se,b as Ie,__tla as Re}from"./el-dropdown-item-C6dpORMi.js";import{_ as De,__tla as Ne}from"./ContentWrap-DZg14iby.js";import{_ as qe,__tla as Ye}from"./index-CmwFi8Xl.js";import Ae,{__tla as Oe}from"./OrderDeliveryForm-B-nADFCn.js";import ze,{__tla as Fe}from"./OrderUpdateRemarkForm-BHoQwu46.js";import{e as He,__tla as Ke}from"./index-eGsURMRC.js";import{a as Le,__tla as Me}from"./index-CQXp_iHR.js";import{g as je,__tla as Ge}from"./index-B07NeSqB.js";import{D as x,T as Je}from"./constants-WoCEnNvc.js";import Xe,{__tla as Qe}from"./OrderTableColumn-JZBZUYMI.js";import{__tla as Ze}from"./index-CS70nJJ8.js";import{__tla as Be}from"./el-card-Dvjjuipo.js";import{__tla as We}from"./Dialog-BjBBVYCI.js";import{__tla as $e}from"./el-image-Bn34T02c.js";import{__tla as ea}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as aa}from"./formatTime-BCfRGyrF.js";let J,la=Promise.all([(()=>{try{return ge}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})()]).then(async()=>{let R;R={class:"flex items-center justify-center"},J=Te(oe({name:"TradeOrder",__name:"index",setup(ta){const{currentRoute:X,push:Q}=ue(),E=i(!0),D=i(2),P=i([]),N=i(),r=i({pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),k=de({queryParam:""}),q=i([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userId",label:"\u7528\u6237UID"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),Z=_=>{var o;(o=q.value.filter(v=>v.value!==_))==null||o.forEach(v=>{r.value.hasOwnProperty(v.value)&&delete r.value[v.value]})},f=async()=>{E.value=!0;try{const _=await He(a(r));P.value=_.list,D.value=_.total}finally{E.value=!1}},S=async()=>{r.value.pageNo=1,await f()},B=()=>{var _;(_=N.value)==null||_.resetFields(),r.value={pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0},S()},Y=i(),A=i();se(()=>X.value,()=>{f()});const I=i([]),O=i([]);return ie(async()=>{await f(),I.value=await Le(),O.value=await je()}),(_,o)=>{const v=qe,c=ye,m=ve,s=fe,W=be,z=he,b=ke,U=Ve,$=Ue,F=De,H=Pe,ee=Se,ae=Ie,le=we,te=xe,K=pe("hasPermi"),re=Ce;return u(),p(n,null,[l(v,{title:"\u3010\u4EA4\u6613\u3011\u4EA4\u6613\u8BA2\u5355",url:"https://doc.iocoder.cn/mall/trade-order/"}),l(v,{title:"\u3010\u4EA4\u6613\u3011\u8D2D\u7269\u8F66",url:"https://doc.iocoder.cn/mall/trade-cart/"}),l(F,null,{default:t(()=>[l($,{ref_key:"queryFormRef",ref:N,inline:!0,model:a(r),class:"-mb-15px","label-width":"68px"},{default:t(()=>[l(s,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:t(()=>[l(m,{modelValue:a(r).status,"onUpdate:modelValue":o[0]||(o[0]=e=>a(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(C)(a(V).TRADE_ORDER_STATUS),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:t(()=>[l(m,{modelValue:a(r).payChannelCode,"onUpdate:modelValue":o[1]||(o[1]=e=>a(r).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(ne)(a(V).PAY_CHANNEL_CODE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[l(W,{modelValue:a(r).createTime,"onUpdate:modelValue":o[2]||(o[2]=e=>a(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(s,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:t(()=>[l(m,{modelValue:a(r).terminal,"onUpdate:modelValue":o[3]||(o[3]=e=>a(r).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(C)(a(V).TERMINAL),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:t(()=>[l(m,{modelValue:a(r).type,"onUpdate:modelValue":o[4]||(o[4]=e=>a(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(C)(a(V).TRADE_ORDER_TYPE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:t(()=>[l(m,{modelValue:a(r).deliveryType,"onUpdate:modelValue":o[5]||(o[5]=e=>a(r).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(C)(a(V).TRADE_DELIVERY_TYPE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r).deliveryType===a(x).EXPRESS.type?(u(),d(s,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:t(()=>[l(m,{modelValue:a(r).logisticsId,"onUpdate:modelValue":o[6]||(o[6]=e=>a(r).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(O),e=>(u(),d(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),a(r).deliveryType===a(x).PICK_UP.type?(u(),d(s,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:t(()=>[l(m,{modelValue:a(r).pickUpStoreId,"onUpdate:modelValue":o[7]||(o[7]=e=>a(r).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),p(n,null,y(a(I),e=>(u(),d(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),a(r).deliveryType===a(x).PICK_UP.type?(u(),d(s,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:t(()=>[l(z,{modelValue:a(r).pickUpVerifyCode,"onUpdate:modelValue":o[8]||(o[8]=e=>a(r).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:_e(S,["enter"])},null,8,["modelValue"])]),_:1})):T("",!0),l(s,{label:"\u805A\u5408\u641C\u7D22"},{default:t(()=>[g(l(z,{modelValue:a(r)[a(k).queryParam],"onUpdate:modelValue":o[10]||(o[10]=e=>a(r)[a(k).queryParam]=e),type:a(k).queryParam==="userId"?"number":"text",class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165"},{prepend:t(()=>[l(m,{modelValue:a(k).queryParam,"onUpdate:modelValue":o[9]||(o[9]=e=>a(k).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:Z},{default:t(()=>[(u(!0),p(n,null,y(a(q),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","type"]),[[ce,!0]])]),_:1}),l(s,null,{default:t(()=>[l(U,{onClick:S},{default:t(()=>[l(b,{class:"mr-5px",icon:"ep:search"}),h(" \u641C\u7D22 ")]),_:1}),l(U,{onClick:B},{default:t(()=>[l(b,{class:"mr-5px",icon:"ep:refresh"}),h(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(F,null,{default:t(()=>[g((u(),d(le,{data:a(P),"row-key":"id"},{default:t(()=>[l(a(Xe),{list:a(P),"pick-up-store-list":a(I)},{default:t(({row:e})=>[me("div",R,[g((u(),d(U,{link:"",type:"primary",onClick:L=>{return w=e.id,void Q({name:"TradeOrderDetail",params:{id:w}});var w}},{default:t(()=>[l(b,{icon:"ep:notification"}),h(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[K,["trade:order:query"]]]),g((u(),d(ae,{onCommand:L=>((w,M)=>{var j,G;switch(w){case"remark":(j=A.value)==null||j.open(M);break;case"delivery":(G=Y.value)==null||G.open(M)}})(L,e)},{dropdown:t(()=>[l(ee,null,{default:t(()=>[e.deliveryType===a(x).EXPRESS.type&&e.status===a(Je).UNDELIVERED.status?(u(),d(H,{key:0,command:"delivery"},{default:t(()=>[l(b,{icon:"ep:takeaway-box"}),h(" \u53D1\u8D27 ")]),_:1})):T("",!0),l(H,{command:"remark"},{default:t(()=>[l(b,{icon:"ep:chat-line-square"}),h(" \u5907\u6CE8 ")]),_:1})]),_:2},1024)]),default:t(()=>[l(U,{link:"",type:"primary"},{default:t(()=>[l(b,{icon:"ep:d-arrow-right"}),h(" \u66F4\u591A ")]),_:1})]),_:2},1032,["onCommand"])),[[K,["trade:order:update"]]])])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[re,a(E)]]),l(te,{limit:a(r).pageSize,"onUpdate:limit":o[11]||(o[11]=e=>a(r).pageSize=e),page:a(r).pageNo,"onUpdate:page":o[12]||(o[12]=e=>a(r).pageNo=e),total:a(D),onPagination:f},null,8,["limit","page","total"])]),_:1}),l(Ae,{ref_key:"deliveryFormRef",ref:Y,onSuccess:f},null,512),l(ze,{ref_key:"updateRemarkForm",ref:A,onSuccess:f},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/trade/order/index.vue"]])});export{la as __tla,J as default};
