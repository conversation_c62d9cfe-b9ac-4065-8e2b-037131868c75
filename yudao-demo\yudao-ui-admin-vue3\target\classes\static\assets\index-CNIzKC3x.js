import{d as Q,u as Z,I as $,n as W,r as d,f as X,bd as aa,C as ea,ay as la,T as ta,o as n,c,i as e,w as t,a as l,F as m,k as y,l as i,U as M,V as ra,G as B,H as k,j as x,t as E,g as sa,a9 as na,dz as oa,J as ia,K as ua,L as da,Z as pa,M as ca,O as ma,P as _a,N as fa,Q as ya,R as wa,_ as ba,__tla as ga}from"./index-Daqg4PFz.js";import{_ as ha,__tla as va}from"./index-BBLwwrga.js";import{_ as ka,__tla as xa}from"./DictTag-BDZzHcIz.js";import{_ as Va,__tla as Ua}from"./ContentWrap-DZg14iby.js";import{_ as Ia,__tla as Sa}from"./index-CmwFi8Xl.js";import{d as R,a as Ta,__tla as Ca}from"./formatTime-BCfRGyrF.js";import{d as Na,e as Pa,__tla as Da}from"./index-Wcjc3rZh.js";import{C as Ma,__tla as Ba}from"./index-B4Qi4fi2.js";import{__tla as Ea}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Ra}from"./el-card-Dvjjuipo.js";let z,za=Promise.all([(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return Ra}catch{}})()]).then(async()=>{z=ba(Q({name:"BpmProcessInstanceManager",__name:"index",setup(Aa){const A=Z(),O=$(),{t:V}=W(),w=d(!0),U=d(0),I=d([]),r=X({pageNo:1,pageSize:10,startUserId:void 0,name:"",processDefinitionId:void 0,category:void 0,status:void 0,createTime:[]}),Y=d(),S=d([]),T=d([]),p=async()=>{w.value=!0;try{const b=await Na(r);I.value=b.list,U.value=b.total}finally{w.value=!1}},C=()=>{r.pageNo=1,p()};return aa(()=>{p()}),ea(async()=>{await p(),S.value=await Ma.getCategorySimpleList(),T.value=await la()}),(b,s)=>{const q=Ia,g=ia,h=ua,u=da,N=pa,H=ca,J=ma,P=Va,o=_a,K=ka,v=fa,L=ya,F=ha,D=ta("hasPermi"),j=wa;return n(),c(m,null,[e(q,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),e(P,null,{default:t(()=>[e(J,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:Y,inline:!0,"label-width":"68px"},{default:t(()=>[e(u,{label:"\u53D1\u8D77\u4EBA",prop:"startUserId"},{default:t(()=>[e(h,{modelValue:l(r).startUserId,"onUpdate:modelValue":s[0]||(s[0]=a=>l(r).startUserId=a),placeholder:"\u8BF7\u9009\u62E9\u53D1\u8D77\u4EBA",class:"!w-240px"},{default:t(()=>[(n(!0),c(m,null,y(l(T),a=>(n(),i(g,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[e(N,{modelValue:l(r).name,"onUpdate:modelValue":s[1]||(s[1]=a=>l(r).name=a),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:M(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6240\u5C5E\u6D41\u7A0B",prop:"processDefinitionId"},{default:t(()=>[e(N,{modelValue:l(r).processDefinitionId,"onUpdate:modelValue":s[2]||(s[2]=a=>l(r).processDefinitionId=a),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u5B9A\u4E49\u7684\u7F16\u53F7",clearable:"",onKeyup:M(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[e(h,{modelValue:l(r).category,"onUpdate:modelValue":s[3]||(s[3]=a=>l(r).category=a),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),c(m,null,y(l(S),a=>(n(),i(g,{key:a.code,label:a.name,value:a.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status"},{default:t(()=>[e(h,{modelValue:l(r).status,"onUpdate:modelValue":s[4]||(s[4]=a=>l(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),c(m,null,y(l(ra)(l(B).BPM_PROCESS_INSTANCE_STATUS),a=>(n(),i(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(H,{modelValue:l(r).createTime,"onUpdate:modelValue":s[5]||(s[5]=a=>l(r).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:t(()=>[k((n(),i(L,{data:l(I)},{default:t(()=>[e(o,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name","min-width":"200px",fixed:"left"}),e(o,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName","min-width":"100",fixed:"left"}),e(o,{label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",align:"center",prop:"startUser.nickname",width:"120"}),e(o,{label:"\u53D1\u8D77\u90E8\u95E8",align:"center",prop:"startUser.deptName",width:"120"}),e(o,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status",width:"120"},{default:t(a=>[e(K,{type:l(B).BPM_PROCESS_INSTANCE_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(o,{label:"\u53D1\u8D77\u65F6\u95F4",align:"center",prop:"startTime",width:"180",formatter:l(R)},null,8,["formatter"]),e(o,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:l(R)},null,8,["formatter"]),e(o,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"169"},{default:t(a=>[x(E(a.row.durationInMillis>0?l(Ta)(a.row.durationInMillis):"-"),1)]),_:1}),e(o,{label:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",prop:"tasks","min-width":"120px"},{default:t(a=>[(n(!0),c(m,null,y(a.row.tasks,_=>(n(),i(v,{type:"primary",key:_.id,link:""},{default:t(()=>[sa("span",null,E(_.name),1)]),_:2},1024))),128))]),_:1}),e(o,{label:"\u6D41\u7A0B\u7F16\u53F7",align:"center",prop:"id","min-width":"320px"}),e(o,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"180"},{default:t(a=>[k((n(),i(v,{link:"",type:"primary",onClick:_=>{return f=a.row,void A.push({name:"BpmProcessInstanceDetail",query:{id:f.id}});var f}},{default:t(()=>[x(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[D,["bpm:process-instance:cancel"]]]),a.row.status===1?k((n(),i(v,{key:0,link:"",type:"primary",onClick:_=>(async f=>{const{value:G}=await oa.prompt("\u8BF7\u8F93\u5165\u53D6\u6D88\u539F\u56E0","\u53D6\u6D88\u6D41\u7A0B",{confirmButtonText:V("common.ok"),cancelButtonText:V("common.cancel"),inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});await Pa(f.id,G),O.success("\u53D6\u6D88\u6210\u529F"),await p()})(a.row)},{default:t(()=>[x(" \u53D6\u6D88 ")]),_:2},1032,["onClick"])),[[D,["bpm:process-instance:query"]]]):na("",!0)]),_:1})]),_:1},8,["data"])),[[j,l(w)]]),e(F,{total:l(U),page:l(r).pageNo,"onUpdate:page":s[6]||(s[6]=a=>l(r).pageNo=a),limit:l(r).pageSize,"onUpdate:limit":s[7]||(s[7]=a=>l(r).pageSize=a),onPagination:p},null,8,["total","page","limit"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/manager/index.vue"]])});export{za as __tla,z as default};
