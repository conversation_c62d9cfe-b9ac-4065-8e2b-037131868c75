import{d as H,u as J,S as L,r as _,C as O,H as q,a,o as S,l as I,w as s,i as l,z as f,j as E,a9 as G,n as K,I as M,aC as k,aD as P,aE as Q,aF as h,A as W,B as X,N as Y,L as Z,O as $,R as aa,_ as ra,__tla as ta}from"./index-Daqg4PFz.js";import{_ as ea,__tla as ia}from"./ContentWrap-DZg14iby.js";import{u as la,__tla as oa}from"./tagsView-CrrEoR03.js";import{g as ca,c as sa,u as _a,__tla as ma}from"./spu-zkQh6zUd.js";import ua,{__tla as na}from"./InfoForm-CH71xOWK.js";import pa,{__tla as da}from"./DescriptionForm-DqzUNdgR.js";import fa,{__tla as ya}from"./OtherForm-CPlUf6ji.js";import va,{__tla as ka}from"./SkuForm-02wk_O3T.js";import Pa,{__tla as ha}from"./DeliveryForm-DhRuyXAd.js";import{__tla as ga}from"./el-card-Dvjjuipo.js";import"./tree-BMqZf9_I.js";import{__tla as Na}from"./category-D3voy_BE.js";import{__tla as Ba}from"./brand-CR_0dsQ_.js";import{__tla as wa}from"./formRules-BBK7HL0H.js";import{__tla as Da}from"./index-BfdQCGa1.js";import{__tla as Fa}from"./SkuList-IlES89tg.js";import{__tla as Ua}from"./el-image-Bn34T02c.js";import{__tla as ba}from"./ProductAttributes-QCFRGJd9.js";import{__tla as Ca}from"./el-text-vv1naHK-.js";import{__tla as Ra}from"./property-Dsd0TI7Q.js";import{__tla as Sa}from"./ProductPropertyAddForm-7tmIJFEp.js";import{__tla as Ia}from"./Dialog-BjBBVYCI.js";import{__tla as Ea}from"./index-By4J75sK.js";import"./constants-WoCEnNvc.js";let T,Ta=Promise.all([(()=>{try{return ta}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ea}catch{}})()]).then(async()=>{T=ra(H({name:"ProductSpuForm",__name:"index",setup(Va){const{t:N}=K(),B=M(),{push:V,currentRoute:x}=J(),{params:w,name:j}=L(),{delView:A}=la(),p=_(!1),t=_("info"),m=_(!1),D=_(),F=_(),U=_(),b=_(),C=_(),u=_({name:"",categoryId:void 0,keyword:"",picUrl:"",sliderPicUrls:[],introduction:"",deliveryTypes:[],deliveryTemplateId:void 0,brandId:void 0,specType:!1,subCommissionType:!1,skus:[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}],description:"",sort:0,giveIntegral:0,virtualSalesCount:0}),z=async()=>{var d,e,o,r,y;p.value=!0;try{await((d=a(D))==null?void 0:d.validate()),await((e=a(F))==null?void 0:e.validate()),await((o=a(U))==null?void 0:o.validate()),await((r=a(b))==null?void 0:r.validate()),await((y=a(C))==null?void 0:y.validate());const n=Q(a(u.value));n.skus.forEach(i=>{i.name=n.name,i.price=h(i.price),i.marketPrice=h(i.marketPrice),i.costPrice=h(i.costPrice),i.firstBrokeragePrice=h(i.firstBrokeragePrice),i.secondBrokeragePrice=h(i.secondBrokeragePrice)});const v=[];n.sliderPicUrls.forEach(i=>{typeof i=="object"?v.push(i.url):v.push(i)}),n.sliderPicUrls=v;const g=n;w.id?(await _a(g),B.success(N("common.updateSuccess"))):(await sa(g),B.success(N("common.createSuccess"))),R()}finally{p.value=!1}},R=()=>{A(a(x)),V({name:"ProductSpu"})};return O(async()=>{await(async()=>{var e;j==="ProductSpuDetail"&&(m.value=!0);const d=w.id;if(d){p.value=!0;try{const o=await ca(d);(e=o.skus)==null||e.forEach(r=>{m.value?(r.price=k(r.price),r.marketPrice=k(r.marketPrice),r.costPrice=k(r.costPrice),r.firstBrokeragePrice=k(r.firstBrokeragePrice),r.secondBrokeragePrice=k(r.secondBrokeragePrice)):(r.price=P(r.price),r.marketPrice=P(r.marketPrice),r.costPrice=P(r.costPrice),r.firstBrokeragePrice=P(r.firstBrokeragePrice),r.secondBrokeragePrice=P(r.secondBrokeragePrice))}),u.value=o}finally{p.value=!1}}})()}),(d,e)=>{const o=W,r=X,y=Y,n=Z,v=$,g=ea,i=aa;return q((S(),I(g,null,{default:s(()=>[l(r,{modelValue:a(t),"onUpdate:modelValue":e[5]||(e[5]=c=>f(t)?t.value=c:null)},{default:s(()=>[l(o,{label:"\u57FA\u7840\u8BBE\u7F6E",name:"info"},{default:s(()=>[l(ua,{ref_key:"infoRef",ref:D,activeName:a(t),"onUpdate:activeName":e[0]||(e[0]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(u)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u4EF7\u683C\u5E93\u5B58",name:"sku"},{default:s(()=>[l(va,{ref_key:"skuRef",ref:F,activeName:a(t),"onUpdate:activeName":e[1]||(e[1]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(u)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u7269\u6D41\u8BBE\u7F6E",name:"delivery"},{default:s(()=>[l(Pa,{ref_key:"deliveryRef",ref:U,activeName:a(t),"onUpdate:activeName":e[2]||(e[2]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(u)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u5546\u54C1\u8BE6\u60C5",name:"description"},{default:s(()=>[l(pa,{ref_key:"descriptionRef",ref:b,activeName:a(t),"onUpdate:activeName":e[3]||(e[3]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(u)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u5176\u5B83\u8BBE\u7F6E",name:"other"},{default:s(()=>[l(fa,{ref_key:"otherRef",ref:C,activeName:a(t),"onUpdate:activeName":e[4]||(e[4]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(u)},null,8,["activeName","is-detail","propFormData"])]),_:1})]),_:1},8,["modelValue"]),l(v,null,{default:s(()=>[l(n,{style:{float:"right"}},{default:s(()=>[a(m)?G("",!0):(S(),I(y,{key:0,loading:a(p),type:"primary",onClick:z},{default:s(()=>[E(" \u4FDD\u5B58 ")]),_:1},8,["loading"])),l(y,{onClick:R},{default:s(()=>[E("\u8FD4\u56DE")]),_:1})]),_:1})]),_:1})]),_:1})),[[i,a(p)]])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/product/spu/form/index.vue"]])});export{Ta as __tla,T as default};
