import{d as E,I as W,n as X,r as _,f as Y,u as $,bd as aa,C as ea,T as ta,o as s,c as D,i as a,w as l,a as t,U as la,F as M,k as ra,V as oa,G as x,l as d,j as c,H as m,t as na,dV as sa,Z as ia,L as ca,J as ua,K as pa,x as _a,N as da,O as ma,v as fa,P as ya,Q as ha,R as wa,_ as ga,__tla as va}from"./index-Daqg4PFz.js";import{_ as ba,__tla as Ca}from"./index-BBLwwrga.js";import{_ as xa,__tla as ka}from"./DictTag-BDZzHcIz.js";import{_ as Ua,__tla as Ta}from"./ContentWrap-DZg14iby.js";import{_ as Ra,__tla as Pa}from"./index-CmwFi8Xl.js";import{d as O,__tla as Sa}from"./formatTime-BCfRGyrF.js";import{d as Na}from"./download--D_IyRio.js";import{b as Va,d as Da,e as Ma,__tla as Oa}from"./index-DlXW_sq5.js";import za,{__tla as Fa}from"./ProductForm-CtKRL4t4.js";import{__tla as Ka}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as qa}from"./el-card-Dvjjuipo.js";import{__tla as Aa}from"./Dialog-BjBBVYCI.js";import{__tla as Ha}from"./index-B6UYj0Cp.js";import"./tree-BMqZf9_I.js";let z,Ia=Promise.all([(()=>{try{return va}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Ha}catch{}})()]).then(async()=>{z=ga(E({name:"CrmProduct",__name:"index",setup(Ja){const w=W(),{t:F}=X(),g=_(!0),k=_(0),U=_([]),r=Y({pageNo:1,pageSize:10,name:void 0,status:void 0}),T=_(),v=_(!1),u=async()=>{g.value=!0;try{const i=await Va(r);U.value=i.list,k.value=i.total}finally{g.value=!1}},b=()=>{r.pageNo=1,u()},K=()=>{T.value.resetFields(),b()},R=_(),P=(i,o)=>{R.value.open(i,o)},{currentRoute:Qa,push:q}=$(),A=async()=>{try{await w.exportConfirm(),v.value=!0;const i=await Ma(r);Na.excel(i,"\u4EA7\u54C1.xls")}catch{}finally{v.value=!1}};return aa(()=>{u()}),ea(()=>{u()}),(i,o)=>{const H=Ra,I=ia,C=ca,J=ua,Q=pa,f=_a,p=da,j=ma,S=Ua,G=fa,n=ya,N=xa,L=ha,Z=ba,y=ta("hasPermi"),B=wa;return s(),D(M,null,[a(H,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u7BA1\u7406\u3001\u4EA7\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/crm/product/"}),a(S,null,{default:l(()=>[a(j,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"68px"},{default:l(()=>[a(C,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"name"},{default:l(()=>[a(I,{modelValue:t(r).name,"onUpdate:modelValue":o[0]||(o[0]=e=>t(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u540D\u79F0",clearable:"",onKeyup:la(b,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(C,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[a(Q,{modelValue:t(r).status,"onUpdate:modelValue":o[1]||(o[1]=e=>t(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(s(!0),D(M,null,ra(t(oa)(t(x).CRM_PRODUCT_STATUS),e=>(s(),d(J,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(C,null,{default:l(()=>[a(p,{onClick:b},{default:l(()=>[a(f,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22 ")]),_:1}),a(p,{onClick:K},{default:l(()=>[a(f,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E ")]),_:1}),m((s(),d(p,{type:"primary",onClick:o[2]||(o[2]=e=>P("create"))},{default:l(()=>[a(f,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[y,["crm:product:create"]]]),m((s(),d(p,{type:"success",plain:"",onClick:A,loading:t(v)},{default:l(()=>[a(f,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["crm:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(S,null,{default:l(()=>[m((s(),d(L,{data:t(U),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(n,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"name",width:"160"},{default:l(e=>[a(G,{underline:!1,type:"primary",onClick:V=>{return h=e.row.id,void q({name:"CrmProductDetail",params:{id:h}});var h}},{default:l(()=>[c(na(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(n,{label:"\u4EA7\u54C1\u7C7B\u578B",align:"center",prop:"categoryName",width:"160"}),a(n,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unit"},{default:l(e=>[a(N,{type:t(x).CRM_PRODUCT_UNIT,value:e.row.unit},null,8,["type","value"])]),_:1}),a(n,{label:"\u4EA7\u54C1\u7F16\u7801",align:"center",prop:"no"}),a(n,{label:"\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"price",formatter:t(sa),width:"100"},null,8,["formatter"]),a(n,{label:"\u4EA7\u54C1\u63CF\u8FF0",align:"center",prop:"description",width:"150"}),a(n,{label:"\u4E0A\u67B6\u72B6\u6001",align:"center",prop:"status",width:"120"},{default:l(e=>[a(N,{type:t(x).CRM_PRODUCT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserName",width:"120"}),a(n,{label:"\u66F4\u65B0\u65F6\u95F4",align:"center",prop:"updateTime",formatter:t(O),width:"180px"},null,8,["formatter"]),a(n,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName",width:"120"}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(O),width:"180px"},null,8,["formatter"]),a(n,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"160"},{default:l(e=>[m((s(),d(p,{link:"",type:"primary",onClick:V=>P("update",e.row.id)},{default:l(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["crm:product:update"]]]),m((s(),d(p,{link:"",type:"danger",onClick:V=>(async h=>{try{await w.delConfirm(),await Da(h),w.success(F("common.delSuccess")),await u()}catch{}})(e.row.id)},{default:l(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["crm:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,t(g)]]),a(Z,{total:t(k),page:t(r).pageNo,"onUpdate:page":o[3]||(o[3]=e=>t(r).pageNo=e),limit:t(r).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>t(r).pageSize=e),onPagination:u},null,8,["total","page","limit"])]),_:1}),a(za,{ref_key:"formRef",ref:R,onSuccess:u},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/product/index.vue"]])});export{Ia as __tla,z as default};
