import{d as de,I as se,n as ie,r as n,f as ce,C as ne,ay as pe,T as _e,o as u,c as b,i as a,w as r,a as l,U as R,F as h,k,l as d,V as me,G as W,j as p,H as f,e5 as fe,dV as Z,t as ye,dX as be,Z as we,L as he,J as ve,K as ge,M as ke,x as Ve,N as xe,O as Se,P as Ce,ax as Ie,Q as Ue,R as Pe,_ as Ne,__tla as Te}from"./index-Daqg4PFz.js";import{_ as Re,__tla as Ae}from"./index-BBLwwrga.js";import{_ as De,__tla as Oe}from"./DictTag-BDZzHcIz.js";import{_ as Le,__tla as ze}from"./ContentWrap-DZg14iby.js";import{_ as Ke,__tla as Ye}from"./index-CmwFi8Xl.js";import{b as qe,__tla as Ee}from"./formatTime-BCfRGyrF.js";import{d as Fe}from"./download--D_IyRio.js";import{S as I,__tla as He}from"./index-DYY4vMWV.js";import Je,{__tla as Me}from"./SaleOutForm-Bq06Cu2Q.js";import{P as We,__tla as Ze}from"./index-BdaXniMm.js";import{C as Ge,__tla as Qe}from"./index-CzfV9k_X.js";import{W as je,__tla as Xe}from"./index-b9NHryvG.js";import{A as $e,__tla as Be}from"./index-AFe43Qgi.js";import{__tla as ea}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as aa}from"./el-card-Dvjjuipo.js";import{__tla as la}from"./Dialog-BjBBVYCI.js";import{__tla as ta}from"./SaleOutItemForm-DECbDqRc.js";import{__tla as ra}from"./index-BUJ03bwx.js";import{__tla as oa}from"./SaleOrderOutEnableList-iH87x4Id.js";import{__tla as ua}from"./index-CJBNxTRD.js";let G,da=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Xe}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})()]).then(async()=>{let A;A={key:0},G=Ne(de({name:"ErpSaleOut",__name:"index",setup(sa){const V=se(),{t:Q}=ie(),U=n(!0),D=n([]),O=n(0),o=ce({pageNo:1,pageSize:10,no:void 0,customerId:void 0,productId:void 0,warehouseId:void 0,outTime:[],orderNo:void 0,receiptStatus:void 0,accountId:void 0,status:void 0,remark:void 0,creator:void 0}),L=n(),P=n(!1),z=n([]),K=n([]),Y=n([]),q=n([]),E=n([]),v=async()=>{U.value=!0;try{const s=await I.getSaleOutPage(o);D.value=s.list,O.value=s.total}finally{U.value=!1}},x=()=>{o.pageNo=1,v()},j=()=>{L.value.resetFields(),x()},F=n(),N=(s,t)=>{F.value.open(s,t)},H=async s=>{try{await V.delConfirm(),await I.deleteSaleOut(s),V.success(Q("common.delSuccess")),await v(),S.value=S.value.filter(t=>!s.includes(t.id))}catch{}},J=async(s,t)=>{try{await V.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u51FA\u5E93\u5417\uFF1F`),await I.updateSaleOutStatus(s,t),V.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},X=async()=>{try{await V.exportConfirm(),P.value=!0;const s=await I.exportSaleOut(o);Fe.excel(s,"\u9500\u552E\u51FA\u5E93.xls")}catch{}finally{P.value=!1}},S=n([]),$=s=>{S.value=s};return ne(async()=>{await v(),z.value=await We.getProductSimpleList(),K.value=await Ge.getCustomerSimpleList(),Y.value=await pe(),q.value=await je.getWarehouseSimpleList(),E.value=await $e.getAccountSimpleList()}),(s,t)=>{const B=Ke,T=we,i=he,m=ve,w=ge,ee=ke,C=Ve,_=xe,ae=Se,M=Le,c=Ce,le=Ie,te=De,re=Ue,oe=Re,y=_e("hasPermi"),ue=Pe;return u(),b(h,null,[a(B,{title:"\u3010\u9500\u552E\u3011\u9500\u552E\u8BA2\u5355\u3001\u51FA\u5E93\u3001\u9000\u8D27",url:"https://doc.iocoder.cn/erp/sale/"}),a(M,null,{default:r(()=>[a(ae,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:L,inline:!0,"label-width":"68px"},{default:r(()=>[a(i,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:r(()=>[a(T,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5E93\u5355\u53F7",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(w,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(z),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:r(()=>[a(ee,{modelValue:l(o).outTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).outTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),a(i,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[a(w,{modelValue:l(o).customerId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).customerId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5BA2\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(K),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(w,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(q),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(w,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(Y),e=>(u(),d(m,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[a(T,{modelValue:l(o).orderNo,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).orderNo=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u8054\u8BA2\u5355",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[a(w,{modelValue:l(o).accountId,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).accountId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(E),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u6536\u6B3E\u72B6\u6001",prop:"receiptStatus"},{default:r(()=>[a(w,{modelValue:l(o).receiptStatus,"onUpdate:modelValue":t[8]||(t[8]=e=>l(o).receiptStatus=e),placeholder:"\u8BF7\u9009\u62E9\u6709\u6B3E\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[a(m,{label:"\u672A\u6536\u6B3E",value:"0"}),a(m,{label:"\u90E8\u5206\u6536\u6B3E",value:"1"}),a(m,{label:"\u5168\u90E8\u6536\u6B3E",value:"2"})]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5BA1\u6838\u72B6\u6001",prop:"status"},{default:r(()=>[a(w,{modelValue:l(o).status,"onUpdate:modelValue":t[9]||(t[9]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(me)(l(W).ERP_AUDIT_STATUS),e=>(u(),d(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(T,{modelValue:l(o).remark,"onUpdate:modelValue":t[10]||(t[10]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,null,{default:r(()=>[a(_,{onClick:x},{default:r(()=>[a(C,{icon:"ep:search",class:"mr-5px"}),p(" \u641C\u7D22")]),_:1}),a(_,{onClick:j},{default:r(()=>[a(C,{icon:"ep:refresh",class:"mr-5px"}),p(" \u91CD\u7F6E")]),_:1}),f((u(),d(_,{type:"primary",plain:"",onClick:t[11]||(t[11]=e=>N("create"))},{default:r(()=>[a(C,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u589E ")]),_:1})),[[y,["erp:sale-out:create"]]]),f((u(),d(_,{type:"success",plain:"",onClick:X,loading:l(P)},{default:r(()=>[a(C,{icon:"ep:download",class:"mr-5px"}),p(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:sale-out:export"]]]),f((u(),d(_,{type:"danger",plain:"",onClick:t[12]||(t[12]=e=>H(l(S).map(g=>g.id))),disabled:l(S).length===0},{default:r(()=>[a(C,{icon:"ep:delete",class:"mr-5px"}),p(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[y,["erp:sale-out:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(M,null,{default:r(()=>[f((u(),d(re,{data:l(D),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:$},{default:r(()=>[a(c,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(c,{"min-width":"180",label:"\u51FA\u5E93\u5355\u53F7",align:"center",prop:"no"}),a(c,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(c,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),a(c,{label:"\u51FA\u5E93\u65F6\u95F4",align:"center",prop:"outTime",formatter:l(qe),width:"120px"},null,8,["formatter"]),a(c,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(c,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(fe)},null,8,["formatter"]),a(c,{label:"\u5E94\u6536\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(Z)},null,8,["formatter"]),a(c,{label:"\u5DF2\u6536\u91D1\u989D",align:"center",prop:"receiptPrice",formatter:l(Z)},null,8,["formatter"]),a(c,{label:"\u672A\u6536\u91D1\u989D",align:"center"},{default:r(e=>[e.row.receiptPrice===e.row.totalPrice?(u(),b("span",A,"0")):(u(),d(le,{key:1,type:"danger"},{default:r(()=>[p(ye(l(be)(e.row.totalPrice-e.row.receiptPrice)),1)]),_:2},1024))]),_:1}),a(c,{label:"\u5BA1\u6838\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(te,{type:l(W).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[f((u(),d(_,{link:"",onClick:g=>N("detail",e.row.id)},{default:r(()=>[p(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:query"]]]),f((u(),d(_,{link:"",type:"primary",onClick:g=>N("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[y,["erp:sale-out:update"]]]),e.row.status===10?f((u(),d(_,{key:0,link:"",type:"primary",onClick:g=>J(e.row.id,20)},{default:r(()=>[p(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:update-status"]]]):f((u(),d(_,{key:1,link:"",type:"danger",onClick:g=>J(e.row.id,10)},{default:r(()=>[p(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:update-status"]]]),f((u(),d(_,{link:"",type:"danger",onClick:g=>H([e.row.id])},{default:r(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:delete"]]])]),_:1})]),_:1},8,["data"])),[[ue,l(U)]]),a(oe,{total:l(O),page:l(o).pageNo,"onUpdate:page":t[13]||(t[13]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[14]||(t[14]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(Je,{ref_key:"formRef",ref:F,onSuccess:v},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/sale/out/index.vue"]])});export{da as __tla,G as default};
