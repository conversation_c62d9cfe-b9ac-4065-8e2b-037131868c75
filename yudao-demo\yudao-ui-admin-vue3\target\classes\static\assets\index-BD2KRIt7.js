import{d as g,b,o as a,c as o,F as f,k as v,av as s,i as c,w as R,g as u,a as w,x as $,_ as B,__tla as T}from"./index-Daqg4PFz.js";import{E as M,__tla as j}from"./el-image-Bn34T02c.js";let n,k=Promise.all([(()=>{try{return T}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{let p,e,i;p={class:"image-slot"},e=93.75,i=g({name:"MagicCube",__name:"index",props:{property:{type:Object,required:!0}},setup(y){const l=y,h=b(()=>{let t=0;return l.property.list.length>0&&(t=Math.max(...l.property.list.map(d=>d.bottom))),t+1});return(t,d)=>{const m=$,x=M;return a(),o("div",{class:"relative",style:s({height:w(h)*e+"px",width:"375px"})},[(a(!0),o(f,null,v(t.property.list,(r,_)=>(a(),o("div",{key:_,class:"absolute",style:s({width:r.width*e-2*t.property.space+"px",height:r.height*e-2*t.property.space+"px",margin:`${t.property.space}px`,top:r.top*e+"px",left:r.left*e+"px"})},[c(x,{class:"h-full w-full",fit:"cover",src:r.imgUrl,style:s({borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`})},{error:R(()=>[u("div",p,[u("div",{class:"flex items-center justify-center",style:s({width:r.width*e+"px",height:r.height*e+"px"})},[c(m,{icon:"ep-picture",color:"gray",size:e})],4)])]),_:2},1032,["src","style"])],4))),128))],4)}}}),n=B(i,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/MagicCube/index.vue"]])});export{k as __tla,n as default};
