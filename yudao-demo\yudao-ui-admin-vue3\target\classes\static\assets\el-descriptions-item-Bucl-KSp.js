import{d as S,b7 as B,cS as al,br as F,bg as H,H as C,h as x,b_ as rl,be as j,bf as nl,a as s,o as a,c as y,g as v,F as g,k as E,l as A,i as O,bh as R,bP as il,c1 as ol,cI as cl,cd as dl,b as pl,a0 as w,aW as T,j as q,t as G,a9 as ul,cT as bl,bi as yl,bl as hl,__tla as vl}from"./index-Daqg4PFz.js";let J,K,gl=Promise.all([(()=>{try{return vl}catch{}})()]).then(async()=>{const I=Symbol("elDescriptions");var _=S({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:B(I,{})}),render(){var k,o,t,$,r,c,N;const i=al(this.cell),l=(((k=this.cell)==null?void 0:k.dirs)||[]).map(Z=>{const{dir:ll,arg:tl,modifiers:el,value:sl}=Z;return[ll,sl,tl,el]}),{border:p,direction:n}=this.descriptions,e=n==="vertical",m=(($=(t=(o=this.cell)==null?void 0:o.children)==null?void 0:t.label)==null?void 0:$.call(t))||i.label,u=(N=(c=(r=this.cell)==null?void 0:r.children)==null?void 0:c.default)==null?void 0:N.call(c),b=i.span,f=i.align?`is-${i.align}`:"",h=i.labelAlign?`is-${i.labelAlign}`:f,D=i.className,P=i.labelClassName,z={width:F(i.width),minWidth:F(i.minWidth)},d=H("descriptions");switch(this.type){case"label":return C(x(this.tag,{style:z,class:[d.e("cell"),d.e("label"),d.is("bordered-label",p),d.is("vertical-label",e),h,P],colSpan:e?b:1},m),l);case"content":return C(x(this.tag,{style:z,class:[d.e("cell"),d.e("content"),d.is("bordered-content",p),d.is("vertical-content",e),f,D],colSpan:e?b:2*b-1},u),l);default:return C(x("td",{style:z,class:[d.e("cell"),f],colSpan:b},[rl(m)?void 0:x("span",{class:[d.e("label"),P]},m),x("span",{class:[d.e("content"),D]},u)]),l)}}});const L=j({row:{type:nl(Array),default:()=>[]}}),M={key:1},Q=S({name:"ElDescriptionsRow"});var U=R(S({...Q,props:L,setup(k){const o=B(I,{});return(t,$)=>s(o).direction==="vertical"?(a(),y(g,{key:0},[v("tr",null,[(a(!0),y(g,null,E(t.row,(r,c)=>(a(),A(s(_),{key:`tr1-${c}`,cell:r,tag:"th",type:"label"},null,8,["cell"]))),128))]),v("tr",null,[(a(!0),y(g,null,E(t.row,(r,c)=>(a(),A(s(_),{key:`tr2-${c}`,cell:r,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(a(),y("tr",M,[(a(!0),y(g,null,E(t.row,(r,c)=>(a(),y(g,{key:`tr3-${c}`},[s(o).border?(a(),y(g,{key:0},[O(s(_),{cell:r,tag:"td",type:"label"},null,8,["cell"]),O(s(_),{cell:r,tag:"td",type:"content"},null,8,["cell"])],64)):(a(),A(s(_),{key:1,cell:r,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}}),[["__file","descriptions-row.vue"]]);const V=j({border:{type:Boolean,default:!1},column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:il,title:{type:String,default:""},extra:{type:String,default:""}}),X=S({name:"ElDescriptions"});var Y=R(S({...X,props:V,setup(k){const o=k,t=H("descriptions"),$=ol(),r=cl();dl(I,o);const c=pl(()=>[t.b(),t.m($.value)]),N=(l,p,n,e=!1)=>(l.props||(l.props={}),p>n&&(l.props.span=n),e&&(l.props.span=p),l),i=()=>{if(!r.default)return[];const l=bl(r.default()).filter(u=>{var b;return((b=u==null?void 0:u.type)==null?void 0:b.name)==="ElDescriptionsItem"}),p=[];let n=[],e=o.column,m=0;return l.forEach((u,b)=>{var f;const h=((f=u.props)==null?void 0:f.span)||1;if(b<l.length-1&&(m+=h>e?e:h),b===l.length-1){const D=o.column-m%o.column;return n.push(N(u,D,e,!0)),void p.push(n)}h<e?(e-=h,n.push(u)):(n.push(N(u,h,e)),p.push(n),e=o.column,n=[])}),p};return(l,p)=>(a(),y("div",{class:w(s(c))},[l.title||l.extra||l.$slots.title||l.$slots.extra?(a(),y("div",{key:0,class:w(s(t).e("header"))},[v("div",{class:w(s(t).e("title"))},[T(l.$slots,"title",{},()=>[q(G(l.title),1)])],2),v("div",{class:w(s(t).e("extra"))},[T(l.$slots,"extra",{},()=>[q(G(l.extra),1)])],2)],2)):ul("v-if",!0),v("div",{class:w(s(t).e("body"))},[v("table",{class:w([s(t).e("table"),s(t).is("bordered",l.border)])},[v("tbody",null,[(a(!0),y(g,null,E(i(),(n,e)=>(a(),A(U,{key:e,row:n},null,8,["row"]))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);let W;W=S({name:"ElDescriptionsItem",props:j({label:{type:String,default:""},span:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},align:{type:String,default:"left"},labelAlign:{type:String,default:""},className:{type:String,default:""},labelClassName:{type:String,default:""}})}),K=yl(Y,{DescriptionsItem:W}),J=hl(W)});export{J as E,gl as __tla,K as a};
