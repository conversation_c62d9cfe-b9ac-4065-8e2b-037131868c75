import{d as z,n as H,I as N,r as m,f as D,b as J,o as V,l as g,w as n,i as l,a as t,j as p,H as L,z as b,Z as O,L as S,ch as Z,am as A,an as B,O as E,N as G,R as K,_ as M,__tla as Q}from"./index-Daqg4PFz.js";import{_ as W,__tla as X}from"./Dialog-BjBBVYCI.js";import{g as Y,b as $,__tla as ee}from"./index-CoiMdO4H.js";let P,ae=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return ee}catch{}})()]).then(async()=>{P=M(z({name:"UpdatePointForm",__name:"UserPointUpdateForm",emits:["success"],setup(le,{expose:U,emit:w}){const{t:k}=H(),_=N(),u=m(!1),s=m(!1),e=m({id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1}),x=D({changePoint:[{required:!0,message:"\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),r=m();U({open:async d=>{if(u.value=!0,C(),d){s.value=!0;try{e.value=await Y(d),e.value.changeType=1,e.value.changePoint=0}finally{s.value=!1}}}});const T=w,F=async()=>{if(r&&await r.value.validate())if(e.value.changePoint<1)_.error("\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 1");else if(c.value<0)_.error("\u53D8\u52A8\u540E\u7684\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 0");else{s.value=!0;try{await $({id:e.value.id,point:e.value.changePoint*e.value.changeType}),_.success(k("common.updateSuccess")),u.value=!1,T("success")}finally{s.value=!1}}},C=()=>{var d;e.value={id:void 0,nickname:void 0,levelId:void 0,reason:void 0},(d=r.value)==null||d.resetFields()},c=J(()=>e.value.point+e.value.changePoint*e.value.changeType);return(d,a)=>{const f=O,i=S,v=Z,h=A,I=B,R=E,y=G,j=W,q=K;return V(),g(j,{title:"\u4FEE\u6539\u7528\u6237\u79EF\u5206",modelValue:t(u),"onUpdate:modelValue":a[7]||(a[7]=o=>b(u)?u.value=o:null),width:"600"},{footer:n(()=>[l(y,{onClick:F,type:"primary",disabled:t(s)},{default:n(()=>[p("\u786E \u5B9A")]),_:1},8,["disabled"]),l(y,{onClick:a[6]||(a[6]=o=>u.value=!1)},{default:n(()=>[p("\u53D6 \u6D88")]),_:1})]),default:n(()=>[L((V(),g(R,{ref_key:"formRef",ref:r,model:t(e),rules:t(x),"label-width":"100px"},{default:n(()=>[l(i,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:n(()=>[l(f,{modelValue:t(e).id,"onUpdate:modelValue":a[0]||(a[0]=o=>t(e).id=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:n(()=>[l(f,{modelValue:t(e).nickname,"onUpdate:modelValue":a[1]||(a[1]=o=>t(e).nickname=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u524D\u79EF\u5206",prop:"point"},{default:n(()=>[l(v,{modelValue:t(e).point,"onUpdate:modelValue":a[2]||(a[2]=o=>t(e).point=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:n(()=>[l(I,{modelValue:t(e).changeType,"onUpdate:modelValue":a[3]||(a[3]=o=>t(e).changeType=o)},{default:n(()=>[l(h,{label:1},{default:n(()=>[p("\u589E\u52A0")]),_:1}),l(h,{label:-1},{default:n(()=>[p("\u51CF\u5C11")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u79EF\u5206",prop:"changePoint"},{default:n(()=>[l(v,{modelValue:t(e).changePoint,"onUpdate:modelValue":a[4]||(a[4]=o=>t(e).changePoint=o),class:"!w-240px",min:0,precision:0},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u540E\u79EF\u5206"},{default:n(()=>[l(v,{modelValue:t(c),"onUpdate:modelValue":a[5]||(a[5]=o=>b(c)?c.value=o:null),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[q,t(s)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/member/user/UserPointUpdateForm.vue"]])});export{ae as __tla,P as default};
