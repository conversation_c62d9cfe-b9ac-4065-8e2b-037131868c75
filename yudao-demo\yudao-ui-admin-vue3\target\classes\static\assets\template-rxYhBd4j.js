import{bE as a,__tla as m}from"./index-Daqg4PFz.js";let e,o,i,p,r,l,s,d,y=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{p=async t=>await a.get({url:"/promotion/diy-template/page",params:t}),e=async t=>await a.get({url:"/promotion/diy-template/get?id="+t}),i=async t=>await a.post({url:"/promotion/diy-template/create",data:t}),o=async t=>await a.put({url:"/promotion/diy-template/update",data:t}),r=async t=>await a.delete({url:"/promotion/diy-template/delete?id="+t}),l=async t=>await a.put({url:"/promotion/diy-template/use?id="+t}),s=async t=>await a.get({url:"/promotion/diy-template/get-property?id="+t}),d=async t=>await a.put({url:"/promotion/diy-template/update-property",data:t})});export{y as __tla,e as a,o as b,i as c,p as d,r as e,l as f,s as g,d as u};
