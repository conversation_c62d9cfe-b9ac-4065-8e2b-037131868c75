import{n as t,__tla as n}from"./index-Daqg4PFz.js";let e,s,i,l=Promise.all([(()=>{try{return n}catch{}})()]).then(async()=>{let a;({t:a}=t()),s={title:{text:a("analysis.monthlySales"),left:"center"},xAxis:{data:[a("analysis.january"),a("analysis.february"),a("analysis.march"),a("analysis.april"),a("analysis.may"),a("analysis.june"),a("analysis.july"),a("analysis.august"),a("analysis.september"),a("analysis.october"),a("analysis.november"),a("analysis.december")],boundaryGap:!1,axisTick:{show:!1}},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},yAxis:{axisTick:{show:!1}},legend:{data:[a("analysis.estimate"),a("analysis.actual")],top:50},series:[{name:a("analysis.estimate"),smooth:!0,type:"line",data:[100,120,161,134,105,160,165,114,163,185,118,123],animationDuration:2800,animationEasing:"cubicInOut"},{name:a("analysis.actual"),smooth:!0,type:"line",itemStyle:{},data:[120,82,91,154,162,140,145,250,134,56,99,123],animationDuration:2800,animationEasing:"quadraticOut"}]},i={title:{text:a("analysis.userAccessSource"),left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:[a("analysis.directAccess"),a("analysis.mailMarketing"),a("analysis.allianceAdvertising"),a("analysis.videoAdvertising"),a("analysis.searchEngines")]},series:[{name:a("analysis.userAccessSource"),type:"pie",radius:"55%",center:["50%","60%"],data:[{value:335,name:a("analysis.directAccess")},{value:310,name:a("analysis.mailMarketing")},{value:234,name:a("analysis.allianceAdvertising")},{value:135,name:a("analysis.videoAdvertising")},{value:1548,name:a("analysis.searchEngines")}]}]},e={title:{text:a("analysis.weeklyUserActivity"),left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:50,right:20,bottom:20},xAxis:{type:"category",data:[a("analysis.monday"),a("analysis.tuesday"),a("analysis.wednesday"),a("analysis.thursday"),a("analysis.friday"),a("analysis.saturday"),a("analysis.sunday")],axisTick:{alignWithLabel:!0}},yAxis:{type:"value"},series:[{name:a("analysis.activeQuantity"),data:[13253,34235,26321,12340,24643,1322,1324],type:"bar"}]},a("workplace.personal"),a("workplace.team"),a("workplace.quote"),a("workplace.contribution"),a("workplace.hot"),a("workplace.yield"),a("workplace.follow"),a("workplace.index"),a("workplace.personal"),a("workplace.team")});export{l as __tla,e as b,s as l,i as p};
