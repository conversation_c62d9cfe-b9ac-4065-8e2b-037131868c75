const i=t=>/^#([0-9a-fA-F]{3}|[0-9a-fA-f]{6})$/.test(t),a=(t,n)=>{let s=t.toLowerCase();if(i(t)){if(s.length===4){let r="#";for(let f=1;f<4;f+=1)r+=s.slice(f,f+1).concat(s.slice(f,f+1));s=r}const e=[];for(let r=1;r<7;r+=2)e.push(parseInt("0x"+s.slice(r,r+2)));return n?"RGBA("+e.join(",")+","+n+")":"RGB("+e.join(",")+")"}return s},o=t=>{if(!i(t))return;const[n,s,e]=a(t).replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map(r=>Number(r));return .299*n+.578*s+.114*e<192},l=(t,n)=>(t=t.indexOf("#")>=0?t.substring(1,t.length):t,n=Math.trunc(255*n/100),`#${c(t.substring(0,2),n)}${c(t.substring(2,4),n)}${c(t.substring(4,6),n)}`),c=(t,n)=>{const s=parseInt(t,16)+n,e=s>255?255:s;return e.toString(16).length>1?e.toString(16):`0${e.toString(16)}`},g=["#ff4500","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","#409EFF","#909399","#C0C4CC","#b7390b","#ff7800","#fad400","#5b8c5f","#00babd","#1f73c3","#711f57"];export{g as P,o as c,a as h,i,l};
