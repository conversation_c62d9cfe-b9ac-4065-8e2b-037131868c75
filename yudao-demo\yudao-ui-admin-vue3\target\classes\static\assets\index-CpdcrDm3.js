import{d as o,o as t,c as l,l as p,av as y,_ as i,__tla as c}from"./index-Daqg4PFz.js";import{E as u,__tla as n}from"./el-image-Bn34T02c.js";let a,_=Promise.all([(()=>{try{return c}catch{}})(),(()=>{try{return n}catch{}})()]).then(async()=>{let e;e=["src","poster","autoplay"],a=i(o({name:"VideoPlayer",__name:"index",props:{property:{type:Object,required:!0}},setup:d=>(r,f)=>{const s=u;return t(),l("div",{class:"w-full",style:y({height:`${r.property.style.height}px`})},[r.property.posterUrl?(t(),p(s,{key:0,class:"w-full w-full",src:r.property.posterUrl},null,8,["src"])):(t(),l("video",{key:1,class:"w-full w-full",src:r.property.videoUrl,poster:r.property.posterUrl,autoplay:r.property.autoplay,controls:""},null,8,e))],4)}}),[["__scopeId","data-v-ff95d3cd"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/VideoPlayer/index.vue"]])});export{_ as __tla,a as default};
