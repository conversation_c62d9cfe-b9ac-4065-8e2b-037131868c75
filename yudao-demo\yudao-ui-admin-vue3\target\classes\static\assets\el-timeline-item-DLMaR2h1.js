import{d as m,bg as u,cd as w,h as T,aW as c,be as S,bp as $,b as B,o as l,c as o,g as p,a0 as s,a,av as E,l as y,w as x,b1 as z,bs as I,a9 as n,t as f,bh as C,bi as P,bl as W,__tla as j}from"./index-Daqg4PFz.js";let v,b,q=Promise.all([(()=>{try{return j}catch{}})()]).then(async()=>{const g=m({name:"ElTimeline",setup(d,{slots:i}){const e=u("timeline");return w("timeline",i),()=>T("ul",{class:[e.b()]},[c(i,"default")])}}),h=S({timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},center:{type:Boolean,default:!1},placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:$},hollow:{type:Boolean,default:!1}}),_=m({name:"ElTimelineItem"});var r=C(m({..._,props:h,setup(d){const i=d,e=u("timeline-item"),k=B(()=>[e.e("node"),e.em("node",i.size||""),e.em("node",i.type||""),e.is("hollow",i.hollow)]);return(t,A)=>(l(),o("li",{class:s([a(e).b(),{[a(e).e("center")]:t.center}])},[p("div",{class:s(a(e).e("tail"))},null,2),t.$slots.dot?n("v-if",!0):(l(),o("div",{key:0,class:s(a(k)),style:E({backgroundColor:t.color})},[t.icon?(l(),y(a(I),{key:0,class:s(a(e).e("icon"))},{default:x(()=>[(l(),y(z(t.icon)))]),_:1},8,["class"])):n("v-if",!0)],6)),t.$slots.dot?(l(),o("div",{key:1,class:s(a(e).e("dot"))},[c(t.$slots,"dot")],2)):n("v-if",!0),p("div",{class:s(a(e).e("wrapper"))},[t.hideTimestamp||t.placement!=="top"?n("v-if",!0):(l(),o("div",{key:0,class:s([a(e).e("timestamp"),a(e).is("top")])},f(t.timestamp),3)),p("div",{class:s(a(e).e("content"))},[c(t.$slots,"default")],2),t.hideTimestamp||t.placement!=="bottom"?n("v-if",!0):(l(),o("div",{key:1,class:s([a(e).e("timestamp"),a(e).is("bottom")])},f(t.timestamp),3))],2)],2))}}),[["__file","timeline-item.vue"]]);b=P(g,{TimelineItem:r}),v=W(r)});export{v as E,q as __tla,b as a};
