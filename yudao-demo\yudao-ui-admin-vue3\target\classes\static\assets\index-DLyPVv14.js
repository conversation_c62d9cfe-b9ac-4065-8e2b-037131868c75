import{d,o as t,c as a,F as f,k as y,g as s,l as v,a9 as h,av as c,t as n,i as b,x as w,_ as g,__tla as j}from"./index-Daqg4PFz.js";import{E as k,__tla as U}from"./el-image-Bn34T02c.js";let p,C=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let l,r,i,o;l={class:"min-h-42px flex flex-col"},r={class:"flex flex-1 flex-row items-center gap-8px"},i={class:"item-center flex flex-row justify-center gap-4px"},o=d({name:"MenuList",__name:"index",props:{property:{type:Object,required:!0}},setup:E=>(x,L)=>{const u=k,_=w;return t(),a("div",l,[(t(!0),a(f,null,y(x.property.list,(e,m)=>(t(),a("div",{key:m,class:"item h-42px flex flex-row items-center justify-between gap-4px p-x-12px"},[s("div",r,[e.iconUrl?(t(),v(u,{key:0,class:"h-16px w-16px",src:e.iconUrl},null,8,["src"])):h("",!0),s("span",{class:"text-16px",style:c({color:e.titleColor})},n(e.title),5)]),s("div",i,[s("span",{class:"text-12px",style:c({color:e.subtitleColor})},n(e.subtitle),5),b(_,{icon:"ep-arrow-right",color:"#000",size:16})])]))),128))])}}),p=g(o,[["__scopeId","data-v-0cbdca86"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/MenuList/index.vue"]])});export{C as __tla,p as default};
