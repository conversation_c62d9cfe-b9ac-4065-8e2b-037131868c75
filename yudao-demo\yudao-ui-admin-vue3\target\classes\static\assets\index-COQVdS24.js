import{d as ue,I as de,n as fe,u as ye,r as p,f as he,C as we,au as ke,T as ge,o as i,c as N,i as a,w as t,a as l,U as $,F as A,k as be,l as n,j as _,H as f,g as q,t as b,a9 as G,z as F,ao as ve,Z as Ce,L as Ve,J as xe,K as De,x as Se,N as Ue,O as Ne,P as Te,ax as Pe,cj as qe,Q as Fe,R as je,_ as ze,__tla as Be}from"./index-Daqg4PFz.js";import{_ as Ie,__tla as Me}from"./Dialog-BjBBVYCI.js";import{_ as Re,__tla as Ke}from"./index-BBLwwrga.js";import{E as Ee,__tla as Je}from"./el-image-Bn34T02c.js";import{_ as <PERSON>,__tla as Oe}from"./ContentWrap-DZg14iby.js";import{_ as <PERSON>,__tla as Qe}from"./index-CmwFi8Xl.js";import{d as We,f as Xe,__tla as Ze}from"./formatTime-BCfRGyrF.js";import{j as $e,__tla as Ae}from"./bpmn-embedded-CyKj3vrC.js";import{a as Ge,d as Ye,b as ea,e as aa,g as ta,__tla as la}from"./index-G-g1aF6n.js";import{g as ra,__tla as oa}from"./index-CSvGj0-b.js";import ia,{__tla as na}from"./ModelForm-DJiWOUXr.js";import sa,{__tla as ca}from"./ModelImportForm-CbS8nKkq.js";import{b as pa,__tla as _a}from"./formCreate-Cp7STxiP.js";import{C as ma,__tla as ua}from"./index-B4Qi4fi2.js";import{__tla as da}from"./index-CS70nJJ8.js";import{__tla as fa}from"./el-card-Dvjjuipo.js";import{__tla as ya}from"./XTextButton-BJLSHFzo.js";import{__tla as ha}from"./XButton-CfHP8l0l.js";import{__tla as wa}from"./el-collapse-item-CUcELNOM.js";import{__tla as ka}from"./el-tree-select-BKcJcOKx.js";import"./tree-BMqZf9_I.js";import{__tla as ga}from"./index-BCA1igdc.js";import{__tla as ba}from"./index-D-Abj-9W.js";import{__tla as va}from"./index-Co1vaaHn.js";import{__tla as Ca}from"./index-BjA_Ugbr.js";import"./constants-WoCEnNvc.js";import{__tla as Va}from"./index-Ch7bD5NQ.js";import{__tla as xa}from"./el-drawer-cP-FViL4.js";import{__tla as Da}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as Sa}from"./index-SjM4AotX.js";let Y,Ua=Promise.all([(()=>{try{return Be}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Sa}catch{}})()]).then(async()=>{let j,z;j={key:2},z={key:0},Y=ze(ue({name:"BpmModel",__name:"index",setup(Na){const w=de(),{t:B}=fe(),{push:v}=ye(),T=p(!0),I=p(0),M=p([]),s=he({pageNo:1,pageSize:10,key:void 0,name:void 0,category:void 0}),R=p(),K=p([]),y=async()=>{T.value=!0;try{const d=await Ge(s);M.value=d.list,I.value=d.total}finally{T.value=!1}},C=()=>{s.pageNo=1,y()},ee=()=>{R.value.resetFields(),C()},E=p(),J=(d,r)=>{E.value.open(d,r)},L=p(),ae=()=>{L.value.open()},V=p(!1),P=p({rule:[],option:{}}),O=async d=>{if(d.formType==10){const r=await ra(d.formId);pa(P,r.conf,r.fields),V.value=!0}else await v({path:d.formCustomCreatePath})},x=p(!1),k=p(null),H=p({prefix:"flowable"});return we(async()=>{await y(),K.value=await ma.getCategorySimpleList()}),(d,r)=>{const D=He,Q=Ce,S=Ve,te=xe,le=De,U=Se,c=Ue,re=Ne,W=Le,m=Te,oe=Ee,X=Pe,ie=qe,ne=Fe,se=Re,ce=ke("form-create"),Z=Ie,h=ge("hasPermi"),pe=je;return i(),N(A,null,[a(D,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08BPMN\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-dingding/"}),a(D,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08\u9489\u9489\u3001\u98DE\u4E66\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-bpmn/"}),a(D,{title:"\u9009\u62E9\u5BA1\u6279\u4EBA\u3001\u53D1\u8D77\u4EBA\u81EA\u9009",url:"https://doc.iocoder.cn/bpm/assignee/"}),a(D,{title:"\u4F1A\u7B7E\u3001\u6216\u7B7E\u3001\u4F9D\u6B21\u5BA1\u6279",url:"https://doc.iocoder.cn/bpm/multi-instance/"}),a(W,null,{default:t(()=>[a(re,{class:"-mb-15px",model:l(s),ref_key:"queryFormRef",ref:R,inline:!0,"label-width":"68px"},{default:t(()=>[a(S,{label:"\u6D41\u7A0B\u6807\u8BC6",prop:"key"},{default:t(()=>[a(Q,{modelValue:l(s).key,"onUpdate:modelValue":r[0]||(r[0]=e=>l(s).key=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u6807\u8BC6",clearable:"",onKeyup:$(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(S,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(Q,{modelValue:l(s).name,"onUpdate:modelValue":r[1]||(r[1]=e=>l(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:$(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(S,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[a(le,{modelValue:l(s).category,"onUpdate:modelValue":r[2]||(r[2]=e=>l(s).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(i(!0),N(A,null,be(l(K),e=>(i(),n(te,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(S,null,{default:t(()=>[a(c,{onClick:C},{default:t(()=>[a(U,{icon:"ep:search",class:"mr-5px"}),_(" \u641C\u7D22")]),_:1}),a(c,{onClick:ee},{default:t(()=>[a(U,{icon:"ep:refresh",class:"mr-5px"}),_(" \u91CD\u7F6E")]),_:1}),f((i(),n(c,{type:"primary",plain:"",onClick:r[3]||(r[3]=e=>J("create"))},{default:t(()=>[a(U,{icon:"ep:plus",class:"mr-5px"}),_(" \u65B0\u5EFA\u6D41\u7A0B ")]),_:1})),[[h,["bpm:model:create"]]]),f((i(),n(c,{type:"success",plain:"",onClick:ae},{default:t(()=>[a(U,{icon:"ep:upload",class:"mr-5px"}),_(" \u5BFC\u5165\u6D41\u7A0B ")]),_:1})),[[h,["bpm:model:import"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(W,null,{default:t(()=>[f((i(),n(ne,{data:l(M)},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u6807\u8BC6",align:"center",prop:"key",width:"200"}),a(m,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name",width:"200"},{default:t(e=>[a(c,{type:"primary",link:"",onClick:u=>(async o=>{const g=await ta(o.id);k.value=g.bpmnXml||"",x.value=!0})(e.row)},{default:t(()=>[q("span",null,b(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u56FE\u6807",align:"center",prop:"icon",width:"100"},{default:t(e=>[a(oe,{src:e.row.icon,class:"w-32px h-32px"},null,8,["src"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName",width:"100"}),a(m,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType",width:"200"},{default:t(e=>[e.row.formType===10?(i(),n(c,{key:0,type:"primary",link:"",onClick:u=>O(e.row)},{default:t(()=>[q("span",null,b(e.row.formName),1)]),_:2},1032,["onClick"])):e.row.formType===20?(i(),n(c,{key:1,type:"primary",link:"",onClick:u=>O(e.row)},{default:t(()=>[q("span",null,b(e.row.formCustomCreatePath),1)]),_:2},1032,["onClick"])):(i(),N("label",j,"\u6682\u65E0\u8868\u5355"))]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:l(We)},null,8,["formatter"]),a(m,{label:"\u6700\u65B0\u90E8\u7F72\u7684\u6D41\u7A0B\u5B9A\u4E49",align:"center"},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"processDefinition.version",width:"100"},{default:t(e=>[e.row.processDefinition?(i(),n(X,{key:0},{default:t(()=>[_(" v"+b(e.row.processDefinition.version),1)]),_:2},1024)):(i(),n(X,{key:1,type:"warning"},{default:t(()=>[_("\u672A\u90E8\u7F72")]),_:1}))]),_:1}),a(m,{label:"\u6FC0\u6D3B\u72B6\u6001",align:"center",prop:"processDefinition.version",width:"85"},{default:t(e=>[e.row.processDefinition?(i(),n(ie,{key:0,modelValue:e.row.processDefinition.suspensionState,"onUpdate:modelValue":u=>e.row.processDefinition.suspensionState=u,"active-value":1,"inactive-value":2,onChange:u=>(async o=>{const g=o.processDefinition.suspensionState;try{const _e=o.id,me="\u662F\u5426\u786E\u8BA4"+(g===1?"\u6FC0\u6D3B":"\u6302\u8D77")+'\u6D41\u7A0B\u540D\u5B57\u4E3A"'+o.name+'"\u7684\u6570\u636E\u9879?';await w.confirm(me),await ea(_e,g),await y()}catch{o.processDefinition.suspensionState=g===1?2:1}})(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])):G("",!0)]),_:1}),a(m,{label:"\u90E8\u7F72\u65F6\u95F4",align:"center",prop:"deploymentTime",width:"180"},{default:t(e=>[e.row.processDefinition?(i(),N("span",z,b(l(Xe)(e.row.processDefinition.deploymentTime)),1)):G("",!0)]),_:1})]),_:1}),a(m,{label:"\u64CD\u4F5C",align:"center",width:"240",fixed:"right"},{default:t(e=>[f((i(),n(c,{link:"",type:"primary",onClick:u=>J("update",e.row.id)},{default:t(()=>[_(" \u4FEE\u6539\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row,void v({name:"BpmModelEditor",query:{modelId:o.id}});var o}},{default:t(()=>[_(" \u8BBE\u8BA1\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row.id,void v({name:"SimpleWorkflowDesignEditor",query:{modelId:o.id}});var o}},{default:t(()=>[_(" \u4EFF\u9489\u9489\u8BBE\u8BA1\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>(async o=>{try{await w.confirm("\u662F\u5426\u90E8\u7F72\u8BE5\u6D41\u7A0B\uFF01\uFF01"),await aa(o.id),w.success(B("\u90E8\u7F72\u6210\u529F")),await y()}catch{}})(e.row)},{default:t(()=>[_(" \u53D1\u5E03\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:deploy"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row,void v({name:"BpmProcessDefinition",query:{key:o.key}});var o}},{default:t(()=>[_(" \u6D41\u7A0B\u5B9A\u4E49 ")]),_:2},1032,["onClick"])),[[h,["bpm:process-definition:query"]]]),f((i(),n(c,{link:"",type:"danger",onClick:u=>(async o=>{try{await w.delConfirm(),await Ye(o),w.success(B("common.delSuccess")),await y()}catch{}})(e.row.id)},{default:t(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["bpm:model:delete"]]])]),_:1})]),_:1},8,["data"])),[[pe,l(T)]]),a(se,{total:l(I),page:l(s).pageNo,"onUpdate:page":r[4]||(r[4]=e=>l(s).pageNo=e),limit:l(s).pageSize,"onUpdate:limit":r[5]||(r[5]=e=>l(s).pageSize=e),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(ia,{ref_key:"formRef",ref:E,onSuccess:y},null,512),a(sa,{ref_key:"importFormRef",ref:L,onSuccess:y},null,512),a(Z,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:l(V),"onUpdate:modelValue":r[6]||(r[6]=e=>F(V)?V.value=e:null),width:"800"},{default:t(()=>[a(ce,{rule:l(P).rule,option:l(P).option},null,8,["rule","option"])]),_:1},8,["modelValue"]),a(Z,{title:"\u6D41\u7A0B\u56FE",modelValue:l(x),"onUpdate:modelValue":r[8]||(r[8]=e=>F(x)?x.value=e:null),width:"800"},{default:t(()=>[a(l($e),ve({key:"designer",modelValue:l(k),"onUpdate:modelValue":r[7]||(r[7]=e=>F(k)?k.value=e:null),value:l(k)},l(H),{prefix:l(H).prefix}),null,16,["modelValue","value","prefix"])]),_:1},8,["modelValue"])],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/model/index.vue"]])});export{Ua as __tla,Y as default};
