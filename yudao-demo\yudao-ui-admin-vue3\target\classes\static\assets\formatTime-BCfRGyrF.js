import{cx as a,__tla as y}from"./index-Daqg4PFz.js";let f,i,l,D,m,o,Y,g,d,M,w,u,h,T,x,b=Promise.all([(()=>{try{return y}catch{}})()]).then(async()=>{Y=[{text:"\u4ECA\u5929",value:()=>new Date},{text:"\u6628\u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-864e5),[t,t]}},{text:"\u6700\u8FD1\u4E03\u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-6048e5),[t,new Date]}},{text:"\u6700\u8FD1 30 \u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-2592e6),[t,new Date]}},{text:"\u672C\u6708",value:()=>{const t=new Date;return t.setDate(1),[t,new Date]}},{text:"\u4ECA\u5E74",value:()=>{const t=new Date;return[new Date(`${t.getFullYear()}-01-01`),t]}}],o=function(t,e){return t&&t?a(t).format(e??"YYYY-MM-DD HH:mm:ss"):""},f=function(t){const e=Math.floor(t/864e5),n=Math.floor(t/36e5-24*e),r=Math.floor(t/6e4-24*e*60-60*n),c=Math.floor(t/1e3-24*e*60*60-60*n*60-60*r);return e>0?e+" \u5929"+n+" \u5C0F\u65F6 "+r+" \u5206\u949F":n>0?n+" \u5C0F\u65F6 "+r+" \u5206\u949F":r>0?r+" \u5206\u949F":c>0?c+" \u79D2":"0 \u79D2"},D=function(t,e,n){return n?o(n):""},i=function(t,e,n){return n?o(n,"YYYY-MM-DD"):""},l=function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0)},m=function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59)},T=function(t,e){return t=s(t),e=s(e),Math.floor((e.getTime()-t.getTime())/864e5)},x=function(t,e){return t=s(t),new Date(t.getTime()+e)};function s(t){return typeof t=="string"?new Date(t):t}h=function(t,e){if(!t||!e)return!1;const n=a(t),r=a(e);return n.year()==r.year()&&n.month()==r.month()&&n.day()==r.day()},g=function(t,e){const n=a(t).add(e,"d");return u(n,n)},d=function(){return u(a().subtract(7,"d"),a().subtract(1,"d"))},M=function(){return u(a().subtract(30,"d"),a().subtract(1,"d"))},w=function(){return u(a().subtract(1,"y"),a().subtract(1,"d"))},u=function(t,e){return[a(t).startOf("d").format("YYYY-MM-DD HH:mm:ss"),a(e).endOf("d").format("YYYY-MM-DD HH:mm:ss")]}});export{b as __tla,f as a,i as b,l as c,D as d,m as e,o as f,Y as g,g as h,d as i,M as j,w as k,u as l,h as m,T as n,x as o};
