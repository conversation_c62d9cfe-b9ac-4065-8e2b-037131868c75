import{bE as a,cM as m,__tla as g}from"./index-Daqg4PFz.js";let t,e,y,i,n,r,l=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{e=async s=>await a.get({url:"/system/notify-message/page",params:s}),n=async s=>await a.get({url:"/system/notify-message/my-page",params:s}),r=async s=>await a.put({url:"/system/notify-message/update-read?"+m.stringify({ids:s},{indices:!1})}),t=async()=>await a.put({url:"/system/notify-message/update-all-read"}),y=async()=>await a.get({url:"/system/notify-message/get-unread-list"}),i=async()=>await a.get({url:"/system/notify-message/get-unread-count"})});export{l as __tla,t as a,e as b,y as c,i as d,n as g,r as u};
