import{be as ie,bo as De,eT as Ge,as as re,cI as Oe,r as m,b as H,a as e,eU as se,bq as Re,c4 as Z,at as q,eh as je,C as ue,c5 as qe,bc as Fe,cd as Ke,cT as Qe,bv as Ue,d as Y,bg as ve,bW as Xe,o as A,c as W,l as ce,w as J,H as F,g as L,a0 as $,b2 as K,i as Q,bs as de,cQ as Ye,a8 as U,a3 as he,a9 as R,cR as Je,aW as fe,av as pe,F as Ze,k as et,t as tt,bh as me,b7 as at,f as lt,bA as ot,bS as nt,bi as it,bl as rt,__tla as st}from"./index-Daqg4PFz.js";let ge,ye,ut=Promise.all([(()=>{try{return st}catch{}})()]).then(async()=>{const be=ie({initialIndex:{type:Number,default:0},height:{type:String,default:""},trigger:{type:String,values:["hover","click"],default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,values:["","none","outside"],default:""},arrow:{type:String,values:["always","hover","never"],default:"hover"},type:{type:String,values:["","card"],default:""},loop:{type:Boolean,default:!0},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},pauseOnHover:{type:Boolean,default:!0},motionBlur:{type:Boolean,default:!1}}),Ie={change:(l,M)=>[l,M].every(De)},ee=Symbol("carouselContextKey"),Ce=(l,M,i)=>{const{children:o,addChild:V,removeChild:w}=Ge(re(),"ElCarouselItem"),B=Oe(),n=m(-1),g=m(null),x=m(!1),y=m(),_=m(0),k=m(!0),f=m(!0),d=m(!1),z=H(()=>l.arrow!=="never"&&!e(S)),b=H(()=>o.value.some(t=>t.props.label.toString().length>0)),h=H(()=>l.type==="card"),S=H(()=>l.direction==="vertical"),D=H(()=>l.height!=="auto"?{height:l.height}:{height:`${_.value}px`,overflow:"hidden"}),G=se(t=>{u(t)},300,{trailing:!0}),O=se(t=>{(function(v){l.trigger==="hover"&&v!==n.value&&(n.value=v,f.value||(d.value=!0))})(t)},300);function E(){g.value&&(clearInterval(g.value),g.value=null)}function P(){l.interval<=0||!l.autoplay||g.value||(g.value=setInterval(()=>p(),l.interval))}const p=()=>{f.value||(d.value=!0),f.value=!1,n.value<o.value.length-1?n.value=n.value+1:l.loop&&(n.value=0)};function u(t){if(f.value||(d.value=!0),f.value=!1,Re(t)){const s=o.value.filter(r=>r.props.name===t);s.length>0&&(t=o.value.indexOf(s[0]))}if(t=Number(t),Number.isNaN(t)||t!==Math.floor(t))return void Z(i,"index must be integer.");const v=o.value.length,T=n.value;n.value=t<0?l.loop?v-1:0:t>=v?l.loop?0:v-1:t,T===n.value&&I(T),a()}function I(t){o.value.forEach((v,T)=>{v.translateItem(T,n.value,t)})}function a(){E(),l.pauseOnHover||P()}q(()=>n.value,(t,v)=>{I(v),k.value&&(t%=2,v%=2),v>-1&&M("change",t,v)}),q(()=>l.autoplay,t=>{t?P():E()}),q(()=>l.loop,()=>{u(n.value)}),q(()=>l.interval,()=>{a()});const C=je();return ue(()=>{q(()=>o.value,()=>{o.value.length>0&&u(l.initialIndex)},{immediate:!0}),C.value=qe(y.value,()=>{I()}),P()}),Fe(()=>{E(),y.value&&C.value&&C.value.stop()}),Ke(ee,{root:y,isCardType:h,isVertical:S,items:o,loop:l.loop,addItem:V,removeItem:w,setActiveItem:u,setContainerHeight:function(t){l.height==="auto"&&(_.value=t)}}),{root:y,activeIndex:n,arrowDisplay:z,hasLabel:b,hover:x,isCardType:h,isTransitioning:d,items:o,isVertical:S,containerStyle:D,isItemsTwoLength:k,handleButtonEnter:function(t){e(S)||o.value.forEach((v,T)=>{t===function(s,r){var c,N,X,le;const j=e(o),oe=j.length;if(oe===0||!s.states.inStage)return!1;const Le=r+1,$e=r-1,ne=oe-1,Ve=j[ne].states.active,Pe=j[0].states.active,We=(N=(c=j[Le])==null?void 0:c.states)==null?void 0:N.active,ze=(le=(X=j[$e])==null?void 0:X.states)==null?void 0:le.active;return r===ne&&Pe||We?"left":!!(r===0&&Ve||ze)&&"right"}(v,T)&&(v.states.hover=!0)})},handleTransitionEnd:function(){d.value=!1},handleButtonLeave:function(){e(S)||o.value.forEach(t=>{t.states.hover=!1})},handleIndicatorClick:function(t){t!==n.value&&(f.value||(d.value=!0)),n.value=t},handleMouseEnter:function(){x.value=!0,l.pauseOnHover&&E()},handleMouseLeave:function(){x.value=!1,P()},setActiveItem:u,prev:function(){u(n.value-1)},next:function(){u(n.value+1)},PlaceholderItem:function(){var t;const v=(t=B.default)==null?void 0:t.call(B);if(!v)return null;const T=Qe(v).filter(s=>Ue(s)&&s.type.name==="ElCarouselItem");return(T==null?void 0:T.length)===2&&l.loop&&!h.value?(k.value=!0,T):(k.value=!1,null)},isTwoLengthShow:t=>!k.value||(n.value<=1?t<=1:t>1),throttledArrowClick:G,throttledIndicatorHover:O}},we=["aria-label"],ke=["aria-label"],Se=["onMouseenter","onClick"],xe=["aria-label"],_e={key:0},Te={key:3,xmlns:"http://www.w3.org/2000/svg",version:"1.1",style:{display:"none"}},Ee=[L("defs",null,[L("filter",{id:"elCarouselHorizontal"},[L("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"12,0"})]),L("filter",{id:"elCarouselVertical"},[L("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"0,10"})])],-1)],te="ElCarousel",Me=Y({name:te});var Be=me(Y({...Me,props:be,emits:Ie,setup(l,{expose:M,emit:i}){const o=l,{root:V,activeIndex:w,arrowDisplay:B,hasLabel:n,hover:g,isCardType:x,items:y,isVertical:_,containerStyle:k,handleButtonEnter:f,handleButtonLeave:d,isTransitioning:z,handleIndicatorClick:b,handleMouseEnter:h,handleMouseLeave:S,handleTransitionEnd:D,setActiveItem:G,prev:O,next:E,PlaceholderItem:P,isTwoLengthShow:p,throttledArrowClick:u,throttledIndicatorHover:I}=Ce(o,i,te),a=ve("carousel"),{t:C}=Xe(),t=H(()=>{const s=[a.b(),a.m(o.direction)];return e(x)&&s.push(a.m("card")),s}),v=H(()=>{const s=[a.e("container")];return o.motionBlur&&e(z)&&s.push(e(_)?`${a.namespace.value}-transitioning-vertical`:`${a.namespace.value}-transitioning`),s}),T=H(()=>{const s=[a.e("indicators"),a.em("indicators",o.direction)];return e(n)&&s.push(a.em("indicators","labels")),o.indicatorPosition==="outside"&&s.push(a.em("indicators","outside")),e(_)&&s.push(a.em("indicators","right")),s});return M({setActiveItem:G,prev:O,next:E}),(s,r)=>(A(),W("div",{ref_key:"root",ref:V,class:$(e(t)),onMouseenter:r[7]||(r[7]=K((...c)=>e(h)&&e(h)(...c),["stop"])),onMouseleave:r[8]||(r[8]=K((...c)=>e(S)&&e(S)(...c),["stop"]))},[e(B)?(A(),ce(he,{key:0,name:"carousel-arrow-left",persisted:""},{default:J(()=>[F(L("button",{type:"button",class:$([e(a).e("arrow"),e(a).em("arrow","left")]),"aria-label":e(C)("el.carousel.leftArrow"),onMouseenter:r[0]||(r[0]=c=>e(f)("left")),onMouseleave:r[1]||(r[1]=(...c)=>e(d)&&e(d)(...c)),onClick:r[2]||(r[2]=K(c=>e(u)(e(w)-1),["stop"]))},[Q(e(de),null,{default:J(()=>[Q(e(Ye))]),_:1})],42,we),[[U,(s.arrow==="always"||e(g))&&(o.loop||e(w)>0)]])]),_:1})):R("v-if",!0),e(B)?(A(),ce(he,{key:1,name:"carousel-arrow-right",persisted:""},{default:J(()=>[F(L("button",{type:"button",class:$([e(a).e("arrow"),e(a).em("arrow","right")]),"aria-label":e(C)("el.carousel.rightArrow"),onMouseenter:r[3]||(r[3]=c=>e(f)("right")),onMouseleave:r[4]||(r[4]=(...c)=>e(d)&&e(d)(...c)),onClick:r[5]||(r[5]=K(c=>e(u)(e(w)+1),["stop"]))},[Q(e(de),null,{default:J(()=>[Q(e(Je))]),_:1})],42,ke),[[U,(s.arrow==="always"||e(g))&&(o.loop||e(w)<e(y).length-1)]])]),_:1})):R("v-if",!0),L("div",{class:$(e(v)),style:pe(e(k)),onTransitionend:r[6]||(r[6]=(...c)=>e(D)&&e(D)(...c))},[Q(e(P)),fe(s.$slots,"default")],38),s.indicatorPosition!=="none"?(A(),W("ul",{key:2,class:$(e(T))},[(A(!0),W(Ze,null,et(e(y),(c,N)=>F((A(),W("li",{key:N,class:$([e(a).e("indicator"),e(a).em("indicator",s.direction),e(a).is("active",N===e(w))]),onMouseenter:X=>e(I)(N),onClick:K(X=>e(b)(N),["stop"])},[L("button",{class:$(e(a).e("button")),"aria-label":e(C)("el.carousel.indicator",{index:N+1})},[e(n)?(A(),W("span",_e,tt(c.props.label),1)):R("v-if",!0)],10,xe)],42,Se)),[[U,e(p)(N)]])),128))],2)):R("v-if",!0),o.motionBlur?(A(),W("svg",Te,Ee)):R("v-if",!0)],34))}}),[["__file","carousel.vue"]]);const He=ie({name:{type:String,default:""},label:{type:[String,Number],default:""}}),Ae=(l,M)=>{const i=at(ee),o=re();i||Z(M,"usage: <el-carousel></el-carousel-item></el-carousel>"),o||Z(M,"compositional hook can only be invoked inside setups");const V=.83,w=m(),B=m(!1),n=m(0),g=m(1),x=m(!1),y=m(!1),_=m(!1),k=m(!1),{isCardType:f,isVertical:d}=i,z=(b,h,S)=>{var D;const G=e(f),O=(D=i.items.value.length)!=null?D:Number.NaN,E=b===h;G||nt(S)||(k.value=E||b===S),!E&&O>2&&i.loop&&(b=function(p,u,I){const a=I-1,C=I/2;return u===0&&p===a?-1:u===a&&p===0?I:p<u-1&&u-p>=C?I+1:p>u+1&&p-u>=C?-2:p}(b,h,O));const P=e(d);x.value=E,G?(_.value=Math.round(Math.abs(b-h))<=1,n.value=function(p,u){var I,a;const C=e(d)?((I=i.root.value)==null?void 0:I.offsetHeight)||0:((a=i.root.value)==null?void 0:a.offsetWidth)||0;return _.value?C*(1.17*(p-u)+1)/4:p<u?-1.83*C/4:3.83*C/4}(b,h),g.value=e(x)?1:V):n.value=function(p,u,I){const a=i.root.value;return a?((I?a.offsetHeight:a.offsetWidth)||0)*(p-u):0}(b,h,P),y.value=!0,E&&w.value&&i.setContainerHeight(w.value.offsetHeight)};return ue(()=>{i.addItem({props:l,states:lt({hover:B,translate:n,scale:g,active:x,ready:y,inStage:_,animating:k}),uid:o.uid,translateItem:z})}),ot(()=>{i.removeItem(o.uid)}),{carouselItemRef:w,active:x,animating:k,hover:B,inStage:_,isVertical:d,translate:n,isCardType:f,scale:g,ready:y,handleItemClick:function(){if(i&&e(f)){const b=i.items.value.findIndex(({uid:h})=>h===o.uid);i.setActiveItem(b)}}}},Ne=Y({name:"ElCarouselItem"});var ae=me(Y({...Ne,props:He,setup(l){const M=l,i=ve("carousel"),{carouselItemRef:o,active:V,animating:w,hover:B,inStage:n,isVertical:g,translate:x,isCardType:y,scale:_,ready:k,handleItemClick:f}=Ae(M,"ElCarouselItem"),d=H(()=>[i.e("item"),i.is("active",V.value),i.is("in-stage",n.value),i.is("hover",B.value),i.is("animating",w.value),{[i.em("item","card")]:y.value,[i.em("item","card-vertical")]:y.value&&g.value}]),z=H(()=>({transform:[`${"translate"+(e(g)?"Y":"X")}(${e(x)}px)`,`scale(${e(_)})`].join(" ")}));return(b,h)=>F((A(),W("div",{ref_key:"carouselItemRef",ref:o,class:$(e(d)),style:pe(e(z)),onClick:h[0]||(h[0]=(...S)=>e(f)&&e(f)(...S))},[e(y)?F((A(),W("div",{key:0,class:$(e(i).e("mask"))},null,2)),[[U,!e(V)]]):R("v-if",!0),fe(b.$slots,"default")],6)),[[U,e(k)]])}}),[["__file","carousel-item.vue"]]);ye=it(Be,{CarouselItem:ae}),ge=rt(ae)});export{ge as E,ut as __tla,ye as a};
