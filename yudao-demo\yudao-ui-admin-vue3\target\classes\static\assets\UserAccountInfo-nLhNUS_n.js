import{d as p,r as f,at as v,o as y,l as h,w as a,i as e,a as l,j as r,t as n,aG as i,_ as g,__tla as x}from"./index-Daqg4PFz.js";import{E as I,a as w,__tla as E}from"./el-descriptions-item-Bucl-KSp.js";import{__tla as U}from"./Descriptions.vue_vue_type_style_index_0_scoped_aee191e8_lang-nMrQIkhC.js";import{D as s,__tla as A}from"./DescriptionsItemLabel-BqetQgPV.js";import{g as P,__tla as R}from"./index-CamnB0Tv.js";import{__tla as j}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";let m,q=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{m=g(p({__name:"UserAccountInfo",props:{user:{type:null,required:!0}},setup(b){const o=b,c={balance:0,totalExpense:0,totalRecharge:0},u=f(c);return v(()=>o.user.id,()=>(async()=>{if(!o.user.id)return void(u.value=c);const _={userId:o.user.id};u.value=await P(_)||c})(),{immediate:!0}),(_,D)=>{const t=I,d=w;return y(),h(d,{column:2},{default:a(()=>[e(t,null,{label:a(()=>[e(l(s),{label:" \u7B49\u7EA7 ",icon:"svg-icon:member_level"})]),default:a(()=>[r(" "+n(_.user.levelName||"\u65E0"),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u6210\u957F\u503C ",icon:"ep:suitcase"})]),default:a(()=>[r(" "+n(_.user.experience||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u5F53\u524D\u79EF\u5206 ",icon:"ep:coin"})]),default:a(()=>[r(" "+n(_.user.point||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u603B\u79EF\u5206 ",icon:"ep:coin"})]),default:a(()=>[r(" "+n(_.user.totalPoint||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u5F53\u524D\u4F59\u989D ",icon:"svg-icon:member_balance"})]),default:a(()=>[r(" "+n(l(i)(l(u).balance||0)),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u652F\u51FA\u91D1\u989D ",icon:"svg-icon:member_expenditure_balance"})]),default:a(()=>[r(" "+n(l(i)(l(u).totalExpense||0)),1)]),_:1}),e(t,null,{label:a(()=>[e(l(s),{label:" \u5145\u503C\u91D1\u989D ",icon:"svg-icon:member_recharge_balance"})]),default:a(()=>[r(" "+n(l(i)(l(u).totalRecharge||0)),1)]),_:1})]),_:1})}}}),[["__scopeId","data-v-057b9a78"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/member/user/detail/UserAccountInfo.vue"]])});export{q as __tla,m as default};
