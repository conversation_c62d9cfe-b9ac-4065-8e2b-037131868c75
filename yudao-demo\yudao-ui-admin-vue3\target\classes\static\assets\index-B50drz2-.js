import{_ as H,__tla as K}from"./Dialog-BjBBVYCI.js";import{d as $,n as G,I as Q,r as c,C as P,au as q,T as W,o as S,c as b,i as a,w as e,g as f,j as d,a as s,t as X,H as Y,a9 as Z,z as aa,F as ea,e6 as ta,e7 as la,b9 as sa,N as ra,E as na,s as oa,b3 as ua,_ as ia,__tla as ca}from"./index-Daqg4PFz.js";import{_ as _a,__tla as ma}from"./ContentWrap-DZg14iby.js";import{u as pa,__tla as fa}from"./useFormCreateDesigner-BiHz4H1U.js";import{H as g,j as da,__tla as ya}from"./java-Bu72MS_h.js";import{__tla as ga}from"./el-card-Dvjjuipo.js";import{__tla as va}from"./dict.type-BqDb60NG.js";let J,ha=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})()]).then(async()=>{function M(o){const r=["true","false","null"],_={scope:"literal",beginKeywords:r.join(" ")};return{name:"JSON",keywords:{literal:r},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},o.QUOTE_STRING_MODE,_,o.C_NUMBER_MODE,o.C_LINE_COMMENT_MODE,o.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}let v,h,O;v={class:"float-right mb-2"},h={key:0,ref:"editor"},O={class:"hljs"},J=ia($({name:"InfraBuild",__name:"index",setup(o){const{t:r}=G(),_=Q(),n=c(),u=c(!1),C=c(""),m=c(-1),i=c("");pa(n);const y=t=>{u.value=!0,C.value=t},k=()=>{y("\u751F\u6210 JSON"),m.value=0,i.value=n.value.getRule()},x=()=>{y("\u751F\u6210 Options"),m.value=1,i.value=n.value.getOption()},j=()=>{y("\u751F\u6210\u7EC4\u4EF6"),m.value=2,i.value=D()},D=()=>{const t=n.value.getRule(),l=n.value.getOption();return`<template>
    <form-create
      v-model:api="fApi"
      :rule="rule"
      :option="option"
      @submit="onSubmit"
    ></form-create>
  </template>
  <script setup lang=ts>
    const faps = ref(null)
    const rule = ref('')
    const option = ref('')
    const init = () => {
      rule.value = formCreate.parseJson('${ta.toJson(t).replaceAll("\\","\\\\")}')
      option.value = formCreate.parseJson('${JSON.stringify(l)}')
    }
    const onSubmit = (formData) => {
      //todo \u63D0\u4EA4\u8868\u5355
    }
    init()
  <\/script>`},w=t=>{let l="json";return m.value===2&&(l="xml"),sa(t)||(t=JSON.stringify(t)),g.highlight(l,t,!0).value||"&nbsp;"};return P(async()=>{g.registerLanguage("xml",da),g.registerLanguage("json",M)}),(t,l)=>{const p=ra,N=na,L=q("FcDesigner"),T=oa,z=_a,I=ua,R=H,U=W("dompurify-html");return S(),b(ea,null,[a(z,null,{default:e(()=>[a(T,null,{default:e(()=>[a(N,null,{default:e(()=>[f("div",v,[a(p,{size:"small",type:"primary",onClick:k},{default:e(()=>[d("\u751F\u6210 JSON")]),_:1}),a(p,{size:"small",type:"success",onClick:x},{default:e(()=>[d("\u751F\u6210 Options")]),_:1}),a(p,{size:"small",type:"danger",onClick:j},{default:e(()=>[d("\u751F\u6210\u7EC4\u4EF6")]),_:1})])]),_:1}),a(N,null,{default:e(()=>[a(L,{ref_key:"designer",ref:n,height:"780px"},null,512)]),_:1})]),_:1})]),_:1}),a(R,{modelValue:s(u),"onUpdate:modelValue":l[1]||(l[1]=E=>aa(u)?u.value=E:null),title:s(C),"max-height":"600"},{default:e(()=>[s(u)?(S(),b("div",h,[a(p,{style:{float:"right"},onClick:l[0]||(l[0]=E=>(async B=>{const{copy:V,copied:A,isSupported:F}=la({source:B});F?(await V(),s(A)&&_.success(r("common.copySuccess"))):_.error(r("common.copyError"))})(s(i)))},{default:e(()=>[d(X(s(r)("common.copy")),1)]),_:1}),a(I,{height:"580"},{default:e(()=>[f("div",null,[f("pre",null,[Y(f("code",O,null,512),[[U,w(s(i))]])])])]),_:1})],512)):Z("",!0)]),_:1},8,["modelValue","title"])],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/build/index.vue"]])});export{ha as __tla,J as default};
