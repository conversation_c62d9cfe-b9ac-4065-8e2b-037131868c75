import{_ as h,__tla as k}from"./AppLinkSelectDialog-D0bg80Di.js";import{d as g,p as x,r as n,at as o,o as A,c as C,i as l,w as r,j as I,a as L,z as U,F as j,N as w,Z as z,_ as F,__tla as J}from"./index-Daqg4PFz.js";let _,N=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{_=F(g({name:"AppLinkInput",__name:"index",props:{modelValue:x.string.def("")},emits:["update:modelValue"],setup(d,{emit:m}){const t=d,a=n(""),u=n(),p=()=>{var e;return(e=u.value)==null?void 0:e.open(a.value)},i=e=>a.value=e;o(()=>t.modelValue,()=>a.value=t.modelValue,{immediate:!0});const c=m;return o(()=>a.value,()=>c("update:modelValue",a.value)),(e,s)=>{const v=w,f=z,V=h;return A(),C(j,null,[l(f,{modelValue:L(a),"onUpdate:modelValue":s[0]||(s[0]=y=>U(a)?a.value=y:null),placeholder:"\u8F93\u5165\u6216\u9009\u62E9\u94FE\u63A5"},{append:r(()=>[l(v,{onClick:p},{default:r(()=>[I("\u9009\u62E9")]),_:1})]),_:1},8,["modelValue"]),l(V,{ref_key:"dialogRef",ref:u,onChange:i},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/AppLinkInput/index.vue"]])});export{_,N as __tla};
