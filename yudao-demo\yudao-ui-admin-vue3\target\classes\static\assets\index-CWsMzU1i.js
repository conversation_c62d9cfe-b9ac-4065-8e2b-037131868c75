import{_ as A,__tla as B}from"./Dialog-BjBBVYCI.js";import{d as L,u as M,S as R,r as m,f as G,C as I,o as p,c as b,i as e,w as t,a as l,al as J,j as n,F as V,k as P,l as Z,t as D,V as H,G as K,z as Q,n as W,I as X,x as Y,N as $,Z as aa,L as ea,am as la,an as ta,O as ra,_ as sa,__tla as ua}from"./index-Daqg4PFz.js";import{_ as oa,__tla as _a}from"./ContentWrap-DZg14iby.js";import{C as ma}from"./constants-WoCEnNvc.js";import{g as na,c as da,u as ia,__tla as ca}from"./index-CSvGj0-b.js";import{s as fa,e as pa,a as ya,__tla as ha}from"./formCreate-Cp7STxiP.js";import{u as va,__tla as ba}from"./tagsView-CrrEoR03.js";import{u as Va,__tla as ka}from"./useFormCreateDesigner-BiHz4H1U.js";import{__tla as ga}from"./el-card-Dvjjuipo.js";import{__tla as wa}from"./dict.type-BqDb60NG.js";let k,xa=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{k=sa(L({name:"BpmFormEditor",__name:"index",setup(Ca){const{t:y}=W(),h=X(),{push:g,currentRoute:w}=M(),{query:x}=R(),{delView:C}=va(),_=m();Va(_);const o=m(!1),d=m(!1),s=m({name:"",status:ma.ENABLE,remark:""}),U=G({name:[{required:!0,message:"\u8868\u5355\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=m(),q=()=>{o.value=!0},S=async()=>{if(i&&await i.value.validate()){d.value=!0;try{const u=s.value;u.conf=pa(_),u.fields=ya(_),u.id?(await ia(u),h.success(y("common.updateSuccess"))):(await da(u),h.success(y("common.createSuccess"))),o.value=!1,O()}finally{d.value=!1}}},O=()=>{C(l(w)),g("/bpm/manager/form")};return I(async()=>{const u=x.id;if(!u)return;const a=await na(u);s.value=a,fa(_,a.conf,a.fields)}),(u,a)=>{const E=Y,c=$,F=oa,v=aa,f=ea,N=la,T=ta,j=ra,z=A;return p(),b(V,null,[e(F,null,{default:t(()=>[e(l(J),{ref_key:"designer",ref:_,height:"780px"},{handle:t(()=>[e(c,{round:"",size:"small",type:"primary",onClick:q},{default:t(()=>[e(E,{class:"mr-5px",icon:"ep:plus"}),n(" \u4FDD\u5B58 ")]),_:1})]),_:1},512)]),_:1}),e(z,{modelValue:l(o),"onUpdate:modelValue":a[4]||(a[4]=r=>Q(o)?o.value=r:null),title:"\u4FDD\u5B58\u8868\u5355",width:"600"},{footer:t(()=>[e(c,{disabled:l(d),type:"primary",onClick:S},{default:t(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),e(c,{onClick:a[3]||(a[3]=r=>o.value=!1)},{default:t(()=>[n("\u53D6 \u6D88")]),_:1})]),default:t(()=>[e(j,{ref_key:"formRef",ref:i,model:l(s),rules:l(U),"label-width":"80px"},{default:t(()=>[e(f,{label:"\u8868\u5355\u540D",prop:"name"},{default:t(()=>[e(v,{modelValue:l(s).name,"onUpdate:modelValue":a[0]||(a[0]=r=>l(s).name=r),placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(T,{modelValue:l(s).status,"onUpdate:modelValue":a[1]||(a[1]=r=>l(s).status=r)},{default:t(()=>[(p(!0),b(V,null,P(l(H)(l(K).COMMON_STATUS),r=>(p(),Z(N,{key:r.value,label:r.value},{default:t(()=>[n(D(r.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(v,{modelValue:l(s).remark,"onUpdate:modelValue":a[2]||(a[2]=r=>l(s).remark=r),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/form/editor/index.vue"]])});export{xa as __tla,k as default};
