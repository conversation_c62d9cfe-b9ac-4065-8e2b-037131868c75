import{bE as t,__tla as g}from"./index-Daqg4PFz.js";let e,r,s,u,p,l,i=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{s=async a=>await t.post({url:"/bpm/user-group/create",data:a}),l=async a=>await t.put({url:"/bpm/user-group/update",data:a}),u=async a=>await t.delete({url:"/bpm/user-group/delete?id="+a}),e=async a=>await t.get({url:"/bpm/user-group/get?id="+a}),r=async a=>await t.get({url:"/bpm/user-group/page",params:a}),p=async()=>await t.get({url:"/bpm/user-group/simple-list"})});export{i as __tla,e as a,r as b,s as c,u as d,p as g,l as u};
