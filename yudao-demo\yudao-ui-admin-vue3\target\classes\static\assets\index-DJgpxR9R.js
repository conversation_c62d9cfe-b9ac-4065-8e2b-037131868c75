import{d as w,b as p,o as t,c as r,g as x,F as k,k as C,av as d,t as I,l as M,a as u,a9 as B,_ as N,__tla as R}from"./index-Daqg4PFz.js";import{_ as U}from"./app-nav-bar-mp-QvSN8lzY.js";import j,{__tla as q}from"./index-CXpecyck.js";let m,D=Promise.all([(()=>{try{return R}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{let o,s,i,c;o={class:"h-full w-full flex items-center"},s={key:0},i=["src"],c={key:0,src:U,alt:"",class:"h-30px w-86px"},m=N(w({name:"NavigationBar",__name:"index",props:{property:{type:Object,required:!0}},setup(_){const a=_,g=p(()=>({background:a.property.bgType==="img"&&a.property.bgImg?`url(${a.property.bgImg}) no-repeat top center / 100% 100%`:a.property.bgColor})),h=p(()=>{var e;return(e=a.property._local)!=null&&e.previewMp?a.property.mpCells:a.property.otherCells}),n=p(()=>{var e;return(e=a.property._local)!=null&&e.previewMp?209/6:285/8}),v=e=>({width:e.width*n.value+10*(e.width-1)+"px",left:e.left*n.value+10*(e.left+1)+"px",position:"absolute"}),f=e=>({height:30,showScan:!1,placeholder:e.placeholder,borderRadius:e.borderRadius});return(e,E)=>{var y;return t(),r("div",{class:"navigation-bar",style:d(u(g))},[x("div",o,[(t(!0),r(k,null,C(u(h),(l,b)=>(t(),r("div",{key:b,style:d(v(l))},[l.type==="text"?(t(),r("span",s,I(l.text),1)):l.type==="image"?(t(),r("img",{key:1,src:l.imgUrl,alt:"",class:"h-full w-full"},null,8,i)):(t(),M(j,{key:2,property:f}))],4))),128))]),(y=e.property._local)!=null&&y.previewMp?(t(),r("img",c)):B("",!0)],4)}}}),[["__scopeId","data-v-8a7668cd"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/NavigationBar/index.vue"]])});export{D as __tla,m as default};
