import{dw as <PERSON>,dv as Zi,d as Qi,r as s6,o as $2,c as f6,i as M4,w as P4,a as l6,l as N4,t as I4,a9 as O4,j as L4,x as $i,bs as Ji,ax as e7,_ as i7,__tla as t7}from"./index-Daqg4PFz.js";let C4,r7=Promise.all([(()=>{try{return t7}catch{}})()]).then(async()=>{var u6={exports:{}};u6.exports=function(){function R2(V1,o1){if(!(V1 instanceof o1))throw new TypeError("Cannot call a class as a function")}function J2(V1,o1){for(var f1=0;f1<o1.length;f1++){var L=o1[f1];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(V1,<PERSON>.key,L)}}function U0(V1,o1,f1){return o1&&J2(V1.prototype,o1),f1&&J2(V1,f1),V1}typeof globalThis<"u"||typeof window<"u"||Ki!==void 0||typeof self<"u";var u2={exports:{}};(function(V1,o1){(function(f1,L){V1.exports=L()})(0,function(){var f1=(function(){var k1,A1=0,D1=[],Y1=[];function e0(g1){k1=g1.sampleRate}function P0(g1){D1.push(g1[0]),Y1.push(g1[1]),A1+=g1[0].length}function K0(g1){var y1=S2(c2(J1(D1,A1),J1(Y1,A1))),C1=new Blob([y1],{type:g1});self.postMessage({type:"blob",data:C1})}function Z0(){var g1=[];g1.push(J1(D1,A1)),g1.push(J1(Y1,A1)),self.postMessage({type:"buffer",data:g1})}function u1(){A1=0,D1=[],Y1=[]}function J1(g1,y1){for(var C1=new Float32Array(y1),i0=0,G1=0;G1<g1.length;G1++)C1.set(g1[G1],i0),i0+=g1[G1].length;return C1}function c2(g1,y1){for(var C1=g1.length+y1.length,i0=new Float32Array(C1),G1=0,q0=0;G1<C1;)i0[G1++]=g1[q0],i0[G1++]=y1[q0],q0++;return i0}function L0(g1,y1,C1){for(var i0=0;i0<C1.length;i0++,y1+=2){var G1=Math.max(-1,Math.min(1,C1[i0]));g1.setInt16(y1,G1<0?32768*G1:32767*G1,!0)}}function Q0(g1,y1,C1){for(var i0=0;i0<C1.length;i0++)g1.setUint8(y1+i0,C1.charCodeAt(i0))}function S2(g1){var y1=new ArrayBuffer(44+2*g1.length),C1=new DataView(y1);return Q0(C1,0,"RIFF"),C1.setUint32(4,36+2*g1.length,!0),Q0(C1,8,"WAVE"),Q0(C1,12,"fmt "),C1.setUint32(16,16,!0),C1.setUint16(20,1,!0),C1.setUint16(22,2,!0),C1.setUint32(24,k1,!0),C1.setUint32(28,4*k1,!0),C1.setUint16(32,4,!0),C1.setUint16(34,16,!0),Q0(C1,36,"data"),C1.setUint32(40,2*g1.length,!0),L0(C1,44,g1),C1}self.onmessage=function(g1){switch(g1.data.command){case"init":e0(g1.data.config);break;case"record":P0(g1.data.buffer);break;case"exportWAV":K0(g1.data.type);break;case"getBuffer":Z0();break;case"clear":u1()}}}).toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),L=function(k1,A1){var D1=A1||{},Y1=D1.bufferLen||4096;this.context=k1.context,this.node=(this.context.createScriptProcessor||this.context.createJavaScriptNode).call(this.context,Y1,2,2);var e0=new Worker((window.URL||window.webkitURL).createObjectURL(new Blob([f1],{type:"text/javascript"})));e0.onmessage=function(u1){u1.data.type==="blob"?K0(u1.data.data):P0(u1.data.data)},e0.postMessage({command:"init",config:{sampleRate:this.context.sampleRate}});var P0,K0,Z0=!1;this.node.onaudioprocess=function(u1){Z0&&e0.postMessage({command:"record",buffer:[u1.inputBuffer.getChannelData(0),u1.inputBuffer.getChannelData(1)]})},this.configure=function(u1){for(var J1 in u1)u1.hasOwnProperty(J1)&&(D1[J1]=u1[J1])},this.record=function(){Z0=!0},this.stop=function(){Z0=!1},this.clear=function(){e0.postMessage({command:"clear"})},this.getBuffer=function(u1){P0=u1||D1.callback,e0.postMessage({command:"getBuffer"})},this.exportWAV=function(u1,J1){if(K0=u1||D1.callback,J1=J1||D1.type||"audio/wav",!K0)throw new Error("Callback not set");e0.postMessage({command:"exportWAV",type:J1})},this.release=function(){this.stop(),this.clear(),this.configure=this.record=this.stop=this.clear=this.getBuffer=this.exportWAV=function(){},k1.disconnect(this.node),this.node.onaudioprocess=null,this.node.disconnect(),e0.terminate()},k1.connect(this.node),this.node.connect(this.context.destination)};return L.forceDownload=function(k1,A1){var D1=(window.URL||window.webkitURL).createObjectURL(k1),Y1=window.document.createElement("a");Y1.href=D1,Y1.download=A1||"output.wav";var e0=document.createEvent("Event");e0.initEvent("click",!0,!0),Y1.dispatchEvent(e0)},L})})(u2);var x2=u2.exports,e5=window.AudioContext||window.webkitAudioContext||window.mozAudioContext,r0=null,B2=!0;e5||(B2=!1,console.warn("Web Audio API is Unsupported."));var h0=function(){function V1(){R2(this,V1),this._recorderStream=null,this._recorderStreamSourceNode=null,this._recorder=null,this._isRecording=!1,this._curSourceNode=null}return U0(V1,[{key:"playPcm",value:function(o1,f1,L,k1){r0&&r0.state!=="closed"||(r0=new e5),r0.state!=="interrupted"&&r0.state!=="suspended"||r0.resume(),f1=f1||8e3,this.stopPcm();var A1,D1=k1&&k1>.001?o1.slice(f1*k1):o1;if(!D1.length)return L();this._curSourceNode=r0.createBufferSource();try{A1=r0.createBuffer(1,D1.length,f1)}catch{f1<11025?(A1=r0.createBuffer(1,D1.length,4*f1),this._curSourceNode.playbackRate.value=.25):(A1=r0.createBuffer(1,D1.length,2*f1),this._curSourceNode.playbackRate.value=.5)}A1.copyToChannel?A1.copyToChannel(D1,0,0):A1.getChannelData(0).set(D1),this._curSourceNode.buffer=A1,this._curSourceNode.loop=!1,this._curSourceNode.connect(r0.destination),this._curSourceNode.onended=L,this._curSourceNode.start()}},{key:"stopPcm",value:function(){this._curSourceNode&&(this._curSourceNode.stop(),this._curSourceNode=null)}},{key:"stopPcmSilently",value:function(){this._curSourceNode.onended=null,this.stopPcm()}},{key:"initRecorder",value:function(){var o1=this;return new Promise(function(f1,L){var k1=function(D1){o1._recorderStream=D1,o1._recorderStreamSourceNode=r0.createMediaStreamSource(D1),o1._recorder=new x2(o1._recorderStreamSourceNode),o1._isRecording=!1,f1()},A1=function(D1){L(D1)};o1._recorder?f1():window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia?window.navigator.mediaDevices.getUserMedia({audio:!0}).then(k1).catch(A1):window.navigator.getUserMedia?window.navigator.getUserMedia({audio:!0},k1,A1):A1()})}},{key:"isRecording",value:function(){return this._recorder&&this._isRecording}},{key:"startRecord",value:function(){this._recorder&&(this._recorder.clear(),this._recorder.record(),this._isRecording=!0)}},{key:"stopRecord",value:function(){this._recorder&&(this._recorder.stop(),this._isRecording=!1)}},{key:"generateRecordSamples",value:function(){var o1=this;return new Promise(function(f1){o1._recorder&&o1._recorder.getBuffer(function(L){f1(L[0])})})}},{key:"releaseRecord",value:function(){this._recorderStream&&this._recorderStream.getTracks&&(this._recorderStream.getTracks().forEach(function(o1){o1.stop()}),this._recorderStream=null),this._recorder&&(this._recorder.release(),this._recorder=null)}}],[{key:"isPlaySupported",value:function(){return B2}},{key:"isRecordSupported",value:function(){return!!(window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia||window.navigator.getUserMedia)}},{key:"getCtxSampleRate",value:function(){return r0&&r0.sampleRate||0}},{key:"getCtxTime",value:function(){return r0&&r0.currentTime||0}},{key:"decodeAudioArrayBufferByContext",value:function(o1){return new Promise(function(f1,L){r0.decodeAudioData(o1,function(k1){var A1=k1.numberOfChannels,D1=new Float32Array(k1.length);switch(A1){default:case 1:D1=k1.getChannelData(0);break;case 2:for(var Y1=k1.getChannelData(0),e0=k1.getChannelData(1),P0=0,K0=D1.length;P0<K0;P0++)D1[P0]=.5*(Y1[P0]+e0[P0]);break;case 4:for(var Z0=k1.getChannelData(0),u1=k1.getChannelData(1),J1=k1.getChannelData(2),c2=k1.getChannelData(3),L0=0,Q0=D1.length;L0<Q0;L0++)D1[L0]=.25*(Z0[L0]+u1[L0]+J1[L0]+c2[L0]);break;case 6:for(var S2=k1.getChannelData(0),g1=k1.getChannelData(1),y1=k1.getChannelData(2),C1=k1.getChannelData(4),i0=k1.getChannelData(5),G1=0,q0=D1.length;G1<q0;G1++)D1[G1]=.7071*(S2[G1]+g1[G1])+y1[G1]+.5*(C1[G1]+i0[G1])}f1(D1)},L)})}}]),V1}(),w6=function(){var V1=function(){var L,k1={toWAV:function(p){var v=this._decode(p);if(!v)return null;var g=new Uint8Array(v.buffer,v.byteOffset,v.byteLength),_=new Uint8Array(g.length+this.WAV_HEADER_SIZE),e=0,s=function(e1){var a1=new Uint8Array(2);new Int16Array(a1.buffer)[0]=e1,_.set(a1,e),e+=2},B=function(e1){var a1=new Uint8Array(4);new Int32Array(a1.buffer)[0]=e1,_.set(a1,e),e+=4},D=function(e1){var a1=new TextEncoder("utf-8").encode(e1);_.set(a1,e),e+=a1.length};D("RIFF"),B(36+g.length),D("WAVEfmt "),B(16);var Y=16,Q=8e3,Z=Y/8*1,O=Z*Q;return s(1),s(1),B(Q),B(O),s(Z),s(Y),D("data"),B(g.length),_.set(g,e),_},decode:function(p){var v=this._decode(p);if(!v)return null;for(var g=new Float32Array(v.length),_=0;_<g.length;_++)g[_]=v[_]/32768;return g},_decode:function(p){if(String.fromCharCode.apply(null,p.subarray(0,this.AMR_HEADER.length))!==this.AMR_HEADER)return null;var v=this.Decoder_Interface_init();if(!v)return null;var g=new Int16Array(Math.floor(p.length/6*this.PCM_BUFFER_COUNT)),_=L._malloc(this.AMR_BUFFER_COUNT),e=new Uint8Array(L.HEAPU8.buffer,_,this.AMR_BUFFER_COUNT);_=L._malloc(2*this.PCM_BUFFER_COUNT);for(var s=new Int16Array(L.HEAPU8.buffer,_,this.PCM_BUFFER_COUNT),B=6,D=0;B+1<p.length&&D+1<g.length;){var Y=this.SIZES[p[B]>>3&15];if(B+Y+1>p.length)break;if(e.set(p.subarray(B,B+Y+1)),this.Decoder_Interface_Decode(v,e.byteOffset,s.byteOffset,0),D+this.PCM_BUFFER_COUNT>g.length){var Q=new Int16Array(2*g.length);Q.set(g.subarray(0,D)),g=Q}g.set(s,D),D+=this.PCM_BUFFER_COUNT,B+=Y+1}return L._free(e.byteOffset),L._free(s.byteOffset),this.Decoder_Interface_exit(v),g.subarray(0,D)},encode:function(p,v,g){if(v<8e3)return console.error("pcmSampleRate should not be less than 8000."),null;g===void 0&&(g=this.Mode.MR795);var _=this.Encoder_Interface_init();if(!_)return null;var e=L._malloc(2*this.PCM_BUFFER_COUNT),s=new Int16Array(L.HEAPU8.buffer,e,this.PCM_BUFFER_COUNT);e=L._malloc(this.AMR_BUFFER_COUNT);for(var B=new Uint8Array(L.HEAPU8.buffer,e,this.AMR_BUFFER_COUNT),D=v/8e3,Y=Math.floor(p.length/D),Q=new Int16Array(Y),Z=0;Z<Y;Z++)Q[Z]=32767*p[Math.floor(Z*D)];var O=this.SIZES[g]+1,e1=new Uint8Array(Math.ceil(Y/this.PCM_BUFFER_COUNT*O)+this.AMR_HEADER.length);e1.set(new TextEncoder("utf-8").encode(this.AMR_HEADER));for(var a1=0,R1=this.AMR_HEADER.length;a1+this.PCM_BUFFER_COUNT<Q.length&&R1+O<e1.length;){s.set(Q.subarray(a1,a1+this.PCM_BUFFER_COUNT));var I1=this.Encoder_Interface_Encode(_,g,s.byteOffset,B.byteOffset,0);if(I1!=O){console.error([I1,O]);break}e1.set(B.subarray(0,I1),R1),a1+=this.PCM_BUFFER_COUNT,R1+=I1}return L._free(s.byteOffset),L._free(B.byteOffset),this.Encoder_Interface_exit(_),e1.subarray(0,R1)},Decoder_Interface_init:function(){return console.warn("Decoder_Interface_init not initialized."),0},Decoder_Interface_exit:function(p){console.warn("Decoder_Interface_exit not initialized.")},Decoder_Interface_Decode:function(p,v,g,_){console.warn("Decoder_Interface_Decode not initialized.")},Encoder_Interface_init:function(p){return console.warn("Encoder_Interface_init not initialized."),0},Encoder_Interface_exit:function(p){console.warn("Encoder_Interface_exit not initialized.")},Encoder_Interface_Encode:function(p,v,g,_,e){console.warn("Encoder_Interface_Encode not initialized.")},Mode:{MR475:0,MR515:1,MR59:2,MR67:3,MR74:4,MR795:5,MR102:6,MR122:7,MRDTX:8},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:`#!AMR
`,WAV_HEADER_SIZE:44};(L={canvas:{},print:function(p){console.log(p)},_main:function(){return k1.Decoder_Interface_init=L._Decoder_Interface_init,k1.Decoder_Interface_exit=L._Decoder_Interface_exit,k1.Decoder_Interface_Decode=L._Decoder_Interface_Decode,k1.Encoder_Interface_init=L._Encoder_Interface_init,k1.Encoder_Interface_exit=L._Encoder_Interface_exit,k1.Encoder_Interface_Encode=L._Encoder_Interface_Encode,0}})||(L=(L!==void 0?L:null)||{});var A1={};for(var D1 in L)L.hasOwnProperty(D1)&&(A1[D1]=L[D1]);var Y1=typeof window=="object",e0=typeof importScripts=="function",P0=!Y1&&!e0;if(P0)L.print||(L.print=print),typeof printErr<"u"&&(L.printErr=printErr),typeof read<"u"?L.read=read:L.read=function(){throw"no read() available (jsc?)"},L.readBinary=function(p){if(typeof readbuffer=="function")return new Uint8Array(readbuffer(p));var v=read(p,"binary");return y1(typeof v=="object"),v},typeof scriptArgs<"u"?L.arguments=scriptArgs:arguments!==void 0&&(L.arguments=arguments);else{if(!Y1&&!e0)throw"Unknown runtime environment. Where are we?";if(L.read=function(p){var v=new XMLHttpRequest;return v.open("GET",p,!1),v.send(null),v.responseText},arguments!==void 0&&(L.arguments=arguments),typeof console<"u")L.print||(L.print=function(p){console.log(p)}),L.printErr||(L.printErr=function(p){console.log(p)});else{var K0=!1;L.print||(L.print=K0&&typeof dump<"u"?function(p){dump(p)}:function(p){})}e0&&(L.load=importScripts),L.setWindowTitle===void 0&&(L.setWindowTitle=function(p){document.title=p})}function Z0(p){eval.call(null,p)}for(var D1 in!L.load&&L.read&&(L.load=function(v){Z0(L.read(v))}),L.print||(L.print=function(){}),L.printErr||(L.printErr=L.print),L.arguments||(L.arguments=[]),L.thisProgram||(L.thisProgram="./this.program"),L.print=L.print,L.printErr=L.printErr,L.preRun=[],L.postRun=[],A1)A1.hasOwnProperty(D1)&&(L[D1]=A1[D1]);var u1={setTempRet0:function(p){L0=p},getTempRet0:function(){return L0},stackSave:function(){return $0},stackRestore:function(p){$0=p},getNativeTypeSize:function(p){switch(p){case"i1":case"i8":return 1;case"i16":return 2;case"i32":case"float":return 4;case"i64":case"double":return 8;default:if(p[p.length-1]==="*")return u1.QUANTUM_SIZE;if(p[0]==="i"){var v=parseInt(p.substr(1));return y1(v%8==0),v/8}return 0}},getNativeFieldSize:function(p){return Math.max(u1.getNativeTypeSize(p),u1.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(p,v){return v==="double"||v==="i64"?7&p&&(y1((7&p)==4),p+=4):y1(!(3&p)),p},getAlignSize:function(p,v,g){return g||p!="i64"&&p!="double"?p?Math.min(v||(p?u1.getNativeFieldSize(p):0),u1.QUANTUM_SIZE):Math.min(v,8):8},dynCall:function(p,v,g){return g&&g.length?(g.splice||(g=Array.prototype.slice.call(g)),g.splice(0,0,v),L["dynCall_"+p].apply(null,g)):L["dynCall_"+p].call(null,v)},functionPointers:[],addFunction:function(p){for(var v=0;v<u1.functionPointers.length;v++)if(!u1.functionPointers[v])return u1.functionPointers[v]=p,2*(1+v);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(p){u1.functionPointers[(p-2)/2]=null},warnOnce:function(p){u1.warnOnce.shown||(u1.warnOnce.shown={}),u1.warnOnce.shown[p]||(u1.warnOnce.shown[p]=1,L.printErr(p))},funcWrappers:{},getFuncWrapper:function(p,v){y1(v),u1.funcWrappers[v]||(u1.funcWrappers[v]={});var g=u1.funcWrappers[v];return g[p]||(g[p]=function(){return u1.dynCall(v,p,arguments)}),g[p]},getCompilerSetting:function(p){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(p){var v=$0;return $0=15+($0=$0+p|0)&-16,v},staticAlloc:function(p){var v=M2;return M2=15+(M2=M2+p|0)&-16,v},dynamicAlloc:function(p){var v=J0;return(J0=15+(J0=J0+p|0)&-16)>=w2&&!J4()?(J0=v,0):v},alignMemory:function(p,v){return p=Math.ceil(p/(v||16))*(v||16)},makeBigInt:function(p,v,g){return g?+(p>>>0)+4294967296*+(v>>>0):+(p>>>0)+4294967296*+(0|v)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};L.Runtime=u1;var J1,c2,L0,Q0,S2,g1=!1;function y1(p,v){p||k2("Assertion failed: "+v)}function C1(p){var v=L["_"+p];if(!v)try{v=[eval][0]("_"+p)}catch{}return y1(v,"Cannot call unknown function "+p+" (perhaps LLVM optimizations or closure removed it?)"),v}function i0(p,v,g,_){switch((g=g||"i8").charAt(g.length-1)==="*"&&(g="i32"),g){case"i1":case"i8":V0[0|p]=v;break;case"i16":h2[p>>1]=v;break;case"i32":g0[p>>2]=v;break;case"i64":c2=[v>>>0,(J1=v,+f3(J1)>=1?J1>0?(0|c3(+u3(J1/4294967296),4294967295))>>>0:~~+l3((J1-+(~~J1>>>0))/4294967296)>>>0:0)],g0[p>>2]=c2[0],g0[p+4>>2]=c2[1];break;case"float":o5[p>>2]=v;break;case"double":n5[p>>3]=v;break;default:k2("invalid type for setValue: "+g)}}function G1(p,v,g){switch((v=v||"i8").charAt(v.length-1)==="*"&&(v="i32"),v){case"i1":case"i8":return V0[0|p];case"i16":return h2[p>>1];case"i32":case"i64":return g0[p>>2];case"float":return o5[p>>2];case"double":return n5[p>>3];default:k2("invalid type for setValue: "+v)}return null}(function(){var p={stackSave:function(){u1.stackSave()},stackRestore:function(){u1.stackRestore()},arrayToC:function(B){var D=u1.stackAlloc(B.length);return R6(B,D),D},stringToC:function(B){var D=0;return B!=null&&B!==0&&M5(B,D=u1.stackAlloc(1+(B.length<<2))),D}},v={string:p.stringToC,array:p.arrayToC};S2=function(B,D,Y,Q,Z){var O=C1(B),e1=[],a1=0;if(Q)for(var R1=0;R1<Q.length;R1++){var I1=v[Y[R1]];I1?(a1===0&&(a1=u1.stackSave()),e1[R1]=I1(Q[R1])):e1[R1]=Q[R1]}var T1=O.apply(null,e1);if(D==="string"&&(T1=U2(T1)),a1!==0){if(Z&&Z.async)return void EmterpreterAsync.asyncFinalizers.push(function(){u1.stackRestore(a1)});u1.stackRestore(a1)}return T1};var g=/^function\s\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function _(B){var D=B.toString().match(g);return D?{arguments:(D=D.slice(1))[0],body:D[1],returnValue:D[2]}:{}}var e={};for(var s in p)p.hasOwnProperty(s)&&(e[s]=_(p[s]));Q0=function(B,D,Y){Y=Y||[];var Q=C1(B),Z=Y.every(function(x1){return x1==="number"}),O=D!=="string";if(O&&Z)return Q;var e1=Y.map(function(x1,H0){return"$"+H0}),a1="(function("+e1.join(",")+") {",R1=Y.length;if(!Z){a1+="var stack = "+e.stackSave.body+";";for(var I1=0;I1<R1;I1++){var T1=e1[I1],M1=Y[I1];if(M1!=="number"){var w0=e[M1+"ToC"];a1+="var "+w0.arguments+" = "+T1+";",a1+=w0.body+";",a1+=T1+"="+w0.returnValue+";"}}}return a1+="var ret = "+_(function(){return Q}).returnValue+"("+e1.join(",")+");",O||(a1+="ret = "+_(function(){return U2}).returnValue+"(ret);"),Z||(a1+=e.stackRestore.body.replace("()","(stack)")+";"),a1+="return ret})",[eval][0](a1)}})(),L.ccall=S2,L.cwrap=Q0,L.setValue=i0,L.getValue=G1;var q0=0,F4=1,D2=2,T4=3,A2=4;function C0(p,v,g,_){var e,s;typeof p=="number"?(e=!0,s=p):(e=!1,s=p.length);var B,D=typeof v=="string"?v:null;if(B=g==A2?_:[H2,u1.stackAlloc,u1.staticAlloc,u1.dynamicAlloc][g===void 0?D2:g](Math.max(s,D?1:v.length)),e){var Y;for(_=B,y1(!(3&B)),Y=B+(-4&s);_<Y;_+=4)g0[_>>2]=0;for(Y=B+s;_<Y;)V0[0|_++]=0;return B}if(D==="i8")return p.subarray||p.slice?F0.set(p,B):F0.set(new Uint8Array(p),B),B;for(var Q,Z,O,e1=0;e1<s;){var a1=p[e1];typeof a1=="function"&&(a1=u1.getFunctionIndex(a1)),(Q=D||v[e1])!==0?(Q=="i64"&&(Q="i32"),i0(B+e1,a1,Q),O!==Q&&(Z=u1.getNativeTypeSize(Q),O=Q),e1+=Z):e1++}return B}function x4(p){return b6?f5!==void 0&&!f5.called||!A5?u1.dynamicAlloc(p):H2(p):u1.staticAlloc(p)}function U2(p,v){if(v===0||!p)return"";for(var g,_=0,e=0;_|=g=F0[p+e|0],(g!=0||v)&&(e++,!v||e!=v););v||(v=e);var s="";if(_<128){for(var B,D=1024;v>0;)B=String.fromCharCode.apply(String,F0.subarray(p,p+Math.min(v,D))),s=s?s+B:B,p+=D,v-=D;return s}return L.UTF8ToString(p)}function B4(p){for(var v="";;){var g=V0[0|p++];if(!g)return v;v+=String.fromCharCode(g)}}function U4(p,v){return S6(p,v,!1)}function d2(p,v){for(var g,_,e,s,B,D="";;){if(!(g=p[v++]))return D;if(128&g)if(_=63&p[v++],(224&g)!=192)if(e=63&p[v++],(240&g)==224?g=(15&g)<<12|_<<6|e:(s=63&p[v++],(248&g)==240?g=(7&g)<<18|_<<12|e<<6|s:(B=63&p[v++],g=(252&g)==248?(3&g)<<24|_<<18|e<<12|s<<6|B:(1&g)<<30|_<<24|e<<18|s<<12|B<<6|63&p[v++])),g<65536)D+=String.fromCharCode(g);else{var Y=g-65536;D+=String.fromCharCode(55296|Y>>10,56320|1023&Y)}else D+=String.fromCharCode((31&g)<<6|_);else D+=String.fromCharCode(g)}}function z4(p){return d2(F0,p)}function t5(p,v,g,_){if(!(_>0))return 0;for(var e=g,s=g+_-1,B=0;B<p.length;++B){var D=p.charCodeAt(B);if(D>=55296&&D<=57343&&(D=65536+((1023&D)<<10)|1023&p.charCodeAt(++B)),D<=127){if(g>=s)break;v[g++]=D}else if(D<=2047){if(g+1>=s)break;v[g++]=192|D>>6,v[g++]=128|63&D}else if(D<=65535){if(g+2>=s)break;v[g++]=224|D>>12,v[g++]=128|D>>6&63,v[g++]=128|63&D}else if(D<=2097151){if(g+3>=s)break;v[g++]=240|D>>18,v[g++]=128|D>>12&63,v[g++]=128|D>>6&63,v[g++]=128|63&D}else if(D<=67108863){if(g+4>=s)break;v[g++]=248|D>>24,v[g++]=128|D>>18&63,v[g++]=128|D>>12&63,v[g++]=128|D>>6&63,v[g++]=128|63&D}else{if(g+5>=s)break;v[g++]=252|D>>30,v[g++]=128|D>>24&63,v[g++]=128|D>>18&63,v[g++]=128|D>>12&63,v[g++]=128|D>>6&63,v[g++]=128|63&D}}return v[g]=0,g-e}function j4(p,v,g){return t5(p,F0,v,g)}function g5(p){for(var v=0,g=0;g<p.length;++g){var _=p.charCodeAt(g);_>=55296&&_<=57343&&(_=65536+((1023&_)<<10)|1023&p.charCodeAt(++g)),_<=127?++v:v+=_<=2047?2:_<=65535?3:_<=2097151?4:_<=67108863?5:6}return v}function q4(p){for(var v=0,g="";;){var _=h2[p+2*v>>1];if(_==0)return g;++v,g+=String.fromCharCode(_)}}function V4(p,v,g){if(g===void 0&&(g=2147483647),g<2)return 0;for(var _=v,e=(g-=2)<2*p.length?g/2:p.length,s=0;s<e;++s){var B=p.charCodeAt(s);h2[v>>1]=B,v+=2}return h2[v>>1]=0,v-_}function H4(p){return 2*p.length}function W4(p){for(var v=0,g="";;){var _=g0[p+4*v>>2];if(_==0)return g;if(++v,_>=65536){var e=_-65536;g+=String.fromCharCode(55296|e>>10,56320|1023&e)}else g+=String.fromCharCode(_)}}function X4(p,v,g){if(g===void 0&&(g=2147483647),g<4)return 0;for(var _=v,e=_+g-4,s=0;s<p.length;++s){var B=p.charCodeAt(s);if(B>=55296&&B<=57343&&(B=65536+((1023&B)<<10)|1023&p.charCodeAt(++s)),g0[v>>2]=B,(v+=4)+4>e)break}return g0[v>>2]=0,v-_}function Y4(p){for(var v=0,g=0;g<p.length;++g){var _=p.charCodeAt(g);_>=55296&&_<=57343&&++g,v+=4}return v}function G4(p){var v=!!L.___cxa_demangle;if(v)try{var g=H2(p.length);M5(p.substr(1),g);var _=H2(4),e=L.___cxa_demangle(g,0,0,_);if(G1(_,"i32")===0&&e)return U2(e)}catch{}finally{g&&N5(g),_&&N5(_),e&&N5(e)}var s=3,B={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},D=[],Y=!0;function Q(){s++,p[s]==="K"&&s++;for(var e1=[];p[s]!=="E";)if(p[s]!=="S")if(p[s]!=="C"){var a1=parseInt(p.substr(s)),R1=a1.toString().length;if(!a1||!R1){s--;break}var I1=p.substr(s+R1,a1);e1.push(I1),D.push(I1),s+=R1+a1}else e1.push(e1[e1.length-1]),s+=2;else{s++;var T1=p.indexOf("_",s),M1=p.substring(s,T1)||0;e1.push(D[M1]||"?"),s=T1+1}return s++,e1}function Z(e1,a1,R1){a1=a1||1/0;var I1,T1="",M1=[];function w0(){return"("+M1.join(", ")+")"}if(p[s]==="N"){if(I1=Q().join("::"),--a1==0)return e1?[I1]:I1}else if((p[s]==="K"||Y&&p[s]==="L")&&s++,D0=parseInt(p.substr(s))){var x1=D0.toString().length;I1=p.substr(s+x1,D0),s+=x1+D0}if(Y=!1,p[s]==="I"){s++;var H0=Z(!0);T1+=Z(!0,1,!0)[0]+" "+I1+"<"+H0.join(", ")+">"}else T1=I1;e:for(;s<p.length&&a1-- >0;){var T0=p[s++];if(T0 in B)M1.push(B[T0]);else switch(T0){case"P":M1.push(Z(!0,1,!0)[0]+"*");break;case"R":M1.push(Z(!0,1,!0)[0]+"&");break;case"L":s++;var D0=p.indexOf("E",s)-s;M1.push(p.substr(s,D0)),s+=D0+2;break;case"A":if(D0=parseInt(p.substr(s)),s+=D0.toString().length,p[s]!=="_")throw"?";s++,M1.push(Z(!0,1,!0)[0]+" ["+D0+"]");break;case"E":break e;default:T1+="?"+T0;break e}}return R1||M1.length!==1||M1[0]!=="void"||(M1=[]),e1?(T1&&M1.push(T1+"?"),M1):T1+w0()}var O=p;try{if(p=="Object._main"||p=="_main")return"main()";if(typeof p=="number"&&(p=U2(p)),p[0]!=="_"||p[1]!=="_"||p[2]!=="Z")return p;switch(p[3]){case"n":return"operator new()";case"d":return"operator delete()"}O=Z()}catch{O+="?"}return O.indexOf("?")>=0&&!v&&u1.warnOnce("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),O}function K4(p){return p.replace(/__Z[\w\d_]+/g,function(v){var g=G4(v);return v===g?v:v+" ["+g+"]"})}function Z4(){var p=new Error;if(!p.stack){try{throw new Error(0)}catch(v){p=v}if(!p.stack)return"(no stack trace available)"}return p.stack.toString()}function y5(){return K4(Z4())}L.ALLOC_NORMAL=q0,L.ALLOC_STACK=F4,L.ALLOC_STATIC=D2,L.ALLOC_DYNAMIC=T4,L.ALLOC_NONE=A2,L.allocate=C0,L.getMemory=x4,L.Pointer_stringify=U2,L.AsciiToString=B4,L.stringToAscii=U4,L.UTF8ArrayToString=d2,L.UTF8ToString=z4,L.stringToUTF8Array=t5,L.stringToUTF8=j4,L.lengthBytesUTF8=g5,L.UTF16ToString=q4,L.stringToUTF16=V4,L.lengthBytesUTF16=H4,L.UTF32ToString=W4,L.stringToUTF32=X4,L.lengthBytesUTF32=Y4,L.stackTrace=y5;var Q4,V0,F0,h2,p6,g0,r5,o5,n5,k6=4096;function $4(p){return p%4096>0&&(p+=4096-p%4096),p}var M2=0,b6=!1,v6=0,$0=0,_5=0,J0=0;function J4(){k2("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+w2+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs.")}for(var z0,R5=L.TOTAL_STACK||65536,w2=L.TOTAL_MEMORY||524288,e2=65536;e2<w2||e2<2*R5;)e2<16777216?e2*=2:e2+=16777216;function z2(p){for(;p.length>0;){var v=p.shift();if(typeof v!="function"){var g=v.func;typeof g=="number"?v.arg===void 0?u1.dynCall("v",g):u1.dynCall("vi",g,[v.arg]):g(v.arg===void 0?null:v.arg)}else v()}}e2!==w2&&(L.printErr("increasing TOTAL_MEMORY to "+e2+" to be compliant with the asm.js spec (and given that TOTAL_STACK="+R5+")"),w2=e2),y1(typeof Int32Array<"u"&&typeof Float64Array<"u"&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support"),z0=new ArrayBuffer(w2),V0=new Int8Array(z0),h2=new Int16Array(z0),g0=new Int32Array(z0),F0=new Uint8Array(z0),p6=new Uint16Array(z0),r5=new Uint32Array(z0),o5=new Float32Array(z0),n5=new Float64Array(z0),g0[0]=255,y1(F0[0]===255&&F0[3]===0,"Typed arrays 2 must be run on a little-endian system"),L.HEAP=Q4,L.buffer=z0,L.HEAP8=V0,L.HEAP16=h2,L.HEAP32=g0,L.HEAPU8=F0,L.HEAPU16=p6,L.HEAPU32=r5,L.HEAPF32=o5,L.HEAPF64=n5;var S5=[],j2=[],D5=[],a5=[],E6=[],A5=!1;function e3(){if(L.preRun)for(typeof L.preRun=="function"&&(L.preRun=[L.preRun]);L.preRun.length;)y6(L.preRun.shift());z2(S5)}function g6(){A5||(A5=!0,z2(j2))}function i3(){z2(D5)}function t3(){z2(a5)}function r3(){if(L.postRun)for(typeof L.postRun=="function"&&(L.postRun=[L.postRun]);L.postRun.length;)_6(L.postRun.shift());z2(E6)}function y6(p){S5.unshift(p)}function o3(p){j2.unshift(p)}function n3(p){D5.unshift(p)}function a3(p){a5.unshift(p)}function _6(p){E6.unshift(p)}function m2(p,v,g){var _=g>0?g:g5(p)+1,e=new Array(_),s=t5(p,e,0,e.length);return v&&(e.length=s),e}function s3(p){for(var v=[],g=0;g<p.length;g++){var _=p[g];_>255&&(_&=255),v.push(String.fromCharCode(_))}return v.join("")}function M5(p,v,g){for(var _=m2(p,g),e=0;e<_.length;){var s=_[e];V0[v+e|0]=s,e+=1}}function R6(p,v){for(var g=0;g<p.length;g++)V0[0|v++]=p[g]}function S6(p,v,g){for(var _=0;_<p.length;++_)V0[0|v++]=p.charCodeAt(_);g||(V0[0|v]=0)}L.addOnPreRun=y6,L.addOnInit=o3,L.addOnPreMain=n3,L.addOnExit=a3,L.addOnPostRun=_6,L.intArrayFromString=m2,L.intArrayToString=s3,L.writeStringToMemory=M5,L.writeArrayToMemory=R6,L.writeAsciiToMemory=S6,Math.imul&&Math.imul(4294967295,5)===-5||(Math.imul=function(p,v){var g=65535&p,_=65535&v;return g*_+((p>>>16)*_+g*(v>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(p){p>>>=0;for(var v=0;v<32;v++)if(p&1<<31-v)return v;return 32}),Math.clz32=Math.clz32;var f3=Math.abs,l3=Math.ceil,u3=Math.floor,c3=Math.min,n2=0,q2=null;function P5(p){n2++,L.monitorRunDependencies&&L.monitorRunDependencies(n2)}function s5(p){if(n2--,L.monitorRunDependencies&&L.monitorRunDependencies(n2),n2==0&&q2){var v=q2;q2=null,v()}}L.addRunDependency=P5,L.removeRunDependency=s5,L.preloadedImages={},L.preloadedAudios={},M2=31784,j2.push(),C0([154,14,0,0,188,14,0,0,226,14,0,0,8,15,0,0,46,15,0,0,84,15,0,0,130,15,0,0,208,15,0,0,66,16,0,0,108,16,0,0,42,17,0,0,248,17,0,0,228,18,0,0,240,19,0,0,24,21,0,0,86,22,0,0,238,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,13,0,15,0,17,0,19,0,20,0,26,0,31,0,5,0,6,0,5,0,5,0,0,0,0,0,0,0,0,0,1,252,146,252,36,253,182,253,72,254,218,254,108,255,0,0,0,0,32,78,32,78,32,78,32,78,32,78,80,70,0,64,0,32,0,0,0,0,255,127,112,125,112,125,112,125,112,125,112,125,153,89,255,127,112,125,112,125,102,102,102,38,153,25,153,25,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,0,96,0,72,0,54,128,40,96,30,200,22,22,17,209,12,157,9,54,7,102,70,184,38,75,21,182,11,113,6,139,3,243,1,18,1,151,0,83,0,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,44,3,128,0,30,2,140,0,57,11,111,4,218,8,74,13,19,8,51,2,133,49,135,2,36,16,6,7,225,21,165,20,9,30,118,1,151,14,185,1,160,42,78,10,31,46,190,9,10,80,29,3,98,20,163,2,68,26,162,32,162,20,160,6,208,5,172,1,250,22,196,1,212,20,232,15,255,13,244,4,165,9,133,3,22,62,237,3,134,58,199,12,91,40,250,18,51,14,229,7,36,10,67,3,72,48,28,19,174,47,168,6,120,52,68,6,158,35,37,9,128,15,2,6,103,21,208,38,211,14,161,1,79,5,158,1,56,14,33,6,59,31,213,13,141,44,133,2,104,33,123,2,216,15,97,5,224,64,236,23,156,44,188,2,215,7,95,2,127,48,42,6,111,43,46,18,112,53,172,6,214,46,205,4,60,31,129,28,175,51,83,22,124,9,135,4,25,8,149,7,74,24,233,23,218,13,12,7,221,34,10,7,231,33,44,6,111,54,248,13,1,52,93,24,254,23,106,4,106,23,198,6,61,55,54,18,7,44,249,12,194,47,15,6,107,54,199,11,217,19,224,40,228,36,50,26,153,6,171,2,156,5,26,5,44,28,93,15,242,15,153,10,113,30,192,2,222,58,34,3,155,24,92,20,241,16,237,20,20,26,29,2,174,23,114,2,83,53,116,14,234,44,104,9,28,63,204,2,145,47,239,2,129,31,225,44,170,24,208,8,114,17,240,1,125,28,11,2,229,39,249,14,202,32,221,11,211,32,198,3,148,55,88,7,255,33,33,21,11,64,255,18,252,28,187,7,201,23,206,4,155,36,46,17,222,56,35,13,247,52,57,11,107,51,185,5,158,21,142,6,82,51,179,57,170,28,88,2,38,5,36,2,156,16,211,13,60,39,60,9,91,41,110,2,32,51,157,2,46,55,198,13,175,19,56,38,234,59,107,2,43,12,78,2,58,64,197,11,182,60,72,16,177,60,75,6,45,60,204,4,151,62,83,36,110,29,112,19,198,7,189,4,183,44,133,4,224,48,143,21,3,37,84,10,36,30,242,7,224,51,191,8,139,62,229,19,130,31,105,26,99,39,133,5,138,19,43,9,235,48,87,23,22,59,83,11,88,71,241,8,211,61,223,9,137,63,14,40,59,57,55,44,5,7,81,1,43,12,141,1,182,13,112,11,240,17,110,10,95,29,116,2,151,44,144,2,58,23,131,9,144,25,199,28,46,32,61,3,160,15,95,3,48,39,188,9,185,62,223,13,28,71,30,4,215,23,174,5,252,22,220,30,64,73,140,13,72,7,32,2,238,35,171,2,103,45,64,16,242,17,108,6,86,12,133,4,81,62,0,10,61,48,149,14,12,68,140,20,218,23,212,7,101,11,206,6,83,64,137,20,147,65,144,6,53,67,223,6,165,18,159,12,218,28,147,23,6,56,28,39,195,15,186,1,98,16,202,1,254,35,194,8,3,29,121,16,60,50,33,3,178,43,57,3,104,49,36,8,156,50,154,25,33,37,228,3,229,25,217,3,41,41,198,9,185,59,142,19,58,49,7,8,124,60,117,6,66,63,9,27,151,55,158,22,66,10,60,3,239,21,150,6,95,53,146,22,84,14,18,6,49,44,73,10,42,38,179,5,179,54,125,18,25,62,147,24,134,24,78,7,230,30,237,8,82,66,219,17,192,64,9,15,144,59,7,9,151,62,172,12,123,56,144,69,71,46,203,10,189,7,127,5,120,5,108,3,239,16,219,13,39,17,114,16,29,21,168,2,53,68,13,3,101,25,254,19,155,31,253,29,187,28,26,3,141,32,158,4,193,58,88,12,80,58,223,11,197,79,112,3,209,56,84,3,49,48,116,57,248,26,128,7,129,16,165,3,26,32,63,4,163,41,244,15,98,39,181,17,175,10,72,3,177,80,57,4,71,65,78,23,1,62,226,17,119,42,14,10,189,14,142,4,183,56,204,15,219,80,67,10,115,59,174,10,170,59,138,8,113,24,154,12,69,51,24,76,28,28,162,3,158,9,82,6,163,17,20,12,28,54,181,16,220,40,65,3,187,67,42,3,251,65,241,8,186,60,25,32,35,53,148,6,125,12,42,7,76,62,4,11,196,61,207,20,110,66,134,9,148,65,46,5,55,61,220,31,206,45,108,33,178,14,5,8,91,37,37,5,249,52,134,26,195,47,144,7,244,31,222,13,231,51,242,6,171,63,199,25,163,63,78,30,73,33,247,9,57,28,85,10,93,71,65,29,245,65,200,8,218,69,68,11,113,67,0,13,201,36,194,78,34,43,128,32,6,5,108,2,151,5,71,2,105,23,241,8,138,15,42,14,24,20,240,2,97,52,62,3,177,21,44,11,244,45,20,23,241,41,48,2,70,21,52,2,9,52,192,11,170,46,99,14,175,77,30,3,97,38,216,2,95,53,44,34,223,28,237,11,211,9,10,3,162,23,65,3,69,25,210,19,113,32,159,9,253,23,73,7,204,59,238,4,72,56,195,17,95,53,163,17,65,12,167,11,175,9,235,4,240,58,39,18,22,60,47,10,156,56,88,9,174,48,233,9,115,29,133,11,109,50,28,47,92,21,172,2,69,12,210,2,217,19,250,4,188,49,104,16,198,59,169,2,139,30,80,2,134,25,229,7,94,64,33,34,52,52,114,3,21,21,131,3,64,57,130,8,149,57,131,16,190,55,18,5,105,54,237,7,117,60,58,29,199,61,220,17,217,9,221,7,198,19,12,7,39,20,182,25,218,27,13,14,168,42,75,6,209,45,172,6,7,66,127,13,140,63,240,25,90,36,239,3,153,36,58,8,238,74,173,19,153,48,173,16,47,62,52,5,253,59,184,13,122,46,61,55,229,62,198,26,218,7,225,2,195,14,93,3,190,44,64,11,236,13,212,13,97,35,217,4,103,48,128,3,98,33,21,18,41,45,144,22,193,31,77,2,26,32,76,2,40,73,171,14,173,50,77,12,113,61,246,2,250,64,242,2,118,59,130,43,255,61,160,8,65,18,98,2,234,39,166,2,153,59,50,16,97,22,255,12,185,32,134,6,150,77,17,9,90,60,135,21,230,54,105,21,96,22,72,11,156,29,66,5,48,56,205,20,108,63,110,15,14,59,160,14,202,59,155,5,5,57,230,15,13,48,80,61,193,29,163,6,122,8,116,3,107,17,215,17,174,70,234,12,198,49,47,3,78,58,139,3,168,58,185,16,158,60,176,32,74,70,63,4,54,9,97,3,153,63,203,14,63,61,244,17,228,63,254,5,200,64,162,8,193,65,225,37,57,62,161,17,205,12,61,4,171,37,139,8,197,46,180,23,239,35,110,17,251,34,93,6,49,40,246,11,97,64,35,20,106,60,154,27,110,53,239,9,153,20,229,8,106,65,69,24,15,65,80,13,80,79,35,13,0,73,193,7,92,55,67,50,50,59,87,61,121,17,252,3,145,6,118,3,215,16,205,16,248,34,73,14,5,23,123,4,127,45,172,5,14,62,179,8,230,17,244,25,17,27,181,4,76,24,31,3,127,48,81,13,96,62,37,15,147,77,61,8,217,37,93,8,150,57,126,34,144,56,39,10,25,7,214,4,91,30,45,3,135,74,58,17,178,21,16,8,103,14,28,11,27,68,208,8,57,65,134,17,71,63,12,21,92,31,203,10,77,13,71,8,18,68,101,21,130,53,226,10,167,77,160,10,138,35,40,15,252,70,225,18,184,67,175,47,252,19,228,3,71,19,220,3,160,38,9,12,126,23,251,20,9,62,131,6,213,32,159,4,239,58,62,9,65,77,90,27,187,46,26,6,111,28,104,4,219,65,252,5,146,61,5,21,116,57,17,8,137,78,107,8,6,67,53,32,247,69,174,24,91,21,224,5,4,16,14,10,13,68,154,26,41,22,72,11,252,64,54,13,15,35,39,7,191,78,129,18,94,76,126,28,2,26,221,10,208,44,249,12,197,75,190,19,190,73,114,18,55,64,69,9,206,79,34,17,89,44,158,103,73,45,252,11,50,11,30,6,244,19,46,4,142,37,51,19,75,19,208,13,117,29,110,3,237,80,83,3,26,27,43,17,159,65,53,30,153,39,251,3,117,38,196,3,134,60,115,15,99,60,102,13,175,73,214,3,152,78,195,3,236,65,87,50,254,55,104,16,199,25,196,4,6,36,46,3,46,66,14,20,29,22,34,19,112,21,6,7,34,79,122,15,109,66,34,24,9,70,41,23,149,36,92,13,50,29,179,7,81,76,57,20,59,74,190,11,70,64,204,14,198,62,63,9,216,33,183,10,229,36,246,102,104,42,7,5,227,13,241,3,230,21,38,14,253,75,136,21,165,48,29,3,154,80,143,3,67,60,250,11,141,66,35,40,195,73,73,10,73,15,244,4,63,76,43,13,132,70,110,20,91,75,142,6,52,76,100,12,152,70,2,42,241,64,189,26,62,12,250,8,117,42,133,9,220,60,1,27,53,49,53,13,108,43,225,12,122,65,120,9,165,73,59,26,19,67,159,38,199,49,45,10,233,34,68,12,89,74,84,30,171,71,40,15,251,79,98,14,146,76,52,13,244,50,173,75,30,41,84,90,1,0,3,0,0,0,1,0,2,0,4,0,82,120,26,113,81,106,240,99,241,93,78,88,2,83,7,78,89,73,242,68,51,115,174,103,80,93,251,83,149,75,6,68,56,61,25,55,150,49,161,44,205,76,21,46,166,27,151,16,244,9,249,5,149,3,38,2,74,1,198,0,249,79,26,80,59,80,92,80,125,80,164,80,197,80,236,80,13,81,52,81,85,81,124,81,157,81,196,81,236,81,19,82,58,82,97,82,137,82,176,82,215,82,255,82,38,83,84,83,123,83,169,83,208,83,254,83,38,84,84,84,129,84,175,84,221,84,11,85,57,85,103,85,149,85,201,85,247,85,43,86,89,86,142,86,194,86,247,86,43,87,95,87,148,87,200,87,3,88,56,88,115,88,174,88,233,88,36,89,95,89,154,89,219,89,22,90,88,90,153,90,212,90,28,91,94,91,159,91,231,91,48,92,113,92,192,92,8,93,80,93,159,93,237,93,60,94,138,94,224,94,46,95,131,95,217,95,52,96,138,96,229,96,72,97,163,97,6,98,104,98,209,98,51,99,156,99,11,100,123,100,234,100,96,101,214,101,76,102,201,102,76,103,207,103,82,104,220,104,108,105,252,105,147,106,48,107,205,107,113,108,27,109,204,109,125,110,59,111,249,111,197,112,150,113,111,114,84,115,64,116,50,117,50,118,63,119,88,120,225,122,255,127,255,127,255,127,255,127,255,127,255,127,255,127,225,122,88,120,63,119,50,118,50,117,64,116,84,115,111,114,150,113,197,112,249,111,59,111,125,110,204,109,27,109,113,108,205,107,48,107,147,106,252,105,108,105,220,104,82,104,207,103,76,103,201,102,76,102,214,101,96,101,234,100,123,100,11,100,156,99,51,99,209,98,104,98,6,98,163,97,72,97,229,96,138,96,52,96,217,95,131,95,46,95,224,94,138,94,60,94,237,93,159,93,80,93,8,93,192,92,113,92,48,92,231,91,159,91,94,91,28,91,212,90,153,90,88,90,22,90,219,89,154,89,95,89,36,89,233,88,174,88,115,88,56,88,3,88,200,87,148,87,95,87,43,87,247,86,194,86,142,86,89,86,43,86,247,85,201,85,149,85,103,85,57,85,11,85,221,84,175,84,129,84,84,84,38,84,254,83,208,83,169,83,123,83,84,83,38,83,255,82,215,82,176,82,137,82,97,82,58,82,19,82,236,81,196,81,157,81,124,81,85,81,52,81,13,81,236,80,197,80,164,80,125,80,92,80,59,80,26,80,249,79,210,79,177,79,145,79,112,79,13,0,14,0,16,0,18,0,20,0,21,0,27,0,32,0,6,0,7,0,6,0,6,0,0,0,0,0,0,0,1,0,13,0,14,0,16,0,18,0,19,0,21,0,26,0,31,0,6,0,6,0,6,0,6,0,0,0,0,0,0,0,1,0,79,115,156,110,74,97,126,77,72,54,9,31,195,10,153,251,125,242,48,239,127,240,173,244,231,249,176,254,22,2,202,3,255,3,55,3,4,2,220,0,0,0,125,255,62,255,41,255,0,0,216,127,107,127,182,126,187,125,123,124,248,122,53,121,53,119,250,116,137,114,128,46,128,67,0,120,0,101,128,94,64,113,64,95,192,28,64,76,192,57,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,10,0,19,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,94,0,0,0,253,255,3,0,3,0,6,0,5,0,9,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,19,0,19,0,19,0,19,0,23,0,39,0,57,0,5,0,8,0,8,0,7,0,8,0,7,0,2,0,8,0,4,0,7,0,2,0,4,0,7,0,2,0,8,0,4,0,7,0,2,0,8,0,8,0,7,0,8,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,8,0,9,0,9,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,9,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,9,0,9,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,9,0,9,0,9,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,9,0,9,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,7,0,8,0,9,0,8,0,6,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,3,0,8,0,9,0,9,0,6,0,95,0,103,0,118,0,134,0,148,0,159,0,204,0,244,0,39,0,43,0,38,0,37,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,23,0,24,0,25,0,26,0,27,0,28,0,48,0,49,0,61,0,62,0,82,0,83,0,47,0,46,0,45,0,44,0,81,0,80,0,79,0,78,0,17,0,18,0,20,0,22,0,77,0,76,0,75,0,74,0,29,0,30,0,43,0,42,0,41,0,40,0,38,0,39,0,16,0,19,0,21,0,50,0,51,0,59,0,60,0,63,0,64,0,72,0,73,0,84,0,85,0,93,0,94,0,32,0,33,0,35,0,36,0,53,0,54,0,56,0,57,0,66,0,67,0,69,0,70,0,87,0,88,0,90,0,91,0,34,0,55,0,68,0,89,0,37,0,58,0,71,0,92,0,31,0,52,0,65,0,86,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,23,0,24,0,25,0,26,0,27,0,46,0,65,0,84,0,45,0,44,0,43,0,64,0,63,0,62,0,83,0,82,0,81,0,102,0,101,0,100,0,42,0,61,0,80,0,99,0,28,0,47,0,66,0,85,0,18,0,41,0,60,0,79,0,98,0,29,0,48,0,67,0,17,0,20,0,22,0,40,0,59,0,78,0,97,0,21,0,30,0,49,0,68,0,86,0,19,0,16,0,87,0,39,0,38,0,58,0,57,0,77,0,35,0,54,0,73,0,92,0,76,0,96,0,95,0,36,0,55,0,74,0,93,0,32,0,51,0,33,0,52,0,70,0,71,0,89,0,90,0,31,0,50,0,69,0,88,0,37,0,56,0,75,0,94,0,34,0,53,0,72,0,91,0,0,0,1,0,4,0,5,0,3,0,6,0,7,0,2,0,13,0,15,0,8,0,9,0,11,0,12,0,14,0,10,0,16,0,28,0,74,0,29,0,75,0,27,0,73,0,26,0,72,0,30,0,76,0,51,0,97,0,50,0,71,0,96,0,117,0,31,0,77,0,52,0,98,0,49,0,70,0,95,0,116,0,53,0,99,0,32,0,78,0,33,0,79,0,48,0,69,0,94,0,115,0,47,0,68,0,93,0,114,0,46,0,67,0,92,0,113,0,19,0,21,0,23,0,22,0,18,0,17,0,20,0,24,0,111,0,43,0,89,0,110,0,64,0,65,0,44,0,90,0,25,0,45,0,66,0,91,0,112,0,54,0,100,0,40,0,61,0,86,0,107,0,39,0,60,0,85,0,106,0,36,0,57,0,82,0,103,0,35,0,56,0,81,0,102,0,34,0,55,0,80,0,101,0,42,0,63,0,88,0,109,0,41,0,62,0,87,0,108,0,38,0,59,0,84,0,105,0,37,0,58,0,83,0,104,0,0,0,1,0,4,0,3,0,5,0,6,0,13,0,7,0,2,0,8,0,9,0,11,0,15,0,12,0,14,0,10,0,28,0,82,0,29,0,83,0,27,0,81,0,26,0,80,0,30,0,84,0,16,0,55,0,109,0,56,0,110,0,31,0,85,0,57,0,111,0,48,0,73,0,102,0,127,0,32,0,86,0,51,0,76,0,105,0,130,0,52,0,77,0,106,0,131,0,58,0,112,0,33,0,87,0,19,0,23,0,53,0,78,0,107,0,132,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,50,0,75,0,104,0,129,0,47,0,72,0,101,0,126,0,54,0,79,0,108,0,133,0,46,0,71,0,100,0,125,0,128,0,103,0,74,0,49,0,45,0,70,0,99,0,124,0,42,0,67,0,96,0,121,0,39,0,64,0,93,0,118,0,38,0,63,0,92,0,117,0,35,0,60,0,89,0,114,0,34,0,59,0,88,0,113,0,44,0,69,0,98,0,123,0,43,0,68,0,97,0,122,0,41,0,66,0,95,0,120,0,40,0,65,0,94,0,119,0,37,0,62,0,91,0,116,0,36,0,61,0,90,0,115,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,16,0,26,0,87,0,27,0,88,0,28,0,89,0,29,0,90,0,30,0,91,0,51,0,80,0,112,0,141,0,52,0,81,0,113,0,142,0,54,0,83,0,115,0,144,0,55,0,84,0,116,0,145,0,58,0,119,0,59,0,120,0,21,0,22,0,23,0,17,0,18,0,19,0,31,0,60,0,92,0,121,0,56,0,85,0,117,0,146,0,20,0,24,0,25,0,50,0,79,0,111,0,140,0,57,0,86,0,118,0,147,0,49,0,78,0,110,0,139,0,48,0,77,0,53,0,82,0,114,0,143,0,109,0,138,0,47,0,76,0,108,0,137,0,32,0,33,0,61,0,62,0,93,0,94,0,122,0,123,0,41,0,42,0,43,0,44,0,45,0,46,0,70,0,71,0,72,0,73,0,74,0,75,0,102,0,103,0,104,0,105,0,106,0,107,0,131,0,132,0,133,0,134,0,135,0,136,0,34,0,63,0,95,0,124,0,35,0,64,0,96,0,125,0,36,0,65,0,97,0,126,0,37,0,66,0,98,0,127,0,38,0,67,0,99,0,128,0,39,0,68,0,100,0,129,0,40,0,69,0,101,0,130,0,8,0,7,0,6,0,5,0,4,0,3,0,2,0,14,0,16,0,9,0,10,0,12,0,13,0,15,0,11,0,17,0,20,0,22,0,24,0,23,0,19,0,18,0,21,0,56,0,88,0,122,0,154,0,57,0,89,0,123,0,155,0,58,0,90,0,124,0,156,0,52,0,84,0,118,0,150,0,53,0,85,0,119,0,151,0,27,0,93,0,28,0,94,0,29,0,95,0,30,0,96,0,31,0,97,0,61,0,127,0,62,0,128,0,63,0,129,0,59,0,91,0,125,0,157,0,32,0,98,0,64,0,130,0,1,0,0,0,25,0,26,0,33,0,99,0,34,0,100,0,65,0,131,0,66,0,132,0,54,0,86,0,120,0,152,0,60,0,92,0,126,0,158,0,55,0,87,0,121,0,153,0,117,0,116,0,115,0,46,0,78,0,112,0,144,0,43,0,75,0,109,0,141,0,40,0,72,0,106,0,138,0,36,0,68,0,102,0,134,0,114,0,149,0,148,0,147,0,146,0,83,0,82,0,81,0,80,0,51,0,50,0,49,0,48,0,47,0,45,0,44,0,42,0,39,0,35,0,79,0,77,0,76,0,74,0,71,0,67,0,113,0,111,0,110,0,108,0,105,0,101,0,145,0,143,0,142,0,140,0,137,0,133,0,41,0,73,0,107,0,139,0,37,0,69,0,103,0,135,0,38,0,70,0,104,0,136,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,16,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,26,0,27,0,28,0,29,0,30,0,31,0,115,0,116,0,117,0,118,0,119,0,120,0,72,0,73,0,161,0,162,0,65,0,68,0,69,0,108,0,111,0,112,0,154,0,157,0,158,0,197,0,200,0,201,0,32,0,33,0,121,0,122,0,74,0,75,0,163,0,164,0,66,0,109,0,155,0,198,0,19,0,23,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,37,0,36,0,35,0,34,0,80,0,79,0,78,0,77,0,126,0,125,0,124,0,123,0,169,0,168,0,167,0,166,0,70,0,67,0,71,0,113,0,110,0,114,0,159,0,156,0,160,0,202,0,199,0,203,0,76,0,165,0,81,0,82,0,92,0,91,0,93,0,83,0,95,0,85,0,84,0,94,0,101,0,102,0,96,0,104,0,86,0,103,0,87,0,97,0,127,0,128,0,138,0,137,0,139,0,129,0,141,0,131,0,130,0,140,0,147,0,148,0,142,0,150,0,132,0,149,0,133,0,143,0,170,0,171,0,181,0,180,0,182,0,172,0,184,0,174,0,173,0,183,0,190,0,191,0,185,0,193,0,175,0,192,0,176,0,186,0,38,0,39,0,49,0,48,0,50,0,40,0,52,0,42,0,41,0,51,0,58,0,59,0,53,0,61,0,43,0,60,0,44,0,54,0,194,0,179,0,189,0,196,0,177,0,195,0,178,0,187,0,188,0,151,0,136,0,146,0,153,0,134,0,152,0,135,0,144,0,145,0,105,0,90,0,100,0,107,0,88,0,106,0,89,0,98,0,99,0,62,0,47,0,57,0,64,0,45,0,63,0,46,0,55,0,56,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,23,0,15,0,16,0,17,0,18,0,19,0,20,0,21,0,22,0,24,0,25,0,26,0,27,0,28,0,38,0,141,0,39,0,142,0,40,0,143,0,41,0,144,0,42,0,145,0,43,0,146,0,44,0,147,0,45,0,148,0,46,0,149,0,47,0,97,0,150,0,200,0,48,0,98,0,151,0,201,0,49,0,99,0,152,0,202,0,86,0,136,0,189,0,239,0,87,0,137,0,190,0,240,0,88,0,138,0,191,0,241,0,91,0,194,0,92,0,195,0,93,0,196,0,94,0,197,0,95,0,198,0,29,0,30,0,31,0,32,0,33,0,34,0,35,0,50,0,100,0,153,0,203,0,89,0,139,0,192,0,242,0,51,0,101,0,154,0,204,0,55,0,105,0,158,0,208,0,90,0,140,0,193,0,243,0,59,0,109,0,162,0,212,0,63,0,113,0,166,0,216,0,67,0,117,0,170,0,220,0,36,0,37,0,54,0,53,0,52,0,58,0,57,0,56,0,62,0,61,0,60,0,66,0,65,0,64,0,70,0,69,0,68,0,104,0,103,0,102,0,108,0,107,0,106,0,112,0,111,0,110,0,116,0,115,0,114,0,120,0,119,0,118,0,157,0,156,0,155,0,161,0,160,0,159,0,165,0,164,0,163,0,169,0,168,0,167,0,173,0,172,0,171,0,207,0,206,0,205,0,211,0,210,0,209,0,215,0,214,0,213,0,219,0,218,0,217,0,223,0,222,0,221,0,73,0,72,0,71,0,76,0,75,0,74,0,79,0,78,0,77,0,82,0,81,0,80,0,85,0,84,0,83,0,123,0,122,0,121,0,126,0,125,0,124,0,129,0,128,0,127,0,132,0,131,0,130,0,135,0,134,0,133,0,176,0,175,0,174,0,179,0,178,0,177,0,182,0,181,0,180,0,185,0,184,0,183,0,188,0,187,0,186,0,226,0,225,0,224,0,229,0,228,0,227,0,232,0,231,0,230,0,235,0,234,0,233,0,238,0,237,0,236,0,96,0,199,0,0,0,2,0,0,0,3,0,0,0,2,0,0,0,3,0,1,0,3,0,2,0,4,0,1,0,4,0,1,0,4,0,0,0,205,12,156,25,0,32,102,38,205,44,0,48,51,51,102,54,154,57,205,60,0,64,51,67,102,70,154,73,205,76,159,0,64,241,53,167,206,0,190,242,52,176,12,1,67,244,88,185,93,1,201,245,133,194,163,1,215,246,223,200,226,1,166,247,189,205,42,2,116,248,147,210,125,2,66,249,109,215,221,2,18,250,77,220,74,3,222,250,30,225,201,3,174,251,0,230,90,4,124,252,216,234,1,5,74,253,179,239,193,5,25,254,141,244,158,6,231,254,104,249,156,7,181,255,67,254,193,8,133,0,33,3,17,10,83,1,252,7,147,11,33,2,213,12,80,13,240,2,178,17,79,15,190,3,140,22,155,17,141,4,104,27,63,20,91,5,67,32,72,23,41,6,29,37,199,26,248,6,249,41,203,30,199,7,212,46,105,35,149,8,175,51,185,40,100,9,138,56,222,48,113,10,224,62,135,63,244,11,253,71,150,82,120,13,27,81,93,107,252,14,57,90,93,107,252,14,57,90,0,0,1,0,3,0,2,0,6,0,4,0,5,0,7,0,0,0,1,0,3,0,2,0,5,0,6,0,4,0,7,0,248,127,211,127,76,127,108,126,51,125,163,123,188,121,127,119,239,116,12,114,217,110,89,107,141,103,121,99,31,95,130,90,166,85,141,80,60,75,182,69,0,64,28,58,15,52,223,45,141,39,32,33,156,26,6,20,97,13,178,6,0,0,78,249,159,242,250,235,100,229,224,222,115,216,33,210,241,203,228,197,0,192,74,186,196,180,115,175,90,170,126,165,225,160,135,156,115,152,167,148,39,145,244,141,17,139,129,136,68,134,93,132,205,130,148,129,180,128,45,128,8,128,255,127,46,124,174,120,118,117,125,114,186,111,41,109,194,106,131,104,102,102,105,100,137,98,194,96,19,95,122,93,245,91,130,90,33,89,207,87,139,86,85,85,44,84,15,83,252,81,244,80,246,79,1,79,20,78,48,77,83,76,126,75,175,74,231,73,37,73,104,72,178,71,0,71,84,70,173,69,10,69,107,68,209,67,59,67,168,66,25,66,142,65,6,65,130,64,0,64,0,0,175,5,50,11,140,16,192,21,207,26,188,31,136,36,53,41,196,45,55,50,143,54,206,58,245,62,4,67,252,70,223,74,174,78,105,82,17,86,167,89,44,93,159,96,3,100,87,103,155,106,209,109,250,112,20,116,33,119,34,122,23,125,255,127,255,127,217,127,98,127,157,126,138,125,42,124,125,122,133,120,66,118,182,115,227,112,202,109,110,106,208,102,242,98,215,94,130,90,246,85,52,81,64,76,29,71,206,65,87,60,186,54,252,48,31,43,40,37,26,31,249,24,200,18,140,12,72,6,0,0,184,249,116,243,56,237,7,231,230,224,216,218,225,212,4,207,70,201,169,195,50,190,227,184,192,179,204,174,10,170,126,165,41,161,14,157,48,153,146,149,54,146,29,143,74,140,190,137,123,135,131,133,214,131,118,130,99,129,158,128,39,128,0,128,249,150,148,221,53,235,27,241,93,244,116,246,223,247,237,248,184,249,86,250,214,250,61,251,148,251,221,251,26,252,78,252,123,252,163,252,197,252,227,252,252,252,18,253,38,253,55,253,69,253,81,253,91,253,100,253,106,253,111,253,114,253,116,253,116,253,114,253,111,253,106,253,100,253,91,253,81,253,69,253,55,253,38,253,18,253,252,252,227,252,197,252,163,252,123,252,78,252,26,252,221,251,148,251,61,251,214,250,86,250,184,249,237,248,223,247,116,246,93,244,27,241,53,235,148,221,249,150,48,117,144,101,8,82,152,58,64,31,0,0,192,224,104,197,248,173,112,154,153,104,33,3,201,9,85,253,154,250,70,2,92,2,6,251,183,13,250,232,182,17,13,254,108,248,195,11,62,236,238,21,58,248,219,251,77,250,90,17,68,253,41,235,1,18,196,1,179,253,232,242,137,11,243,4,68,251,226,245,195,6,86,14,133,238,49,252,39,17,23,246,181,3,173,250,45,252,102,22,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,98,57,254,44,244,4,55,245,217,233,90,29,221,255,9,245,32,244,215,18,136,11,24,223,201,14,175,5,131,8,67,222,115,31,201,247,82,250,9,3,84,4,175,246,206,8,149,254,94,253,201,247,158,23,207,233,48,4,51,12,62,236,192,20,231,246,112,241,12,27,207,240,163,2,17,249,29,0,161,39,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,0,64,103,65,213,66,76,68,203,69,82,71,226,72,122,74,28,76,199,77,123,79,56,81,255,82,209,84,172,86,146,88,130,90,126,92,132,94,150,96,180,98,221,100,18,103,84,105,162,107,254,109,102,112,221,114,96,117,242,119,147,122,66,125,255,127,3,115,186,110,119,98,225,79,109,57,245,33,71,12,184,250,206,238,23,233,38,233,191,237,33,245,96,253,187,4,232,9,58,12,175,11,211,8,146,4,0,0,23,252,140,249,180,248,126,249,133,251,48,254,218,0,244,2,36,4,75,4,136,3,38,2,135,0,11,255,254,253,134,253,166,253,61,254,25,255,0,0,191,0,52,1,84,1,40,1,198,0,78,0,220,255,136,255,93,255,91,255,124,255,177,255,237,255,34,0,73,0,91,0,89,0,70,0,38,0,0,0,254,254,194,254,73,254,134,253,112,253,251,252,57,253,10,254,244,254,63,255,254,255,125,0,122,0,217,255,247,255,105,0,129,0,27,1,116,1,63,2,235,254,188,254,59,255,25,254,67,254,150,254,220,254,229,255,177,0,31,2,86,1,5,2,4,2,130,0,27,0,152,255,136,255,116,255,182,255,200,255,204,253,81,252,16,250,59,252,210,252,242,253,190,254,254,255,159,0,145,2,200,254,228,254,126,254,171,253,19,254,242,253,94,254,27,255,105,0,193,1,211,253,154,252,205,251,105,252,74,252,16,253,59,253,196,254,62,0,230,1,198,254,65,255,53,255,182,254,96,255,153,255,205,255,131,0,82,1,3,2,10,6,224,8,194,14,112,21,60,27,190,32,63,39,221,43,222,49,146,53,84,37,17,42,27,49,236,51,45,56,131,45,92,41,39,38,145,33,84,25,6,0,82,0,125,255,154,0,200,255,33,253,183,0,191,255,247,254,9,0,46,255,151,254,113,0,206,2,25,7,242,3,190,4,37,6,89,3,53,5,228,8,59,3,32,6,141,7,205,2,197,7,158,8,70,3,148,4,31,7,209,2,232,3,106,8,30,1,220,1,229,5,9,255,237,253,230,0,147,0,174,255,57,2,26,0,79,255,80,252,229,255,239,254,180,2,92,255,248,254,73,255,224,0,22,3,15,4,131,3,178,3,89,2,229,1,3,3,126,4,12,2,165,2,135,3,116,255,119,1,10,3,154,1,164,2,173,1,45,1,18,2,241,3,207,2,134,2,38,0,226,0,111,1,40,0,145,0,211,255,7,254,34,1,121,0,135,255,46,1,127,0,166,0,132,255,129,254,68,252,154,254,57,254,47,252,203,2,110,3,126,3,210,3,155,3,211,0,221,1,16,1,64,0,188,0,178,255,17,0,113,255,191,255,38,0,131,2,74,2,109,2,122,255,86,254,117,253,91,1,33,2,4,11,164,4,166,10,138,9,142,0,176,255,199,6,27,1,130,0,205,1,250,254,113,254,135,251,101,254,155,0,174,1,73,1,119,1,11,3,53,0,30,255,117,255,127,255,20,255,146,6,29,1,232,2,47,5,226,2,185,2,128,6,56,1,153,1,10,1,69,1,208,2,135,0,1,0,221,0,197,1,8,0,203,0,145,0,43,1,128,2,248,2,29,0,212,1,126,2,103,0,173,1,123,1,164,1,186,3,164,3,46,5,186,4,234,4,192,2,244,3,128,4,90,255,68,254,246,254,196,254,126,255,136,254,191,0,127,4,112,7,16,255,225,253,20,251,144,255,12,1,183,4,70,0,38,4,47,6,22,1,80,5,38,6,254,254,240,254,0,253,19,0,51,2,192,8,253,255,247,254,135,0,217,254,177,253,124,254,140,0,98,1,50,255,252,254,8,254,229,252,79,254,50,253,217,250,109,0,75,1,194,3,83,254,169,255,140,2,216,254,170,1,251,3,17,255,7,3,83,3,233,1,54,5,49,4,178,254,180,254,25,0,31,2,182,4,15,7,70,1,61,0,215,2,66,2,81,3,125,5,48,255,235,254,73,1,104,255,64,0,157,2,78,254,90,253,41,253,58,254,185,255,251,0,93,2,224,1,254,0,30,254,11,0,228,3,223,254,139,1,230,1,210,2,25,4,160,5,226,255,196,254,238,252,150,255,141,255,149,253,93,3,194,5,132,5,31,4,86,5,160,4,44,3,213,4,157,3,42,0,5,255,192,253,86,1,141,0,58,254,88,255,176,255,79,5,170,254,112,253,29,249,100,0,53,3,213,2,222,3,235,2,32,3,76,1,184,1,56,2,151,2,123,1,84,3,112,0,165,0,143,254,85,2,142,3,26,1,248,255,66,3,1,5,160,254,60,2,183,2,206,1,198,8,14,7,89,1,190,0,94,5,160,1,147,3,118,8,168,0,174,255,24,1,252,253,66,254,72,3,47,0,21,2,44,0,150,254,57,253,137,251,22,0,193,0,192,5,171,255,233,0,21,7,194,255,67,2,224,5,38,2,176,3,213,6,211,2,138,2,124,4,204,3,116,3,115,5,87,254,131,2,0,0,232,3,184,3,74,4,249,0,166,5,160,2,178,254,169,255,124,8,214,253,90,7,112,10,140,0,34,7,61,7,152,3,213,6,30,10,52,4,141,7,246,7,119,255,69,254,237,249,245,4,150,4,212,1,19,254,134,255,241,5,61,254,9,4,190,4,226,1,159,6,94,4,47,3,137,2,128,1,66,254,76,253,107,0,193,254,163,253,138,255,49,255,7,254,13,2,44,254,244,255,176,10,75,0,142,7,25,5,112,3,54,9,219,8,5,5,39,6,212,7,208,255,208,254,94,251,77,254,51,254,5,255,146,254,108,254,221,253,223,254,163,253,171,253,230,253,214,252,91,255,136,255,3,0,100,1,127,2,217,4,222,5,96,0,177,0,238,2,77,254,183,253,106,251,156,254,109,0,177,255,27,254,32,1,213,7,9,0,92,4,219,2,112,3,86,8,178,3,247,254,49,6,41,4,133,4,186,4,75,3,14,254,100,253,175,1,118,1,65,1,27,255,160,5,53,8,101,5,193,1,205,1,131,4,151,255,39,0,128,254,249,254,111,1,182,0,141,254,108,253,5,3,68,255,127,4,203,3,53,5,96,6,155,5,6,3,243,4,197,4,30,254,192,252,47,250,19,255,46,255,92,3,122,3,79,6,40,4,216,1,38,4,168,4,185,0,53,4,221,3,200,253,32,252,88,249,63,254,122,252,5,248,114,255,135,254,54,254,46,255,214,253,251,251,245,255,109,4,217,8,183,254,93,253,131,252,6,255,145,2,163,4,7,2,230,5,243,6,8,2,27,2,123,5,15,2,141,5,22,5,205,253,153,252,32,251,109,255,49,254,111,3,180,255,30,9,24,11,51,2,13,10,81,9,120,2,134,7,104,11,207,2,231,7,48,7,223,253,45,253,84,4,129,0,131,255,116,3,137,5,96,6,157,3,162,255,30,6,215,6,171,254,253,5,15,6,79,2,139,1,238,254,180,255,213,3,15,11,153,0,169,11,52,7,8,4,5,10,189,10,228,5,16,11,87,7,23,3,175,4,26,2,66,255,59,254,209,5,234,254,220,253,134,4,11,255,149,7,252,7,0,4,24,6,114,6,0,2,253,0,210,1,194,255,189,254,127,4,39,254,136,254,251,1,79,254,100,5,114,8,131,3,151,7,165,5,134,0,192,2,184,1,204,1,13,2,228,255,62,254,23,1,58,5,0,0,203,3,252,0,67,254,141,253,33,252,164,254,166,253,112,250,142,1,200,2,120,6,149,255,58,1,78,255,93,0,178,8,190,8,6,2,81,3,144,2,50,254,57,253,65,254,174,0,222,255,167,4,137,255,42,0,237,3,140,254,18,1,246,2,12,4,48,9,46,7,163,2,188,6,218,5,174,1,6,5,85,8,127,255,73,254,0,0,139,254,32,3,96,8,6,0,51,6,174,9,222,1,84,2,80,8,84,254,32,253,225,5,129,1,178,0,212,3,139,0,193,1,201,4,242,253,182,252,42,252,145,0,18,6,218,4,111,2,168,5,144,2,93,1,248,3,202,5,31,0,232,254,159,1,196,254,212,2,105,6,104,1,34,4,44,2,76,254,154,254,177,4,157,254,99,4,147,7,145,1,48,6,200,8,241,253,12,252,99,1,233,0,238,0,185,8,218,253,127,252,129,253,147,254,11,254,165,7,133,1,68,7,85,6,162,0,108,4,240,4,19,255,150,4,110,5,128,253,101,254,116,0,28,255,158,6,250,8,103,6,138,8,219,8,50,2,249,4,98,10,67,1,82,1,238,6,66,2,83,4,84,3,22,0,82,2,166,3,113,255,206,2,190,1,50,0,71,0,247,255,174,254,70,253,129,250,102,0,118,255,204,252,202,254,43,254,133,251,158,1,67,0,245,254,36,4,46,3,161,5,12,6,80,5,248,4,218,6,103,7,125,6,227,7,85,8,28,7,16,7,14,9,53,7,132,2,163,255,198,1,90,3,73,1,120,255,233,1,254,254,128,255,58,255,23,253,215,255,204,255,247,254,39,252,90,1,137,0,223,1,51,249,20,253,84,253,117,251,67,249,145,254,129,252,135,251,240,252,24,254,78,252,56,252,171,255,122,254,43,253,215,0,172,254,85,255,252,3,148,3,177,7,52,2,179,0,234,2,150,2,209,3,198,6,119,3,110,2,146,3,171,3,88,3,141,4,53,1,176,2,35,3,149,3,161,0,58,2,118,0,236,255,229,254,208,252,214,255,204,0,52,251,187,254,50,254,61,252,54,255,113,255,36,252,28,254,151,254,66,253,46,252,35,254,210,254,234,252,92,251,156,255,238,252,192,251,226,251,77,252,108,249,54,255,181,252,242,252,241,251,158,250,123,252,144,253,146,255,171,255,100,1,213,0,246,255,19,254,108,1,6,3,169,1,54,3,223,1,173,255,45,2,8,2,32,252,232,249,196,253,165,253,27,253,230,255,10,254,130,253,121,252,209,0,50,1,147,0,196,254,175,253,172,253,171,255,45,255,31,255,106,252,239,253,117,0,233,0,73,254,30,253,77,4,239,2,121,2,177,5,180,6,231,5,229,6,177,5,142,3,98,4,132,4,81,3,74,5,100,3,214,1,153,252,130,251,252,248,153,252,163,252,32,252,138,255,155,0,212,0,229,251,175,252,162,253,163,251,199,248,66,245,5,252,109,250,179,248,114,1,72,255,98,254,191,3,237,1,104,0,190,3,15,4,31,2,154,0,141,2,201,0,225,4,251,1,150,0,151,2,247,1,230,0,111,2,9,3,163,2,147,2,88,0,146,255,75,3,244,0,224,0,126,1,29,2,46,1,212,2,177,1,154,2,142,4,222,2,85,1,118,255,20,0,115,254,97,251,88,254,210,255,191,254,160,254,132,255,53,5,253,3,56,4,6,1,110,1,211,2,154,3,27,1,217,253,31,0,132,253,157,253,79,253,71,253,97,254,72,252,245,252,55,255,207,250,170,253,153,254,71,252,251,250,166,0,237,1,49,1,221,0,78,3,191,2],"i8",A2,u1.GLOBAL_BASE),C0([98,2,72,3,168,3,6,3,45,253,212,250,19,251,155,254,255,251,148,250,184,251,160,250,147,254,120,250,167,248,160,253,250,248,65,249,94,253,223,253,107,251,65,253,166,2,18,3,148,0,133,255,184,2,8,5,132,2,94,1,246,255,158,1,102,2,15,0,137,0,88,1,45,255,210,252,24,250,205,252,121,254,94,252,180,253,47,0,177,253,126,252,115,252,183,251,93,255,8,251,113,251,99,255,72,250,11,250,123,254,6,251,92,251,144,253,159,2,213,0,198,1,124,0,238,254,243,253,39,253,16,254,104,255,192,250,122,0,135,0,167,244,179,253,118,254,64,249,185,1,206,255,196,5,136,3,19,3,60,1,236,0,72,254,165,254,217,0,157,1,113,252,107,252,121,0,57,254,92,252,202,0,164,255,47,254,137,254,232,1,134,1,218,1,108,3,217,2,60,1,233,248,224,250,99,253,87,0,194,3,176,1,51,2,7,255,222,251,250,0,29,1,81,4,117,4,171,1,184,2,242,251,128,249,210,249,76,252,90,1,160,0,203,254,240,254,166,252,158,2,112,2,226,4,80,252,104,254,102,253,162,253,192,254,128,254,20,254,230,0,65,0,78,1,206,255,240,255,240,255,78,253,139,250,255,6,180,6,119,5,174,9,15,8,124,5,221,4,191,5,146,5,130,254,243,251,254,255,173,0,114,254,121,4,211,5,232,7,9,7,4,3,250,4,226,5,149,5,199,6,209,7,55,4,194,4,249,4,126,251,197,248,207,250,216,252,147,251,184,251,61,254,247,251,70,249,65,0,66,2,172,255,60,250,126,246,14,249,3,253,170,250,18,254,38,255,174,253,93,252,81,1,20,255,50,2,53,9,102,10,146,7,209,5,252,4,106,3,189,0,102,1,118,1,17,250,23,247,214,246,57,252,9,251,209,247,140,253,92,251,250,249,125,6,19,4,34,2,53,2,37,4,220,2,192,255,188,252,78,254,76,254,160,255,203,0,54,4,192,4,100,6,139,3,254,5,218,3,70,1,197,3,77,3,142,0,172,255,197,0,214,1,75,9,34,6,109,4,214,1,190,4,139,1,96,5,176,4,101,4,18,4,92,1,225,253,46,251,136,254,41,255,75,255,225,1,101,248,171,249,46,255,18,253,95,251,134,1,29,0,113,254,27,0,52,3,212,4,243,2,183,2,211,3,153,1,82,255,173,4,11,4,144,3,76,5,54,7,32,252,99,250,228,1,51,250,92,249,208,0,100,254,180,4,152,5,241,254,128,3,120,4,96,254,241,6,154,5,96,249,172,245,52,255,3,249,241,249,9,4,136,249,233,249,23,5,27,251,203,249,57,4,99,253,185,251,190,255,86,253,64,1,167,254,147,2,49,1,45,4,244,250,220,252,237,255,157,249,245,250,29,0,109,249,15,254,71,0,225,254,249,255,156,255,18,254,62,252,19,255,84,3,89,7,204,6,63,251,149,250,227,0,108,253,46,1,117,1,96,0,63,4,233,4,206,251,123,249,160,0,229,1,28,8,6,7,90,252,36,255,40,2,172,253,156,253,237,0,80,1,184,6,111,3,131,2,117,2,178,1,243,4,10,2,97,6,15,0,244,0,71,254,195,5,205,2,184,0,27,7,54,6,173,6,220,3,5,1,169,3,45,8,41,9,240,5,91,8,66,7,70,6,191,253,189,253,77,251,68,252,135,0,24,254,48,254,51,0,174,254,139,253,164,254,45,253,122,4,25,8,162,5,144,8,186,5,143,3,92,250,220,249,26,247,120,5,198,2,17,5,55,5,121,2,160,3,154,5,146,8,34,10,118,9,156,8,89,7,214,3,194,8,62,7,124,1,24,3,121,4,193,255,229,253,158,1,4,255,60,252,198,254,19,251,85,253,244,252,193,252,242,253,19,252,126,249,145,251,88,254,181,249,60,254,213,254,244,4,24,4,130,2,123,4,85,3,88,3,93,253,176,254,139,0,220,8,63,5,138,5,29,0,0,3,29,3,56,251,167,1,52,2,218,250,198,251,245,0,234,250,212,252,61,2,238,250,175,249,134,2,56,252,66,3,211,2,225,3,116,6,235,7,65,255,207,252,176,1,150,2,60,0,198,0,114,2,229,3,50,5,112,6,171,7,9,5,195,249,163,255,211,255,192,251,37,0,172,255,117,6,47,10,33,9,41,4,248,7,73,9,115,4,22,9,70,8,91,3,101,1,230,5,152,2,203,4,75,4,223,1,80,5,144,3,105,7,218,6,227,7,144,4,117,7,248,6,143,1,34,0,0,1,175,253,208,254,227,251,35,2,158,6,127,5,135,2,157,255,171,254,212,5,111,6,166,4,38,0,124,253,44,255,139,1,78,3,222,0,64,253,3,253,52,253,44,253,84,248,12,245,106,255,35,1,174,255,209,4,179,5,239,3,116,255,101,255,153,0,183,1,41,1,32,6,7,250,102,254,132,253,0,6,199,1,19,255,208,250,117,255,252,254,19,2,42,2,100,3,13,1,240,4,94,2,23,255,115,3,207,1,230,2,88,2,136,255,183,255,165,1,212,0,73,254,198,255,36,3,250,250,39,251,216,2,38,1,22,254,50,0,177,253,119,252,26,251,42,0,81,253,147,0,231,255,17,1,84,2,201,254,189,4,89,2,14,253,81,3,72,2,173,1,95,2,75,2,166,253,90,255,205,1,228,252,201,252,9,3,100,5,142,3,219,6,119,0,137,5,204,3,37,255,144,252,196,249,231,251,14,252,182,1,55,253,157,250,78,0,0,0,65,254,101,251,144,251,217,250,219,249,200,8,231,6,29,5,178,3,47,6,152,5,126,4,226,1,180,1,43,254,172,251,106,2,65,254,58,252,64,4,28,251,21,250,142,255,176,251,40,248,189,253,210,0,101,2,241,1,73,248,99,250,130,2,11,251,168,252,243,3,146,249,95,251,39,4,237,249,96,253,180,4,100,249,166,251,111,2,45,252,210,250,3,251,27,2,109,255,126,3,182,250,127,252,78,254,120,3,219,1,172,1,153,0,128,254,82,1,44,250,1,254,103,1,50,252,165,251,42,254,105,0,218,253,165,2,87,252,135,251,109,3,124,1,252,254,210,0,149,6,156,3,232,4,239,6,166,4,71,4,139,5,119,2,21,2,115,2,43,1,165,254,101,254,234,253,135,2,118,253,29,0,173,253,134,254,169,250,27,6,122,5,97,4,185,5,65,4,130,5,136,2,208,247,190,251,250,255,55,1,62,255,155,252,129,253,193,252,160,1,118,251,56,251,69,5,33,251,83,252,21,7,111,247,61,248,197,1,149,253,169,250,68,252,186,249,76,248,29,250,105,251,223,251,176,251,135,254,89,2,201,0,84,7,57,3,118,1,82,254,213,250,29,0,139,250,31,251,205,250,17,252,32,250,192,3,135,250,39,248,197,0,157,250,99,248,20,255,203,251,123,0,166,1,103,2,245,4,34,2,206,254,246,5,136,3,170,4,252,6,153,4,142,253,140,252,10,250,199,0,254,2,224,5,215,251,94,3,197,0,246,251,19,249,137,252,224,252,145,0,87,2,146,251,249,253,114,2,75,251,122,248,244,1,114,252,239,251,141,250,60,250,225,249,55,252,245,253,74,3,34,0,2,7,134,2,94,3,73,251,160,248,22,252,178,255,247,255,96,253,20,4,247,2,80,0,168,253,115,4,251,3,57,0,208,7,142,5,191,252,134,5,97,4,78,251,94,6,236,4,51,254,140,5,220,4,1,6,207,3,253,0,229,254,68,1,153,254,87,2,61,255,106,0,76,2,62,0,181,253,11,253,133,2,205,0,51,0,177,4,246,2,71,251,161,2,122,254,144,253,45,6,173,3,105,255,255,3,223,2,4,11,21,5,178,2,210,254,12,2,157,255,124,252,204,249,91,251,60,4,251,0,238,0,222,7,0,7,242,3,221,4,97,6,205,6,53,251,252,249,72,251,147,253,200,1,147,255,40,0,191,255,20,3,219,252,69,253,186,250,185,253,136,3,64,3,223,252,20,2,82,2,180,7,128,5,71,5,103,251,168,248,190,247,251,252,56,2,180,3,9,252,55,4,236,4,169,251,226,1,126,255,242,6,20,4,12,3,45,250,245,0,144,3,196,254,139,251,107,252,232,253,94,250,214,246,239,252,246,249,60,248,45,248,1,1,141,3,199,248,135,253,71,251,254,249,130,248,226,251,70,6,191,8,40,6,201,253,36,250,248,249,1,251,195,0,89,5,207,252,37,1,195,4,243,253,118,2,173,4,94,249,135,246,208,248,209,254,219,2,235,2,111,251,5,255,13,1,74,252,181,255,148,6,98,251,59,254,237,3,193,249,73,2,122,1,229,247,197,253,85,254,239,253,121,251,109,251,229,254,51,255,204,253,228,252,222,4,205,2,229,8,159,3,27,2,58,254,47,2,184,1,51,253,180,5,79,6,250,251,28,4,74,6,111,251,118,255,79,3,226,0,39,0,156,253,29,251,150,255,39,253,117,253,200,3,22,5,54,253,132,253,191,6,97,1,45,4,154,1,226,252,100,255,75,4,194,253,150,3,190,1,226,250,244,3,210,1,128,5,55,6,253,2,149,5,100,5,221,6,157,7,164,7,74,9,42,6,255,7,100,8,148,3,98,0,249,255,101,7,138,5,93,8,92,1,125,5,43,6,152,0,110,4,9,7,245,254,154,0,115,5,114,251,213,1,30,4,138,251,107,254,207,251,195,250,40,247,211,249,148,254,101,3,170,6,118,251,37,2,14,6,55,251,116,248,126,249,51,250,71,248,249,247,65,249,118,252,158,255,151,248,233,0,212,5,124,3,108,0,181,254,64,249,110,251,92,249,220,251,188,7,254,6,210,251,51,249,139,248,245,255,3,6,37,5,192,249,94,0,241,1,165,1,187,1,59,255,214,249,163,254,30,252,169,253,229,253,116,4,59,252,117,250,127,255,195,250,175,0,65,254,137,254,31,5,7,8,141,254,118,253,205,254,207,251,93,2,109,1,247,247,143,255,174,1,140,2,146,3,199,3,12,252,206,249,237,246,225,5,224,4,47,2,6,1,26,254,111,254,65,249,62,5,10,6,50,0,56,0,176,1,182,254,119,0,164,253,19,250,200,251,214,252,178,3,103,4,31,4,136,250,89,249,80,249,10,251,64,253,219,250,39,3,29,7,119,4,200,10,70,6,123,8,96,4,153,1,106,255,109,255,148,1,191,3,135,9,119,7,141,8,118,252,115,255,158,252,120,252,114,255,54,254,211,253,60,253,113,249,194,252,105,250,209,249,206,248,190,250,194,251,188,249,240,254,147,3,84,251,4,3,32,4,130,253,46,251,151,248,12,254,175,255,202,252,247,250,179,249,33,253,139,255,17,3,168,0,190,251,109,4,154,3,184,251,22,253,104,5,31,1,221,253,217,251,160,250,103,247,76,251,128,247,222,249,35,249,25,250,63,247,253,252,55,249,75,4,62,3,204,249,212,2,219,4,250,249,181,2,37,3,102,249,16,255,129,6,92,249,252,255,100,253,101,8,48,3,18,4,206,252,207,248,22,0,4,253,5,254,193,1,129,251,151,253,33,1,181,252,196,249,16,255,242,1,22,255,111,253,16,253,224,1,142,6,193,254,31,254,193,0,213,252,171,0,137,255,176,247,54,255,176,252,181,6,116,4,164,6,67,0,239,255,66,0,244,255,102,249,187,253,152,255,240,254,204,251,94,251,203,248,136,254,140,251,98,252,92,254,198,255,253,254,112,253,146,251,215,253,252,6,203,4,199,1,129,0,206,1,185,1,16,255,240,253,72,3,2,2,130,0,181,255,90,4,111,2,153,0,216,0,44,4,52,2,250,255,236,254,95,4,215,2,190,0,188,255,192,2,50,1,119,0,248,254,73,1,61,0,156,255,156,0,108,1,123,0,183,0,48,255,85,255,133,255,220,0,191,255,206,254,194,255,146,1,17,0,108,253,86,252,246,254,0,0,129,1,235,0,20,1,29,1,64,1,12,1,176,254,56,255,44,253,17,0,172,255,125,1,224,253,173,1,238,1,7,2,139,255,32,1,48,1,73,1,131,2,157,0,189,2,252,1,176,4,113,2,28,3,96,2,230,3,165,1,236,1,120,2,180,4,12,3,190,1,132,0,233,4,76,3,35,2,193,1,61,3,146,2,29,2,214,1,108,4,234,4,150,3,127,2,35,2,51,0,167,1,23,1,9,0,136,1,83,0,94,0,30,2,31,2,229,0,109,255,58,255,129,0,194,0,71,255,161,252,215,250,210,254,30,0,171,253,139,253,237,255,114,0,124,252,199,251,210,1,97,1,53,250,219,249,15,0,113,255,84,249,245,247,17,253,196,0,172,248,237,247,126,253,254,254,225,246,66,250,62,254,204,253,184,253,70,255,152,252,98,254,243,248,36,252,155,251,226,250,42,253,151,251,28,0,169,0,241,251,160,252,50,253,10,255,228,1,36,0,23,255,207,255,9,1,67,0,33,1,211,1,178,0,31,2,42,3,28,2,84,0,26,1,160,2,191,2,49,252,247,252,129,0,31,1,86,252,29,255,187,3,83,2,175,249,223,254,68,3,137,2,201,248,41,255,82,4,206,2,14,248,195,251,138,2,184,1,203,247,239,253,139,3,63,2,37,248,176,254,158,2,204,0,171,246,76,253,104,1,137,0,148,247,100,247,247,255,24,1,246,254,119,0,39,0,193,0,78,0,197,255,136,255,226,0,49,252,166,252,243,252,185,251,149,253,99,254,61,254,182,252,64,251,215,250,211,252,141,252,160,250,177,249,118,254,84,254,31,253,167,251,219,253,234,252,144,252,49,252,57,252,126,253,39,252,138,252,7,251,175,250,39,254,220,252,135,250,129,250,160,0,247,254,105,252,237,254,8,255,6,255,50,253,132,254,97,0,153,255,137,254,27,255,97,254,63,255,121,255,213,253,116,2,105,1,119,0,216,0,67,2,108,1,135,1,209,0,122,2,10,2,102,255,108,255,14,2,133,1,170,0,33,0,105,0,11,1,64,0,124,1,33,250,24,252,226,255,143,254,210,251,58,0,135,2,223,0,16,250,221,254,109,2,51,1,5,250,156,0,250,2,148,1,19,248,141,0,222,2,243,1,199,248,118,253,50,1,0,2,69,255,152,255,197,255,182,1,134,0,26,255,156,0,70,255,195,255,252,254,240,255,10,0,199,253,253,255,91,254,215,254,67,249,247,253,166,254,178,0,174,250,197,255,212,255,157,0,158,247,51,254,42,254,163,254,134,247,255,255,143,254,135,255,213,249,139,254,124,252,9,252,163,251,177,253,155,253,240,252,207,253,122,0,181,255,63,254,252,255,85,255,133,255,140,254,192,0,168,0,180,255,124,255,252,0,149,255,84,1,210,0,136,1,253,1,16,1,181,0,147,255,145,0,218,0,119,0,96,254,249,254,229,1,9,1,75,255,248,255,226,254,226,0,12,255,38,255,69,0,222,254,98,255,191,0,255,255,192,255,176,253,166,255,213,0,160,255,255,0,179,1,178,0,176,255,143,254,238,255,223,255,176,255,214,255,159,1,140,0,34,255,119,4,139,2,137,2,73,1,255,2,44,2,249,0,235,0,180,3,157,1,186,1,23,1,141,0,83,1,100,1,45,2,42,254,86,255,99,0,237,0,199,253,224,252,96,1,53,2,26,1,217,1,214,1,76,1,57,255,78,253,252,250,107,252,63,255,86,254,224,252,158,251,230,255,141,254,22,254,63,255,125,2,83,2,7,2,74,1,152,1,141,255,79,0,12,0,221,1,87,0,153,255,136,254,102,253,165,254,235,254,221,254,2,254,31,254,169,0,41,1,195,252,30,253,51,255,85,255,192,254,228,253,72,1,27,1,165,252,66,252,186,1,254,255,44,2,174,2,130,0,56,0,103,5,244,3,243,2,171,1,100,2,229,2,116,2,41,2,173,254,228,252,134,0,21,1,135,253,195,251,254,255,10,255,144,252,245,251,185,249,216,251,30,252,38,254,142,251,24,254,98,254,229,252,73,0,50,255,248,255,117,255,183,1,204,0,80,255,190,253,23,0,131,0,243,254,11,253,65,255,245,0,147,255,174,254,112,0,60,1,120,0,106,254,138,255,99,2,76,255,70,255,123,253,115,0,83,255,34,0,250,253,23,254,105,255,61,0,185,253,180,252,220,0,118,255,87,253,4,252,135,1,239,255,170,253,191,254,157,0,217,254,129,0,155,0,98,252,149,252,37,252,29,1,241,0,173,255,131,255,131,255,108,2,85,2,176,1,92,0,137,1,78,0,153,1,61,0,119,254,29,253,99,254,20,253,83,0,54,0,105,1,27,0,196,251,130,0,175,254,74,253,227,249,41,1,62,1,237,255,175,248,36,0,51,0,195,254,237,246,10,255,231,0,172,255,254,246,241,252,40,0,77,255,71,247,94,252,38,254,50,254,14,253,170,255,224,254,142,253,149,246,57,254,193,255,171,0,181,251,186,251,230,255,113,255,87,251,57,254,106,254,131,254,163,253,46,255,160,255,205,255,188,253,36,254,236,254,241,255,85,251,134,253,77,251,143,252,134,254,35,255,99,253,72,252,82,2,178,0,109,254,92,253,251,2,71,1,89,2,34,1,172,0,44,1,203,0,157,0,200,255,176,254,100,1,24,0,28,255,216,254,253,254,227,255,70,255,7,1,160,1,14,0,159,254,117,1,244,255,40,255,1,1,96,0,174,0,57,0,10,250,152,253,70,252,13,254,15,254,104,255,179,254,125,0,105,0,200,0,179,0,159,255,181,254,32,255,253,2,185,2,248,2,0,1,45,1,59,0,199,1,171,255,204,0,32,1,254,253,240,0,251,0,147,255,0,1,161,1,222,255,99,254,101,0,174,1,128,1,156,0,225,255,246,255,206,0,170,1,77,2,145,0,143,0,71,0,40,3,138,3,77,1,93,1,218,3,170,3,77,2,75,1,20,5,56,3,187,0,253,1,38,4,141,2,123,1,210,1,182,5,169,3,145,1,18,1,19,3,93,3,9,1,2,0,97,2,41,2,28,0,49,1,158,3,84,1,106,0,130,1,241,0,245,254,109,255,225,0,78,255,234,253,91,1,246,1,125,253,131,254,141,1,30,0,117,253,35,253,77,254,142,1,105,254,42,253,28,254,8,255,235,252,110,252,74,254,36,254,14,254,122,254,75,0,217,254,60,252,178,253,162,253,150,0,135,255,207,255,101,255,178,255,167,3,38,2,133,1,38,0,191,254,127,0,168,1,59,1,227,254,143,255,27,1,3,1,146,2,203,0,66,1,230,1,135,3,249,1,236,2,161,1,99,2,167,1,43,2,0,2,239,0,173,255,190,253,237,255,173,254,37,253,93,1,13,0,90,252,137,250,142,255,152,254,107,0,180,2,182,0,90,0,37,251,254,249,241,249,43,253,200,253,121,252,173,250,243,253,251,253,171,252,163,252,20,252,88,255,78,253,189,252,63,0,119,255,212,253,221,253,144,0,226,254,207,252,229,1,63,1,109,255,104,254,14,2,246,0,165,254,78,254,41,1,228,255,222,254,41,254,170,251,251,250,52,254,153,254,36,252,230,252,67,5,19,5,178,2,11,2,192,4,44,4,70,4,245,2,57,3,116,4,240,2,238,1,228,4,85,5,171,4,130,3,9,2,29,4,20,2,176,1,178,254,40,255,199,254,249,254,96,255,52,0,40,254,101,255,127,0,136,0,132,254,44,0,83,3,154,1,94,255,23,254,123,0,1,255,228,252,101,253,66,4,149,3,21,3,237,1,117,5,173,4,46,2,202,0,205,255,138,255,170,254,67,253,83,0,108,0,214,255,71,254,61,0,95,0,31,1,0,1,229,255,89,0,12,2,19,2,95,1,227,0,80,2,33,2,185,2,155,0,92,255,51,1,126,2,18,1,23,254,206,255,242,2,240,0,90,255,132,255,140,255,189,253,68,251,193,255,190,0,217,254,240,251,240,250,147,0,136,254,79,255,143,255,73,3,217,4,27,4,156,2,2,0,37,1,39,2,48,1,184,251,71,252,8,255,120,1,18,253,59,252,87,0,4,2,237,254,252,253,177,2,135,1,133,254,125,253,108,3,82,2,122,254,11,252,123,253,61,2,149,255,200,253,79,253,198,252,255,251,229,255,184,254,53,255,93,3,237,2,36,2,233,0,132,249,237,251,195,1,108,0,108,253,148,253,174,1,236,0,21,0,116,254,122,251,137,253,92,5,18,5,199,3,65,2,101,4,101,4,77,2,198,1,189,254,159,252,45,254,153,0,44,254,69,253,220,252,3,254,120,254,50,253,52,255,221,255,165,253,187,251,201,253,94,255,7,254,20,252,154,255,94,1,219,0,224,0,167,1,252,0,139,1,79,2,96,2,107,1,22,253,160,255,117,1,172,0,171,0,39,1,202,2,83,1,233,0,77,0,107,0,21,1,157,0,153,0,13,254,156,254,11,6,49,4,64,2,238,1,220,254,173,254,8,254,176,253,121,252,184,255,149,253,31,254,198,249,163,251,201,253,2,255,231,252,5,254,204,253,221,254,20,254,236,253,246,1,48,2,130,254,171,1,88,2,230,0,29,255,221,1,251,0,75,0,29,1,74,3,45,3,220,1,226,250,203,250,186,0,121,1,181,253,107,252,131,2,125,1,94,251,215,253,155,1,82,0,153,251,204,252,82,255,228,253,164,253,119,0,31,2,205,0,132,254,145,2,141,3,55,2,112,0,214,254,138,254,114,0,167,252,5,255,56,0,159,0,145,1,89,1,222,255,116,255,145,255,161,253,41,0,102,2,99,1,142,255,179,255,218,1,66,2,56,0,170,5,156,3,74,4,140,5,229,2,144,1,246,0,22,0,76,2,57,1,135,255,71,1,63,3,216,1,142,251,160,253,88,3,40,2,39,251,208,251,126,2,88,2,154,254,254,0,179,254,209,254,122,253,227,2,102,1,74,0,202,4,135,6,197,4,81,3,193,8,88,6,215,3,124,2,49,7,197,5,237,2,128,1,94,1,7,1,87,0,128,0,146,248,83,252,112,255,192,255,58,249,1,255,32,1,225,255,172,245,42,251,110,1,235,0,149,249,188,251,192,250,208,254,227,253,205,251,164,251,123,0,102,251,4,255,208,252,76,255,8,252,21,2,53,2,233,0,25,254,82,254,68,255,78,1,99,3,212,4,22,2,171,0,202,249,185,249,123,2,118,2,108,247,54,1,156,3,156,1,202,246,184,254,188,3,17,2,177,245,135,254,118,2,22,1,214,245,61,1,31,3,43,1,154,246,133,0,84,1,31,0,148,247,68,250,131,0,125,0,96,251,22,254,117,255,46,0,24,253,191,1,123,3,52,2,67,0,61,254,134,2,92,2,215,253,83,254,148,252,140,1,162,0,190,255,25,5,147,3,223,1,67,2,64,4,26,3,194,1,22,1,54,2,68,1,223,251,102,255,148,0,79,255,15,246,168,0,46,4,80,2,209,246,214,255,51,3,89,1,216,246,61,253,209,2,250,0,129,247,39,250,203,254,122,0,178,255,183,255,120,0,173,0,252,255,6,1,249,254,251,254,81,254,192,255,107,254,36,253,207,245,116,0,173,255,63,255,11,250,80,252,35,254,43,253,4,254,51,1,170,0,172,0,64,3,161,1,64,3,174,2,31,255,177,0,126,3,50,3,30,254,123,254,255,4,15,4,129,254,201,0,162,254,40,0,218,2,123,2,226,0,14,2,247,1,206,1,82,1,142,1,23,2,202,2,40,0,230,254,202,5,191,5,61,4,219,2,25,6,48,4,141,3,181,2,139,5,2,5,121,3,111,3,129,4,216,2,162,4,72,3,30,255,106,4,181,3,177,2,18,254,38,252,236,249,128,255,200,253,47,253,55,253,230,255,61,1,12,2,70,0,135,0,107,254,159,252,26,249,116,253,82,255,223,252,117,3,5,3,103,255,165,255,75,4,239,2,6,254,131,251,85,3,134,2,241,0,14,3,7,2,27,2,61,7,164,6,77,4,172,2,31,251,50,250,48,254,188,0,131,252,127,250,224,250,171,254,121,255,182,1,81,255,18,0,87,4,208,3,63,1,208,0,106,250,24,249,83,0,202,1,238,253,24,252,51,1,129,0,184,252,241,255,227,255,156,254,113,252,100,252,133,251,14,255,137,255,240,253,127,0,123,255,7,253,3,253,190,0,173,255,197,254,127,3,10,2,231,0,34,255,102,0,193,255,84,254,60,1,187,2,123,1,70,0,25,0,204,2,58,1,148,255,251,1,106,3,54,2,238,0,108,0,173,3,7,2,195,0,169,1,196,255,85,254,1,1,139,0,153,255,138,253,190,1,78,1,114,1,156,1,48,0,84,255,78,253,229,254,45,2,187,0,226,254,158,0,227,1,140,0,14,1,168,254,137,253,156,3,67,2,140,255,132,0,142,0,210,1,188,255,192,255,230,0,111,255,210,254,226,253,221,252,112,252,250,3,225,2,251,252,247,3,118,2,41,1,220,245,95,0,189,1,80,1,182,247,235,1,254,1,191,0,27,251,161,0,254,255,188,254,86,250,135,253,56,253,151,255,182,252,2,255,101,254,100,0,128,253,222,254,242,3,251,2,118,253,57,1,145,4,218,2,140,0,249,1,6,4,254,2,4,3,31,1,43,4,55,3,239,1,237,2,49,1,67,1,92,255,206,1,78,0,143,1,170,254,150,252,69,0,85,2,240,255,108,2,109,2,81,1,118,255,68,254,247,254,218,0,84,0,62,254,185,3,154,2,34,255,221,252,29,2,92,2,103,252,160,250,244,0,116,0,183,252,45,253,118,2,76,2,140,0,151,2,38,1,112,1,167,3,22,4,113,3,247,2,210,6,184,5,148,3,116,2,180,1,195,3,25,1,1,0,137,255,74,0,30,2,213,0,1,0,201,253,45,1,241,0,4,1,179,1,222,0,140,1,168,3,189,3,84,4,191,2,254,1,250,1,40,3,222,1,89,2,182,2,192,3,108,2,204,3,229,2,212,3,88,2,66,3,205,2,255,2,172,2,131,2,204,3,167,3,126,2,245,1,149,2,208,2,83,3,151,255,136,253,209,254,139,255,83,254,130,0,21,3,186,1,246,253,68,255,192,2,117,1,9,253,42,0,46,3,11,2,237,253,143,251,117,1,66,2,86,253,77,251,57,254,29,1,117,251,215,249,182,251,44,0,81,0,174,255,200,2,107,1,221,1,246,0,186,3,110,2,68,6,86,6,253,4,123,3,129,5,91,3,156,3,124,3,6,3,17,4,179,3,118,4,40,0,222,253,181,255,32,1,152,253,150,255,71,253,230,255,87,255,96,255,133,252,29,253,233,254,128,254,251,251,162,254,245,6,28,5,22,4,48,3,44,6,253,5,192,5,154,4,225,5,52,4,192,4,131,3,122,3,136,3,52,2,142,2,152,3,180,2,253,3,88,3,19,254,132,0,177,0,249,1,71,0,195,0,228,255,97,0,200,1,95,1,92,255,88,0,183,1,22,1,216,255,94,1,115,5,181,3,234,0,161,255,219,252,40,254,38,0,93,255,111,1,158,255,233,1,11,2,1,4,154,4,188,4,138,3,63,1,34,5,46,3,205,1,133,255,225,253,220,252,191,1,20,253,188,254,127,252,153,251,31,253,11,254,235,252,55,253,203,2,9,3,215,4,154,3,157,7,147,7,88,5,97,3,218,2,112,3,246,2,132,1,153,252,198,1,17,0,5,255,131,254,214,252,209,249,239,0,247,253,58,252,232,252,3,1,134,252,178,250,254,252,183,255,166,0,93,1,44,255,67,1,184,252,211,254,217,1,179,1,89,253,48,254,216,2,95,1,100,255,57,255,155,2,176,1,29,0,4,255,159,1,224,1,37,253,133,254,145,0,47,2,240,253,137,253,122,251,97,255,189,1,17,1,123,0,127,2,117,1,130,255,32,3,56,2,84,0,94,255,208,2,200,2,194,252,232,253,71,255,222,0,152,1,196,1,245,1,3,3,127,252,181,250,189,255,186,1,232,252,130,250,54,2,90,2,167,0,186,254,253,1,74,1,161,255,142,253,38,253,168,254,132,6,193,4,11,3,199,1,36,5,60,3,72,2,207,2,148,1,225,255,245,3,21,3,89,0,107,0,123,3,37,2,103,3,45,6,149,3,159,2,98,3,199,5,9,5,86,3,135,1,44,4,98,4,44,3,78,0,206,253,89,1,51,2,173,1,153,255,161,1,19,3,134,255,75,254,155,1,20,3,111,252,95,254,90,2,242,2,30,255,240,255,151,0,248,2,68,253,118,0,152,255,242,255,152,251,48,0,28,1,137,1,122,254,93,254,129,253,140,255,114,252,50,1,60,1,243,255,183,4,216,3,53,3,157,2,85,251,75,253,140,0,43,255,140,252,96,254,57,255,210,253,152,253,245,0,108,254,104,253,6,1,56,0,151,253,44,253,171,255,21,254,192,254,112,253,198,253,193,252,127,255,240,253,30,250,193,255,145,254,127,254,154,254,191,254,4,0,51,0,146,254,42,255,63,1,255,1,146,0,159,2,239,255,221,254,146,255,208,1,117,255,16,254,54,255,220,0,200,254,137,253,108,253,183,255,113,253,204,252,106,253,115,253,248,250,167,252,82,254,71,252,65,252,248,254,207,255,44,254,184,255,131,254,162,254,205,253,63,255,105,254,55,0,104,254,221,252,11,0,203,254,137,2,188,0,58,255,0,254,205,1,177,255,54,254,218,250,249,254,122,255,245,253,135,249,77,254,17,254,3,253,57,0,165,254,98,254,178,1,139,251,14,255,104,253,167,252,34,0,188,255,61,253,174,254,163,1,163,0,226,255,250,254,57,254,235,252,106,250,47,253,238,3,152,2,13,1,25,0,107,2,4,1,183,0,96,0,56,252,178,250,124,254,135,0,75,253,67,3,200,1,154,0,81,4,191,2,57,2,107,1,89,6,46,5,217,3,236,2,36,255,219,0,76,0,48,255,81,250,130,249,49,0,149,0,60,252,84,255,16,253,176,254,113,2,209,0,6,255,190,255,7,252,186,252,254,255,61,1,136,247,51,250,118,255,123,0,172,248,205,247,247,253,85,0,57,252,146,254,73,253,143,252,103,252,13,252,5,253,75,252,132,255,0,255,160,254,108,253,178,0,207,1,98,1,48,1,48,249,177,253,230,254,79,0,55,247,175,0,99,3,243,1,118,255,76,255,75,255,235,255,13,247,39,251,52,254,248,253,253,252,195,1,246,255,204,254,15,1,191,255,4,0,214,0,233,254,77,254,213,255,164,254,98,253,35,0,191,255,45,255,38,3,23,2,85,0,41,1,57,0,239,0,210,2,237,1,225,0,149,2,72,3,35,2,228,253,136,254,14,0,93,1,213,1,209,2,75,1,162,0,224,253,16,253,194,255,246,255,142,1,168,255,212,2,189,2,237,255,235,253,162,255,89,2,136,0,185,255,87,253,21,253,90,255,168,254,5,1,206,255,161,0,204,255,229,1,81,1,117,249,50,0,190,0,163,255,22,247,25,255,62,255,174,255,161,255,173,253,102,255,128,0,126,3,245,1,76,2,201,1,167,254,206,0,122,0,110,0,137,253,29,255,199,253,3,0,152,1,239,0,141,1,226,0,59,255,254,255,128,0,235,1,1,5,136,3,36,1,215,0,26,2,50,1,3,1,253,1,91,253,233,251,13,0,65,1,89,253,180,253,154,254,44,255,210,253,243,0,134,2,223,1,230,1,86,1,122,2,20,2,107,0,34,3,75,1,136,0,144,255,114,254,249,251,226,254,186,254,63,253,32,1,16,1,19,5,120,4,154,4,92,3,89,254,121,0,127,254,108,255,217,254,210,254,190,252,205,252,16,0,232,255,55,255,36,254,43,2,91,0,11,255,38,1,218,255,133,254,62,252,59,251,89,251,18,250,239,254,117,254,122,254,11,252,123,253,61,2,205,248,250,251,249,1,212,1,232,2,179,3,97,2,237,1,79,253,108,251,140,253,121,255,254,251,195,0,155,1,196,0,46,6,123,4,63,2,81,1,41,251,247,252,120,253,114,255,83,2,57,3,199,3,223,2,74,251,54,252,175,255,170,254,23,253,13,0,184,255,119,1,198,1,19,0,127,5,153,3,145,249,84,255,93,3,50,2,160,3,1,6,39,4,228,2,88,246,72,252,8,1,82,0,10,254,59,252,202,250,123,0,99,3,212,4,22,2,171,0,240,246,52,254,12,3,107,1,90,251,151,253,252,0,195,255,82,255,34,0,243,3,20,3,227,246,247,0,167,1,153,0,240,255,157,254,6,1,193,1,216,249,207,251,224,253,141,254,153,253,207,254,27,4,37,3,175,2,16,2,6,0,74,255,167,3,107,3,234,3,41,3,199,0,1,1,126,0,76,0,184,253,142,251,87,2,44,2,175,251,145,250,201,249,249,253,47,252,211,250,108,0,91,1,46,253,49,252,109,1,101,0,111,255,169,2,249,0,103,255,0,0,178,254,198,253,159,0,156,1,29,1,176,254,151,253,71,252,58,252,119,3,177,2,29,251,84,0,71,255,114,254,176,253,177,1,20,4,141,2,85,0,73,1,216,255,105,1,79,254,63,253,210,1,62,2,102,255,142,2,80,2,34,1,89,255,72,0,93,1,175,0,162,2,41,1,209,3,208,2,211,4,180,4,245,2,232,1,112,254,243,254,26,2,116,1,186,250,149,250,86,251,165,255,238,4,108,3,7,3,188,2,169,253,218,255,82,254,46,253,184,7,94,6,223,3,96,2,111,0,20,1,30,255,160,255,77,252,124,254,245,255,249,255,209,254,237,253,185,252,82,1,198,6,174,6,125,5,245,3,252,253,169,252,123,253,210,0,80,253,96,254,1,2,230,0,202,252,131,253,134,251,192,254,72,252,110,253,74,253,183,0,142,255,145,253,50,3,162,2,65,255,52,255,219,2,123,2,51,0,197,4,115,3,64,2,70,252,81,254,58,3,86,2,170,254,13,253,124,252,105,254,154,251,158,254,50,255,0,254,221,253,214,252,155,254,148,253,66,0,3,2,183,255,102,254,152,252,79,252,92,250,53,251,191,0,239,255,224,253,25,255,252,249,224,253,123,252,138,252,134,252,242,249,19,246,205,252,54,252,175,0,198,252,46,251,6,253,169,253,234,255,122,2,213,252,37,252,122,252,189,254,203,0,26,0,129,254,21,255,243,252,113,254,238,4,138,3,92,252,137,250,156,250,144,253,93,0,87,0,98,254,229,253,77,253,37,0,121,2,254,1,125,254,36,254,206,250,143,1,66,0,7,1,105,254,207,255,177,254,95,254,17,4,73,7,245,252,191,251,96,250,22,253,166,252,64,3,187,253,9,253,141,254,95,253,6,254,40,8,208,253,134,253,101,251,15,1,241,0,14,0,74,254,12,255,115,254,207,1,178,4,23,4,162,253,227,252,98,250,205,255,189,254,225,1,32,255,184,253,241,253,238,1,113,3,170,2,79,254,206,254,22,252,42,2,147,2,222,0,171,0,96,255,159,254,169,2,6,7,29,6,172,252,99,251,97,249,176,254,102,253,114,0,187,253,12,253,24,253,61,255,119,1,241,1,47,254,220,252,182,251,154,0,26,1,125,255,206,255,65,255,49,253,67,1,220,2,6,6,46,253,205,252,132,250,105,0,6,255,185,0,78,255,10,254,26,253,65,1,254,1,87,4,189,254,201,253,58,252,127,0,228,1,82,1,96,255,52,0,174,254,220,2,87,5,18,6,142,253,222,252,96,249,226,254,182,253,164,2,73,253,169,254,142,254,22,254,39,1,101,7,138,253,194,253,10,252,176,255,133,2,187,255,250,255,194,254,148,254,14,3,170,5,14,4,199,254,35,253,141,250,120,0,60,0,221,1,248,254,183,253,133,255,199,2,221,4,121,2,165,255,157,254,8,252,3,3,246,2,5,1,253,0,81,0,38,254,162,3,167,8,184,6,216,252,181,251,123,248,208,253,242,252,169,0,220,252,206,251,68,255,142,253,201,255,125,5,74,253,52,253,86,251,108,253,98,1,73,1,254,253,201,255,225,253,110,1,9,4,158,4,110,253,65,252,179,250,201,255,72,255,93,0,163,253,226,254,106,253,148,1,193,1,59,3,226,254,162,254,17,251,116,2,50,1,227,0,240,255,147,0,145,253,186,0,155,3,98,8,94,253,134,252,186,249,69,254,28,255,83,1,143,254,234,252,103,254,231,0,86,0,189,5,64,254,187,253,219,251,82,2,194,1,79,255,132,255,86,255,65,254,159,2,135,4,124,5,36,254,101,253,25,250,179,255,118,255,204,2,79,255,140,254,131,254,195,1,166,3,147,3,6,255,80,254,202,252,16,1,60,3,190,1,26,0,19,0,225,255,186,2,156,6,120,8,122,253,47,252,124,248,77,255,39,254,12,1,133,254,23,253,77,253,11,0,127,0,9,4,24,254,107,252,199,252,61,0,67,1,135,0,147,0,111,255,82,253,173,2,18,3,146,6,6,254,176,252,239,250,35,0,90,0,222,0,233,255,166,254,98,253,199,1,79,2,7,5,53,255,175,253,194,251,140,2,96,1,181,1,39,0,63,0,55,254,73,3,241,4,57,8,248,253,142,252,208,249,184,254,57,253,141,5,172,253,170,254,186,255,209,0,173,0,136,7,89,254,170,253,103,252,165,1,93,2,218,255,254,255,11,255,129,255,128,3,177,7,111,4,133,254,250,253,213,249,173,0,118,0,241,2,201,255,131,254,204,255,217,3,253,3,241,2,254,255,221,254,133,252,241,2,224,3,167,1,8,1,131,0,60,255,127,3,226,8,239,9,133,253,192,251,61,246,239,253,42,252,14,2,4,253,194,252,220,253,76,254,60,1,87,2,93,253,84,252,22,253,199,255,236,0,245,255,55,255,175,255,226,252,16,0,77,3,22,6,31,253,39,252,68,251,44,254,17,0,34,1,233,254,184,253,68,253,183,0,54,3,193,2,247,254,20,254,93,251,165,1,152,0,212,1,122,254,166,0,244,254,39,0,14,6,76,7,133,253,58,252,221,249,59,254,20,254,142,3,228,254,253,251,181,255,75,255,123,255,60,7,67,254,144,253,106,251,164,1,111,1,207,255,123,254,44,255,87,255,195,2,49,4,184,4,229,253,58,253,87,250,83,0,93,255,228,1,20,255,225,253,157,254,82,1,151,4,46,3,10,255,203,254,66,252,94,2,248,2,60,0,166,0,248,255,93,255,206,254,57,7,3,10,21,253,255,251,9,249,93,254,66,254,209,0,50,253,202,253,234,253,6,254,181,2,89,3,49,254,71,253,198,251,69,1,175,1,50,255,241,255,248,255,5,253,33,2,151,3,238,5,157,253,241,252,223,250,0,1,201,255,208,0,91,255,164,254,106,253,65,1,168,2,162,3,186,254,83,254,73,252,228,1,190,1,58,2,59,255,72,0,183,255,141,3,175,5,205,6,205,253,31,253,74,248,132,255,96,254,206,2,34,254,108,254,198,254,240,255,190,1,100,6,217,253,231,253,18,253,198,255,126,2,214,0,55,0,71,255,241,254,124,4,21,5,188,4,29,254,97,253,16,251,117,0,29,1,31,2,52,255,121,254,145,255,1,2,2,6,86,3,142,255,66,255,46,252,109,3,83,2,208,1,4,1,4,1,201,254,236,2,235,8,168,8,251,253,79,252,133,247,186,254,60,253,122,1,212,252,77,253,24,255,208,253,175,2,129,5,36,253,78,253,188,252,153,254,133,2,130,1,247,254,62,0,90,253,145,0,108,6,184,4,213,253,36,252,47,251,178,255,14,0,114,0,185,254,154,254,23,254,136,1,165,2,185,2,55,255,20,255,140,251,181,2,193,1,178,0,13,255,0,1,79,254,99,2,105,5,152,9,156,253,123,252,72,250,205,254,239,255,243,1,197,254,101,253,2,255,0,1,172,1,183,5,26,254,90,254,224,251,143,2,114,1,18,0,154,255,71,255,236,254,243,2,42,6,55,5,24,254,165,253,118,250,182,0,163,255,102,3,183,255,54,254,164,254,67,3,94,3,189,3,230,254,179,254,22,253,35,2,71,3,172,1,17,1,167,255,13,0,172,3,172,6,16,10,94,254,196,251,34,249,212,255,154,254,3,1,15,254,125,253,208,253,99,0,45,2,193,3,91,254,2,253,107,252,39,1,70,1,184,0,175,0,15,0,142,253,20,2,110,3,189,7,69,254,0,253,5,251,221,0,156,0,12,1,39,0,149,254,7,254,183,2,4,3,116,4,94,255,53,254,112,252,197,2,188,1,146,2,25,0,47,1,200,254,244,4,130,5,179,6,215,254,2,253,212,248,249,254,148,255,46,4,106,254,243,255,127,255,57,0,182,1,174,10,138,254,25,254,189,252,48,1,184,2,164,0,104,0,21,255,5,0,75,6,108,7,119,5,27,255,186,253,211,250,149,1,192,0,49,3,169,255,74,254,111,0,4,4,175,4,225,3,68,0,81,255,90,252,9,4,93,4,195,1,222,1,200,0,8,255,79,8,136,10,250,7,189,252,213,250,173,247,225,252,76,253,210,1,212,252,248,251,43,254,146,253,32,1,152,3,67,253,183,252,210,251,101,254,0,2,8,0,122,254,165,255,24,253,226,255,19,4,137,4,202,252,132,251,124,251,218,254,210,255,110,0,101,254,138,254,90,253,214,0,19,2,156,2,106,254,92,254,86,251,231,1,232,0,47,1,194,254,91,0,40,254,123,0,208,4,141,9,46,253,72,252,41,250,30,253,93,253,52,5,225,253,162,253,45,255,161,255,158,255,228,5,219,253,254,253,87,251,217,1,211,0,73,0,224,254,144,255,123,254,25,2,52,5,234,4,201,253,13,253,247,249,71,0,229,254,120,2,86,255,31,254,19,254,169,2,234,3,49,3,156,254,181,254,147,252,163,1,194,2,90,1,241,0,222,255,186,254,121,1,158,7,91,7,41,253,205,251,167,249,23,255,225,253,116,0,244,253,218,252,183,253,183,255,222,1,217,2,224,254,99,252,137,251,173,0,191,1,204,255,68,0,27,255,162,253,193,1,17,2,5,7,177,253,149,252,173,250,183,0,112,255,68,1,153,255,60,254,102,253,111,2,232,1,152,4,18,255,1,254,20,252,70,1,40,2,202,1,136,0,108,0,193,254,114,2,63,5,91,7,22,254,122,253,62,249,70,255,63,254,216,3,30,253,180,255,86,255,218,253,243,2,0,10,16,254,2,254,77,252,210,0,182,2,204,255,84,0,190,254,57,255,66,4,89,6,200,4,136,254,165,253,140,250,87,1,74,0,120,2,81,255,10,254,224,255,204,3,52,5,222,2,52,0,217,254,167,251,41,4,150,3,160,0,137,1,107,0,115,254,190,4,89,10,205,6,136,253,79,251,157,248,49,253,235,254,97,1,117,253,144,252,134,255,45,255,209,0,58,5,206,253,54,253,221,251,48,255,132,1,159,0,192,254,195,255,217,253,37,1,68,4,163,5,120,253,159,252,27,251,207,255,113,255,49,1,111,254,29,255,183,253,49,2,20,2,159,3,139,255,69,254,92,251,251,1,180,1,36,1,177,255,233,0,54,254,159,2,1,4,92,9,135,253,182,252,11,250,204,254,226,254,128,2,139,254,147,253,105,254,162,1,253,0,25,5,197,254,187,253,143,251,60,2,173,2,231,254,61,0,188,255,141,254,223,3,77,4,218,5,19,254,85,253,174,250,209,255,164,0,192,2,0,255,198,254,244,254,119,2,181,3,28,4,138,255,164,254,191,252,68,0,156,4,56,2,152,0,117,0,34,0,89,4,110,7,191,8,167,253,65,252,86,249,113,255,23,254,224,1,180,254,113,253,194,253,54,0,97,1,168,4,50,254,116,253,228,252,150,0,37,2,112,0,195,0,145,255,253,253,167,2,84,4,111,6,210,253,19,253,63,251,247,255,16,1,85,1,203,255,247,254,233,253,233,1,75,3,18,5,136,255,30,254,248,251,120,2,31,2,152,1,179,0,50,1,242,253,100,4,184,5,196,8,95,254,238,252,230,249,32,255,128,254,84,5,135,254,53,254,231,255,129,1,233,1,126,8,180,254,117,253,195,252,32,2,41,2,61,0,22,0,143,255,167,255,104,4,189,6,244,5,40,255,139,254,139,249,161,0,60,1,140,3,91,255,34,255,189,255,82,5,151,4,21,3,73,0,4,255,1,253,226,2,164,3,104,2,106,1,246,0,130,255,19,3,94,10,211,11,77,253,174,251,114,247,203,253,180,253,12,2,178,253,45,252,22,254,249,254,141,1,214,3,191,253,187,252,79,252,234,255,179,1,207,255,66,255,138,255,139,253,168,255,216,4,233,5,132,253,229,251,5,252,221,254,189,0,3,1,255,254,42,254,139,253,145,0,177,3,126,3,186,254,148,254,186,251,31,2,4,1,118,2,54,255,189,0,47,255,101,1,99,5,43,8,199,253,205,251,87,250,54,253,17,255,151,3,92,254,63,253,172,255,147,255,142,255,103,9,99,254,239,253,103,251,226,1,112,1,131,0,70,255,184,255,125,255,93,3,231,4,196,4,157,253,110,253,195,250,227,0,135,255,119,2,80,255,23,254,38,255,233,2,151,4,189,3,191,254,108,255,88,252,159,2,198,3,216,0,84,1,253,255,113,255,213,1,56,7,133,9,39,253,63,252,109,249,43,255,2,255,65,1,1,254,74,254,247,253,130,255,213,2,135,3,172,254,83,253,248,251,60,1,224,1,20,0,23,0,167,255,217,253,97,1,27,4,253,6,224,253,11,253,172,250,42,1,231,255,180,1,156,255,120,254,249,253,211,1,242,2,54,4,46,255,114,254,202,251,108,2,146,2,118,2],"i8",A2,u1.GLOBAL_BASE+10240),C0([33,0,147,0,78,255,153,3,151,6,129,7,187,254,240,253,70,248,2,0,227,254,142,3,141,254,22,254,26,255,0,0,85,2,218,7,16,254,117,254,190,252,37,0,177,3,245,0,181,0,96,255,112,255,201,5,93,5,77,5,157,254,167,253,10,251,42,1,66,1,160,2,63,255,176,254,77,0,65,4,253,5,154,3,177,0,217,255,155,251,228,3,13,3,24,2,200,1,110,1,80,254,135,5,136,9,231,8,46,254,10,253,235,246,209,254,3,254,131,1,41,253,211,253,66,0,111,255,131,2,224,4,224,253,92,253,108,252,31,255,94,3,76,2,104,255,40,0,235,253,167,1,143,5,22,6,196,253,181,252,135,251,128,255,85,0,205,1,18,255,255,254,184,253,93,2,236,2,93,3,24,0,54,255,127,250,29,3,231,1,47,1,75,255,108,1,74,255,104,2,98,5,126,11,18,254,172,252,95,250,220,254,61,0,44,3,172,255,45,253,74,255,43,2,20,2,226,5,147,254,19,254,223,251,54,3,76,2,11,0,242,255,238,255,26,255,233,3,121,5,171,5,38,254,199,253,244,250,46,1,62,0,38,4,186,255,136,254,34,255,214,3,206,3,125,4,60,255,22,255,229,252,223,1,74,4,243,1,106,1,58,0,70,0,123,4,21,8,41,11,25,254,146,252,224,248,73,0,224,254,92,1,154,254,12,254,4,254,199,0,209,2,218,4,178,255,71,253,229,252,105,1,24,2,196,0,118,1,110,0,33,253,79,3,27,4,104,7,146,254,55,253,98,251,59,1,64,1,173,1,72,0,41,255,62,254,247,2,118,3,83,5,226,255,84,254,190,252,93,3,115,2,28,3,118,0,212,1,233,254,75,5,91,7,101,7,68,255,126,253,180,249,63,0,81,255,174,4,94,254,45,255,51,0,158,1,75,2,41,10,22,255,211,253,166,252,168,1,121,3,222,0,136,0,155,255,83,0,133,5,230,8,103,5,172,255,67,254,147,250,158,1,57,1,21,4,29,0,169,254,65,0,16,6,111,6,212,3,183,0,165,255,195,252,249,4,133,5,104,1,41,2,16,1,149,255,51,6,77,12,43,10,104,5,29,8,92,13,244,19,86,26,186,31,135,38,84,43,170,49,133,53,61,254,215,251,239,253,231,250,62,254,12,253,15,254,161,252,128,254,149,253,99,254,99,253,195,254,230,253,181,254,212,253,98,254,4,254,88,254,134,254,238,254,188,254,78,254,154,253,30,255,12,254,24,255,254,253,249,254,135,254,214,254,102,254,105,255,58,253,82,255,206,252,107,255,100,254,100,255,83,254,224,254,50,254,70,255,53,255,86,255,210,254,65,255,191,254,125,255,109,255,215,254,117,254,28,255,42,255,11,255,64,255,189,255,196,254,185,255,185,254,152,255,51,255,162,255,73,255,113,255,218,255,63,255,161,255,16,0,180,255,132,255,8,255,23,0,19,255,24,0,12,255,18,0,120,255,44,0,145,255,223,255,232,255,231,255,0,0,149,0,19,0,23,0,113,255,158,0,87,255,174,0,75,255,133,0,201,255,165,0,230,255,111,0,84,0,98,0,75,0,87,0,183,0,141,255,245,255,248,255,130,0,11,0,170,0,254,0,77,0,205,0,17,0,183,0,112,0,6,1,194,0,202,0,31,1,95,0,189,0,214,255,151,255,234,0,179,0,39,0,186,0,163,0,89,1,76,1,199,0,43,1,161,0,202,255,29,1,178,255,25,1,123,255,141,0,74,255,111,0,249,0,85,1,15,1,108,1,93,0,147,1,75,0,135,1,92,0,254,1,118,255,220,0,71,255,227,255,222,255,105,1,141,255,64,1,3,0,42,2,99,0,30,1,218,0,79,2,11,255,150,1,244,254,197,1,0,0,68,2,25,0,94,2,19,1,20,2,148,0,194,1,183,255,227,2,227,254,6,2,224,254,94,0,53,255,162,2,116,255,182,255,205,0,202,2,142,255,43,1,176,0,155,3,182,0,45,2,240,0,193,2,240,255,1,2,229,1,81,2,37,1,128,1,195,1,105,2,218,255,50,0,51,2,17,2,47,1,209,0,203,1,107,1,177,1,196,1,194,1,198,1,111,1,94,2,221,1,229,2,176,1,97,1,112,1,11,1,105,1,204,2,17,1,71,2,197,1,166,0,254,1,172,0,201,0,117,2,18,1,191,0,56,2,127,2,46,1,42,1,122,2,131,1,131,2,94,1,75,2,48,2,100,2,53,2,88,2,20,3,231,1,160,2,0,2,247,3,65,1,77,1,101,1,86,3,131,255,157,1,218,1,200,2,17,0,105,255,52,2,29,1,14,1,15,255,203,3,121,3,233,1,220,0,254,1,128,3,37,2,156,3,71,1,57,3,34,1,143,3,28,2,84,4,158,0,37,3,199,0,189,3,255,1,218,2,100,0,106,3,13,0,23,3,179,1,120,2,164,2,204,3,249,0,132,3,211,1,194,4,13,3,50,4,73,2,17,3,233,255,157,2,11,1,19,4,107,2,60,4,103,2,121,4,110,2,137,3,148,3,25,4,80,0,75,1,72,2,51,4,89,0,127,2,220,3,193,3,2,3,208,2,30,3,187,2,236,1,191,1,131,3,115,2,15,1,164,4,213,2,53,5,87,0,91,2,64,3,67,6,104,2,103,4,122,3,225,5,232,3,132,4,98,3,241,3,227,3,59,3,125,4,90,3,49,3,170,5,5,3,40,5,244,1,109,5,56,1,129,4,236,255,60,4,64,0,3,5,2,0,148,4,143,1,77,7,2,2,170,6,246,1,100,6,118,3,242,5,160,1,88,2,107,4,70,5,251,4,110,5,121,3,3,7,146,3,230,6,227,0,159,4,226,4,34,7,249,1,62,7,151,3,49,9,57,255,175,1,152,0,199,6,43,255,228,255,136,1,54,5,103,255,204,255,210,3,127,4,189,254,112,254,45,3,167,6,120,255,84,0,169,5,223,7,181,254,113,255,119,255,168,4,0,255,22,2,99,255,7,4,205,254,73,254,30,2,219,2,183,254,92,254,159,255,104,2,150,254,88,255,190,254,110,1,9,255,146,255,45,255,89,0,60,255,203,254,20,0,59,0,148,254,49,254,226,254,89,0,176,254,175,0,80,254,141,0,133,254,66,255,78,254,60,255,177,255,150,0,234,254,29,255,232,254,166,0,213,253,90,254,101,255,29,2,146,254,54,0,227,255,173,255,211,254,250,252,186,0,116,2,115,254,248,254,242,0,37,1,59,255,183,253,124,0,154,1,53,0,123,255,10,0,84,1,198,253,215,251,65,0,66,254,68,0,19,254,127,1,169,3,155,254,57,253,153,254,6,255,91,253,212,251,36,1,230,255,107,1,6,0,95,2,33,5,129,255,246,255,233,5,94,7,201,2,204,3,189,5,133,8,163,5,224,7,161,249,192,249,252,248,14,247,253,251,22,249,180,251,23,248,3,251,148,250,169,250,2,250,77,252,75,250,52,252,12,250,25,252,58,251,4,252,108,251,209,252,37,252,32,252,165,250,64,251,18,252,247,250,186,251,24,253,12,251,13,253,243,250,162,252,101,252,119,252,40,252,90,253,229,251,83,253,230,251,193,251,39,252,218,251,89,253,35,252,127,253,153,251,48,252,6,253,114,253,134,252,218,252,191,252,189,251,62,253,139,253,147,253,218,252,128,253,212,252,249,252,134,253,245,252,225,253,28,252,203,253,205,251,188,253,222,253,157,253,196,253,149,253,8,253,222,254,145,252,242,253,201,252,50,254,229,252,3,255,215,253,97,254,179,253,73,254,235,253,172,254,76,253,89,252,7,254,252,252,66,253,149,251,249,254,206,254,53,252,29,254,67,254,182,255,213,253,220,253,154,253,127,255,75,253,22,255,116,254,10,255,37,254,6,255,247,254,108,254,136,254,254,253,95,254,2,254,212,254,199,254,178,254,104,253,49,254,210,252,126,254,64,253,175,254,153,253,22,255,55,255,23,255,17,255,89,255,201,253,53,255,149,253,109,255,97,254,141,255,160,254,90,255,18,253,85,255,7,253,242,254,145,252,248,254,121,252,145,254,24,253,43,0,37,254,14,0,115,253,43,0,98,253,11,0,64,254,197,255,247,253,130,255,137,255,101,255,155,253,214,255,161,252,229,255,93,252,136,0,29,254,183,0,44,254,55,0,214,254,55,0,208,254,57,1,159,253,57,1,48,253,66,1,89,255,100,0,227,253,253,255,137,255,145,255,69,255,233,0,20,255,4,1,22,255,26,0,91,255,134,0,211,255,216,255,219,253,104,1,53,255,122,1,124,254,194,1,129,254,19,1,20,0,182,0,153,255,246,0,145,255,175,1,37,0,206,1,110,255,231,1,99,255,228,254,197,255,247,1,72,255,24,0,53,0,253,255,54,0,122,0,3,1,77,1,66,0,228,1,104,0,180,1,68,0,195,0,116,0,190,0,206,0,13,1,247,255,226,1,96,1,126,1,29,1,143,1,21,1,196,1,0,1,69,0,186,0,13,0,41,1,243,255,3,1,161,255,30,0,56,0,138,1,196,0,169,1,205,0,200,1,25,1,65,2,15,0,191,0,119,1,34,1,151,1,64,2,200,255,227,0,32,2,149,1,0,0,37,2,164,255,16,2,27,255,95,1,11,255,82,1,150,254,179,1,167,0,15,2,181,255,46,1,91,0,56,3,129,0,87,2,240,1,167,2,186,0,237,2,153,0,225,2,231,254,88,2,164,254,103,2,20,255,1,3,41,0,113,3,38,0,122,3,36,255,73,3,155,254,115,3,119,254,135,3,134,253,218,1,68,254,82,3,81,255,166,2,19,254,242,0,249,253,17,3,54,253,70,2,227,253,110,1,225,253,178,1,171,253,244,1,3,253,222,0,66,253,149,3,25,253,194,3,155,252,245,1,125,252,36,2,133,254,200,0,77,254,157,0,205,252,214,0,163,252,157,0,154,253,40,0,136,253,94,0,141,252,202,255,27,253,4,2,11,254,42,1,154,253,85,255,154,252,95,255,159,252,233,255,206,252,93,0,9,252,245,254,106,253,153,254,219,253,2,0,70,254,135,255,135,254,0,0,29,255,33,0,98,254,130,255,127,255,212,0,90,252,34,0,198,251,230,254,161,251,244,254,58,253,199,252,92,254,65,255,204,251,96,252,107,252,163,255,140,253,154,254,97,0,7,0,50,255,119,254,155,255,24,0,53,255,38,0,88,255,83,0,169,253,89,254,233,254,170,1,68,253,118,0,181,255,206,0,43,252,95,253,88,253,161,1,145,254,37,0,233,254,218,1,127,255,194,254,63,1,40,1,142,253,217,255,87,1,90,2,72,253,217,255,209,254,172,3,104,0,233,0,132,254,137,0,220,255,13,1,181,255,42,255,120,0,43,0,239,253,35,254,203,1,164,0,54,255,27,255,207,255,89,255,97,2,24,3,98,0,36,255,147,3,148,0,37,1,27,1,101,3,91,0,63,2,138,1,70,1,178,255,205,2,67,0,109,1,189,254,104,2,220,255,219,2,27,0,107,2,238,0,120,2,17,1,192,1,99,0,33,3,220,1,101,3,17,1,173,2,64,0,21,3,72,0,253,3,217,0,25,3,203,1,222,2,104,1,134,2,224,1,104,1,66,1,173,1,208,1,126,2,174,1,244,2,107,1,232,3,148,1,171,2,16,2,90,2,103,2,143,2,157,1,178,3,175,2,169,3,90,2,136,3,92,2,43,2,225,2,18,3,150,2,211,1,142,2,106,1,77,2,161,3,198,2,242,1,222,1,159,1,164,1,181,2,115,3,45,3,171,2,13,3,157,3,145,3,171,3,214,2,220,2,235,1,85,3,19,2,180,3,222,2,195,3,59,1,40,3,249,2,243,2,120,4,248,2,143,2,52,4,58,3,33,4,67,4,70,3,235,3,40,3,23,4,109,4,147,2,77,4,224,3,26,4,50,4,51,4,203,3,182,2,202,4,30,4,59,2,73,3,116,3,124,5,99,5,72,4,56,4,93,3,207,4,223,2,4,5,248,2,248,4,223,3,87,5,29,4,233,4,188,2,26,4,22,2,220,3,197,1,240,4,87,2,116,4,167,2,85,6,47,3,104,5,9,2,37,5,137,1,28,6,37,3,168,5,174,2,44,4,136,2,107,3,51,1,59,4,105,1,23,4,61,1,137,5,196,3,163,2,59,2,128,4,79,0,90,4,209,255,250,5,55,1,185,6,58,1,142,4,177,2,2,2,162,255,93,1,26,1,132,5,72,1,1,4,231,1,191,255,57,0,37,3,202,3,36,0,62,0,1,3,249,254,23,3,166,254,125,2,187,2,119,255,108,2,22,2,29,2,33,253,194,0,199,2,44,1,244,254,161,252,158,3,1,3,60,253,84,254,250,1,174,0,132,252,138,253,179,1,35,2,101,250,254,254,109,2,215,1,6,252,168,250,119,254,9,2,104,252,82,253,231,255,20,0,42,252,124,251,84,1,9,0,234,249,145,251,160,254,48,0,213,249,110,254,137,252,6,0,124,251,136,252,220,253,160,254,149,249,112,251,97,255,98,2,24,248,61,252,31,255,193,0,136,249,88,248,11,255,19,254,60,252,112,249,88,252,133,253,237,250,48,249,148,250,164,253,252,249,189,252,139,250,121,255,204,249,222,254,122,249,56,253,37,248,160,249,129,249,229,255,46,247,213,252,123,251,184,0,15,251,189,0,169,250,74,2,37,248,201,0,234,252,200,2,70,251,3,0,247,251,40,3,29,251,62,3,145,255,123,2,156,249,191,1,49,254,75,252,67,254,96,252,8,254,118,251,11,254,69,251,144,0,161,254,140,254,228,251,229,254,221,251,233,254,157,251,193,253,98,250,181,253,178,249,89,252,40,252,229,0,178,2,103,252,49,253,109,254,82,5,83,253,47,254,106,3,141,1,3,254,210,255,61,1,54,5,27,254,200,1,45,3,183,1,101,254,83,1,130,3,43,4,87,254,46,0,161,5,241,1,115,252,224,252,185,5,22,4,2,255,191,254,150,5,141,4,68,0,94,1,10,4,154,2,114,1,11,0,31,5,22,3,143,0,232,0,17,4,26,6,142,255,151,2,80,6,54,4,198,1,67,2,251,4,16,4,180,255,141,3,240,2,43,4,153,0,0,2,92,1,190,4,102,2,129,1,51,7,40,3,13,1,10,4,203,0,62,4,140,2,249,3,247,6,106,4,173,1,47,5,131,1,104,5,207,255,159,4,184,255,191,4,96,254,233,3,32,2,213,6,160,254,199,4,10,254,175,4,179,253,57,2,29,255,94,6,114,255,42,6,26,255,179,6,54,253,8,5,186,252,118,5,107,4,77,5,48,255,208,4,181,1,197,3,95,252,50,3,43,3,130,5,91,3,227,5,164,0,188,4,107,5,1,7,228,1,82,7,200,1,15,8,228,3,146,4,46,5,122,5,36,5,80,5,111,4,238,4,210,4,82,6,81,5,232,6,141,5,203,4,48,6,67,5,86,3,160,2,149,6,30,6,115,4,246,4,224,7,33,7,237,6,45,6,252,5,180,5,207,5,178,3,123,6,253,3,208,6,188,4,112,5,209,3,236,6,137,4,34,7,140,4,182,6,149,5,181,7,55,6,161,4,96,3,84,8,37,4,7,7,46,3,46,7,245,2,56,8,35,5,6,8,234,4,65,8,147,3,27,9,162,3,187,5,123,4,30,10,159,5,197,8,208,6,42,8,84,6,54,9,174,5,106,10,226,5,84,7,45,7,22,8,183,7,203,6,41,6,170,2,9,5,48,6,253,7,174,5,50,8,194,9,212,7,151,10,18,8,214,2,52,6,196,10,32,9,228,0,79,3,152,9,123,6,36,0,45,1,150,7,165,7,66,254,160,255,106,8,116,5,253,5,77,4,14,0,96,2,101,252,36,253,103,5,190,7,65,5,184,3,88,253,65,1,1,5,244,4,198,249,109,1,173,3,178,3,55,249,202,252,70,9,227,10,29,7,228,10,236,248,29,247,169,248,23,246,152,249,200,248,97,249,44,248,60,251,136,248,59,251,198,247,233,249,204,249,219,249,236,249,85,251,177,249,56,251,65,249,177,250,129,251,176,249,100,248,6,251,145,250,231,250,133,250,185,249,101,251,116,249,225,250,93,250,58,250,169,250,126,252,24,251,221,251,205,250,146,251,42,252,147,251,131,251,32,250,200,251,228,250,4,252,97,251,44,252,50,250,57,252,41,250,36,252,102,252,233,251,203,251,186,252,101,251,166,252,58,251,149,251,239,251,216,251,1,253,152,252,123,251,67,253,144,252,62,253,118,252,250,252,8,252,190,253,200,251,223,252,58,250,177,253,169,251,176,253,134,251,55,253,148,250,128,253,160,250,171,253,221,251,96,254,121,252,82,253,192,252,107,253,60,253,68,254,156,252,22,254,103,252,138,254,248,252,149,253,110,251,183,253,219,253,255,252,229,252,77,254,109,253,238,253,27,253,14,254,187,252,155,254,171,253,233,254,153,252,13,255,137,252,230,254,103,253,232,254,101,253,91,255,208,253,118,254,121,252,150,254,102,254,64,254,185,253,103,254,194,253,199,254,155,254,131,253,220,253,198,253,76,254,128,252,8,254,130,254,11,253,198,255,31,254,91,255,150,253,65,255,138,254,22,255,130,254,34,255,85,253,231,255,32,254,94,254,153,254,38,253,159,254,188,254,99,255,80,254,190,254,118,254,209,254,228,254,152,255,167,253,223,254,212,253,60,255,180,253,106,255,109,253,160,253,39,254,232,255,188,255,64,254,38,254,248,255,6,254,211,255,20,253,72,255,180,252,4,255,123,252,165,255,184,253,159,255,116,253,138,0,4,253,125,255,90,253,244,255,98,253,165,0,253,254,253,255,184,252,149,255,115,252,37,0,32,252,44,0,170,252,97,254,185,252,13,0,23,252,241,254,254,251,203,254,226,252,34,254,192,252,24,254,81,252,168,0,168,251,125,254,95,251,155,255,97,251,216,255,83,252,196,254,250,251,254,252,236,251,143,253,199,251,230,253,56,251,213,254,224,250,76,254,83,251,105,253,113,251,95,255,64,251,78,253,43,251,193,252,104,250,48,253,133,250,19,254,126,252,28,253,102,252,223,252,178,251,110,254,213,249,60,252,219,251,130,253,11,251,98,250,37,250,90,252,34,250,129,252,194,249,204,253,69,249,51,253,162,253,171,253,114,251,195,251,167,250,44,254,102,248,43,250,210,248,71,252,116,248,93,252,37,250,68,255,157,249,91,254,79,250,174,254,88,250,234,255,106,248,90,254,42,248,7,255,16,254,142,255,138,248,13,253,247,250,174,0,85,250,147,255,30,254,255,254,59,251,4,254,175,249,151,0,98,249,208,0,114,253,107,0,141,249,29,0,139,251,23,1,65,251,50,1,52,251,6,254,38,253,81,255,44,251,155,255,55,252,39,2,154,252,22,1,201,252,59,1,205,253,120,1,229,251,228,0,5,254,24,1,169,253,25,1,10,253,253,0,207,254,123,1,13,253,122,255,157,253,148,2,200,252,24,2,207,252,134,2,99,254,49,0,171,254,177,0,59,254,14,2,30,254,77,2,185,255,83,1,111,253,8,1,12,255,39,1,19,255,59,1,125,254,57,2,6,254,247,255,135,254,14,0,96,255,149,2,40,255,40,0,204,254,210,255,95,0,214,0,14,255,167,0,170,255,192,0,200,255,27,0,180,255,31,0,36,0,53,1,150,255,74,255,143,255,74,0,71,254,234,255,23,0,139,0,81,0,245,255,44,0,15,0,169,255,119,255,138,255,49,255,98,255,198,255,16,1,164,255,100,255,71,254,8,0,120,255,128,0,35,255,101,0,38,255,40,0,59,255,180,255,56,254,9,0,67,254,33,0,89,254,226,0,60,0,73,0,34,255,156,0,113,254,24,1,194,254,245,0,171,254,166,0,13,254,83,1,66,255,71,1,37,255,69,1,119,255,167,255,172,253,100,0,141,253,144,0,91,253,231,1,28,0,252,0,121,254,214,0,215,255,26,1,228,255,99,0,226,254,75,1,49,0,203,1,124,254,53,2,143,254,180,1,28,0,80,1,247,255,141,1,89,255,106,2,34,0,84,2,239,255,49,2,116,255,43,1,79,0,10,2,125,0,203,0,2,0,244,0,32,1,255,0,211,0,175,0,82,0,84,2,187,0,5,2,108,0,125,1,255,0,109,1,41,1,241,1,96,1,71,1,174,255,25,0,210,0,115,1,245,0,5,1,3,0,33,2,193,1,140,0,38,1,44,0,39,1,212,0,91,1,244,0,238,1,75,1,16,2,201,0,51,1,93,1,155,1,101,2,28,1,102,2,157,1,208,1,66,1,112,2,141,1,97,0,200,0,96,255,128,1,149,0,106,1,239,1,13,2,13,1,73,2,33,0,235,1,135,255,177,1,171,1,99,2,242,1,4,2,171,0,187,1,241,1,154,2,184,1,19,1,54,2,63,2,146,0,127,2,155,0,158,2,223,255,173,0,212,0,184,2,90,255,89,2,65,255,183,2,23,254,247,1,175,0,230,2,214,0,220,1,116,1,59,4,66,2,18,2,74,2,9,3,169,1,106,3,59,1,73,3,118,1,80,3,91,255,53,2,35,0,223,3,217,255,38,4,73,1,200,2,18,3,72,3,133,2,27,3,149,2,164,2,59,2,150,3,120,2,55,4,161,2,49,3,62,1,132,1,106,3,244,3,52,2,80,3,112,3,108,2,45,2,223,1,159,2,197,1,180,2,212,1,72,3,130,2,76,3,133,2,250,1,172,1,129,3,55,2,69,3,131,1,194,3,243,1,179,2,49,2,171,3,158,3,15,3,40,1,22,3,12,1,4,4,18,2,106,3,73,1,36,2,143,0,163,2,35,1,247,1,66,0,17,4,103,1,18,3,97,0,37,3,33,0,69,3,214,1,255,1,49,0,68,4,71,1,150,4,67,1,3,0,242,0,104,3,218,1,177,2,173,1,49,5,166,2,18,4,108,2,85,4,152,2,65,1,193,0,121,3,182,3,129,4,106,3,125,3,123,2,109,3,94,3,180,3,145,3,13,5,153,2,40,5,127,2,229,3,25,3,122,5,6,4,152,4,244,3,86,4,191,3,130,5,157,3,123,5,147,3,31,2,94,3,92,4,198,4,67,3,166,4,67,3,166,4,191,3,124,4,123,4,96,5,20,5,169,4,135,5,207,4,55,5,61,5,234,2,68,4,175,6,3,5,109,5,49,4,54,5,30,6,129,4,195,5,109,6,113,4,33,7,196,4,32,4,102,5,241,5,194,6,96,6,9,6,84,6,6,6,87,3,60,6,97,3,131,6,181,2,117,3,180,6,239,5,143,4,16,5,161,8,224,6,160,7,213,5,228,7,202,5,254,5,74,7,158,6,216,7,30,6,236,2,225,6,57,3,38,1,112,5,60,4,10,8,109,2,35,5,109,1,7,5,198,0,4,4,232,1,128,5,249,0,147,1,246,3,25,6,68,1,107,1,109,6,20,4,193,0,111,1,242,7,67,7,5,255,67,2,238,2,226,3,13,255,30,0,45,5,111,3,228,255,87,255,112,2,149,3,59,254,159,0,186,0,90,5,154,253,6,0,25,2,136,1,162,255,221,254,13,3,229,0,128,255,214,254,245,0,235,1,67,253,120,253,204,3,21,3,11,254,128,253,178,0,255,0,147,254,122,254,1,255,61,1,66,252,218,254,65,255,228,0,249,252,65,254,157,0,19,255,111,253,48,253,105,254,92,0,139,255,157,253,78,1,26,255,89,253,196,251,112,255,195,254,123,252,163,252,30,253,152,254,171,255,41,253,166,255,237,252,100,0,234,255,121,254,249,254,200,255,183,255,175,254,14,253,5,0,67,255,62,253,144,253,89,0,168,254,121,255,167,251,159,254,19,255,84,253,145,251,237,254,178,251,243,254,77,251,152,0,145,0,46,253,48,251,49,0,80,0,32,251,248,252,8,255,135,1,36,253,221,253,213,1,218,0,1,255,160,252,69,0,110,1,90,255,27,254,80,253,191,0,68,251,84,251,86,255,87,255,228,250,161,249,65,1,214,1,117,250,37,251,192,255,16,1,175,250,8,255,236,1,53,2,47,253,159,253,195,0,229,1,195,253,123,255,171,1,202,0,85,255,138,255,199,0,63,2,2,0,225,255,182,2,243,2,170,250,217,255,40,2,45,2,23,254,15,1,168,2,25,2,13,0,59,254,87,3,186,3,123,255,204,255,175,255,226,2,111,251,125,2,31,4,35,4,161,255,164,2,235,4,57,4,233,1,49,1,63,254,186,3,234,253,228,3,55,252,98,3,222,251,35,4,242,250,106,2,120,250,105,2,54,254,86,5,97,255,29,7,250,252,240,253,242,255,86,4,78,251,123,252,252,252,177,1,24,251,25,251,13,252,210,254,166,253,183,253,9,253,174,249,8,253,243,249,184,252,127,248,208,252,229,253,23,249,69,247,29,255,220,255,14,248,217,248,197,247,154,251,89,246,232,248,66,250,252,0,115,245,97,254,197,253,45,254,229,5,18,6,132,8,183,7,22,9,228,7,191,248,111,249,191,248,37,249,248,247,130,251,170,247,138,249,173,249,181,251,88,249,149,251,191,250,184,249,177,250,154,249,198,250,243,250,211,250,15,251,128,249,143,249,49,250,173,252,190,250,216,248,123,250,116,247,254,250,87,253,7,249,143,249,58,252,198,251,97,251,116,249,226,251,207,251,138,251,122,251,73,251,24,253,6,251,27,252,90,252,153,250,97,252,120,250,14,252,231,250,241,252,69,252,231,251,124,252,31,252,207,252,31,253,201,252,52,252,91,251,30,253,186,251,30,253,126,251,240,252,223,252,214,252,238,252,132,252,248,253,24,252,206,252,124,253,59,252,191,253,142,252,227,253,74,253,97,253,107,252,173,253,126,253,122,253,153,253,68,252,147,253,99,252,253,253,41,253,29,254,209,252,27,254,184,252,190,253,72,254,55,253,190,253,187,254,111,253,98,253,126,254,198,253,71,254,102,253,254,253,237,252,120,254,239,253,246,253,59,254,25,254,89,254,152,253,183,253,151,253,99,255,106,253,244,254,88,253,164,254,190,254,189,254,136,253,68,254,208,254,82,254,180,254,54,254,235,254,44,254,109,253,231,252,193,254,132,253,29,255,214,253,139,254,165,254,178,254,46,255,56,254,64,255,238,253,14,255,40,255,58,255,146,254,142,254,174,254,95,255,103,254,20,253,149,255,132,254,218,254,125,253,33,255,103,253,22,255,27,253,115,255,16,254,126,255,2,254,117,255,185,254,84,255,207,254,206,254,188,253,92,255,249,254,250,254,84,255,189,255,110,254,31,0,146,254,246,255,76,254,170,255,241,253,71,0,135,254,234,255,159,253,244,255,90,253,189,255,193,254,63,0,65,255,35,0,75,255,217,255,14,255,126,0,89,255,116,255,224,253,155,0,215,254,174,0,215,254,38,0,248,255,117,0,132,254,197,0,60,254,240,0,246,253,223,0,153,255,110,0,69,255,87,0,101,255,169,0,209,255,157,0,26,0,173,255,156,255,128,0,80,0,209,0,194,255,6,0,7,0,22,0,5,0,62,1,236,255,248,0,211,255,56,255,193,255,156,0,187,255,250,0,73,255,113,1,130,255,143,255,180,255,114,255,134,255,192,255,2,255,225,255,35,0,79,255,185,255,249,255,171,0,93,0,27,0,108,0,212,0,182,254,47,255,133,255,186,255,233,254,95,0,160,255,20,0,68,255,195,255,198,254,87,0,212,254,178,255,158,254,122,255,11,0,122,0,116,255,122,0,237,254,152,0,219,254,140,0,174,255,138,0,191,254,145,255,32,254,100,255,153,254,76,0,2,255,216,255,133,253,160,255,246,253,79,0,5,254,8,0,244,254,47,1,229,253,68,0,66,254,61,0,246,253,50,1,111,0,189,0,77,254,122,0,133,254,166,0,197,253,114,254,136,253,182,255,21,253,161,255,57,254,194,0,72,252,83,0,226,252,192,0,13,253,192,0,243,252,94,255,149,253,234,0,105,253,215,254,24,254,147,255,60,252,124,255,186,252,188,255,181,252,58,0,168,251,170,255,219,252,213,254,80,252,3,255,246,252,206,255,59,252,219,253,160,254,158,255,32,252,169,254,163,251,197,254,163,251,205,254,125,251,138,254,131,253,26,255,114,251,213,255,237,250,156,255,99,252,119,254,6,251,168,253,79,253,126,255,57,250,200,254,215,250,2,255,72,250,70,254,244,250,155,253,19,251,9,254,35,250,144,254,214,250,26,0,104,250,190,255,49,249,95,255,148,249,45,254,32,249,220,253,143,250,200,253,236,249,153,252,41,250,246,251,149,250,197,253,131,248,240,253,9,249,133,255,151,248,25,255,250,247,189,254,252,247,118,252,72,248,201,253,131,248,148,253,1,248,35,252,203,251,142,254,17,248,64,253,205,246,19,253,76,245,191,251,139,248,159,0,36,248,248,0,142,253,133,255,221,246,62,252,99,253,104,254,157,250,106,251,60,254,148,254,236,251,33,253,124,255,183,0,172,249,16,253,221,253,205,254,247,252,19,251,158,255,41,0,144,252,189,251,255,254,97,0,190,249,215,248,31,0,230,255,124,253,207,253,76,255,222,253,127,254,185,251,102,254,222,252,98,254,197,252,55,254,54,252,22,254,171,251,41,255,108,252,112,255,87,252,19,254,11,251,251,253,29,250,181,0,101,0,180,254,135,252,188,252,87,252,209,253,83,254,139,253,221,253,73,255,175,254,223,253,174,255,6,255,226,254,5,0,124,255,164,254,4,255,219,254,40,254,98,255,100,0,227,255,197,0,20,255,88,254,163,252,43,255,116,255,249,255,85,254,69,254,187,0,159,255,84,253,32,253,219,254,2,1,144,254,104,255,106,255,136,1,159,253,175,0,114,255,43,1,118,255,152,0,137,255,73,1,26,254,204,255,37,1,198,0,73,255,117,0,175,0,75,1,198,255,238,254,231,0,44,1,224,254,74,1,207,254,116,1,145,255,153,1,247,255,167,1,83,0,0,1,67,0,111,1,237,255,248,0,91,0,113,0,221,255,150,1,65,255,154,0,238,0,40,1,5,0,197,0,141,0,221,0,57,1,198,0,211,0,165,1,244,0,78,1,88,0,170,1,13,255,198,1,202,0,40,2,251,255,147,1,35,1,185,0,219,0,45,1,251,0,138,0,128,0,69,0,197,0,32,1,116,255,195,255,188,0,105,1,197,0,86,2,186,1,17,1,34,1,143,0,216,1,226,1,157,0,114,1,159,1,65,1,116,1,129,1,146,1,40,2,155,0,24,0,38,2,7,1,245,255,21,0,104,1,227,0,147,0,2,255,168,1,97,0,110,1,243,255,119,1,141,0,193,1,232,0,140,1,251,1,218,1,16,1,189,2,68,1,106,1,209,255,75,2,148,0,31,2,69,0,144,1,205,255,49,2,59,0,220,0,246,255,96,1,147,0,206,0,211,0,141,2,185,0,51,2,41,1,53,2,28,1,82,2,121,0,254,2,192,0,142,1,118,0,130,2,178,1,233,0,8,1,225,1,211,1,129,0,91,255,187,2,239,0,90,0,26,0,86,1,218,1,201,255,27,0,132,1,94,0,84,255,0,0,213,2,123,1,196,255,81,1,114,1,209,1,95,0,63,1,38,3,83,2,78,0,4,1,241,1,83,3,210,0,48,2,202,1,62,2,48,254,202,0,241,1,113,2,54,255,152,0,48,0,200,2,236,255,54,2,100,0,203,2,199,1,212,1,155,1,93,2,63,1,134,2,195,0,103,2,145,1,26,2,168,2,227,2,201,0,155,2,178,1,186,3,198,1,169,1,134,2,235,1,94,2,169,2,160,1,252,1,241,1,54,3,170,1,47,3,148,2,135,2,116,2,204,2,185,2,210,1,106,2,201,1,173,2,204,1,109,1,53,1,209,2,55,2,68,3,89,2,97,2,44,1,57,3,203,1,175,3,175,2,169,2,21,2,147,3,86,2,79,2,243,0,108,3,195,1,106,3,164,1,18,3,61,1,220,2,220,0,154,3,61,1,84,4,111,1,19,2,210,1,4,4,137,2,29,4,103,2,10,4,41,2,61,3,90,2,253,3,31,3,159,3,35,3,110,3,251,2,31,3,240,1,93,5,5,3,73,2,2,3,35,3,162,3,75,4,25,3,198,4,94,3,185,4,127,3,1,4,215,2,4,3,77,3,148,4,91,4,99,3,253,3,62,3,245,3,73,3,142,3,250,1,191,2,215,4,53,4,108,2,51,3,172,4,59,4,131,4,57,4,118,4,139,3,11,6,97,4,29,5,136,2,63,5,100,2,204,5,220,3,199,5,169,3,217,3,48,5,187,3,61,5,173,1,142,3,73,3,58,5,52,2,155,4,156,1,132,4,147,5,40,5,154,5,50,5,128,2,248,2,190,6,130,5,190,0,43,2,49,4,237,3,170,1,1,1,71,3,212,3,235,0,231,0,240,5,143,4,109,0,37,1,246,3,33,6,49,1,142,0,124,4,27,2,221,254,148,255,189,4,204,3,22,0,40,255,155,2,60,3,30,254,182,1,197,1,151,5,187,253,90,254,21,3,131,1,154,254,58,254,174,0,12,3,220,255,140,254,134,1,122,255,139,253,160,0,206,254,239,2,22,251,181,254,177,0,10,2,8,255,62,2,5,255,127,2,237,253,151,1,172,253,138,1,93,254,21,3,151,253,33,3,38,252,143,1,167,252,215,2,249,255,6,2,65,253,54,1,137,251,232,255,22,252,31,1,64,252,107,1,237,250,56,1,2,250,245,0,235,249,49,1,28,0,153,0,165,252,81,255,223,255,76,1,138,250,102,255,212,0,154,1,175,253,59,255,188,251,64,253,120,252,191,255,26,1,111,1,106,252,82,253,89,1,93,0,254,254,155,254,184,2,132,2,75,253,228,255,192,1,237,1,239,254,193,0,15,2,34,2,13,255,255,253,128,1,120,255,17,1,159,254,0,2,114,255,25,2,58,255,173,3,238,2,83,0,248,0,66,2,93,3,200,255,80,2,74,3,44,0,124,3,24,0,33,0,122,3,240,255,214,3,63,3,118,5,255,5,106,7,180,6,96,5,156,7,185,5,22,252,95,252,184,251,77,251,127,253,93,252,164,253,63,252,245,252,95,253,189,252,236,252,96,254,104,253,54,254,2,253,116,253,247,253,106,253,17,254,1,252,3,254,1,252,84,254,68,254,216,253,144,254,63,254,33,254,45,255,226,251,121,252,196,254,7,255,199,253,177,253,199,253,237,254,227,253,65,255,52,253,68,255,182,252,248,254,179,254,8,255,194,254,28,255,237,254,1,0,201,253,28,255,141,255,35,255,18,255,138,254,59,255,5,254,34,255,189,253,254,254,80,254,195,255,12,255,167,254,2,0,174,254,39,0,41,255,87,255,198,255,0,0,200,255,250,255,53,255,125,255,1,0,70,255,251,255,45,255,6,0,132,254,11,0,94,254,140,255,131,0,122,255,113,0,89,0,252,255,71,0,254,255,237,255,64,255,6,1,24,0,189,0,151,0,123,255,147,255,186,0,103,255,166,0,37,255,37,0,139,0,193,0,171,0,81,1,124,0,158,0,195,255,141,0,226,0,243,255,190,0,231,0,34,0,98,1,109,0,60,1,201,0,244,0,164,0,74,1,171,255,134,1,172,255,254,0,71,1,1,1,79,1,235,1,147,0,220,1,105,0,54,0,77,0,181,1,114,1,165,1,58,1,193,1,86,1,73,1,126,0,161,2,36,1,59,2,132,1,243,0,193,0,141,2,64,1,109,2,24,1,194,0,124,1,5,2,69,2,45,0,67,1,111,0,166,1,233,1,139,1,222,2,22,2,110,2,34,2,230,1,246,1,62,1,60,2,189,0,38,2,129,1,166,1,99,255,153,0,131,255,126,1,59,255,130,1,249,254,78,1,228,0,185,2,68,255,1,0,51,0,41,1,5,254,213,0,136,254,141,1,232,255,255,0,221,253,89,0,10,254,162,255,131,1,179,0,148,253,68,0,84,253,112,0,126,253,162,254,252,254,172,0,74,254,188,254,8,1,136,2,60,252,252,255,159,251,7,0,122,255,134,0,147,251,206,254,143,0,96,0,92,254,15,254,59,251,162,254,9,250,83,253,95,255,72,0,105,3,179,2,220,2,27,1,153,3,97,1,78,1,219,1,71,4,53,3,96,3,12,2,75,3,241,1,202,2,199,2,20,3,238,2,52,4,202,2,180,4,241,2,65,2,150,2,124,245,170,192,38,3,44,7,95,251,33,228,37,12,28,4,40,248,202,208,85,16,107,5,192,249,99,218,69,9,145,5,232,249,78,219,176,12,193,7,210,251,214,230,35,7,16,9,184,252,64,236,173,3,242,12,199,254,163,248,47,9,161,11,41,254,234,244,32,14,116,9,247,252,183,237,123,13,24,12,98,254,70,246,139,11,205,16,72,0,178,1,56,7,148,17,139,0,68,3,44,15,40,21,157,1,180,9,163,4,42,28,67,3,166,19,11,12,40,35,139,4,90,27,216,28,115,3,37,247,177,202,74,23,226,5,58,250,60,221,35,20,86,8,61,252,88,233,8,31,217,7,228,251,65,231,107,25,202,8,139,252,49,235,246,29,192,10,180,253,47,242,64,23,200,11,60,254,92,245,34,19,180,14,131,255,17,253,77,27,4,14,60,255,103,251,238,31,138,15,213,255,252,254,176,23,52,17,107,0,133,2,29,30,223,19,64,1,136,7,147,21,133,23,57,2,98,13,89,30,214,27,50,3,62,19,172,23,2,31,209,3,253,22,218,21,223,44,243,5,212,35,85,41,76,5,159,249,153,217,89,35,61,6,145,250,68,223,66,38,243,7,247,251,180,231,242,34,111,9,244,252,164,237,56,40,24,10,87,253,253,239,191,36,174,10,171,253,245,241,252,33,146,12,156,254,160,247,29,38,67,13,235,254,123,249,193,39,52,15,181,255,58,254,210,35,176,17,148,0,123,3,168,39,140,19,40,1,245,6,154,35,103,22,241,1,177,11,4,41,122,24,116,2,198,14,126,39,207,29,151,3,158,21,140,34,23,34,93,4,72,26,252,34,208,48,112,6,193,38,124,50,208,3,185,247,47,206,171,44,219,6,28,251,141,226,106,47,24,9,189,252,96,236,124,44,64,9,214,252,248,236,204,41,248,11,83,254,236,245,44,48,45,11,238,253,136,243,202,45,255,12,205,254,200,248,6,44,116,14,106,255,120,252,109,42,61,17,110,0,151,2,50,47,181,17,150,0,134,3,19,44,85,20,98,1,84,8,184,46,161,24,125,2,253,14,159,43,110,29,132,3,44,21,96,47,137,32,25,4,168,24,217,42,25,42,149,5,156,33,60,40,224,67,87,8,53,50,75,54,145,6,220,250,15,225,36,49,253,7,254,251,221,231,209,51,135,9,2,253,254,237,209,54,173,11,47,254,14,245,140,52,26,12,99,254,78,246,108,48,74,14,89,255,18,252,198,52,196,14,137,255,55,253,80,50,176,16,62,0,118,1,221,52,253,18,253,0,243,5,123,49,81,21,168,1,248,9,30,54,218,23,78,2,223,13,231,50,83,25,166,2,244,15,245,52,41,30,169,3,7,22,157,50,95,36,189,4,136,28,146,53,31,45,252,5,5,36,47,49,102,59,146,7,147,45,9,59,4,6,91,250,4,222,224,58,29,9,192,252,113,236,191,56,207,9,45,253,0,239,100,57,127,12,147,254,107,247,22,60,232,13,49,255,33,251,53,55,120,15,206,255,212,254,254,58,140,16,50,0,42,1,252,55,216,18,242,0,174,5,254,57,75,21,166,1,238,9,202,59,195,23,72,2,190,13,249,55,232,26,0,3,15,18,212,58,9,30,162,3,226,21,70,56,210,36,207,4,245,28,27,60,13,38,0,5,26,30,232,57,191,55,52,7,94,43,32,53,107,97,109,10,195,62,12,64,177,7,198,251,139,230,177,65,16,11,223,253,45,243,97,61,27,11,229,253,80,243,232,62,8,13,209,254,223,248,0,64,123,15,207,255,218,254,44,66,227,17,165,0,224,3,95,61,247,17,171,0,6,4,94,63,72,21,165,1,233,9,192,65,238,24,143,2,105,15,129,61,229,27,53,3,80,19,198,63,45,29,120,3,223,20,227,64,176,33,76,4,222,25,132,66,178,40,99,5,111,32,33,62,41,46,29,6,207,36,238,65,98,57,95,7,96,44,131,64,134,81,102,9,147,56,222,70,35,8,25,252,131,232,201,75,106,12,137,254,47,247,100,68,98,13,248,254,203,249,86,78,187,15,231,255,105,255,149,70,153,16,54,0,70,1,8,74,202,19,58,1,98,7,47,69,26,21,153,1,157,9,123,77,48,24,98,2,92,14,30,70,102,27,27,3,176,18,70,83,197,30,198,3,184,22,246,69,73,36,186,4,115,28,200,74,74,36,186,4,116,28,37,80,117,44,230,5,129,35,155,70,149,56,74,7,226,43,31,78,218,69,129,8,52,51,154,73,252,127,0,12,62,72,61,42,81,112,63,11,181,67,0,80,225,10,198,253,153,242,153,73,194,25,191,2,139,16,81,24,245,28,108,3,156,20,51,67,204,40,103,5,133,32,122,84,245,4,61,249,74,215,143,82,71,17,113,0,171,2,40,44,20,6,106,250,95,222,61,74,20,50,150,6,164,39,215,67,194,9,37,253,210,238,194,69,225,18,244,0,192,5,10,39,194,9,37,253,210,238,122,68,184,30,196,3,170,22,174,55,92,7,133,251,5,229,20,62,81,12,125,254,233,246,61,26,10,7,67,251,121,227,10,71,225,78,53,9,109,55,102,70,215,11,67,254,138,245,71,65,225,22,16,2,109,12,143,34,174,15,226,255,76,255,20,62,10,35,134,4,60,27,102,70,112,5,198,249,129,218,71,65,0,16,0,0,0,0,0,32,143,2,108,245,79,192,133,59,102,54,16,7,132,42,174,55,40,12,106,254,116,246,10,55,61,18,193,0,141,4,30,21,143,10,154,253,143,241,122,52,153,25,182,2,84,16,163,48,133,3,67,247,100,203,163,48,102,10,131,253,7,241,184,14,143,2,108,245,79,192,153,57,215,91,22,10,183,60,225,74,153,9,13,253,62,238,184,78,215,19,62,1,121,7,225,26,0,16,0,0,0,0,0,80,112,33,65,4,156,25,204,76,225,2,26,246,105,196,61,74,163,16,58,0,91,1,184,30,40,8,29,252,151,232,204,44,0,48,87,6,43,38,20,62,194,5,26,250,126,220,112,61,20,18,180,0,62,4,215,35,153,5,240,249,131,219,184,62,92,27,25,3,164,18,235,57,225,2,26,246,105,196,225,58,204,8,140,252,55,235,215,19,204,4,12,249,38,214,215,51,174,67,83,8,27,50,163,64,30,9,193,252,118,236,225,58,184,22,6,2,46,12,92,15,102,14,100,255,86,252,174,55,153,33,72,4,198,25,235,65,10,3,106,246,74,198,225,58,225,14,149,255,122,253,174,23,102,2,12,245,17,190,122,36,40,36,180,4,83,28,215,51,225,6,33,251,172,226,215,51,194,13,33,255,193,250,153,9,174,7,196,251,127,230,204,44,153,21,187,1,108,10,245,40,225,2,26,246,105,196,112,45,122,12,145,254,92,247,194,5,10,3,106,246,74,198,0,64,248,65,226,67,190,69,142,71,82,73,12,75,188,76,98,78,0,80,150,81,35,83,170,84,42,86,163,87,22,89,130,90,234,91,76,93,168,94,0,96,83,97,161,98,236,99,49,101,115,102,177,103,235,104,34,106,85,107,132,108,177,109,218,110,0,112,35,113,67,114,97,115,123,116,147,117,169,118,188,119,204,120,218,121,230,122,239,123,247,124,252,125,255,126,255,127,255,127,61,10,63,10,69,10,78,10,91,10,108,10,129,10,153,10,181,10,212,10,248,10,31,11,74,11,120,11,170,11,224,11,25,12,86,12,151,12,219,12,35,13,110,13,189,13,15,14,101,14,190,14,27,15,123,15,223,15,70,16,176,16,30,17,143,17,3,18,123,18,245,18,115,19,244,19,120,20,0,21,138,21,23,22,168,22,59,23,209,23,106,24,6,25,165,25,70,26,234,26,145,27,59,28,231,28,149,29,70,30,250,30,176,31,104,32,35,33,224,33,159,34,97,35,36,36,234,36,178,37,124,38,71,39,21,40,228,40,181,41,136,42,93,43,51,44,11,45,228,45,191,46,155,47,121,48,88,49,56,50,26,51,252,51,224,52,196,53,170,54,145,55,120,56,96,57,73,58,51,59,29,60,8,61,243,61,223,62,203,63,184,64,165,65,146,66,127,67,108,68,90,69,71,70,52,71,33,72,14,73,251,73,231,74,211,75,191,76,170,77,149,78,126,79,104,80,80,81,56,82,31,83,5,84,234,84,207,85,178,86,148,87,116,88,84,89,50,90,15,91,235,91,197,92,157,93,117,94,74,95,30,96,240,96,192,97,143,98,91,99,38,100,239,100,181,101,122,102,60,103,253,103,187,104,119,105,48,106,232,106,156,107,79,108,255,108,172,109,87,110,255,110,165,111,71,112,231,112,133,113,31,114,183,114,75,115,221,115,108,116,248,116,129,117,6,118,137,118,8,119,133,119,254,119,116,120,230,120,86,121,194,121,42,122,144,122,242,122,80,123,171,123,3,124,87,124,167,124,244,124,62,125,132,125,198,125,5,126,64,126,120,126,172,126,220,126,9,127,49,127,87,127,120,127,150,127,176,127,199,127,217,127,232,127,243,127,251,127,255,127,255,127,229,127,153,127,25,127,103,126,129,125],"i8",A2,u1.GLOBAL_BASE+20480),C0([106,124,33,123,167,121,252,119,34,118,24,116,223,113,122,111,231,108,41,106,65,103,47,100,245,96,149,93,15,90,101,86,153,82,171,78,158,74,116,70,45,66,204,61,82,57,193,52,27,48,98,43,151,38,189,33,213,28,226,23,230,18,226,13,216,8,203,3,61,10,64,10,73,10,88,10,108,10,135,10,167,10,205,10,249,10,43,11,99,11,160,11,227,11,44,12,122,12,207,12,40,13,136,13,237,13,87,14,199,14,60,15,183,15,55,16,189,16,71,17,215,17,108,18,6,19,165,19,73,20,242,20,159,21,82,22,9,23,196,23,133,24,73,25,18,26,224,26,177,27,135,28,97,29,62,30,32,31,5,32,238,32,219,33,203,34,191,35,182,36,176,37,174,38,174,39,177,40,184,41,193,42,204,43,218,44,235,45,254,46,19,48,42,49,67,50,94,51,123,52,154,53,186,54,219,55,254,56,34,58,71,59,109,60,148,61,188,62,228,63,13,65,54,66,96,67,138,68,180,69,221,70,7,72,48,73,89,74,130,75,169,76,208,77,246,78,27,80,63,81,98,82,132,83,164,84,194,85,223,86,250,87,19,89,43,90,64,91,83,92,99,93,113,94,125,95,134,96,140,97,143,98,144,99,141,100,135,101,126,102,114,103,98,104,79,105,56,106,30,107,255,107,221,108,183,109,140,110,94,111,43,112,244,112,185,113,121,114,53,115,236,115,158,116,76,117,245,117,153,118,55,119,209,119,102,120,246,120,129,121,6,122,134,122,1,123,118,123,230,123,81,124,182,124,21,125,111,125,195,125,17,126,90,126,157,126,219,126,18,127,68,127,112,127,150,127,183,127,209,127,230,127,244,127,253,127,255,127,255,127,244,127,208,127,149,127,66,127,215,126,85,126,188,125,12,125,69,124,104,123,117,122,108,121,78,120,28,119,213,117,122,116,13,115,140,113,250,111,87,110,162,108,222,106,11,105,40,103,57,101,60,99,51,97,30,95,255,92,215,90,165,88,108,86,44,84,229,81,154,79,74,77,247,74,161,72,74,70,243,67,156,65,71,63,244,60,164,58,88,56,18,54,209,51,152,49,103,47,62,45,31,43,11,41,2,39,5,37,21,35,51,33,95,31,155,29,231,27,67,26,177,24,49,23,195,21,105,20,34,19,239,17,209,16,201,15,214,14,249,13,50,13,130,12,232,11,102,11,252,10,169,10,109,10,73,10,61,10,61,10,63,10,67,10,74,10,84,10,96,10,111,10,129,10,150,10,174,10,200,10,229,10,5,11,39,11,77,11,117,11,159,11,205,11,253,11,48,12,101,12,157,12,216,12,22,13,86,13,153,13,222,13,38,14,113,14,190,14,13,15,96,15,181,15,12,16,102,16,194,16,33,17,130,17,230,17,76,18,180,18,31,19,140,19,252,19,110,20,226,20,88,21,209,21,76,22,201,22,72,23,202,23,77,24,211,24,91,25,229,25,113,26,254,26,142,27,32,28,180,28,74,29,225,29,123,30,22,31,179,31,82,32,242,32,149,33,57,34,222,34,133,35,46,36,216,36,132,37,50,38,224,38,145,39,66,40,245,40,169,41,95,42,22,43,206,43,135,44,66,45,253,45,186,46,120,47,54,48,246,48,183,49,120,50,59,51,254,51,194,52,135,53,77,54,19,55,218,55,161,56,106,57,50,58,252,58,197,59,144,60,90,61,37,62,240,62,188,63,136,64,84,65,32,66,236,66,185,67,133,68,82,69,30,70,235,70,183,71,132,72,80,73,28,74,231,74,179,75,126,76,73,77,19,78,221,78,166,79,111,80,56,81,0,82,199,82,142,83,84,84,25,85,221,85,161,86,100,87,38,88,231,88,167,89,103,90,37,91,226,91,158,92,89,93,19,94,204,94,131,95,57,96,238,96,162,97,84,98,5,99,181,99,99,100,15,101,186,101,100,102,12,103,178,103,87,104,250,104,155,105,59,106,217,106,117,107,16,108,168,108,63,109,211,109,102,110,247,110,134,111,19,112,158,112,39,113,174,113,50,114,181,114,53,115,179,115,47,116,169,116,33,117,150,117,9,118,122,118,232,118,84,119,190,119,37,120,138,120,236,120,76,121,170,121,5,122,94,122,180,122,7,123,88,123,167,123,242,123,60,124,130,124,198,124,8,125,71,125,131,125,188,125,243,125,39,126,89,126,136,126,180,126,221,126,4,127,40,127,73,127,103,127,131,127,156,127,178,127,197,127,214,127,228,127,239,127,247,127,253,127,255,127,255,127,97,125,160,117,15,105,48,88,181,67,116,44,98,19,68,101,99,111,100,101,114,0,101,110,99,111,100,101,114,0],"i8",A2,u1.GLOBAL_BASE+30720);var D6=u1.alignMemory(C0(12,"i8",D2),8);function f5(p){var v=f5;v.called||(J0=$4(J0),v.called=!0,y1(u1.dynamicAlloc),v.alloc=u1.dynamicAlloc,u1.dynamicAlloc=function(){k2("cannot dynamically allocate, sbrk now has control")});var g=J0;return p==0||v.alloc(p)?g:4294967295}function V2(p){return L.___errno_location&&(g0[L.___errno_location()>>2]=p),p}y1(D6%8==0);var J={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function d3(p){switch(p){case 30:return k6;case 85:return e2/k6;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return typeof navigator=="object"&&navigator.hardwareConcurrency||1}return V2(J.EINVAL),-1}function h3(p,v,g){return F0.set(F0.subarray(v,v+g),p),p}function w3(){L.abort()}L._memcpy=E3,L._memmove=b3,L._memset=v3;var m3={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},p2={ttys:[],init:function(){},shutdown:function(){},register:function(p,v){p2.ttys[p]={input:[],output:[],ops:v},P.registerDevice(p,p2.stream_ops)},stream_ops:{open:function(p){var v=p2.ttys[p.node.rdev];if(!v)throw new P.ErrnoError(J.ENODEV);p.tty=v,p.seekable=!1},close:function(p){p.tty.ops.flush(p.tty)},flush:function(p){p.tty.ops.flush(p.tty)},read:function(p,v,g,_,e){if(!p.tty||!p.tty.ops.get_char)throw new P.ErrnoError(J.ENXIO);for(var s=0,B=0;B<_;B++){var D;try{D=p.tty.ops.get_char(p.tty)}catch{throw new P.ErrnoError(J.EIO)}if(D===void 0&&s===0)throw new P.ErrnoError(J.EAGAIN);if(D==null)break;s++,v[g+B]=D}return s&&(p.node.timestamp=Date.now()),s},write:function(p,v,g,_,e){if(!p.tty||!p.tty.ops.put_char)throw new P.ErrnoError(J.ENXIO);for(var s=0;s<_;s++)try{p.tty.ops.put_char(p.tty,v[g+s])}catch{throw new P.ErrnoError(J.EIO)}return _&&(p.node.timestamp=Date.now()),s}},default_tty_ops:{get_char:function(p){if(!p.input.length){var v=null;if(typeof window<"u"&&typeof window.prompt=="function"?(v=window.prompt("Input: "))!==null&&(v+=`
`):typeof readline=="function"&&(v=readline())!==null&&(v+=`
`),!v)return null;p.input=m2(v,!0)}return p.input.shift()},put_char:function(p,v){v===null||v===10?(L.print(d2(p.output,0)),p.output=[]):v!=0&&p.output.push(v)},flush:function(p){p.output&&p.output.length>0&&(L.print(d2(p.output,0)),p.output=[])}},default_tty1_ops:{put_char:function(p,v){v===null||v===10?(L.printErr(d2(p.output,0)),p.output=[]):v!=0&&p.output.push(v)},flush:function(p){p.output&&p.output.length>0&&(L.printErr(d2(p.output,0)),p.output=[])}}},_1={ops_table:null,mount:function(p){return _1.createNode(null,"/",16895,0)},createNode:function(p,v,g,_){if(P.isBlkdev(g)||P.isFIFO(g))throw new P.ErrnoError(J.EPERM);_1.ops_table||(_1.ops_table={dir:{node:{getattr:_1.node_ops.getattr,setattr:_1.node_ops.setattr,lookup:_1.node_ops.lookup,mknod:_1.node_ops.mknod,rename:_1.node_ops.rename,unlink:_1.node_ops.unlink,rmdir:_1.node_ops.rmdir,readdir:_1.node_ops.readdir,symlink:_1.node_ops.symlink},stream:{llseek:_1.stream_ops.llseek}},file:{node:{getattr:_1.node_ops.getattr,setattr:_1.node_ops.setattr},stream:{llseek:_1.stream_ops.llseek,read:_1.stream_ops.read,write:_1.stream_ops.write,allocate:_1.stream_ops.allocate,mmap:_1.stream_ops.mmap,msync:_1.stream_ops.msync}},link:{node:{getattr:_1.node_ops.getattr,setattr:_1.node_ops.setattr,readlink:_1.node_ops.readlink},stream:{}},chrdev:{node:{getattr:_1.node_ops.getattr,setattr:_1.node_ops.setattr},stream:P.chrdev_stream_ops}});var e=P.createNode(p,v,g,_);return P.isDir(e.mode)?(e.node_ops=_1.ops_table.dir.node,e.stream_ops=_1.ops_table.dir.stream,e.contents={}):P.isFile(e.mode)?(e.node_ops=_1.ops_table.file.node,e.stream_ops=_1.ops_table.file.stream,e.usedBytes=0,e.contents=null):P.isLink(e.mode)?(e.node_ops=_1.ops_table.link.node,e.stream_ops=_1.ops_table.link.stream):P.isChrdev(e.mode)&&(e.node_ops=_1.ops_table.chrdev.node,e.stream_ops=_1.ops_table.chrdev.stream),e.timestamp=Date.now(),p&&(p.contents[v]=e),e},getFileDataAsRegularArray:function(p){if(p.contents&&p.contents.subarray){for(var v=[],g=0;g<p.usedBytes;++g)v.push(p.contents[g]);return v}return p.contents},getFileDataAsTypedArray:function(p){return p.contents?p.contents.subarray?p.contents.subarray(0,p.usedBytes):new Uint8Array(p.contents):new Uint8Array},expandFileStorage:function(p,v){if(p.contents&&p.contents.subarray&&v>p.contents.length&&(p.contents=_1.getFileDataAsRegularArray(p),p.usedBytes=p.contents.length),!p.contents||p.contents.subarray){var g=p.contents?p.contents.buffer.byteLength:0;if(g>=v)return;var _=1048576;v=Math.max(v,g*(g<_?2:1.125)|0),g!=0&&(v=Math.max(v,256));var e=p.contents;return p.contents=new Uint8Array(v),void(p.usedBytes>0&&p.contents.set(e.subarray(0,p.usedBytes),0))}for(!p.contents&&v>0&&(p.contents=[]);p.contents.length<v;)p.contents.push(0)},resizeFileStorage:function(p,v){if(p.usedBytes!=v){if(v==0)return p.contents=null,void(p.usedBytes=0);if(!p.contents||p.contents.subarray){var g=p.contents;return p.contents=new Uint8Array(new ArrayBuffer(v)),g&&p.contents.set(g.subarray(0,Math.min(v,p.usedBytes))),void(p.usedBytes=v)}if(p.contents||(p.contents=[]),p.contents.length>v)p.contents.length=v;else for(;p.contents.length<v;)p.contents.push(0);p.usedBytes=v}},node_ops:{getattr:function(p){var v={};return v.dev=P.isChrdev(p.mode)?p.id:1,v.ino=p.id,v.mode=p.mode,v.nlink=1,v.uid=0,v.gid=0,v.rdev=p.rdev,P.isDir(p.mode)?v.size=4096:P.isFile(p.mode)?v.size=p.usedBytes:P.isLink(p.mode)?v.size=p.link.length:v.size=0,v.atime=new Date(p.timestamp),v.mtime=new Date(p.timestamp),v.ctime=new Date(p.timestamp),v.blksize=4096,v.blocks=Math.ceil(v.size/v.blksize),v},setattr:function(p,v){v.mode!==void 0&&(p.mode=v.mode),v.timestamp!==void 0&&(p.timestamp=v.timestamp),v.size!==void 0&&_1.resizeFileStorage(p,v.size)},lookup:function(p,v){throw P.genericErrors[J.ENOENT]},mknod:function(p,v,g,_){return _1.createNode(p,v,g,_)},rename:function(p,v,g){if(P.isDir(p.mode)){var _;try{_=P.lookupNode(v,g)}catch{}if(_)for(var e in _.contents)throw new P.ErrnoError(J.ENOTEMPTY)}delete p.parent.contents[p.name],p.name=g,v.contents[g]=p,p.parent=v},unlink:function(p,v){delete p.contents[v]},rmdir:function(p,v){var g=P.lookupNode(p,v);for(var _ in g.contents)throw new P.ErrnoError(J.ENOTEMPTY);delete p.contents[v]},readdir:function(p){var v=[".",".."];for(var g in p.contents)p.contents.hasOwnProperty(g)&&v.push(g);return v},symlink:function(p,v,g){var _=_1.createNode(p,v,41471,0);return _.link=g,_},readlink:function(p){if(!P.isLink(p.mode))throw new P.ErrnoError(J.EINVAL);return p.link}},stream_ops:{read:function(p,v,g,_,e){var s=p.node.contents;if(e>=p.node.usedBytes)return 0;var B=Math.min(p.node.usedBytes-e,_);if(y1(B>=0),B>8&&s.subarray)v.set(s.subarray(e,e+B),g);else for(var D=0;D<B;D++)v[g+D]=s[e+D];return B},write:function(p,v,g,_,e,s){if(!_)return 0;var B=p.node;if(B.timestamp=Date.now(),v.subarray&&(!B.contents||B.contents.subarray)){if(s)return B.contents=v.subarray(g,g+_),B.usedBytes=_,_;if(B.usedBytes===0&&e===0)return B.contents=new Uint8Array(v.subarray(g,g+_)),B.usedBytes=_,_;if(e+_<=B.usedBytes)return B.contents.set(v.subarray(g,g+_),e),_}if(_1.expandFileStorage(B,e+_),B.contents.subarray&&v.subarray)B.contents.set(v.subarray(g,g+_),e);else for(var D=0;D<_;D++)B.contents[e+D]=v[g+D];return B.usedBytes=Math.max(B.usedBytes,e+_),_},llseek:function(p,v,g){var _=v;if(g===1?_+=p.position:g===2&&P.isFile(p.node.mode)&&(_+=p.node.usedBytes),_<0)throw new P.ErrnoError(J.EINVAL);return _},allocate:function(p,v,g){_1.expandFileStorage(p.node,v+g),p.node.usedBytes=Math.max(p.node.usedBytes,v+g)},mmap:function(p,v,g,_,e,s,B){if(!P.isFile(p.node.mode))throw new P.ErrnoError(J.ENODEV);var D,Y,Q=p.node.contents;if(2&B||Q.buffer!==v&&Q.buffer!==v.buffer){if((e>0||e+_<p.node.usedBytes)&&(Q=Q.subarray?Q.subarray(e,e+_):Array.prototype.slice.call(Q,e,e+_)),Y=!0,!(D=H2(_)))throw new P.ErrnoError(J.ENOMEM);v.set(Q,D)}else Y=!1,D=Q.byteOffset;return{ptr:D,allocated:Y}},msync:function(p,v,g,_,e){if(!P.isFile(p.node.mode))throw new P.ErrnoError(J.ENODEV);return 2&e||_1.stream_ops.write(p,v,0,_,g,!1),0}}},$1={dbs:{},indexedDB:function(){if(typeof indexedDB<"u")return indexedDB;var p=null;return typeof window=="object"&&(p=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB),y1(p,"IDBFS used, but indexedDB not supported"),p},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(p){return _1.mount.apply(null,arguments)},syncfs:function(p,v,g){$1.getLocalSet(p,function(_,e){if(_)return g(_);$1.getRemoteSet(p,function(s,B){if(s)return g(s);var D=v?B:e,Y=v?e:B;$1.reconcile(D,Y,g)})})},getDB:function(p,v){var g,_=$1.dbs[p];if(_)return v(null,_);try{g=$1.indexedDB().open(p,$1.DB_VERSION)}catch(e){return v(e)}g.onupgradeneeded=function(e){var s,B=e.target.result,D=e.target.transaction;(s=B.objectStoreNames.contains($1.DB_STORE_NAME)?D.objectStore($1.DB_STORE_NAME):B.createObjectStore($1.DB_STORE_NAME)).indexNames.contains("timestamp")||s.createIndex("timestamp","timestamp",{unique:!1})},g.onsuccess=function(){_=g.result,$1.dbs[p]=_,v(null,_)},g.onerror=function(e){v(this.error),e.preventDefault()}},getLocalSet:function(p,v){var g={};function _(Y){return Y!=="."&&Y!==".."}function e(Y){return function(Q){return N1.join2(Y,Q)}}for(var s=P.readdir(p.mountpoint).filter(_).map(e(p.mountpoint));s.length;){var B,D=s.pop();try{B=P.stat(D)}catch(Y){return v(Y)}P.isDir(B.mode)&&s.push.apply(s,P.readdir(D).filter(_).map(e(D))),g[D]={timestamp:B.mtime}}return v(null,{type:"local",entries:g})},getRemoteSet:function(p,v){var g={};$1.getDB(p.mountpoint,function(_,e){if(_)return v(_);var s=e.transaction([$1.DB_STORE_NAME],"readonly");s.onerror=function(B){v(this.error),B.preventDefault()},s.objectStore($1.DB_STORE_NAME).index("timestamp").openKeyCursor().onsuccess=function(B){var D=B.target.result;if(!D)return v(null,{type:"remote",db:e,entries:g});g[D.primaryKey]={timestamp:D.key},D.continue()}})},loadLocalEntry:function(p,v){var g,_;try{_=P.lookupPath(p).node,g=P.stat(p)}catch(e){return v(e)}return P.isDir(g.mode)?v(null,{timestamp:g.mtime,mode:g.mode}):P.isFile(g.mode)?(_.contents=_1.getFileDataAsTypedArray(_),v(null,{timestamp:g.mtime,mode:g.mode,contents:_.contents})):v(new Error("node type not supported"))},storeLocalEntry:function(p,v,g){try{if(P.isDir(v.mode))P.mkdir(p,v.mode);else{if(!P.isFile(v.mode))return g(new Error("node type not supported"));P.writeFile(p,v.contents,{encoding:"binary",canOwn:!0})}P.chmod(p,v.mode),P.utime(p,v.timestamp,v.timestamp)}catch(_){return g(_)}g(null)},removeLocalEntry:function(p,v){try{P.lookupPath(p);var g=P.stat(p);P.isDir(g.mode)?P.rmdir(p):P.isFile(g.mode)&&P.unlink(p)}catch(_){return v(_)}v(null)},loadRemoteEntry:function(p,v,g){var _=p.get(v);_.onsuccess=function(e){g(null,e.target.result)},_.onerror=function(e){g(this.error),e.preventDefault()}},storeRemoteEntry:function(p,v,g,_){var e=p.put(g,v);e.onsuccess=function(){_(null)},e.onerror=function(s){_(this.error),s.preventDefault()}},removeRemoteEntry:function(p,v,g){var _=p.delete(v);_.onsuccess=function(){g(null)},_.onerror=function(e){g(this.error),e.preventDefault()}},reconcile:function(p,v,g){var _=0,e=[];Object.keys(p.entries).forEach(function(Z){var O=p.entries[Z],e1=v.entries[Z];(!e1||O.timestamp>e1.timestamp)&&(e.push(Z),_++)});var s=[];if(Object.keys(v.entries).forEach(function(Z){v.entries[Z],p.entries[Z]||(s.push(Z),_++)}),!_)return g(null);var B=0,D=(p.type==="remote"?p.db:v.db).transaction([$1.DB_STORE_NAME],"readwrite"),Y=D.objectStore($1.DB_STORE_NAME);function Q(Z){return Z?Q.errored?void 0:(Q.errored=!0,g(Z)):++B>=_?g(null):void 0}D.onerror=function(Z){Q(this.error),Z.preventDefault()},e.sort().forEach(function(Z){v.type==="local"?$1.loadRemoteEntry(Y,Z,function(O,e1){if(O)return Q(O);$1.storeLocalEntry(Z,e1,Q)}):$1.loadLocalEntry(Z,function(O,e1){if(O)return Q(O);$1.storeRemoteEntry(Y,Z,e1,Q)})}),s.sort().reverse().forEach(function(Z){v.type==="local"?$1.removeLocalEntry(Z,Q):$1.removeRemoteEntry(Y,Z,Q)})}},o0={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:function(p){y1(e0),o0.reader||(o0.reader=new FileReaderSync);var v=o0.createNode(null,"/",o0.DIR_MODE,0),g={};function _(s){for(var B=s.split("/"),D=v,Y=0;Y<B.length-1;Y++){var Q=B.slice(0,Y+1).join("/");g[Q]||(g[Q]=o0.createNode(D,Q,o0.DIR_MODE,0)),D=g[Q]}return D}function e(s){var B=s.split("/");return B[B.length-1]}return Array.prototype.forEach.call(p.opts.files||[],function(s){o0.createNode(_(s.name),e(s.name),o0.FILE_MODE,0,s,s.lastModifiedDate)}),(p.opts.blobs||[]).forEach(function(s){o0.createNode(_(s.name),e(s.name),o0.FILE_MODE,0,s.data)}),(p.opts.packages||[]).forEach(function(s){s.metadata.files.forEach(function(B){var D=B.filename.substr(1);o0.createNode(_(D),e(D),o0.FILE_MODE,0,s.blob.slice(B.start,B.end))})}),v},createNode:function(p,v,g,_,e,s){var B=P.createNode(p,v,g);return B.mode=g,B.node_ops=o0.node_ops,B.stream_ops=o0.stream_ops,B.timestamp=(s||new Date).getTime(),y1(o0.FILE_MODE!==o0.DIR_MODE),g===o0.FILE_MODE?(B.size=e.size,B.contents=e):(B.size=4096,B.contents={}),p&&(p.contents[v]=B),B},node_ops:{getattr:function(p){return{dev:1,ino:void 0,mode:p.mode,nlink:1,uid:0,gid:0,rdev:void 0,size:p.size,atime:new Date(p.timestamp),mtime:new Date(p.timestamp),ctime:new Date(p.timestamp),blksize:4096,blocks:Math.ceil(p.size/4096)}},setattr:function(p,v){v.mode!==void 0&&(p.mode=v.mode),v.timestamp!==void 0&&(p.timestamp=v.timestamp)},lookup:function(p,v){throw new P.ErrnoError(J.ENOENT)},mknod:function(p,v,g,_){throw new P.ErrnoError(J.EPERM)},rename:function(p,v,g){throw new P.ErrnoError(J.EPERM)},unlink:function(p,v){throw new P.ErrnoError(J.EPERM)},rmdir:function(p,v){throw new P.ErrnoError(J.EPERM)},readdir:function(p){throw new P.ErrnoError(J.EPERM)},symlink:function(p,v,g){throw new P.ErrnoError(J.EPERM)},readlink:function(p){throw new P.ErrnoError(J.EPERM)}},stream_ops:{read:function(p,v,g,_,e){if(e>=p.node.size)return 0;var s=p.node.contents.slice(e,e+_),B=o0.reader.readAsArrayBuffer(s);return v.set(new Uint8Array(B),g),s.size},write:function(p,v,g,_,e){throw new P.ErrnoError(J.EIO)},llseek:function(p,v,g){var _=v;if(g===1?_+=p.position:g===2&&P.isFile(p.node.mode)&&(_+=p.node.size),_<0)throw new P.ErrnoError(J.EINVAL);return _}}};C0(1,"i32*",D2),C0(1,"i32*",D2),C0(1,"i32*",D2);var P={root:null,mounts:[],devices:[null],streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,handleFSError:function(p){if(!(p instanceof P.ErrnoError))throw p+" : "+y5();return V2(p.errno)},lookupPath:function(p,v){if(v=v||{},!(p=N1.resolve(P.cwd(),p)))return{path:"",node:null};var g={follow_mount:!0,recurse_count:0};for(var _ in g)v[_]===void 0&&(v[_]=g[_]);if(v.recurse_count>8)throw new P.ErrnoError(J.ELOOP);for(var e=N1.normalizeArray(p.split("/").filter(function(O){return!!O}),!1),s=P.root,B="/",D=0;D<e.length;D++){var Y=D===e.length-1;if(Y&&v.parent)break;if(s=P.lookupNode(s,e[D]),B=N1.join2(B,e[D]),P.isMountpoint(s)&&(!Y||Y&&v.follow_mount)&&(s=s.mounted.root),!Y||v.follow)for(var Q=0;P.isLink(s.mode);){var Z=P.readlink(B);if(B=N1.resolve(N1.dirname(B),Z),s=P.lookupPath(B,{recurse_count:v.recurse_count}).node,Q++>40)throw new P.ErrnoError(J.ELOOP)}}return{path:B,node:s}},getPath:function(p){for(var v;;){if(P.isRoot(p)){var g=p.mount.mountpoint;return v?g[g.length-1]!=="/"?g+"/"+v:g+v:g}v=v?p.name+"/"+v:p.name,p=p.parent}},hashName:function(p,v){for(var g=0,_=0;_<v.length;_++)g=(g<<5)-g+v.charCodeAt(_)|0;return(p+g>>>0)%P.nameTable.length},hashAddNode:function(p){var v=P.hashName(p.parent.id,p.name);p.name_next=P.nameTable[v],P.nameTable[v]=p},hashRemoveNode:function(p){var v=P.hashName(p.parent.id,p.name);if(P.nameTable[v]===p)P.nameTable[v]=p.name_next;else for(var g=P.nameTable[v];g;){if(g.name_next===p){g.name_next=p.name_next;break}g=g.name_next}},lookupNode:function(p,v){var g=P.mayLookup(p);if(g)throw new P.ErrnoError(g,p);for(var _=P.hashName(p.id,v),e=P.nameTable[_];e;e=e.name_next){var s=e.name;if(e.parent.id===p.id&&s===v)return e}return P.lookup(p,v)},createNode:function(p,v,g,_){if(!P.FSNode){P.FSNode=function(D,Y,Q,Z){D||(D=this),this.parent=D,this.mount=D.mount,this.mounted=null,this.id=P.nextInode++,this.name=Y,this.mode=Q,this.node_ops={},this.stream_ops={},this.rdev=Z},P.FSNode.prototype={};var e=365,s=146;Object.defineProperties(P.FSNode.prototype,{read:{get:function(){return(this.mode&e)===e},set:function(D){D?this.mode|=e:this.mode&=~e}},write:{get:function(){return(this.mode&s)===s},set:function(D){D?this.mode|=s:this.mode&=~s}},isFolder:{get:function(){return P.isDir(this.mode)}},isDevice:{get:function(){return P.isChrdev(this.mode)}}})}var B=new P.FSNode(p,v,g,_);return P.hashAddNode(B),B},destroyNode:function(p){P.hashRemoveNode(p)},isRoot:function(p){return p===p.parent},isMountpoint:function(p){return!!p.mounted},isFile:function(p){return(61440&p)==32768},isDir:function(p){return(61440&p)==16384},isLink:function(p){return(61440&p)==40960},isChrdev:function(p){return(61440&p)==8192},isBlkdev:function(p){return(61440&p)==24576},isFIFO:function(p){return(61440&p)==4096},isSocket:function(p){return!(49152&~p)},flagModes:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(p){var v=P.flagModes[p];if(v===void 0)throw new Error("Unknown file open mode: "+p);return v},flagsToPermissionString:function(p){var v=["r","w","rw"][3&p];return 512&p&&(v+="w"),v},nodePermissions:function(p,v){return P.ignorePermissions||(v.indexOf("r")===-1||292&p.mode)&&(v.indexOf("w")===-1||146&p.mode)&&(v.indexOf("x")===-1||73&p.mode)?0:J.EACCES},mayLookup:function(p){var v=P.nodePermissions(p,"x");return v||(p.node_ops.lookup?0:J.EACCES)},mayCreate:function(p,v){try{return P.lookupNode(p,v),J.EEXIST}catch{}return P.nodePermissions(p,"wx")},mayDelete:function(p,v,g){var _;try{_=P.lookupNode(p,v)}catch(s){return s.errno}var e=P.nodePermissions(p,"wx");if(e)return e;if(g){if(!P.isDir(_.mode))return J.ENOTDIR;if(P.isRoot(_)||P.getPath(_)===P.cwd())return J.EBUSY}else if(P.isDir(_.mode))return J.EISDIR;return 0},mayOpen:function(p,v){return p?P.isLink(p.mode)?J.ELOOP:P.isDir(p.mode)&&(2097155&v||512&v)?J.EISDIR:P.nodePermissions(p,P.flagsToPermissionString(v)):J.ENOENT},MAX_OPEN_FDS:4096,nextfd:function(p,v){p=p||0,v=v||P.MAX_OPEN_FDS;for(var g=p;g<=v;g++)if(!P.streams[g])return g;throw new P.ErrnoError(J.EMFILE)},getStream:function(p){return P.streams[p]},createStream:function(p,v,g){P.FSStream||(P.FSStream=function(){},P.FSStream.prototype={},Object.defineProperties(P.FSStream.prototype,{object:{get:function(){return this.node},set:function(B){this.node=B}},isRead:{get:function(){return(2097155&this.flags)!=1}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}}));var _=new P.FSStream;for(var e in p)_[e]=p[e];p=_;var s=P.nextfd(v,g);return p.fd=s,P.streams[s]=p,p},closeStream:function(p){P.streams[p]=null},chrdev_stream_ops:{open:function(p){var v=P.getDevice(p.node.rdev);p.stream_ops=v.stream_ops,p.stream_ops.open&&p.stream_ops.open(p)},llseek:function(){throw new P.ErrnoError(J.ESPIPE)}},major:function(p){return p>>8},minor:function(p){return 255&p},makedev:function(p,v){return p<<8|v},registerDevice:function(p,v){P.devices[p]={stream_ops:v}},getDevice:function(p){return P.devices[p]},getMounts:function(p){for(var v=[],g=[p];g.length;){var _=g.pop();v.push(_),g.push.apply(g,_.mounts)}return v},syncfs:function(p,v){typeof p=="function"&&(v=p,p=!1);var g=P.getMounts(P.root.mount),_=0;function e(s){if(s)return e.errored?void 0:(e.errored=!0,v(s));++_>=g.length&&v(null)}g.forEach(function(s){if(!s.type.syncfs)return e(null);s.type.syncfs(s,p,e)})},mount:function(p,v,g){var _,e=g==="/",s=!g;if(e&&P.root)throw new P.ErrnoError(J.EBUSY);if(!e&&!s){var B=P.lookupPath(g,{follow_mount:!1});if(g=B.path,_=B.node,P.isMountpoint(_))throw new P.ErrnoError(J.EBUSY);if(!P.isDir(_.mode))throw new P.ErrnoError(J.ENOTDIR)}var D={type:p,opts:v,mountpoint:g,mounts:[]},Y=p.mount(D);return Y.mount=D,D.root=Y,e?P.root=Y:_&&(_.mounted=D,_.mount&&_.mount.mounts.push(D)),Y},unmount:function(p){var v=P.lookupPath(p,{follow_mount:!1});if(!P.isMountpoint(v.node))throw new P.ErrnoError(J.EINVAL);var g=v.node,_=g.mounted,e=P.getMounts(_);Object.keys(P.nameTable).forEach(function(B){for(var D=P.nameTable[B];D;){var Y=D.name_next;e.indexOf(D.mount)!==-1&&P.destroyNode(D),D=Y}}),g.mounted=null;var s=g.mount.mounts.indexOf(_);y1(s!==-1),g.mount.mounts.splice(s,1)},lookup:function(p,v){return p.node_ops.lookup(p,v)},mknod:function(p,v,g){var _=P.lookupPath(p,{parent:!0}).node,e=N1.basename(p);if(!e||e==="."||e==="..")throw new P.ErrnoError(J.EINVAL);var s=P.mayCreate(_,e);if(s)throw new P.ErrnoError(s);if(!_.node_ops.mknod)throw new P.ErrnoError(J.EPERM);return _.node_ops.mknod(_,e,v,g)},create:function(p,v){return v=v!==void 0?v:438,v&=4095,v|=32768,P.mknod(p,v,0)},mkdir:function(p,v){return v=v!==void 0?v:511,v&=1023,v|=16384,P.mknod(p,v,0)},mkdev:function(p,v,g){return g===void 0&&(g=v,v=438),v|=8192,P.mknod(p,v,g)},symlink:function(p,v){if(!N1.resolve(p))throw new P.ErrnoError(J.ENOENT);var g=P.lookupPath(v,{parent:!0}).node;if(!g)throw new P.ErrnoError(J.ENOENT);var _=N1.basename(v),e=P.mayCreate(g,_);if(e)throw new P.ErrnoError(e);if(!g.node_ops.symlink)throw new P.ErrnoError(J.EPERM);return g.node_ops.symlink(g,_,p)},rename:function(p,v){var g,_,e=N1.dirname(p),s=N1.dirname(v),B=N1.basename(p),D=N1.basename(v);try{g=P.lookupPath(p,{parent:!0}).node,_=P.lookupPath(v,{parent:!0}).node}catch{throw new P.ErrnoError(J.EBUSY)}if(!g||!_)throw new P.ErrnoError(J.ENOENT);if(g.mount!==_.mount)throw new P.ErrnoError(J.EXDEV);var Y,Q=P.lookupNode(g,B),Z=N1.relative(p,s);if(Z.charAt(0)!==".")throw new P.ErrnoError(J.EINVAL);if((Z=N1.relative(v,e)).charAt(0)!==".")throw new P.ErrnoError(J.ENOTEMPTY);try{Y=P.lookupNode(_,D)}catch{}if(Q!==Y){var O=P.isDir(Q.mode),e1=P.mayDelete(g,B,O);if(e1)throw new P.ErrnoError(e1);if(e1=Y?P.mayDelete(_,D,O):P.mayCreate(_,D))throw new P.ErrnoError(e1);if(!g.node_ops.rename)throw new P.ErrnoError(J.EPERM);if(P.isMountpoint(Q)||Y&&P.isMountpoint(Y))throw new P.ErrnoError(J.EBUSY);if(_!==g&&(e1=P.nodePermissions(g,"w")))throw new P.ErrnoError(e1);try{P.trackingDelegate.willMovePath&&P.trackingDelegate.willMovePath(p,v)}catch(a1){console.log("FS.trackingDelegate['willMovePath']('"+p+"', '"+v+"') threw an exception: "+a1.message)}P.hashRemoveNode(Q);try{g.node_ops.rename(Q,_,D)}catch(a1){throw a1}finally{P.hashAddNode(Q)}try{P.trackingDelegate.onMovePath&&P.trackingDelegate.onMovePath(p,v)}catch(a1){console.log("FS.trackingDelegate['onMovePath']('"+p+"', '"+v+"') threw an exception: "+a1.message)}}},rmdir:function(p){var v=P.lookupPath(p,{parent:!0}).node,g=N1.basename(p),_=P.lookupNode(v,g),e=P.mayDelete(v,g,!0);if(e)throw new P.ErrnoError(e);if(!v.node_ops.rmdir)throw new P.ErrnoError(J.EPERM);if(P.isMountpoint(_))throw new P.ErrnoError(J.EBUSY);try{P.trackingDelegate.willDeletePath&&P.trackingDelegate.willDeletePath(p)}catch(s){console.log("FS.trackingDelegate['willDeletePath']('"+p+"') threw an exception: "+s.message)}v.node_ops.rmdir(v,g),P.destroyNode(_);try{P.trackingDelegate.onDeletePath&&P.trackingDelegate.onDeletePath(p)}catch(s){console.log("FS.trackingDelegate['onDeletePath']('"+p+"') threw an exception: "+s.message)}},readdir:function(p){var v=P.lookupPath(p,{follow:!0}).node;if(!v.node_ops.readdir)throw new P.ErrnoError(J.ENOTDIR);return v.node_ops.readdir(v)},unlink:function(p){var v=P.lookupPath(p,{parent:!0}).node,g=N1.basename(p),_=P.lookupNode(v,g),e=P.mayDelete(v,g,!1);if(e)throw e===J.EISDIR&&(e=J.EPERM),new P.ErrnoError(e);if(!v.node_ops.unlink)throw new P.ErrnoError(J.EPERM);if(P.isMountpoint(_))throw new P.ErrnoError(J.EBUSY);try{P.trackingDelegate.willDeletePath&&P.trackingDelegate.willDeletePath(p)}catch(s){console.log("FS.trackingDelegate['willDeletePath']('"+p+"') threw an exception: "+s.message)}v.node_ops.unlink(v,g),P.destroyNode(_);try{P.trackingDelegate.onDeletePath&&P.trackingDelegate.onDeletePath(p)}catch(s){console.log("FS.trackingDelegate['onDeletePath']('"+p+"') threw an exception: "+s.message)}},readlink:function(p){var v=P.lookupPath(p).node;if(!v)throw new P.ErrnoError(J.ENOENT);if(!v.node_ops.readlink)throw new P.ErrnoError(J.EINVAL);return N1.resolve(P.getPath(v.parent),v.node_ops.readlink(v))},stat:function(p,v){var g=P.lookupPath(p,{follow:!v}).node;if(!g)throw new P.ErrnoError(J.ENOENT);if(!g.node_ops.getattr)throw new P.ErrnoError(J.EPERM);return g.node_ops.getattr(g)},lstat:function(p){return P.stat(p,!0)},chmod:function(p,v,g){var _;if(!(_=typeof p=="string"?P.lookupPath(p,{follow:!g}).node:p).node_ops.setattr)throw new P.ErrnoError(J.EPERM);_.node_ops.setattr(_,{mode:4095&v|-4096&_.mode,timestamp:Date.now()})},lchmod:function(p,v){P.chmod(p,v,!0)},fchmod:function(p,v){var g=P.getStream(p);if(!g)throw new P.ErrnoError(J.EBADF);P.chmod(g.node,v)},chown:function(p,v,g,_){var e;if(!(e=typeof p=="string"?P.lookupPath(p,{follow:!_}).node:p).node_ops.setattr)throw new P.ErrnoError(J.EPERM);e.node_ops.setattr(e,{timestamp:Date.now()})},lchown:function(p,v,g){P.chown(p,v,g,!0)},fchown:function(p,v,g){var _=P.getStream(p);if(!_)throw new P.ErrnoError(J.EBADF);P.chown(_.node,v,g)},truncate:function(p,v){if(v<0)throw new P.ErrnoError(J.EINVAL);var g;if(!(g=typeof p=="string"?P.lookupPath(p,{follow:!0}).node:p).node_ops.setattr)throw new P.ErrnoError(J.EPERM);if(P.isDir(g.mode))throw new P.ErrnoError(J.EISDIR);if(!P.isFile(g.mode))throw new P.ErrnoError(J.EINVAL);var _=P.nodePermissions(g,"w");if(_)throw new P.ErrnoError(_);g.node_ops.setattr(g,{size:v,timestamp:Date.now()})},ftruncate:function(p,v){var g=P.getStream(p);if(!g)throw new P.ErrnoError(J.EBADF);if(!(2097155&g.flags))throw new P.ErrnoError(J.EINVAL);P.truncate(g.node,v)},utime:function(p,v,g){var _=P.lookupPath(p,{follow:!0}).node;_.node_ops.setattr(_,{timestamp:Math.max(v,g)})},open:function(p,v,g,_,e){if(p==="")throw new P.ErrnoError(J.ENOENT);var s;if(g=g===void 0?438:g,g=64&(v=typeof v=="string"?P.modeStringToFlags(v):v)?4095&g|32768:0,typeof p=="object")s=p;else{p=N1.normalize(p);try{s=P.lookupPath(p,{follow:!(131072&v)}).node}catch{}}var B=!1;if(64&v)if(s){if(128&v)throw new P.ErrnoError(J.EEXIST)}else s=P.mknod(p,g,0),B=!0;if(!s)throw new P.ErrnoError(J.ENOENT);if(P.isChrdev(s.mode)&&(v&=-513),65536&v&&!P.isDir(s.mode))throw new P.ErrnoError(J.ENOTDIR);if(!B){var D=P.mayOpen(s,v);if(D)throw new P.ErrnoError(D)}512&v&&P.truncate(s,0),v&=-641;var Y=P.createStream({node:s,path:P.getPath(s),flags:v,seekable:!0,position:0,stream_ops:s.stream_ops,ungotten:[],error:!1},_,e);Y.stream_ops.open&&Y.stream_ops.open(Y),!L.logReadFiles||1&v||(P.readFiles||(P.readFiles={}),p in P.readFiles||(P.readFiles[p]=1,L.printErr("read file: "+p)));try{if(P.trackingDelegate.onOpenFile){var Q=0;(2097155&v)!=1&&(Q|=P.tracking.openFlags.READ),2097155&v&&(Q|=P.tracking.openFlags.WRITE),P.trackingDelegate.onOpenFile(p,Q)}}catch(Z){console.log("FS.trackingDelegate['onOpenFile']('"+p+"', flags) threw an exception: "+Z.message)}return Y},close:function(p){p.getdents&&(p.getdents=null);try{p.stream_ops.close&&p.stream_ops.close(p)}catch(v){throw v}finally{P.closeStream(p.fd)}},llseek:function(p,v,g){if(!p.seekable||!p.stream_ops.llseek)throw new P.ErrnoError(J.ESPIPE);return p.position=p.stream_ops.llseek(p,v,g),p.ungotten=[],p.position},read:function(p,v,g,_,e){if(_<0||e<0)throw new P.ErrnoError(J.EINVAL);if((2097155&p.flags)==1)throw new P.ErrnoError(J.EBADF);if(P.isDir(p.node.mode))throw new P.ErrnoError(J.EISDIR);if(!p.stream_ops.read)throw new P.ErrnoError(J.EINVAL);var s=!0;if(e===void 0)e=p.position,s=!1;else if(!p.seekable)throw new P.ErrnoError(J.ESPIPE);var B=p.stream_ops.read(p,v,g,_,e);return s||(p.position+=B),B},write:function(p,v,g,_,e,s){if(_<0||e<0)throw new P.ErrnoError(J.EINVAL);if(!(2097155&p.flags))throw new P.ErrnoError(J.EBADF);if(P.isDir(p.node.mode))throw new P.ErrnoError(J.EISDIR);if(!p.stream_ops.write)throw new P.ErrnoError(J.EINVAL);1024&p.flags&&P.llseek(p,0,2);var B=!0;if(e===void 0)e=p.position,B=!1;else if(!p.seekable)throw new P.ErrnoError(J.ESPIPE);var D=p.stream_ops.write(p,v,g,_,e,s);B||(p.position+=D);try{p.path&&P.trackingDelegate.onWriteToFile&&P.trackingDelegate.onWriteToFile(p.path)}catch(Y){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+Y.message)}return D},allocate:function(p,v,g){if(v<0||g<=0)throw new P.ErrnoError(J.EINVAL);if(!(2097155&p.flags))throw new P.ErrnoError(J.EBADF);if(!P.isFile(p.node.mode)&&!P.isDir(node.mode))throw new P.ErrnoError(J.ENODEV);if(!p.stream_ops.allocate)throw new P.ErrnoError(J.EOPNOTSUPP);p.stream_ops.allocate(p,v,g)},mmap:function(p,v,g,_,e,s,B){if((2097155&p.flags)==1)throw new P.ErrnoError(J.EACCES);if(!p.stream_ops.mmap)throw new P.ErrnoError(J.ENODEV);return p.stream_ops.mmap(p,v,g,_,e,s,B)},msync:function(p,v,g,_,e){return p&&p.stream_ops.msync?p.stream_ops.msync(p,v,g,_,e):0},munmap:function(p){return 0},ioctl:function(p,v,g){if(!p.stream_ops.ioctl)throw new P.ErrnoError(J.ENOTTY);return p.stream_ops.ioctl(p,v,g)},readFile:function(p,v){if((v=v||{}).flags=v.flags||"r",v.encoding=v.encoding||"binary",v.encoding!=="utf8"&&v.encoding!=="binary")throw new Error('Invalid encoding type "'+v.encoding+'"');var g,_=P.open(p,v.flags),e=P.stat(p).size,s=new Uint8Array(e);return P.read(_,s,0,e,0),v.encoding==="utf8"?g=d2(s,0):v.encoding==="binary"&&(g=s),P.close(_),g},writeFile:function(p,v,g){if((g=g||{}).flags=g.flags||"w",g.encoding=g.encoding||"utf8",g.encoding!=="utf8"&&g.encoding!=="binary")throw new Error('Invalid encoding type "'+g.encoding+'"');var _=P.open(p,g.flags,g.mode);if(g.encoding==="utf8"){var e=new Uint8Array(g5(v)+1),s=t5(v,e,0,e.length);P.write(_,e,0,s,0,g.canOwn)}else g.encoding==="binary"&&P.write(_,v,0,v.length,0,g.canOwn);P.close(_)},cwd:function(){return P.currentPath},chdir:function(p){var v=P.lookupPath(p,{follow:!0});if(!P.isDir(v.node.mode))throw new P.ErrnoError(J.ENOTDIR);var g=P.nodePermissions(v.node,"x");if(g)throw new P.ErrnoError(g);P.currentPath=v.path},createDefaultDirectories:function(){P.mkdir("/tmp"),P.mkdir("/home"),P.mkdir("/home/<USER>")},createDefaultDevices:function(){var p;if(P.mkdir("/dev"),P.registerDevice(P.makedev(1,3),{read:function(){return 0},write:function(g,_,e,s,B){return s}}),P.mkdev("/dev/null",P.makedev(1,3)),p2.register(P.makedev(5,0),p2.default_tty_ops),p2.register(P.makedev(6,0),p2.default_tty1_ops),P.mkdev("/dev/tty",P.makedev(5,0)),P.mkdev("/dev/tty1",P.makedev(6,0)),typeof crypto<"u"){var v=new Uint8Array(1);p=function(){return crypto.getRandomValues(v),v[0]}}else p=function(){return 256*Math.random()|0};P.createDevice("/dev","random",p),P.createDevice("/dev","urandom",p),P.mkdir("/dev/shm"),P.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){P.mkdir("/proc"),P.mkdir("/proc/self"),P.mkdir("/proc/self/fd"),P.mount({mount:function(){var p=P.createNode("/proc/self","fd",16895,73);return p.node_ops={lookup:function(v,g){var _=+g,e=P.getStream(_);if(!e)throw new P.ErrnoError(J.EBADF);var s={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return e.path}}};return s.parent=s,s}},p}},{},"/proc/self/fd")},createStandardStreams:function(){L.stdin?P.createDevice("/dev","stdin",L.stdin):P.symlink("/dev/tty","/dev/stdin"),L.stdout?P.createDevice("/dev","stdout",null,L.stdout):P.symlink("/dev/tty","/dev/stdout"),L.stderr?P.createDevice("/dev","stderr",null,L.stderr):P.symlink("/dev/tty1","/dev/stderr");var p=P.open("/dev/stdin","r");y1(p.fd===0,"invalid handle for stdin ("+p.fd+")");var v=P.open("/dev/stdout","w");y1(v.fd===1,"invalid handle for stdout ("+v.fd+")");var g=P.open("/dev/stderr","w");y1(g.fd===2,"invalid handle for stderr ("+g.fd+")")},ensureErrnoError:function(){P.ErrnoError||(P.ErrnoError=function(p,v){this.node=v,this.setErrno=function(g){for(var _ in this.errno=g,J)if(J[_]===g){this.code=_;break}},this.setErrno(p),this.message=m3[p]},P.ErrnoError.prototype=new Error,P.ErrnoError.prototype.constructor=P.ErrnoError,[J.ENOENT].forEach(function(p){P.genericErrors[p]=new P.ErrnoError(p),P.genericErrors[p].stack="<generic error, no stack>"}))},staticInit:function(){P.ensureErrnoError(),P.nameTable=new Array(4096),P.mount(_1,{},"/"),P.createDefaultDirectories(),P.createDefaultDevices(),P.createSpecialDirectories(),P.filesystems={MEMFS:_1,IDBFS:$1,NODEFS:{},WORKERFS:o0}},init:function(p,v,g){y1(!P.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),P.init.initialized=!0,P.ensureErrnoError(),L.stdin=p||L.stdin,L.stdout=v||L.stdout,L.stderr=g||L.stderr,P.createStandardStreams()},quit:function(){P.init.initialized=!1;var p=L._fflush;p&&p(0);for(var v=0;v<P.streams.length;v++){var g=P.streams[v];g&&P.close(g)}},getMode:function(p,v){var g=0;return p&&(g|=365),v&&(g|=146),g},joinPath:function(p,v){var g=N1.join.apply(null,p);return v&&g[0]=="/"&&(g=g.substr(1)),g},absolutePath:function(p,v){return N1.resolve(v,p)},standardizePath:function(p){return N1.normalize(p)},findObject:function(p,v){var g=P.analyzePath(p,v);return g.exists?g.object:(V2(g.error),null)},analyzePath:function(p,v){try{p=(_=P.lookupPath(p,{follow:!v})).path}catch{}var g={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var _=P.lookupPath(p,{parent:!0});g.parentExists=!0,g.parentPath=_.path,g.parentObject=_.node,g.name=N1.basename(p),_=P.lookupPath(p,{follow:!v}),g.exists=!0,g.path=_.path,g.object=_.node,g.name=_.node.name,g.isRoot=_.path==="/"}catch(e){g.error=e.errno}return g},createFolder:function(p,v,g,_){var e=N1.join2(typeof p=="string"?p:P.getPath(p),v),s=P.getMode(g,_);return P.mkdir(e,s)},createPath:function(p,v,g,_){p=typeof p=="string"?p:P.getPath(p);for(var e=v.split("/").reverse();e.length;){var s=e.pop();if(s){var B=N1.join2(p,s);try{P.mkdir(B)}catch{}p=B}}return B},createFile:function(p,v,g,_,e){var s=N1.join2(typeof p=="string"?p:P.getPath(p),v),B=P.getMode(_,e);return P.create(s,B)},createDataFile:function(p,v,g,_,e,s){var B=v?N1.join2(typeof p=="string"?p:P.getPath(p),v):p,D=P.getMode(_,e),Y=P.create(B,D);if(g){if(typeof g=="string"){for(var Q=new Array(g.length),Z=0,O=g.length;Z<O;++Z)Q[Z]=g.charCodeAt(Z);g=Q}P.chmod(Y,146|D);var e1=P.open(Y,"w");P.write(e1,g,0,g.length,0,s),P.close(e1),P.chmod(Y,D)}return Y},createDevice:function(p,v,g,_){var e=N1.join2(typeof p=="string"?p:P.getPath(p),v),s=P.getMode(!!g,!!_);P.createDevice.major||(P.createDevice.major=64);var B=P.makedev(P.createDevice.major++,0);return P.registerDevice(B,{open:function(D){D.seekable=!1},close:function(D){_&&_.buffer&&_.buffer.length&&_(10)},read:function(D,Y,Q,Z,O){for(var e1=0,a1=0;a1<Z;a1++){var R1;try{R1=g()}catch{throw new P.ErrnoError(J.EIO)}if(R1===void 0&&e1===0)throw new P.ErrnoError(J.EAGAIN);if(R1==null)break;e1++,Y[Q+a1]=R1}return e1&&(D.node.timestamp=Date.now()),e1},write:function(D,Y,Q,Z,O){for(var e1=0;e1<Z;e1++)try{_(Y[Q+e1])}catch{throw new P.ErrnoError(J.EIO)}return Z&&(D.node.timestamp=Date.now()),e1}}),P.mkdev(e,s,B)},createLink:function(p,v,g,_,e){var s=N1.join2(typeof p=="string"?p:P.getPath(p),v);return P.symlink(g,s)},forceLoadFile:function(p){if(p.isDevice||p.isFolder||p.link||p.contents)return!0;var v=!0;if(typeof XMLHttpRequest<"u")throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!L.read)throw new Error("Cannot load without read() or XMLHttpRequest.");try{p.contents=m2(L.read(p.url),!0),p.usedBytes=p.contents.length}catch{v=!1}return v||V2(J.EIO),v},createLazyFile:function(p,v,g,_,e){function s(){this.lengthKnown=!1,this.chunks=[]}if(s.prototype.get=function(Z){if(!(Z>this.length-1||Z<0)){var O=Z%this.chunkSize,e1=Z/this.chunkSize|0;return this.getter(e1)[O]}},s.prototype.setDataGetter=function(Z){this.getter=Z},s.prototype.cacheLength=function(){var Z=new XMLHttpRequest;if(Z.open("HEAD",g,!1),Z.send(null),!(Z.status>=200&&Z.status<300||Z.status===304))throw new Error("Couldn't load "+g+". Status: "+Z.status);var O,e1=Number(Z.getResponseHeader("Content-length")),a1=(O=Z.getResponseHeader("Accept-Ranges"))&&O==="bytes",R1=1048576;a1||(R1=e1);var I1=function(M1,w0){if(M1>w0)throw new Error("invalid range ("+M1+", "+w0+") or no bytes requested!");if(w0>e1-1)throw new Error("only "+e1+" bytes available! programmer error!");var x1=new XMLHttpRequest;if(x1.open("GET",g,!1),e1!==R1&&x1.setRequestHeader("Range","bytes="+M1+"-"+w0),typeof Uint8Array<"u"&&(x1.responseType="arraybuffer"),x1.overrideMimeType&&x1.overrideMimeType("text/plain; charset=x-user-defined"),x1.send(null),!(x1.status>=200&&x1.status<300||x1.status===304))throw new Error("Couldn't load "+g+". Status: "+x1.status);return x1.response!==void 0?new Uint8Array(x1.response||[]):m2(x1.responseText||"",!0)},T1=this;T1.setDataGetter(function(M1){var w0=M1*R1,x1=(M1+1)*R1-1;if(x1=Math.min(x1,e1-1),T1.chunks[M1]===void 0&&(T1.chunks[M1]=I1(w0,x1)),T1.chunks[M1]===void 0)throw new Error("doXHR failed!");return T1.chunks[M1]}),this._length=e1,this._chunkSize=R1,this.lengthKnown=!0},typeof XMLHttpRequest<"u"){if(!e0)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var B=new s;Object.defineProperty(B,"length",{get:function(){return this.lengthKnown||this.cacheLength(),this._length}}),Object.defineProperty(B,"chunkSize",{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}});var D={isDevice:!1,contents:B}}else D={isDevice:!1,url:g};var Y=P.createFile(p,v,D,_,e);D.contents?Y.contents=D.contents:D.url&&(Y.contents=null,Y.url=D.url),Object.defineProperty(Y,"usedBytes",{get:function(){return this.contents.length}});var Q={};return Object.keys(Y.stream_ops).forEach(function(Z){var O=Y.stream_ops[Z];Q[Z]=function(){if(!P.forceLoadFile(Y))throw new P.ErrnoError(J.EIO);return O.apply(null,arguments)}}),Q.read=function(Z,O,e1,a1,R1){if(!P.forceLoadFile(Y))throw new P.ErrnoError(J.EIO);var I1=Z.node.contents;if(R1>=I1.length)return 0;var T1=Math.min(I1.length-R1,a1);if(y1(T1>=0),I1.slice)for(var M1=0;M1<T1;M1++)O[e1+M1]=I1[R1+M1];else for(M1=0;M1<T1;M1++)O[e1+M1]=I1.get(R1+M1);return T1},Y.stream_ops=Q,Y},createPreloadedFile:function(p,v,g,_,e,s,B,D,Y,Q){X.init();var Z=v?N1.resolve(N1.join2(p,v)):p;function O(e1){function a1(I1){Q&&Q(),D||P.createDataFile(p,v,I1,_,e,Y),s&&s(),s5()}var R1=!1;L.preloadPlugins.forEach(function(I1){R1||I1.canHandle(Z)&&(I1.handle(e1,Z,a1,function(){B&&B(),s5()}),R1=!0)}),R1||a1(e1)}P5(),typeof g=="string"?X.asyncLoad(g,function(e1){O(e1)},B):O(g)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(p,v,g){v=v||function(){},g=g||function(){};var _=P.indexedDB();try{var e=_.open(P.DB_NAME(),P.DB_VERSION)}catch(s){return g(s)}e.onupgradeneeded=function(){console.log("creating db"),e.result.createObjectStore(P.DB_STORE_NAME)},e.onsuccess=function(){var s=e.result.transaction([P.DB_STORE_NAME],"readwrite"),B=s.objectStore(P.DB_STORE_NAME),D=0,Y=0,Q=p.length;function Z(){Y==0?v():g()}p.forEach(function(O){var e1=B.put(P.analyzePath(O).object.contents,O);e1.onsuccess=function(){++D+Y==Q&&Z()},e1.onerror=function(){Y++,D+Y==Q&&Z()}}),s.onerror=g},e.onerror=g},loadFilesFromDB:function(p,v,g){v=v||function(){},g=g||function(){};var _=P.indexedDB();try{var e=_.open(P.DB_NAME(),P.DB_VERSION)}catch(s){return g(s)}e.onupgradeneeded=g,e.onsuccess=function(){var s=e.result;try{var B=s.transaction([P.DB_STORE_NAME],"readonly")}catch(e1){return void g(e1)}var D=B.objectStore(P.DB_STORE_NAME),Y=0,Q=0,Z=p.length;function O(){Q==0?v():g()}p.forEach(function(e1){var a1=D.get(e1);a1.onsuccess=function(){P.analyzePath(e1).exists&&P.unlink(e1),P.createDataFile(N1.dirname(e1),N1.basename(e1),a1.result,!0,!0,!0),++Y+Q==Z&&O()},a1.onerror=function(){Q++,Y+Q==Z&&O()}}),B.onerror=g},e.onerror=g}},N1={splitPath:function(p){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(p).slice(1)},normalizeArray:function(p,v){for(var g=0,_=p.length-1;_>=0;_--){var e=p[_];e==="."?p.splice(_,1):e===".."?(p.splice(_,1),g++):g&&(p.splice(_,1),g--)}if(v)for(;g--;g)p.unshift("..");return p},normalize:function(p){var v=p.charAt(0)==="/",g=p.substr(-1)==="/";return(p=N1.normalizeArray(p.split("/").filter(function(_){return!!_}),!v).join("/"))||v||(p="."),p&&g&&(p+="/"),(v?"/":"")+p},dirname:function(p){var v=N1.splitPath(p),g=v[0],_=v[1];return g||_?(_&&(_=_.substr(0,_.length-1)),g+_):"."},basename:function(p){if(p==="/")return"/";var v=p.lastIndexOf("/");return v===-1?p:p.substr(v+1)},extname:function(p){return N1.splitPath(p)[3]},join:function(){var p=Array.prototype.slice.call(arguments,0);return N1.normalize(p.join("/"))},join2:function(p,v){return N1.normalize(p+"/"+v)},resolve:function(){for(var p="",v=!1,g=arguments.length-1;g>=-1&&!v;g--){var _=g>=0?arguments[g]:P.cwd();if(typeof _!="string")throw new TypeError("Arguments to path.resolve must be strings");if(!_)return"";p=_+"/"+p,v=_.charAt(0)==="/"}return(v?"/":"")+(p=N1.normalizeArray(p.split("/").filter(function(e){return!!e}),!v).join("/"))||"."},relative:function(p,v){function g(Q){for(var Z=0;Z<Q.length&&Q[Z]==="";Z++);for(var O=Q.length-1;O>=0&&Q[O]==="";O--);return Z>O?[]:Q.slice(Z,O-Z+1)}p=N1.resolve(p).substr(1),v=N1.resolve(v).substr(1);for(var _=g(p.split("/")),e=g(v.split("/")),s=Math.min(_.length,e.length),B=s,D=0;D<s;D++)if(_[D]!==e[D]){B=D;break}var Y=[];for(D=B;D<_.length;D++)Y.push("..");return(Y=Y.concat(e.slice(B))).join("/")}};function l5(p,v){if(X.mainLoop.timingMode=p,X.mainLoop.timingValue=v,!X.mainLoop.func)return 1;if(p==0)X.mainLoop.scheduler=function(){setTimeout(X.mainLoop.runner,v)},X.mainLoop.method="timeout";else if(p==1)X.mainLoop.scheduler=function(){X.requestAnimationFrame(X.mainLoop.runner)},X.mainLoop.method="rAF";else if(p==2){if(!window.setImmediate){let e=function(s){s.source===window&&s.data===_&&(s.stopPropagation(),g.shift()())};var g=[],_="__emcc";window.addEventListener("message",e,!0),window.setImmediate=function(s){g.push(s),window.postMessage(_,"*")}}X.mainLoop.scheduler=function(){window.setImmediate(X.mainLoop.runner)},X.mainLoop.method="immediate"}return 0}function A6(p,v,g,_,e){L.noExitRuntime=!0,y1(!X.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),X.mainLoop.func=p,X.mainLoop.arg=_;var s=X.mainLoop.currentlyRunningMainloop;if(X.mainLoop.runner=function(){if(!g1){if(X.mainLoop.queue.length>0){var B=Date.now(),D=X.mainLoop.queue.shift();if(D.func(D.arg),X.mainLoop.remainingBlockers){var Y=X.mainLoop.remainingBlockers,Q=Y%1==0?Y-1:Math.floor(Y);D.counted?X.mainLoop.remainingBlockers=Q:(Q+=.5,X.mainLoop.remainingBlockers=(8*Y+Q)/9)}return console.log('main loop blocker "'+D.name+'" took '+(Date.now()-B)+" ms"),X.mainLoop.updateStatus(),void setTimeout(X.mainLoop.runner,0)}s<X.mainLoop.currentlyRunningMainloop||(X.mainLoop.currentFrameNumber=X.mainLoop.currentFrameNumber+1|0,X.mainLoop.timingMode==1&&X.mainLoop.timingValue>1&&X.mainLoop.currentFrameNumber%X.mainLoop.timingValue!=0?X.mainLoop.scheduler():(X.mainLoop.method==="timeout"&&L.ctx&&(L.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),X.mainLoop.method=""),X.mainLoop.runIter(function(){_!==void 0?u1.dynCall("vi",p,[_]):u1.dynCall("v",p)}),s<X.mainLoop.currentlyRunningMainloop||(typeof SDL=="object"&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),X.mainLoop.scheduler())))}},e||(v&&v>0?l5(0,1e3/v):l5(1,1),X.mainLoop.scheduler()),g)throw"SimulateInfiniteLoop"}var X={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){X.mainLoop.scheduler=null,X.mainLoop.currentlyRunningMainloop++},resume:function(){X.mainLoop.currentlyRunningMainloop++;var p=X.mainLoop.timingMode,v=X.mainLoop.timingValue,g=X.mainLoop.func;X.mainLoop.func=null,A6(g,0,!1,X.mainLoop.arg,!0),l5(p,v),X.mainLoop.scheduler()},updateStatus:function(){if(L.setStatus){var p=L.statusMessage||"Please wait...",v=X.mainLoop.remainingBlockers,g=X.mainLoop.expectedBlockers;v?v<g?L.setStatus(p+" ("+(g-v)+"/"+g+")"):L.setStatus(p):L.setStatus("")}},runIter:function(p){if(!g1){if(L.preMainLoop&&L.preMainLoop()===!1)return;try{p()}catch(v){if(v instanceof P2)return;throw v&&typeof v=="object"&&v.stack&&L.printErr("exception thrown: "+[v,v.stack]),v}L.postMainLoop&&L.postMainLoop()}}},isFullScreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(L.preloadPlugins||(L.preloadPlugins=[]),!X.initted){X.initted=!0;try{new Blob,X.hasBlobConstructor=!0}catch{X.hasBlobConstructor=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}X.BlobBuilder=typeof MozBlobBuilder<"u"?MozBlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:X.hasBlobConstructor?null:console.log("warning: no BlobBuilder"),X.URLObject=typeof window<"u"?window.URL?window.URL:window.webkitURL:void 0,L.noImageDecoding||X.URLObject!==void 0||(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),L.noImageDecoding=!0);var p={canHandle:function(e){return!L.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},handle:function(e,s,B,D){var Y=null;if(X.hasBlobConstructor)try{(Y=new Blob([e],{type:X.getMimetype(s)})).size!==e.length&&(Y=new Blob([new Uint8Array(e).buffer],{type:X.getMimetype(s)}))}catch(e1){u1.warnOnce("Blob constructor present but fails: "+e1+"; falling back to blob builder")}if(!Y){var Q=new X.BlobBuilder;Q.append(new Uint8Array(e).buffer),Y=Q.getBlob()}var Z=X.URLObject.createObjectURL(Y),O=new Image;O.onload=function(){y1(O.complete,"Image "+s+" could not be decoded");var e1=document.createElement("canvas");e1.width=O.width,e1.height=O.height,e1.getContext("2d").drawImage(O,0,0),L.preloadedImages[s]=e1,X.URLObject.revokeObjectURL(Z),B&&B(e)},O.onerror=function(e1){console.log("Image "+Z+" could not be decoded"),D&&D()},O.src=Z}};L.preloadPlugins.push(p);var v={canHandle:function(e){return!L.noAudioDecoding&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(e,s,B,D){var Y=!1;function Q(R1){Y||(Y=!0,L.preloadedAudios[s]=R1,B&&B(e))}function Z(){Y||(Y=!0,L.preloadedAudios[s]=new Audio,D&&D())}if(!X.hasBlobConstructor)return Z();try{var O=new Blob([e],{type:X.getMimetype(s)})}catch{return Z()}var e1=X.URLObject.createObjectURL(O),a1=new Audio;a1.addEventListener("canplaythrough",function(){Q(a1)},!1),a1.onerror=function(R1){function I1(T1){for(var M1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",w0="=",x1="",H0=0,T0=0,D0=0;D0<T1.length;D0++)for(H0=H0<<8|T1[D0],T0+=8;T0>=6;){var I6=H0>>T0-6&63;T0-=6,x1+=M1[I6]}return T0==2?(x1+=M1[(3&H0)<<4],x1+=w0+w0):T0==4&&(x1+=M1[(15&H0)<<2],x1+=w0),x1}Y||(console.log("warning: browser could not fully decode audio "+s+", trying slower base64 approach"),a1.src="data:audio/x-"+s.substr(-3)+";base64,"+I1(e),Q(a1))},a1.src=e1,X.safeSetTimeout(function(){Q(a1)},1e4)}};L.preloadPlugins.push(v);var g=L.canvas;g&&(g.requestPointerLock=g.requestPointerLock||g.mozRequestPointerLock||g.webkitRequestPointerLock||g.msRequestPointerLock||function(){},g.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},g.exitPointerLock=g.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",_,!1),document.addEventListener("mozpointerlockchange",_,!1),document.addEventListener("webkitpointerlockchange",_,!1),document.addEventListener("mspointerlockchange",_,!1),L.elementPointerLock&&g.addEventListener("click",function(e){!X.pointerLock&&g.requestPointerLock&&(g.requestPointerLock(),e.preventDefault())},!1))}function _(){X.pointerLock=document.pointerLockElement===g||document.mozPointerLockElement===g||document.webkitPointerLockElement===g||document.msPointerLockElement===g}},createContext:function(p,v,g,_){if(v&&L.ctx&&p==L.canvas)return L.ctx;var e,s;if(v){var B={antialias:!1,alpha:!1};if(_)for(var D in _)B[D]=_[D];(s=GL.createContext(p,B))&&(e=GL.getContext(s).GLctx),p.style.backgroundColor="black"}else e=p.getContext("2d");return e?(g&&(v||y1(typeof GLctx>"u","cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),L.ctx=e,v&&GL.makeContextCurrent(s),L.useWebGL=v,X.moduleContextCreatedCallbacks.forEach(function(Y){Y()}),X.init()),e):null},destroyContext:function(p,v,g){},fullScreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullScreen:function(p,v,g){X.lockPointer=p,X.resizeCanvas=v,X.vrDevice=g,X.lockPointer===void 0&&(X.lockPointer=!0),X.resizeCanvas===void 0&&(X.resizeCanvas=!1),X.vrDevice===void 0&&(X.vrDevice=null);var _=L.canvas;function e(){X.isFullScreen=!1;var B=_.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===B?(_.cancelFullScreen=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},_.cancelFullScreen=_.cancelFullScreen.bind(document),X.lockPointer&&_.requestPointerLock(),X.isFullScreen=!0,X.resizeCanvas&&X.setFullScreenCanvasSize()):(B.parentNode.insertBefore(_,B),B.parentNode.removeChild(B),X.resizeCanvas&&X.setWindowedCanvasSize()),L.onFullScreen&&L.onFullScreen(X.isFullScreen),X.updateCanvasDimensions(_)}X.fullScreenHandlersInstalled||(X.fullScreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",e,!1),document.addEventListener("mozfullscreenchange",e,!1),document.addEventListener("webkitfullscreenchange",e,!1),document.addEventListener("MSFullscreenChange",e,!1));var s=document.createElement("div");_.parentNode.insertBefore(s,_),s.appendChild(_),s.requestFullScreen=s.requestFullScreen||s.mozRequestFullScreen||s.msRequestFullscreen||(s.webkitRequestFullScreen?function(){s.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),g?s.requestFullScreen({vrDisplay:g}):s.requestFullScreen()},nextRAF:0,fakeRequestAnimationFrame:function(p){var v=Date.now();if(X.nextRAF===0)X.nextRAF=v+1e3/60;else for(;v+2>=X.nextRAF;)X.nextRAF+=1e3/60;var g=Math.max(X.nextRAF-v,0);setTimeout(p,g)},requestAnimationFrame:function(p){typeof window>"u"?X.fakeRequestAnimationFrame(p):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||X.fakeRequestAnimationFrame),window.requestAnimationFrame(p))},safeCallback:function(p){return function(){if(!g1)return p.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){X.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(X.allowAsyncCallbacks=!0,X.queuedAsyncCallbacks.length>0){var p=X.queuedAsyncCallbacks;X.queuedAsyncCallbacks=[],p.forEach(function(v){v()})}},safeRequestAnimationFrame:function(p){return X.requestAnimationFrame(function(){g1||(X.allowAsyncCallbacks?p():X.queuedAsyncCallbacks.push(p))})},safeSetTimeout:function(p,v){return L.noExitRuntime=!0,setTimeout(function(){g1||(X.allowAsyncCallbacks?p():X.queuedAsyncCallbacks.push(p))},v)},safeSetInterval:function(p,v){return L.noExitRuntime=!0,setInterval(function(){g1||X.allowAsyncCallbacks&&p()},v)},getMimetype:function(p){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[p.substr(p.lastIndexOf(".")+1)]},getUserMedia:function(p){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(p)},getMovementX:function(p){return p.movementX||p.mozMovementX||p.webkitMovementX||0},getMovementY:function(p){return p.movementY||p.mozMovementY||p.webkitMovementY||0},getMouseWheelDelta:function(p){var v=0;switch(p.type){case"DOMMouseScroll":v=p.detail;break;case"mousewheel":v=p.wheelDelta;break;case"wheel":v=p.deltaY;break;default:throw"unrecognized mouse wheel event: "+p.type}return v},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(p){if(X.pointerLock)p.type!="mousemove"&&"mozMovementX"in p?X.mouseMovementX=X.mouseMovementY=0:(X.mouseMovementX=X.getMovementX(p),X.mouseMovementY=X.getMovementY(p)),typeof SDL<"u"?(X.mouseX=SDL.mouseX+X.mouseMovementX,X.mouseY=SDL.mouseY+X.mouseMovementY):(X.mouseX+=X.mouseMovementX,X.mouseY+=X.mouseMovementY);else{var v=L.canvas.getBoundingClientRect(),g=L.canvas.width,_=L.canvas.height,e=window.scrollX!==void 0?window.scrollX:window.pageXOffset,s=window.scrollY!==void 0?window.scrollY:window.pageYOffset;if(p.type==="touchstart"||p.type==="touchend"||p.type==="touchmove"){var B=p.touch;if(B===void 0)return;var D=B.pageX-(e+v.left),Y=B.pageY-(s+v.top),Q={x:D*=g/v.width,y:Y*=_/v.height};if(p.type==="touchstart")X.lastTouches[B.identifier]=Q,X.touches[B.identifier]=Q;else if(p.type==="touchend"||p.type==="touchmove"){var Z=X.touches[B.identifier];Z||(Z=Q),X.lastTouches[B.identifier]=Z,X.touches[B.identifier]=Q}return}var O=p.pageX-(e+v.left),e1=p.pageY-(s+v.top);O*=g/v.width,e1*=_/v.height,X.mouseMovementX=O-X.mouseX,X.mouseMovementY=e1-X.mouseY,X.mouseX=O,X.mouseY=e1}},xhrLoad:function(p,v,g){var _=new XMLHttpRequest;_.open("GET",p,!0),_.responseType="arraybuffer",_.onload=function(){_.status==200||_.status==0&&_.response?v(_.response):g()},_.onerror=g,_.send(null)},asyncLoad:function(p,v,g,_){X.xhrLoad(p,function(e){y1(e,'Loading data file "'+p+'" failed (no arrayBuffer).'),v(new Uint8Array(e)),_||s5()},function(e){if(!g)throw'Loading data file "'+p+'" failed.';g()}),_||P5()},resizeListeners:[],updateResizeListeners:function(){var p=L.canvas;X.resizeListeners.forEach(function(v){v(p.width,p.height)})},setCanvasSize:function(p,v,g){var _=L.canvas;X.updateCanvasDimensions(_,p,v),g||X.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullScreenCanvasSize:function(){if(typeof SDL<"u"){var p=r5[SDL.screen+0*u1.QUANTUM_SIZE>>2];p|=8388608,g0[SDL.screen+0*u1.QUANTUM_SIZE>>2]=p}X.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL<"u"){var p=r5[SDL.screen+0*u1.QUANTUM_SIZE>>2];p&=-8388609,g0[SDL.screen+0*u1.QUANTUM_SIZE>>2]=p}X.updateResizeListeners()},updateCanvasDimensions:function(p,v,g){v&&g?(p.widthNative=v,p.heightNative=g):(v=p.widthNative,g=p.heightNative);var _=v,e=g;if(L.forcedAspectRatio&&L.forcedAspectRatio>0&&(_/e<L.forcedAspectRatio?_=Math.round(e*L.forcedAspectRatio):e=Math.round(_/L.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===p.parentNode&&typeof screen<"u"){var s=Math.min(screen.width/_,screen.height/e);_=Math.round(_*s),e=Math.round(e*s)}X.resizeCanvas?(p.width!=_&&(p.width=_),p.height!=e&&(p.height=e),p.style!==void 0&&(p.style.removeProperty("width"),p.style.removeProperty("height"))):(p.width!=v&&(p.width=v),p.height!=g&&(p.height=g),p.style!==void 0&&(_!=v||e!=g?(p.style.setProperty("width",_+"px","important"),p.style.setProperty("height",e+"px","important")):(p.style.removeProperty("width"),p.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var p=X.nextWgetRequestHandle;return X.nextWgetRequestHandle++,p}};function p3(p){var v=Date.now()/1e3|0;return p&&(g0[p>>2]=v),v}function k3(){return 0}L.requestFullScreen=function(p,v,g){X.requestFullScreen(p,v,g)},L.requestAnimationFrame=function(p){X.requestAnimationFrame(p)},L.setCanvasSize=function(p,v,g){X.setCanvasSize(p,v,g)},L.pauseMainLoop=function(){X.mainLoop.pause()},L.resumeMainLoop=function(){X.mainLoop.resume()},L.getUserMedia=function(){X.getUserMedia()},L.createContext=function(p,v,g,_){return X.createContext(p,v,g,_)},P.staticInit(),j2.unshift(function(){L.noFSInit||P.init.initialized||P.init()}),D5.push(function(){P.ignorePermissions=!1}),a5.push(function(){P.quit()}),L.FS_createFolder=P.createFolder,L.FS_createPath=P.createPath,L.FS_createDataFile=P.createDataFile,L.FS_createPreloadedFile=P.createPreloadedFile,L.FS_createLazyFile=P.createLazyFile,L.FS_createLink=P.createLink,L.FS_createDevice=P.createDevice,L.FS_unlink=P.unlink,j2.unshift(function(){}),a5.push(function(){}),v6=$0=u1.alignMemory(M2),b6=!0,_5=v6+R5,y1((J0=u1.alignMemory(_5))<w2,"TOTAL_MEMORY not big enough for stack"),L.asmGlobalArg={Math,Int8Array,Int16Array,Int32Array,Uint8Array,Uint16Array,Uint32Array,Float32Array,Float64Array,NaN:NaN,Infinity:1/0},L.asmLibraryArg={abort:k2,assert:y1,_sysconf:d3,_pthread_self:k3,_abort:w3,___setErrNo:V2,_sbrk:f5,_time:p3,_emscripten_set_main_loop_timing:l5,_emscripten_memcpy_big:h3,_emscripten_set_main_loop:A6,STACKTOP:$0,STACK_MAX:_5,tempDoublePtr:D6,ABORT:g1};var n0=function(p,v,g){var _=new p.Int8Array(g),e=new p.Int16Array(g),s=new p.Int32Array(g),B=new p.Uint8Array(g),D=new p.Uint16Array(g),Y=new p.Uint32Array(g),Q=new p.Float32Array(g),Z=new p.Float64Array(g),O=v.STACKTOP|0,e1=v.STACK_MAX|0,a1=v.tempDoublePtr|0,R1=v.ABORT|0,I1=0,T1=0,M1=0,w0=0,x1=p.NaN,H0=p.Infinity,T0=0,D0=0,I6=0,o7=0,n7=0,a7=0,s7=0,f7=0,l7=0,O6=0,u7=0,c7=0,d7=0,h7=0,w7=0,m7=0,p7=0,k7=0,b7=0,v7=p.Math.floor,E7=p.Math.abs,g7=p.Math.sqrt,y7=p.Math.pow,_7=p.Math.cos,R7=p.Math.sin,S7=p.Math.tan,D7=p.Math.acos,A7=p.Math.asin,M7=p.Math.atan,P7=p.Math.atan2,N7=p.Math.exp,I7=p.Math.log,O7=p.Math.ceil,y=p.Math.imul,L7=p.Math.min,C7=p.Math.clz32,F7=v.abort,T7=v.assert,g3=v._sysconf,x7=v._pthread_self,b1=v._abort,B7=v.___setErrNo,b2=v._sbrk,y3=v._time,U7=v._emscripten_set_main_loop_timing,_3=v._emscripten_memcpy_big,z7=v._emscripten_set_main_loop,j7=0;function R3(i){i=i|0;var r=0;return r=O,O=O+i|0,O=O+15&-16,r|0}function S3(){return O|0}function D3(i){i=i|0,O=i}function A3(i,r){i=i|0,r=r|0,O=i,e1=r}function M3(i,r){i=i|0,r=r|0,I1||(I1=i,T1=r)}function q7(i){i|0,_[a1>>0]=_[i>>0],_[a1+1>>0]=_[i+1>>0],_[a1+2>>0]=_[i+2>>0],_[a1+3>>0]=_[i+3>>0]}function V7(i){i|0,_[a1>>0]=_[i>>0],_[a1+1>>0]=_[i+1>>0],_[a1+2>>0]=_[i+2>>0],_[a1+3>>0]=_[i+3>>0],_[a1+4>>0]=_[i+4>>0],_[a1+5>>0]=_[i+5>>0],_[a1+6>>0]=_[i+6>>0],_[a1+7>>0]=_[i+7>>0]}function P3(i){i=i|0,O6=i}function N3(){return O6|0}function I3(){var i=0,r=0;return r=O,O=O+16|0,i=r,s[i>>2]=0,pe(i,31756)|0,O=r,s[i>>2]|0}function O3(i){i=i|0;var r=0,t=0;r=O,O=O+16|0,t=r,s[t>>2]=i,ke(t),O=r}function L3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,U3(i,o|0?15:(B[r>>0]|0)>>>3&15,r+1|0,t,2)|0}function C3(i){i=i|0;var r=0;return r=s0(8)|0,Ee(r,r+4|0,i)|0,r|0}function F3(i){i=i|0,ge(i,i+4|0),K1(i)}function T3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0;return n=O,O=O+16|0,a=n,s[a>>2]=r,t=(ye(s[i>>2]|0,s[i+4>>2]|0,r,t,o,a,3)|0)<<16>>16,_[o>>0]=B[o>>0]|0|4,O=n,t|0}function x3(i){return i=i|0,i?(e[i>>1]=4096,i=0):i=-1,i|0}function L6(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0;if(d=s[a>>2]|0,k=n<<16>>16>0,k){f=0,l=0;do c=e[t+(f<<1)>>1]|0,c=y(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+l|0,(c^l|0)>0&(u^l|0)<0?(s[a>>2]=1,l=(l>>>31)+2147483647|0):l=u):(s[a>>2]=1,l=2147483647),f=f+1|0;while((f&65535)<<16>>16!=n<<16>>16);if((l|0)==2147483647){s[a>>2]=d,c=0,u=0;do l=e[t+(c<<1)>>1]>>2,l=y(l,l)|0,(l|0)!=1073741824?(f=(l<<1)+u|0,(l^u|0)>0&(f^u|0)<0?(s[a>>2]=1,u=(u>>>31)+2147483647|0):u=f):(s[a>>2]=1,u=2147483647),c=c+1|0;while((c&65535)<<16>>16!=n<<16>>16)}else h=8}else l=0,h=8;if((h|0)==8&&(u=l>>4),!u){e[i>>1]=0;return}if(w=((B1(u)|0)&65535)+65535|0,l=w<<16>>16,(w&65535)<<16>>16>0?(f=u<<l,(f>>l|0)==(u|0)?u=f:u=u>>31^2147483647):(l=0-l<<16,(l|0)<2031616?u=u>>(l>>16):u=0),m=S1(u,a)|0,f=s[a>>2]|0,k){l=0,u=0;do d=e[r+(l<<1)>>1]|0,d=y(d,d)|0,(d|0)!=1073741824?(c=(d<<1)+u|0,(d^u|0)>0&(c^u|0)<0?(s[a>>2]=1,u=(u>>>31)+2147483647|0):u=c):(s[a>>2]=1,u=2147483647),l=l+1|0;while((l&65535)<<16>>16!=n<<16>>16);if((u|0)==2147483647){s[a>>2]=f,d=0,u=0;do c=e[r+(d<<1)>>1]>>2,c=y(c,c)|0,(c|0)!=1073741824?(l=(c<<1)+u|0,(c^u|0)>0&(l^u|0)<0?(s[a>>2]=1,u=(u>>>31)+2147483647|0):u=l):(s[a>>2]=1,u=2147483647),d=d+1|0;while((d&65535)<<16>>16!=n<<16>>16)}else h=29}else u=0,h=29;if((h|0)==29&&(u=u>>4),u?(l=(B1(u)|0)<<16>>16,f=w-l|0,c=f&65535,u=(a0(m,S1(u<<l,a)|0)|0)<<16>>16,l=u<<7,f=f<<16>>16,c<<16>>16>0?f=c<<16>>16<31?l>>f:0:(h=0-f<<16>>16,f=l<<h,f=(f>>h|0)==(l|0)?f:u>>24^2147483647),c=(y(((t2(f,a)|0)<<9)+32768>>16,32767-(o&65535)<<16>>16)|0)>>>15<<16>>16):c=0,f=e[i>>1]|0,k)for(u=o<<16>>16,l=0;o=((y(f<<16>>16,u)|0)>>>15&65535)+c|0,f=o&65535,e[t>>1]=(y(e[t>>1]|0,o<<16>>16)|0)>>>12,l=l+1<<16>>16,!(l<<16>>16>=n<<16>>16);)t=t+2|0;e[i>>1]=f}function B3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0;if(f=s[o>>2]|0,n=t<<16>>16>0,n){l=0,a=0;do c=e[r+(l<<1)>>1]|0,c=y(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+a|0,(c^a|0)>0&(u^a|0)<0?(s[o>>2]=1,a=(a>>>31)+2147483647|0):a=u):(s[o>>2]=1,a=2147483647),l=l+1|0;while((l&65535)<<16>>16!=t<<16>>16);if((a|0)==2147483647){s[o>>2]=f,c=0,f=0;do u=e[r+(c<<1)>>1]>>2,u=y(u,u)|0,(u|0)!=1073741824?(l=(u<<1)+f|0,(u^f|0)>0&(l^f|0)<0?(s[o>>2]=1,f=(f>>>31)+2147483647|0):f=l):(s[o>>2]=1,f=2147483647),c=c+1|0;while((c&65535)<<16>>16!=t<<16>>16)}else w=8}else a=0,w=8;if((w|0)==8&&(f=a>>4),!!f){if(m=((B1(f)|0)&65535)+65535|0,u=m<<16>>16,(m&65535)<<16>>16>0?(l=f<<u,(l>>u|0)==(f|0)?f=l:f=f>>31^2147483647):(u=0-u<<16,(u|0)<2031616?f=f>>(u>>16):f=0),d=S1(f,o)|0,f=s[o>>2]|0,n){l=0,a=0;do c=e[i+(l<<1)>>1]|0,c=y(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+a|0,(c^a|0)>0&(u^a|0)<0?(s[o>>2]=1,a=(a>>>31)+2147483647|0):a=u):(s[o>>2]=1,a=2147483647),l=l+1|0;while((l&65535)<<16>>16!=t<<16>>16);if((a|0)==2147483647){s[o>>2]=f,f=0,l=0;do c=e[i+(f<<1)>>1]>>2,c=y(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+l|0,(c^l|0)>0&(u^l|0)<0?(s[o>>2]=1,l=(l>>>31)+2147483647|0):l=u):(s[o>>2]=1,l=2147483647),f=f+1|0;while((f&65535)<<16>>16!=t<<16>>16)}else w=28}else a=0,w=28;if((w|0)==28&&(l=a>>4),l?(c=B1(l)|0,u=c<<16>>16,c<<16>>16>0?(f=l<<u,(f>>u|0)==(l|0)?l=f:l=l>>31^2147483647):(u=0-u<<16,(u|0)<2031616?l=l>>(u>>16):l=0),f=m-(c&65535)|0,u=f&65535,a=(a0(d,S1(l,o)|0)|0)<<16>>16,n=a<<7,f=f<<16>>16,u<<16>>16>0?n=u<<16>>16<31?n>>f:0:(m=0-f<<16>>16,i=n<<m,n=(i>>m|0)==(n|0)?i:a>>24^2147483647),n=t2(n,o)|0,(n|0)>4194303?n=2147483647:n=(n|0)<-4194304?-2147483648:n<<9,n=S1(n,o)|0):n=0,a=(t&65535)+65535&65535,!(a<<16>>16<=-1))for(c=n<<16>>16,u=t+-1<<16>>16<<16>>16;;){f=r+(u<<1)|0,n=y(e[f>>1]|0,c)|0;do if((n|0)!=1073741824)if(l=n<<1,(l|0)<=268435455)if((l|0)<-268435456){e[f>>1]=-32768;break}else{e[f>>1]=n>>>12;break}else w=52;else s[o>>2]=1,w=52;while(!1);if((w|0)==52&&(w=0,e[f>>1]=32767),a=a+-1<<16>>16,a<<16>>16<=-1)break;u=u+-1|0}}}function U3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0;u=O,O=O+496|0,l=u,f=(n|0)==2;do if(f&1|(n|0)==4){a=i+1168|0,f?(ve(r,t,l,a),a=604):(se(r,t,l,a),a=3436),n=e[a+(r<<1)>>1]|0;do if(r>>>0>=8){if((r|0)==8){r=e[l+76>>1]<<2|(e[l+74>>1]<<1|e[l+72>>1]),a=e[l+70>>1]|0?5:4;break}if(r>>>0<15)return i=-1,O=u,i|0;r=s[i+1760>>2]|0,a=7;break}else a=0;while(!1);if(n<<16>>16==-1)return i=-1,O=u,i|0}else{if(n)return i=-1,O=u,i|0;for(f=e[t>>1]|0,r=t+490|0,n=t+2|0,a=0;e[l+(a<<1)>>1]=e[n>>1]|0,a=a+1|0,(a|0)!=244;)n=n+2|0;if(a=f<<16>>16,f<<16>>16==7){n=492,r=s[i+1760>>2]|0;break}else{n=492,r=e[r>>1]|0;break}}while(!1);return be(i,r,l,a,o),s[i+1760>>2]=r,i=n,O=u,i|0}function z3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;b=O,O=O+48|0,h=b+20|0,k=b,n=h,o=n+20|0;do e[n>>1]=e[i>>1]|0,n=n+2|0,i=i+2|0;while((n|0)<(o|0));i=e[h+18>>1]|0,w=(i&65535)-((i&65535)>>>15&65535)|0;e:do if(((w<<16>>31^w)&65535)<<16>>16<=4095){for(o=9,w=9;;){i=i<<16>>16,i=(i<<19>>19|0)==(i|0)?i<<3:i>>>15^32767,m=r+(o<<1)|0,e[m>>1]=i,i=i<<16>>16,i=y(i,i)|0,(i|0)==1073741824?(s[t>>2]=1,n=2147483647):n=i<<1,i=2147483647-n|0,(i&n|0)<0&&(s[t>>2]=1,i=2147483647),c=B1(i)|0,d=15-(c&65535)&65535,a=c<<16>>16,c<<16>>16>0?(n=i<<a,(n>>a|0)!=(i|0)&&(n=i>>31^2147483647)):(n=0-a<<16,(n|0)<2031616?n=i>>(n>>16):n=0),n=a0(16384,S1(n,t)|0)|0;do if(w<<16>>16>0){for(c=o+-1|0,f=n<<16>>16,l=w<<16>>16,u=0;;){if(o=D[h+(u<<1)>>1]|0,i=o<<16,a=y(e[h+(c-u<<1)>>1]|0,e[m>>1]|0)|0,(a|0)==1073741824?(s[t>>2]=1,n=2147483647):n=a<<1,a=i-n|0,((a^i)&(n^i)|0)<0&&(s[t>>2]=1,a=(o>>>15)+2147483647|0),a=y((S1(a,t)|0)<<16>>16,f)|0,(a|0)==1073741824?(s[t>>2]=1,a=2147483647):a=a<<1,a=Q5(a,d,t)|0,n=a-(a>>>31)|0,(n>>31^n|0)>32767){a=24;break}if(e[k+(u<<1)>>1]=a,u=u+1|0,(l|0)<=(u|0)){a=26;break}}if((a|0)==24){a=0,n=r,o=n+20|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));i=10}else if((a|0)==26)if(a=0,w<<16>>16>0)i=w;else{a=28;break}n=i+-1<<16>>16,_0(h|0,k|0,((n&65535)<<1)+2|0)|0,o=n<<16>>16}else a=28;while(!1);if((a|0)==28)if(i=w+-1<<16>>16,i<<16>>16>-1)o=i<<16>>16,n=32767;else break;if(i=e[h+(o<<1)>>1]|0,w=(i&65535)-((i&65535)>>>15&65535)|0,((w<<16>>31^w)&65535)<<16>>16>4095)break e;w=n}O=b;return}while(!1);n=r,o=n+20|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));O=b}function j3(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,a=0,f=0;if(r<<16>>16<=0)return i=0,i|0;o=s[i>>2]|0,n=0,t=0;do f=o&1,t=f|t<<1&131070,a=o>>1,o=(f|0)==(o>>>28&1|0)?a:a|1073741824,n=n+1<<16>>16;while(n<<16>>16<r<<16>>16);return s[i>>2]=o,f=t&65535,f|0}function u5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0;n=r,o=n+80|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));o=0,n=s[i>>2]|0;do u=n&1,l=n>>1,l=(u|0)==(n>>>28&1|0)?l:l|1073741824,a=l&1,f=l>>1,s[i>>2]=(a|0)==(l>>>28&1|0)?f:f|1073741824,a=n1((y(u<<1|a,1310720)|0)>>>17&65535,o,t)|0,u=s[i>>2]|0,f=u&1,l=u>>1,n=(f|0)==(u>>>28&1|0)?l:l|1073741824,s[i>>2]=n,e[r+(a<<16>>16<<1)>>1]=((f&65535)<<13&65535)+-4096<<16>>16,o=o+1<<16>>16;while(o<<16>>16<10)}function q3(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0;if(f=e[i>>1]|0,(f*31821|0)==1073741824?(s[a>>2]=1,l=1073741823):l=f*63642>>1,f=l+13849|0,(l|0)>-1&(f^l|0)<0&&(s[a>>2]=1,f=(l>>>31)+2147483647|0),e[i>>1]=f,!(r<<16>>16<=0))for(l=0,f=n+((f&127)<<1)|0;e[o+(l<<1)>>1]=(-65536<<e[t+(l<<1)>>1]>>>16^65535)&D[f>>1],l=l+1|0,(l&65535)<<16>>16!=r<<16>>16;)f=f+2|0}function C6(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+122|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function V3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0;for(l=159,f=0;;)if(c=e[t+(l<<1)>>1]|0,c=y(c,c)|0,c=(c|0)==1073741824?2147483647:c<<1,a=c+f|0,(c^f|0)>-1&(a^f|0)<0?(s[n>>2]=1,f=(f>>>31)+2147483647|0):f=a,(l|0)>0)l=l+-1|0;else{l=f;break}for(n=l>>>14&65535,f=32767,a=59;c=e[i+(a<<1)>>1]|0,f=c<<16>>16<f<<16>>16?c:f,(a|0)>0;)a=a+-1|0;for(c=(l|0)>536870911?32767:n,n=f<<16>>16,a=n<<20>>16,l=f<<16>>16>0?32767:-32768,t=55,f=e[i>>1]|0;u=e[i+(t<<1)>>1]|0,f=f<<16>>16<u<<16>>16?u:f,(t|0)>1;)t=t+-1|0;t=e[i+80>>1]|0,u=e[i+82>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+84>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+86>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+88>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+90>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+92>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+94>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+96>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+98>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+100>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+102>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+104>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+106>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+108>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+110>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+112>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+114>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+116>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=i+118|0,m=e[u>>1]|0;do if((c+-21&65535)<17557&f<<16>>16>20&&((c<<16>>16|0)<(((n<<4|0)==(a|0)?a:l)|0)||(t<<16>>16<m<<16>>16?m:t)<<16>>16<1953))if(f=i+120|0,a=e[f>>1]|0,a<<16>>16>29){e[f>>1]=30,t=f,l=1;break}else{l=(a&65535)+1&65535,e[f>>1]=l,t=f,l=l<<16>>16>1&1;break}else d=14;while(!1);(d|0)==14&&(t=i+120|0,e[t>>1]=0,l=0),f=0;do m=f,f=f+1|0,e[i+(m<<1)>>1]=e[i+(f<<1)>>1]|0;while((f|0)!=59);return e[u>>1]=c,f=e[t>>1]|0,f=f<<16>>16>15?16383:f<<16>>16>8?15565:13926,a=E2(r+8|0,5)|0,(e[t>>1]|0)>20?((E2(r,9)|0)<<16>>16|0)>(f|0)&&(d=20):(a<<16>>16|0)>(f|0)&&(d=20),(d|0)==20?(e[o>>1]=0,l|0):(a=(D[o>>1]|0)+1&65535,a<<16>>16>10?(e[o>>1]=10,l|0):(e[o>>1]=a,l|0))}function L5(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+18|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function H3(i,r,t,o,n,a,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0;A=i+2|0,e[i>>1]=e[A>>1]|0,F=i+4|0,e[A>>1]=e[F>>1]|0,T=i+6|0,e[F>>1]=e[T>>1]|0,x=i+8|0,e[T>>1]=e[x>>1]|0,M=i+10|0,e[x>>1]=e[M>>1]|0,C=i+12|0,e[M>>1]=e[C>>1]|0,e[C>>1]=t,E=0,N=0;do{w=n+(N<<1)|0,k=E1(e[w>>1]|0,e[o+(N<<1)>>1]|0,m)|0,k=(k&65535)-((k&65535)>>>15&65535)|0,k=k<<16>>31^k,I=((O2(k&65535)|0)&65535)+65535|0,h=I<<16>>16,(I&65535)<<16>>16<0?(b=0-h<<16,(b|0)<983040?R=k<<16>>16>>(b>>16)&65535:R=0):(b=k<<16>>16,k=b<<h,(k<<16>>16>>h|0)==(b|0)?R=k&65535:R=(b>>>15^32767)&65535),S=O2(e[w>>1]|0)|0,k=e[w>>1]|0,h=S<<16>>16,S<<16>>16<0?(b=0-h<<16,(b|0)<983040?b=k<<16>>16>>(b>>16)&65535:b=0):(b=k<<16>>16,k=b<<h,(k<<16>>16>>h|0)==(b|0)?b=k&65535:b=(b>>>15^32767)&65535),h=a0(R,b)|0,b=(I&65535)+2-(S&65535)|0,k=b&65535;do if(b&32768){if(k<<16>>16!=-32768){if(I=0-b|0,b=I<<16>>16,(I&65535)<<16>>16<0){if(b=0-b<<16,(b|0)>=983040){b=0;break}b=h<<16>>16>>(b>>16)&65535;break}}else b=32767;k=h<<16>>16,h=k<<b,(h<<16>>16>>b|0)==(k|0)?b=h&65535:b=(k>>>15^32767)&65535}else b=W1(h,k,m)|0;while(!1);E=n1(E,b,m)|0,N=N+1|0}while((N|0)!=10);switch(b=E&65535,k=E<<16>>16>5325,E=i+14|0,k?(n=(D[E>>1]|0)+1&65535,e[E>>1]=n,n<<16>>16>10&&(e[i+16>>1]=0)):e[E>>1]=0,r|0){case 0:case 1:case 2:case 3:case 6:break;default:return C=i+16|0,m=t,t=e[C>>1]|0,t=t&65535,t=t+1|0,t=t&65535,e[C>>1]=t,m|0}return R=(f|a)<<16>>16==0,S=c<<16>>16==0,I=r>>>0<3,E=b+(I&((S|(R&(l<<16>>16==0|u<<16>>16==0)|d<<16>>16<2))^1)?61030:62259)&65535,E=E<<16>>16>0?E:0,E<<16>>16<=2048?(E=E<<16>>16,(E<<18>>18|0)==(E|0)?u=E<<2:u=E>>>15^32767):u=8192,l=i+16|0,d=k|(e[l>>1]|0)<40,E=e[F>>1]|0,(E*6554|0)==1073741824?(s[m>>2]=1,k=2147483647):k=E*13108|0,E=e[T>>1]|0,b=E*6554|0,(b|0)!=1073741824?(E=(E*13108|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(s[m>>2]=1,E=(k>>>31)+2147483647|0)):(s[m>>2]=1,E=2147483647),b=e[x>>1]|0,k=b*6554|0,(k|0)!=1073741824?(b=(b*13108|0)+E|0,(k^E|0)>0&(b^E|0)<0&&(s[m>>2]=1,b=(E>>>31)+2147483647|0)):(s[m>>2]=1,b=2147483647),E=e[M>>1]|0,k=E*6554|0,(k|0)!=1073741824?(E=(E*13108|0)+b|0,(k^b|0)>0&(E^b|0)<0?(s[m>>2]=1,k=(b>>>31)+2147483647|0):k=E):(s[m>>2]=1,k=2147483647),E=e[C>>1]|0,b=E*6554|0,(b|0)!=1073741824?(E=(E*13108|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(s[m>>2]=1,E=(k>>>31)+2147483647|0)):(s[m>>2]=1,E=2147483647),k=S1(E,m)|0,I&((R|S)^1)&&(E=e[i>>1]|0,(E*4681|0)==1073741824?(s[m>>2]=1,k=2147483647):k=E*9362|0,E=e[A>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0?(s[m>>2]=1,k=(k>>>31)+2147483647|0):k=E):(s[m>>2]=1,k=2147483647),E=e[F>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0?(s[m>>2]=1,k=(k>>>31)+2147483647|0):k=E):(s[m>>2]=1,k=2147483647),E=e[T>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(s[m>>2]=1,E=(k>>>31)+2147483647|0)):(s[m>>2]=1,E=2147483647),b=e[x>>1]|0,k=b*4681|0,(k|0)!=1073741824?(b=(b*9362|0)+E|0,(k^E|0)>0&(b^E|0)<0?(s[m>>2]=1,E=(E>>>31)+2147483647|0):E=b):(s[m>>2]=1,E=2147483647),b=e[M>>1]|0,k=b*4681|0,(k|0)!=1073741824?(b=(b*9362|0)+E|0,(k^E|0)>0&(b^E|0)<0&&(s[m>>2]=1,b=(E>>>31)+2147483647|0)):(s[m>>2]=1,b=2147483647),k=e[C>>1]|0,w=k*4681|0,(w|0)!=1073741824?(h=(k*9362|0)+b|0,(w^b|0)>0&(h^b|0)<0&&(s[m>>2]=1,h=(b>>>31)+2147483647|0)):(s[m>>2]=1,h=2147483647),k=S1(h,m)|0),E=d?8192:u<<16>>16,w=y(E,t<<16>>16)|0,(w|0)==1073741824?(s[m>>2]=1,b=2147483647):b=w<<1,k=k<<16>>16,h=k<<13,(h|0)!=1073741824?(w=b+(k<<14)|0,(b^h|0)>0&(w^b|0)<0?(s[m>>2]=1,b=(b>>>31)+2147483647|0):b=w):(s[m>>2]=1,b=2147483647),w=y(k,E)|0,(w|0)==1073741824?(s[m>>2]=1,h=2147483647):h=w<<1,w=b-h|0,((w^b)&(h^b)|0)<0&&(s[m>>2]=1,w=(b>>>31)+2147483647|0),C=w<<2,t=l,m=S1((C>>2|0)==(w|0)?C:w>>31^2147483647,m)|0,C=e[t>>1]|0,C=C&65535,C=C+1|0,C=C&65535,e[t>>1]=C,m|0}function W3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0;o=r,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));o=0;do f=e[i+(o<<1)>>1]|0,n=((f&8)<<10&65535^8192)+-4096<<16>>16,a=o<<16,f=((e[t+((f&7)<<1)>>1]|0)*327680|0)+a>>16,e[r+(f<<1)>>1]=n,a=((e[t+((D[i+(o+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+a>>16,(a|0)<(f|0)&&(n=0-(n&65535)&65535),f=r+(a<<1)|0,e[f>>1]=(D[f>>1]|0)+(n&65535),o=o+1|0;while((o|0)!=5)}function X3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;n=r<<16>>16,o=(n<<1&2|1)+((n>>>1&7)*5|0)|0,r=n>>>4&3,r=((n>>>6&7)*5|0)+((r|0)==3?4:r)|0,n=t,a=n+80|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(a|0));i=i<<16>>16,e[t+(o<<1)>>1]=(0-(i&1)&16383)+57344,e[t+(r<<1)>>1]=(0-(i>>>1&1)&16383)+57344}function Y3(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0;a=t<<16>>16,l=a>>>3,i=i<<16>>16,i=((i<<17>>17|0)==(i|0)?i<<1:i>>>15^32767)+(l&8)<<16,l=(D[o+(i+65536>>16<<1)>>1]|0)+((l&7)*5|0)|0,t=r<<16>>16,f=(0-(t&1)&16383)+57344&65535,i=n+((D[o+(i>>16<<1)>>1]|0)+((a&7)*5|0)<<16>>16<<1)|0,r=n,a=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(a|0));e[i>>1]=f,e[n+(l<<16>>16<<1)>>1]=(0-(t>>>1&1)&16383)+57344}function G3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0;r=r<<16>>16,o=(r&7)*5|0,n=(r>>>2&2|1)+((r>>>4&7)*5|0)|0,r=(r>>>6&2)+2+((r>>>8&7)*5|0)|0,a=t,f=a+80|0;do e[a>>1]=0,a=a+2|0;while((a|0)<(f|0));i=i<<16>>16,e[t+(o<<1)>>1]=(0-(i&1)&16383)+57344,e[t+(n<<1)>>1]=(0-(i>>>1&1)&16383)+57344,e[t+(r<<1)>>1]=(0-(i>>>2&1)&16383)+57344}function K3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0;r=r<<16>>16,f=e[t+((r&7)<<1)>>1]|0,l=e[t+((r>>>3&7)<<1)>>1]|0,a=e[t+((r>>>6&7)<<1)>>1]|0,t=(r>>>9&1)+3+((e[t+((r>>>10&7)<<1)>>1]|0)*5|0)|0,r=o,n=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(n|0));i=i<<16>>16,e[o+(f*327680>>16<<1)>>1]=(0-(i&1)&16383)+57344,e[o+((l*327680|0)+65536>>16<<1)>>1]=(0-(i>>>1&1)&16383)+57344,e[o+((a*327680|0)+131072>>16<<1)>>1]=(0-(i>>>2&1)&16383)+57344,e[o+(t<<16>>16<<1)>>1]=(0-(i>>>3&1)&16383)+57344}function Z3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0;w=O,O=O+32|0,m=w+16|0,d=w,a=r,n=a+80|0;do e[a>>1]=0,a=a+2|0;while((a|0)<(n|0));n=e[i>>1]|0,e[m>>1]=n,e[m+2>>1]=e[i+2>>1]|0,e[m+4>>1]=e[i+4>>1]|0,e[m+6>>1]=e[i+6>>1]|0,u=e[i+8>>1]|0,F6(u>>>3&65535,u&7,0,4,1,d,t),u=e[i+10>>1]|0,F6(u>>>3&65535,u&7,2,6,5,d,t),u=e[i+12>>1]|0,o=u>>2;do if((o*25|0)!=1073741824){if(a=(y(o,1638400)|0)+786432>>21,o=a*6554>>15,(o|0)>32767){s[t>>2]=1,f=1,l=1,i=163835,c=6;break}i=(o<<16>>16)*5|0,f=o&1,(i|0)==1073741824?(s[t>>2]=1,l=0,i=65535):(l=0,c=6)}else s[t>>2]=1,f=0,o=0,l=0,a=0,i=0,c=6;while(!1);for((c|0)==6&&(i=i&65535),c=a-i|0,f=f<<16>>16?4-c|0:c,c=f<<16>>16,e[d+6>>1]=n1(((f<<17>>17|0)==(c|0)?f<<1:c>>>15^32767)&65535,u&1,t)|0,l&&(s[t>>2]=1,o=32767),c=o<<16>>16,e[d+14>>1]=((o<<17>>17|0)==(c|0)?o<<1:c>>>15^32767)+(u>>>1&1),o=0;n=n<<16>>16?-8191:8191,c=(e[d+(o<<1)>>1]<<2)+o<<16,a=c>>16,(c|0)<2621440&&(e[r+(a<<1)>>1]=n),f=(e[d+(o+4<<1)>>1]<<2)+o<<16,i=f>>16,(i|0)<(a|0)&&(n=0-(n&65535)&65535),(f|0)<2621440&&(c=r+(i<<1)|0,e[c>>1]=(D[c>>1]|0)+(n&65535)),o=o+1|0,(o|0)!=4;)n=e[m+(o<<1)>>1]|0;O=w}function F6(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0;u=i<<16>>16>124?124:i,i=(u<<16>>16)*1311>>15,h=(i|0)>32767,h?(s[f>>2]=1,l=32767,w=4):(l=i<<16>>16,(l*25|0)==1073741824?(s[f>>2]=1,l=1073741823):w=4),(w|0)==4&&(l=(l*50|0)>>>1),d=(u&65535)-l|0,l=(d<<16>>16)*6554>>15,m=(l|0)>32767,m?(s[f>>2]=1,u=32767,w=9):(u=l<<16>>16,(u*5|0)==1073741824?(s[f>>2]=1,c=1073741823):w=9),(w|0)==9&&(c=(u*10|0)>>>1),d=d-c|0,w=d<<16>>16,u=r<<16>>16,c=u>>2,u=u-(c<<2)|0,e[a+(t<<16>>16<<1)>>1]=((d<<17>>17|0)==(w|0)?d<<1:w>>>15^32767)+(u&1),m&&(s[f>>2]=1,l=32767),t=l<<16>>16,e[a+(o<<16>>16<<1)>>1]=((l<<17>>17|0)==(t|0)?l<<1:t>>>15^32767)+(u<<16>>17),h&&(s[f>>2]=1,i=32767),o=i<<16>>16,e[a+(n<<16>>16<<1)>>1]=n1(c&65535,((i<<17>>17|0)==(o|0)?i<<1:o>>>15^32767)&65535,f)|0}function Q3(i){i=i|0;var r=0,t=0,o=0,n=0;if(!i)return n=-1,n|0;k4(i+1168|0),e[i+460>>1]=40,s[i+1164>>2]=0,r=i+646|0,t=i+1216|0,o=i+462|0,n=o+22|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));return F5(r,s[t>>2]|0)|0,x5(i+686|0)|0,T5(i+700|0)|0,L5(i+608|0)|0,j6(i+626|0,s[t>>2]|0)|0,C6(i+484|0)|0,V6(i+730|0)|0,z6(i+748|0)|0,s2(i+714|0)|0,C5(i,0)|0,n=0,n|0}function C5(i,r){i=i|0,r=r|0;var t=0,o=0;if(!i)return i=-1,i|0;if(s[i+388>>2]=i+308,o2(i|0,0,308)|0,r=(r|0)!=8,r){t=i+412|0,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));e[i+392>>1]=3e4,e[i+394>>1]=26e3,e[i+396>>1]=21e3,e[i+398>>1]=15e3,e[i+400>>1]=8e3,e[i+402>>1]=0,e[i+404>>1]=-8e3,e[i+406>>1]=-15e3,e[i+408>>1]=-21e3,e[i+410>>1]=-26e3}if(e[i+432>>1]=0,e[i+434>>1]=40,s[i+1164>>2]=0,e[i+436>>1]=0,e[i+438>>1]=0,e[i+440>>1]=0,e[i+460>>1]=40,e[i+462>>1]=0,e[i+464>>1]=0,r){t=i+442|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));t=i+466|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));L5(i+608|0)|0,o=i+1216|0,j6(i+626|0,s[o>>2]|0)|0,F5(i+646|0,s[o>>2]|0)|0,x5(i+686|0)|0,T5(i+700|0)|0,s2(i+714|0)|0}else{t=i+466|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));L5(i+608|0)|0,F5(i+646|0,s[i+1216>>2]|0)|0,x5(i+686|0)|0,T5(i+700|0)|0}return C6(i+484|0)|0,e[i+606>>1]=21845,V6(i+730|0)|0,r?(z6(i+748|0)|0,i=0,i|0):(i=0,i|0)}function $3(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0,Z1=0,z1=0,f0=0,j1=0,Q1=0,l0=0,t0=0,H1=0,m0=0,j0=0,Y0=0,v0=0,d0=0,u0=0,O1=0,p0=0,g2=0,y2=0,I0=0,l2=0,S4=0,D4=0,C2=0,A4=0,i6=0,Z2=0,t6=0,F2=0,M0=0,k0=0,p1=0,v5=0,Q2=0,r6=0,o6=0,B0=0,_2=0,R0=0,G0=0,T2=0,E0=0,X1=0,n6=0,a6=0,O0=0,S0=0;if(S0=O,O=O+336|0,w=S0+236|0,m=S0+216|0,a6=S0+112|0,n6=S0+12|0,R0=S0+256|0,T2=S0+136|0,G0=S0+32|0,B0=S0+8|0,_2=S0+6|0,X1=S0+4|0,E0=S0+2|0,O0=S0,p1=i+1164|0,v5=i+748|0,Q2=ne(v5,o,p1)|0,Q2){C5(i,8)|0,re(v5,i+412|0,i+646|0,i+714|0,i+608|0,Q2,r,t,i+1168|0,n,a,p1),O0=i+666|0,b0(O0,i+392|0,10,p1),q6(i+626|0,O0,p1),O0=i+1156|0,s[O0>>2]=Q2,O=S0;return}switch(o|0){case 1:{f=1,S=6;break}case 2:case 7:{q3(i+606|0,e[(s[i+1256>>2]|0)+(r<<1)>>1]|0,s[(s[i+1260>>2]|0)+(r<<2)>>2]|0,t,s[i+1276>>2]|0,p1),S=9;break}case 3:{S=9;break}default:f=0,S=6}do if((S|0)==6)if(o=i+440|0,(e[o>>1]|0)==6){e[o>>1]=5,M0=0,k0=0;break}else{e[o>>1]=0,M0=0,k0=0;break}else(S|0)==9&&(o=i+440|0,M0=(D[o>>1]|0)+1&65535,e[o>>1]=M0<<16>>16>6?6:M0,M0=1,k0=1,f=0);while(!1);switch(i6=i+1156|0,s[i6>>2]|0){case 1:{e[o>>1]=5,e[i+436>>1]=0;break}case 2:{e[o>>1]=5,e[i+436>>1]=1;break}default:}u=i+646|0,Z2=i+666|0,l=a6,c=Z2,d=l+20|0;do _[l>>0]=_[c>>0]|0,l=l+1|0,c=c+1|0;while((l|0)<(d|0));t6=(r|0)!=7,F2=i+1168|0,t6?(U6(u,r,k0,t,F2,w,p1),l=i+392|0,v4(l,w,a,p1),t=t+6|0):(te(u,k0,t,F2,m,w,p1),l=i+392|0,b4(l,m,w,a,p1),t=t+10|0),c=w,d=l+20|0;do e[l>>1]=e[c>>1]|0,l=l+2|0,c=c+2|0;while((l|0)<(d|0));for(A4=r>>>0>1,F=r>>>0<4&1,C2=(r|0)==5,D4=C2?10:5,C2=C2?19:9,M=i+434|0,C=143-C2&65535,z=i+460|0,j=i+462|0,G=i+464|0,T=r>>>0>2,K=i+388|0,V=(r|0)==0,U=r>>>0<2,q=i+1244|0,i1=i+432|0,W=r>>>0<6,r1=i+1168|0,s1=(r|0)==6,c1=k0<<16>>16==0,h1=i+714|0,d1=i+686|0,t1=i+436|0,l1=i+700|0,w1=(r|0)==7,m1=i+482|0,$=r>>>0<3,H=i+608|0,v1=i+626|0,F1=i+438|0,P1=r>>>0<7,q1=i+730|0,x=M0^1,U1=f<<16>>16!=0,S4=U1?k0^1:0,L1=i+442|0,Z1=i+458|0,z1=i+412|0,f0=i+80|0,j1=i+1236|0,Q1=i+1240|0,l0=i+468|0,t0=i+466|0,H1=i+470|0,m0=i+472|0,j0=i+474|0,Y0=i+476|0,v0=i+478|0,d0=i+480|0,u0=i+444|0,O1=i+446|0,p0=i+448|0,g2=i+450|0,y2=i+452|0,I0=i+454|0,l2=i+456|0,I=0,N=0,h=0,k=0,A=-1;;){A=(A<<16>>16)+1|0,d=A&65535,N=1-(N<<16>>16)|0,E=N&65535,m=A4&h<<16>>16==80?0:h,b=t+2|0,w=e[t>>1]|0;e:do if(t6){if(R=e[M>>1]|0,l=(R&65535)-D4&65535,l=l<<16>>16<20?20:l,c=(l&65535)+C2&65535,u=c<<16>>16>143,J3(w,u?C:l,u?143:c,m,R,B0,_2,F,p1),m=e[B0>>1]|0,e[z>>1]=m,M0?(w=e[M>>1]|0,w<<16>>16<143&&(w=(w&65535)+1&65535,e[M>>1]=w),e[B0>>1]=w,e[_2>>1]=0,e[j>>1]|0&&!(T|(e[G>>1]|0)<5)&&(e[B0>>1]=m,w=m),m=0):(w=m,m=e[_2>>1]|0),G2(s[K>>2]|0,w,m,40,1,p1),U){if(m=t+6|0,Y3(d,e[t+4>>1]|0,e[b>>1]|0,s[q>>2]|0,R0,p1),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=V;break}c=V,w=t<<16>>16>0?32767:-32768;break}switch(r|0){case 2:{if(m=t+6|0,X3(e[t+4>>1]|0,e[b>>1]|0,R0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=V;break e}c=V,w=t<<16>>16>0?32767:-32768;break e}case 3:{if(m=t+6|0,G3(e[t+4>>1]|0,e[b>>1]|0,R0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=V;break e}c=V,w=t<<16>>16>0?32767:-32768;break e}default:{if(W){if(m=t+6|0,K3(e[t+4>>1]|0,e[b>>1]|0,s[r1>>2]|0,R0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=V;break e}c=V,w=t<<16>>16>0?32767:-32768;break e}if(!s1){c=V,S=44;break e}if(Z3(b,R0,p1),w=t+16|0,t=e[i1>>1]|0,R=t<<16>>16,d=R<<1,(d|0)==(R<<17>>16|0)){m=w,c=V,w=d;break e}m=w,c=V,w=t<<16>>16>0?32767:-32768;break e}}}else ee(w,18,143,m,B0,_2,p1),c1&&m<<16>>16==0|w<<16>>16<61?(w=e[B0>>1]|0,m=e[_2>>1]|0):(e[z>>1]=e[B0>>1]|0,w=e[M>>1]|0,e[B0>>1]=w,e[_2>>1]=0,m=0),G2(s[K>>2]|0,w,m,40,0,p1),c=0,S=44;while(!1);(S|0)==44&&(S=0,M0?h5(d1,e[o>>1]|0,X1,p1):e[X1>>1]=B6(r,e[b>>1]|0,s[Q1>>2]|0)|0,w5(d1,k0,e[t1>>1]|0,X1,p1),W3(t+4|0,R0,s[r1>>2]|0),w=t+24|0,t=e[X1>>1]|0,R=t<<16>>16,d=R<<1,(d|0)==(R<<17>>16|0)?(m=w,w=d):(m=w,w=t<<16>>16>0?32767:-32768)),t=e[B0>>1]|0;e:do if(t<<16>>16<40)for(l=w<<16>>16,u=t,w=t<<16>>16;;){if(d=R0+(w<<1)|0,t=(y(e[R0+(w-(u<<16>>16)<<1)>>1]|0,l)|0)>>15,(t|0)>32767&&(s[p1>>2]=1,t=32767),R=t&65535,e[O0>>1]=R,e[d>>1]=n1(e[d>>1]|0,R,p1)|0,w=w+1|0,(w&65535)<<16>>16==40)break e;u=e[B0>>1]|0}while(!1);e:do if(c)c=(N&65535|0)==0,c?(t=m,d=k):(t=m+2|0,d=e[m>>1]|0),c1?T6(h1,r,d,R0,E,X1,E0,F2,p1):(h5(d1,e[o>>1]|0,X1,p1),c5(l1,h1,e[o>>1]|0,E0,p1)),w5(d1,k0,e[t1>>1]|0,X1,p1),d5(l1,k0,e[t1>>1]|0,E0,p1),m=e[X1>>1]|0,w=m<<16>>16>13017?13017:m,c?S=80:R=d;else switch(t=m+2|0,w=e[m>>1]|0,r|0){case 1:case 2:case 3:case 4:case 6:{if(c1?T6(h1,r,w,R0,E,X1,E0,F2,p1):(h5(d1,e[o>>1]|0,X1,p1),c5(l1,h1,e[o>>1]|0,E0,p1)),w5(d1,k0,e[t1>>1]|0,X1,p1),d5(l1,k0,e[t1>>1]|0,E0,p1),m=e[X1>>1]|0,w=m<<16>>16>13017?13017:m,!s1){d=k,S=80;break e}if((e[M>>1]|0)<=45){d=k,S=80;break e}d=k,w=w<<16>>16>>>2&65535,S=80;break e}case 5:{M0?h5(d1,e[o>>1]|0,X1,p1):e[X1>>1]=B6(5,w,s[Q1>>2]|0)|0,w5(d1,k0,e[t1>>1]|0,X1,p1),c1?x6(h1,5,e[t>>1]|0,R0,s[j1>>2]|0,E0,p1):c5(l1,h1,e[o>>1]|0,E0,p1),d5(l1,k0,e[t1>>1]|0,E0,p1),w=e[X1>>1]|0,t=m+4|0,m=w,d=k,w=w<<16>>16>13017?13017:w,S=80;break e}default:{c1?x6(h1,r,w,R0,s[j1>>2]|0,E0,p1):c5(l1,h1,e[o>>1]|0,E0,p1),d5(l1,k0,e[t1>>1]|0,E0,p1),w=e[X1>>1]|0,m=w,d=k,S=80;break e}}while(!1);(S|0)==80&&(S=0,e[i1>>1]=m<<16>>16>13017?13017:m,R=d),w=w<<16>>16,w=(w<<17>>17|0)==(w|0)?w<<1:w>>>15^32767,E=(w&65535)<<16>>16>16384;e:do if(E){if(b=w<<16>>16,w1)m=0;else for(m=0;;)if(w=(y(e[(s[K>>2]|0)+(m<<1)>>1]|0,b)|0)>>15,(w|0)>32767&&(s[p1>>2]=1,w=32767),e[O0>>1]=w,w=y(e[X1>>1]|0,w<<16>>16)|0,(w|0)==1073741824?(s[p1>>2]=1,w=2147483647):w=w<<1,e[T2+(m<<1)>>1]=S1(w,p1)|0,m=m+1|0,(m|0)==40)break e;do w=(y(e[(s[K>>2]|0)+(m<<1)>>1]|0,b)|0)>>15,(w|0)>32767&&(s[p1>>2]=1,w=32767),e[O0>>1]=w,w=y(e[X1>>1]|0,w<<16>>16)|0,(w|0)!=1073741824?(w=w<<1,(w|0)<0?w=~((w^-2)>>1):S=88):(s[p1>>2]=1,w=2147483647,S=88),(S|0)==88&&(S=0,w=w>>1),e[T2+(m<<1)>>1]=S1(w,p1)|0,m=m+1|0;while((m|0)!=40)}while(!1);switch(c1&&(e[t0>>1]=e[l0>>1]|0,e[l0>>1]=e[H1>>1]|0,e[H1>>1]=e[m0>>1]|0,e[m0>>1]=e[j0>>1]|0,e[j0>>1]=e[Y0>>1]|0,e[Y0>>1]=e[v0>>1]|0,e[v0>>1]=e[d0>>1]|0,e[d0>>1]=e[m1>>1]|0,e[m1>>1]=e[X1>>1]|0),M0|(e[t1>>1]|0)!=0&&$&(e[j>>1]|0)!=0&&(r6=e[X1>>1]|0,r6<<16>>16>12288)&&(S=(((r6<<16>>16)+118784|0)>>>1)+12288&65535,e[X1>>1]=S<<16>>16>14745?14745:S),fe(a6,Z2,h,n6,p1),w=H3(H,r,e[E0>>1]|0,n6,v1,k0,e[t1>>1]|0,f,e[F1>>1]|0,e[j>>1]|0,e[G>>1]|0,p1)|0,r|0){case 0:case 1:case 2:case 3:case 6:{d=e[X1>>1]|0,b=1;break}default:w=e[E0>>1]|0,d=e[X1>>1]|0,P1?b=1:(m=d<<16>>16,d<<16>>16<0?m=~((m^-2)>>1):m=m>>>1,d=m&65535,b=2)}l=d<<16>>16,h=b&65535,m=s[K>>2]|0,k=0;do m=m+(k<<1)|0,e[G0+(k<<1)>>1]=e[m>>1]|0,m=y(e[m>>1]|0,l)|0,(m|0)==1073741824?(s[p1>>2]=1,u=2147483647):u=m<<1,c=y(e[E0>>1]|0,e[R0+(k<<1)>>1]|0)|0,(c|0)!=1073741824?(m=(c<<1)+u|0,(c^u|0)>0&(m^u|0)<0&&(s[p1>>2]=1,m=(u>>>31)+2147483647|0)):(s[p1>>2]=1,m=2147483647),S=m<<h,S=S1((S>>h|0)==(m|0)?S:m>>31^2147483647,p1)|0,m=s[K>>2]|0,e[m+(k<<1)>>1]=S,k=k+1|0;while((k|0)!=40);ue(q1),$&&(e[G>>1]|0)>3&&!((e[j>>1]|0)==0|x)&&le(q1),ce(q1,r,G0,w,e[X1>>1]|0,R0,d,b,F2,p1),w=0,c=0;do m=e[G0+(c<<1)>>1]|0,m=y(m,m)|0,(m|0)!=1073741824?(d=(m<<1)+w|0,(m^w|0)>0&(d^w|0)<0?(s[p1>>2]=1,w=(w>>>31)+2147483647|0):w=d):(s[p1>>2]=1,w=2147483647),c=c+1|0;while((c|0)!=40);(w|0)<0?w=~((w^-2)>>1):w=w>>1,w=e6(w,O0,p1)|0,d=((e[O0>>1]|0)>>>1)+15|0,m=d&65535,d=d<<16>>16,m<<16>>16>0?m<<16>>16<31?(w=w>>d,S=135):(w=0,S=137):(b=0-d<<16>>16,S=w<<b,w=(S>>b|0)==(w|0)?S:w>>31^2147483647,S=135),(S|0)==135&&(S=0,(w|0)<0?w=~((w^-4)>>2):S=137),(S|0)==137&&(S=0,w=w>>>2),w=w&65535;do if($&&(o6=e[G>>1]|0,o6<<16>>16>5))if(e[j>>1]|0)if((e[o>>1]|0)<4){if(U1?M0|(e[F1>>1]|0)!=0||(S=145):M0||(S=145),(S|0)==145&&!(e[t1>>1]|0)){S=147;break}ae(G0,w,L1,o6,e[t1>>1]|0,S4,p1)|0,S=147}else S=147;else S=151;else S=147;while(!1);do if((S|0)==147)if(S=0,e[j>>1]|0){if(!M0&&!(e[t1>>1]|0)){S=151;break}(e[o>>1]|0)>=4&&(S=151)}else S=151;while(!1);if((S|0)==151&&(S=0,e[L1>>1]=e[u0>>1]|0,e[u0>>1]=e[O1>>1]|0,e[O1>>1]=e[p0>>1]|0,e[p0>>1]=e[g2>>1]|0,e[g2>>1]=e[y2>>1]|0,e[y2>>1]=e[I0>>1]|0,e[I0>>1]=e[l2>>1]|0,e[l2>>1]=e[Z1>>1]|0,e[Z1>>1]=w),E){w=0;do E=T2+(w<<1)|0,e[E>>1]=n1(e[E>>1]|0,e[G0+(w<<1)>>1]|0,p1)|0,w=w+1|0;while((w|0)!=40);B3(G0,T2,40,p1),s[p1>>2]=0,c0(a,T2,n+(I<<1)|0,40,z1,0)}else s[p1>>2]=0,c0(a,G0,n+(I<<1)|0,40,z1,0);if(!(s[p1>>2]|0))r2(z1|0,n+(I+30<<1)|0,20)|0;else{for(d=193;;)if(m=i+(d<<1)|0,E=e[m>>1]|0,w=E<<16>>16,E<<16>>16<0?w=~((w^-4)>>2):w=w>>>2,e[m>>1]=w,(d|0)>0)d=d+-1|0;else{d=39;break}for(;m=G0+(d<<1)|0,E=e[m>>1]|0,w=E<<16>>16,E<<16>>16<0?w=~((w^-4)>>2):w=w>>>2,e[m>>1]=w,(d|0)>0;)d=d+-1|0;c0(a,G0,n+(I<<1)|0,40,z1,1)}if(r2(i|0,f0|0,308)|0,e[M>>1]=e[B0>>1]|0,w=I+40|0,h=w&65535,h<<16>>16>=160)break;I=w<<16>>16,a=a+22|0,k=R}e[j>>1]=V3(i+484|0,i+466|0,n,G,p1)|0,oe(v5,Z2,n,p1),e[t1>>1]=k0,e[F1>>1]=f,q6(i+626|0,Z2,p1),O0=i6,s[O0>>2]=Q2,O=S0}function T6(i,r,t,o,n,a,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0;switch(h=O,O=O+16|0,m=h+2|0,w=h,t=t<<16>>16,t=(t<<18>>18|0)==(t|0)?t<<2:t>>>15^32767,r|0){case 3:case 4:case 6:{d=t<<16>>16,t=s[l+84>>2]|0,e[a>>1]=e[t+(d<<1)>>1]|0,l=e[t+(d+1<<1)>>1]|0,c=e[t+(d+3<<1)>>1]|0,a=e[t+(d+2<<1)>>1]|0;break}case 0:{l=(t&65535)+(n<<16>>16<<1^2)|0,l=(l&65535)<<16>>16>1022?1022:l<<16>>16,e[a>>1]=e[782+(l<<1)>>1]|0,a=e[782+(l+1<<1)>>1]|0,f2(a<<16>>16,w,m,u),e[w>>1]=(D[w>>1]|0)+65524,l=K2(e[m>>1]|0,5,u)|0,d=e[w>>1]|0,d=n1(l,((d<<26>>26|0)==(d|0)?d<<10:d>>>15^32767)&65535,u)|0,l=e[m>>1]|0,t=e[w>>1]|0,(t*24660|0)==1073741824?(s[u>>2]=1,n=2147483647):n=t*49320|0,c=(l<<16>>16)*24660>>15,t=n+(c<<1)|0,(n^c|0)>0&(t^n|0)<0&&(s[u>>2]=1,t=(n>>>31)+2147483647|0),c=t<<13,l=a,c=S1((c>>13|0)==(t|0)?c:t>>31^2147483647,u)|0,a=d;break}default:d=t<<16>>16,t=s[l+80>>2]|0,e[a>>1]=e[t+(d<<1)>>1]|0,l=e[t+(d+1<<1)>>1]|0,c=e[t+(d+3<<1)>>1]|0,a=e[t+(d+2<<1)>>1]|0}if(I2(i,r,o,w,m,0,0,u),n=y((x0(14,e[m>>1]|0,u)|0)<<16>>16,l<<16>>16)|0,(n|0)==1073741824?(s[u>>2]=1,t=2147483647):t=n<<1,l=10-(D[w>>1]|0)|0,n=l&65535,l=l<<16>>16,n<<16>>16>0){w=n<<16>>16<31?t>>l:0,w=w>>>16,w=w&65535,e[f>>1]=w,W0(i,a,c),O=h;return}else{u=0-l<<16>>16,w=t<<u,w=(w>>u|0)==(t|0)?w:t>>31^2147483647,w=w>>>16,w=w&65535,e[f>>1]=w,W0(i,a,c),O=h;return}}function J3(i,r,t,o,n,a,f,l,u){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,!(o<<16>>16)){if(l=i<<16>>16,i<<16>>16>=197){e[a>>1]=l+65424,e[f>>1]=0;return}n=((l<<16)+131072>>16)*10923>>15,(n|0)>32767&&(s[u>>2]=1,n=32767),i=(n&65535)+19|0,e[a>>1]=i,e[f>>1]=l+58-((i*196608|0)>>>16);return}if(!(l<<16>>16)){u=i<<16>>16<<16,i=((u+131072>>16)*21846|0)+-65536>>16,e[a>>1]=i+(r&65535),e[f>>1]=((u+-131072|0)>>>16)-((i*196608|0)>>>16);return}if((E1(n,r,u)|0)<<16>>16>5&&(n=(r&65535)+5&65535),l=t<<16>>16,l=(l-(n&65535)&65535)<<16>>16>4?l+65532&65535:n,n=i<<16>>16,i<<16>>16<4){e[a>>1]=((((l&65535)<<16)+-327680|0)>>>16)+n,e[f>>1]=0;return}if(n=n<<16,i<<16>>16<12){u=(((n+-327680>>16)*10923|0)>>>15<<16)+-65536|0,i=u>>16,e[a>>1]=(l&65535)+i,e[f>>1]=((n+-589824|0)>>>16)-(u>>>15)-i;return}else{e[a>>1]=((n+-786432+((l&65535)<<16)|0)>>>16)+1,e[f>>1]=0;return}}function ee(i,r,t,o,n,a,f){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,o<<16>>16){f=(D[n>>1]|0)+65531|0,f=(f<<16>>16|0)<(r<<16>>16|0)?r:f&65535,t=t<<16>>16,r=i<<16>>16<<16,i=((r+327680>>16)*10924|0)+-65536>>16,e[n>>1]=(((((f&65535)<<16)+589824>>16|0)>(t|0)?t+65527&65535:f)&65535)+i,e[a>>1]=((r+-196608|0)>>>16)-((i*393216|0)>>>16);return}if(o=i<<16>>16,i<<16>>16<463){i=((((o<<16)+327680>>16)*10924|0)>>>16)+17|0,e[n>>1]=i,e[a>>1]=o+105-((i*393216|0)>>>16);return}else{e[n>>1]=o+65168,e[a>>1]=0;return}}function x6(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0;d=O,O=O+16|0,u=d+6|0,l=d+4|0,I2(i,r,o,u,l,d+2|0,d,f),c=(t&31)*3|0,o=n+(c<<1)|0,(E1(r&65535,7,f)|0)<<16>>16?(t=x0(14,e[l>>1]|0,f)|0,t=y(t<<16>>16,e[o>>1]|0)|0,(t|0)==1073741824?(s[f>>2]=1,o=2147483647):o=t<<1,t=E1(9,e[u>>1]|0,f)|0,l=t<<16>>16,t<<16>>16>0?l=t<<16>>16<31?o>>l:0:(f=0-l<<16>>16,l=o<<f,l=(l>>f|0)==(o|0)?l:o>>31^2147483647),l=l>>>16):(u=x0(e[u>>1]|0,e[l>>1]|0,f)|0,l=u<<16>>16,l=(y(((u<<20>>20|0)==(l|0)?u<<4:l>>>15^32767)<<16>>16,e[o>>1]|0)|0)>>15,(l|0)>32767&&(s[f>>2]=1,l=32767),o=l<<16,t=o>>16,(l<<17>>17|0)==(t|0)?l=o>>15:l=t>>>15^32767),e[a>>1]=l,W0(i,e[n+(c+1<<1)>>1]|0,e[n+(c+2<<1)>>1]|0),O=d}function B6(i,r,t){return i=i|0,r=r|0,t=t|0,r=e[t+(r<<16>>16<<1)>>1]|0,(i|0)!=7?(i=r,i|0):(i=r&65532,i|0)}function U6(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=O,O=O+48|0,h=R+20|0,E=R,b=s[n+44>>2]|0,k=s[n+64>>2]|0,l=s[n+4>>2]|0,w=s[n+12>>2]|0,c=s[n+20>>2]|0,u=s[n+56>>2]|0,t<<16>>16){l=0;do t=(e[i+20+(l<<1)>>1]|0)*29491>>15,(t|0)>32767&&(s[f>>2]=1,t=32767),u=(e[b+(l<<1)>>1]|0)*3277>>15,(u|0)>32767&&(s[f>>2]=1,u=32767),e[E+(l<<1)>>1]=n1(u&65535,t&65535,f)|0,l=l+1|0;while((l|0)!=10);if((r|0)==8){l=0;do k=i+(l<<1)|0,h=n1(e[b+(l<<1)>>1]|0,e[k>>1]|0,f)|0,e[k>>1]=E1(e[E+(l<<1)>>1]|0,h,f)|0,l=l+1|0;while((l|0)!=10);A0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));b0(E,a,10,f),O=R;return}else l=0;do u=i+(l<<1)|0,t=(y(e[k+(l<<1)>>1]|0,e[u>>1]|0)|0)>>15,(t|0)>32767&&(s[f>>2]=1,t=32767),h=n1(e[b+(l<<1)>>1]|0,t&65535,f)|0,e[u>>1]=E1(e[E+(l<<1)>>1]|0,h,f)|0,l=l+1|0;while((l|0)!=10);A0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));b0(E,a,10,f),O=R;return}else{if(d=r>>>0<2,d?(t=765,m=508,c=s[n+52>>2]|0):(n=(r|0)==5,t=n?1533:765,m=2044,l=n?u:l),u=e[o>>1]|0,t=((u*196608>>16|0)>(t&65535|0)?t:u*3&65535)<<16>>16,u=e[l+(t<<1)>>1]|0,e[h>>1]=u,e[h+2>>1]=e[l+(t+1<<1)>>1]|0,e[h+4>>1]=e[l+(t+2<<1)>>1]|0,t=e[o+2>>1]|0,d&&(t=t<<16>>16<<1&65535),d=(t<<16>>16)*196608|0,d=(d|0)>100466688?1533:d>>16,e[h+6>>1]=e[w+(d<<1)>>1]|0,e[h+8>>1]=e[w+(d+1<<1)>>1]|0,e[h+10>>1]=e[w+(d+2<<1)>>1]|0,o=e[o+4>>1]|0,o=((o<<18>>16|0)>(m&65535|0)?m:o<<2&65535)<<16>>16,e[h+12>>1]=e[c+(o<<1)>>1]|0,e[h+14>>1]=e[c+((o|1)<<1)>>1]|0,e[h+16>>1]=e[c+((o|2)<<1)>>1]|0,e[h+18>>1]=e[c+((o|3)<<1)>>1]|0,(r|0)==8){for(t=0;k=i+(t<<1)|0,e[E+(t<<1)>>1]=n1(u,n1(e[b+(t<<1)>>1]|0,e[k>>1]|0,f)|0,f)|0,e[k>>1]=u,t=t+1|0,(t|0)!=10;)u=e[h+(t<<1)>>1]|0;A0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));b0(E,a,10,f),O=R;return}else l=0;do u=i+(l<<1)|0,t=(y(e[k+(l<<1)>>1]|0,e[u>>1]|0)|0)>>15,(t|0)>32767&&(s[f>>2]=1,t=32767),o=n1(e[b+(l<<1)>>1]|0,t&65535,f)|0,r=e[h+(l<<1)>>1]|0,e[E+(l<<1)>>1]=n1(r,o,f)|0,e[u>>1]=r,l=l+1|0;while((l|0)!=10);A0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));b0(E,a,10,f),O=R;return}}function ie(i,r,t){i=i|0,r=r|0,t=t|0,r2(i|0,t+((r<<16>>16)*10<<1)|0,20)|0}function te(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=O,O=O+80|0,w=R+60|0,h=R+40|0,b=R+20|0,E=R,k=s[o+48>>2]|0,c=s[o+24>>2]|0,d=s[o+28>>2]|0,m=s[o+32>>2]|0,r<<16>>16){l=0;do w=k+(l<<1)|0,t=n1(((e[w>>1]|0)*1639|0)>>>15&65535,((e[i+20+(l<<1)>>1]|0)*31128|0)>>>15&65535,f)|0,e[b+(l<<1)>>1]=t,e[E+(l<<1)>>1]=t,h=i+(l<<1)|0,e[h>>1]=E1(t,n1(e[w>>1]|0,((e[h>>1]|0)*21299|0)>>>15&65535,f)|0,f)|0,l=l+1|0;while((l|0)!=10);A0(b,205,10,f),A0(E,205,10,f),l=i+20|0,o=E,r=l+20|0;do _[l>>0]=_[o>>0]|0,l=l+1|0,o=o+1|0;while((l|0)<(r|0));b0(b,n,10,f),b0(E,a,10,f),O=R;return}r=s[o+16>>2]|0,o=s[o+8>>2]|0,u=e[t>>1]|0,u=((u<<18>>18|0)==(u|0)?u<<2:u>>>15^32767)<<16>>16,e[w>>1]=e[o+(u<<1)>>1]|0,e[w+2>>1]=e[o+(u+1<<1)>>1]|0,e[h>>1]=e[o+(u+2<<1)>>1]|0,e[h+2>>1]=e[o+(u+3<<1)>>1]|0,u=e[t+2>>1]|0,u=((u<<18>>18|0)==(u|0)?u<<2:u>>>15^32767)<<16>>16,e[w+4>>1]=e[r+(u<<1)>>1]|0,e[w+6>>1]=e[r+(u+1<<1)>>1]|0,e[h+4>>1]=e[r+(u+2<<1)>>1]|0,e[h+6>>1]=e[r+(u+3<<1)>>1]|0,u=e[t+4>>1]|0,o=u<<16>>16,u<<16>>16<0?r=~((o^-2)>>1):r=o>>>1,u=r<<16>>16,u=((r<<18>>18|0)==(u|0)?r<<2:u>>>15^32767)<<16>>16,l=c+(u+1<<1)|0,r=e[c+(u<<1)>>1]|0,o&1?(r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[w+8>>1]=r,r=e[l>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[w+10>>1]=r,r=e[c+(u+2<<1)>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[h+8>>1]=r,r=e[c+(u+3<<1)>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[h+10>>1]=r):(e[w+8>>1]=r,e[w+10>>1]=e[l>>1]|0,e[h+8>>1]=e[c+(u+2<<1)>>1]|0,e[h+10>>1]=e[c+(u+3<<1)>>1]|0),l=e[t+6>>1]|0,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16,e[w+12>>1]=e[d+(l<<1)>>1]|0,e[w+14>>1]=e[d+(l+1<<1)>>1]|0,e[h+12>>1]=e[d+(l+2<<1)>>1]|0,e[h+14>>1]=e[d+(l+3<<1)>>1]|0,l=e[t+8>>1]|0,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16,e[w+16>>1]=e[m+(l<<1)>>1]|0,e[w+18>>1]=e[m+(l+1<<1)>>1]|0,e[h+16>>1]=e[m+(l+2<<1)>>1]|0,e[h+18>>1]=e[m+(l+3<<1)>>1]|0,l=0;do o=i+(l<<1)|0,r=(e[o>>1]|0)*21299>>15,(r|0)>32767&&(s[f>>2]=1,r=32767),m=n1(e[k+(l<<1)>>1]|0,r&65535,f)|0,e[b+(l<<1)>>1]=n1(e[w+(l<<1)>>1]|0,m,f)|0,t=e[h+(l<<1)>>1]|0,e[E+(l<<1)>>1]=n1(t,m,f)|0,e[o>>1]=t,l=l+1|0;while((l|0)!=10);A0(b,205,10,f),A0(E,205,10,f),l=i+20|0,o=E,r=l+20|0;do _[l>>0]=_[o>>0]|0,l=l+1|0,o=o+1|0;while((l|0)<(r|0));b0(b,n,10,f),b0(E,a,10,f),O=R}function F5(i,r){i=i|0,r=r|0;var t=0,o=0;if(!i)return o=-1,o|0;t=i,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return r2(i+20|0,r|0,20)|0,o=0,o|0}function z6(i){i=i|0;var r=0,t=0,o=0,n=0,a=0;if(!i)return a=-1,a|0;e[i>>1]=0,e[i+2>>1]=8192,r=i+4|0,e[r>>1]=3500,e[i+6>>1]=3500,s[i+8>>2]=1887529304,e[i+12>>1]=3e4,e[i+14>>1]=26e3,e[i+16>>1]=21e3,e[i+18>>1]=15e3,e[i+20>>1]=8e3,e[i+22>>1]=0,e[i+24>>1]=-8e3,e[i+26>>1]=-15e3,e[i+28>>1]=-21e3,e[i+30>>1]=-26e3,e[i+32>>1]=3e4,e[i+34>>1]=26e3,e[i+36>>1]=21e3,e[i+38>>1]=15e3,e[i+40>>1]=8e3,e[i+42>>1]=0,e[i+44>>1]=-8e3,e[i+46>>1]=-15e3,e[i+48>>1]=-21e3,e[i+50>>1]=-26e3,e[i+212>>1]=0,e[i+374>>1]=0,e[i+392>>1]=0,t=i+52|0,e[t>>1]=1384,e[i+54>>1]=2077,e[i+56>>1]=3420,e[i+58>>1]=5108,e[i+60>>1]=6742,e[i+62>>1]=8122,e[i+64>>1]=9863,e[i+66>>1]=11092,e[i+68>>1]=12714,e[i+70>>1]=13701,o=i+72|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+92|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+112|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+132|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+152|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+172|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));o=i+192|0,n=t,a=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(a|0));return o2(i+214|0,0,160)|0,e[i+376>>1]=3500,e[i+378>>1]=3500,a=e[r>>1]|0,e[i+380>>1]=a,e[i+382>>1]=a,e[i+384>>1]=a,e[i+386>>1]=a,e[i+388>>1]=a,e[i+390>>1]=a,e[i+394>>1]=0,e[i+396>>1]=7,e[i+398>>1]=32767,e[i+400>>1]=0,e[i+402>>1]=0,e[i+404>>1]=0,s[i+408>>2]=1,e[i+412>>1]=0,a=0,a|0}function re(i,r,t,o,n,a,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0;if(H=O,O=O+304|0,s1=H+192|0,i1=H+168|0,h1=H+148|0,w1=H+216|0,d1=H+146|0,t1=H+144|0,W=H+124|0,r1=H+104|0,c1=H+84|0,l1=H+60|0,U=H+40|0,V=H,$=i+404|0,m1=i+400|0,e[$>>1]|0&&e[m1>>1]|0){K=i+394|0,e[K>>1]=e[636+(f<<1)>>1]|0,N=e[i+212>>1]|0,I=N+10|0,r2(i+52+(((I&65535|0)==80?0:I<<16>>16)<<1)|0,i+52+(N<<1)|0,20)|0,N=e[i+392>>1]|0,I=N+1|0,e[i+376+(((I&65535|0)==8?0:I<<16>>16)<<1)>>1]=e[i+376+(N<<1)>>1]|0,I=i+4|0,e[I>>1]=0,N=V+36|0,A=V+32|0,F=V+28|0,T=V+24|0,x=V+20|0,M=V+16|0,C=V+12|0,z=V+8|0,j=V+4|0,G=i+52|0,k=V,q=k+40|0;do s[k>>2]=0,k=k+4|0;while((k|0)<(q|0));for(h=0,w=7;;){for(q=e[i+376+(w<<1)>>1]|0,S=q<<16>>16,q<<16>>16<0?S=~((S^-8)>>3):S=S>>>3,h=n1(h,S&65535,m)|0,e[I>>1]=h,E=w*10|0,k=9;b=V+(k<<2)|0,R=s[b>>2]|0,q=e[i+52+(k+E<<1)>>1]|0,S=q+R|0,(q^R|0)>-1&(S^R|0)<0&&(s[m>>2]=1,S=(R>>>31)+2147483647|0),s[b>>2]=S,(k|0)>0;)k=k+-1|0;if((w|0)<=0)break;w=w+-1|0}for(e[U+18>>1]=(s[N>>2]|0)>>>3,e[U+16>>1]=(s[A>>2]|0)>>>3,e[U+14>>1]=(s[F>>2]|0)>>>3,e[U+12>>1]=(s[T>>2]|0)>>>3,e[U+10>>1]=(s[x>>2]|0)>>>3,e[U+8>>1]=(s[M>>2]|0)>>>3,e[U+6>>1]=(s[C>>2]|0)>>>3,e[U+4>>1]=(s[z>>2]|0)>>>3,e[U+2>>1]=(s[j>>2]|0)>>>3,e[U>>1]=(s[V>>2]|0)>>>3,b0(U,i+12|0,10,m),e[I>>1]=E1(e[I>>1]|0,e[K>>1]|0,m)|0,_0(i+214|0,G|0,160)|0,U=9;;){for(q=e[i+214+(U+70<<1)>>1]|0,b=q<<16>>16,V=e[i+214+(U+60<<1)>>1]|0,k=(V<<16>>16)+b|0,(V^q)<<16>>16>-1&(k^b|0)<0&&(s[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+50<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(s[m>>2]=1,b=(k>>>31)+2147483647|0),q=e[i+214+(U+40<<1)>>1]|0,k=q+b|0,(q^b|0)>-1&(k^b|0)<0&&(s[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+30<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(s[m>>2]=1,b=(k>>>31)+2147483647|0),q=e[i+214+(U+20<<1)>>1]|0,k=q+b|0,(q^b|0)>-1&(k^b|0)<0&&(s[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+10<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0?(s[m>>2]=1,k=(k>>>31)+2147483647|0):k=b,q=e[i+214+(U<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(s[m>>2]=1,b=(k>>>31)+2147483647|0),(b|0)<0?b=~((b^-8)>>3):b=b>>>3,S=b&65535,E=e[654+(U<<1)>>1]|0,R=7;w=i+214+((R*10|0)+U<<1)|0,b=E1(e[w>>1]|0,S,m)|0,e[w>>1]=b,b=(y(E,b<<16>>16)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[w>>1]=b,h=(b&65535)-(b>>>15&1)|0,h=h<<16>>31^h,k=h&65535,k<<16>>16>655&&(k=(((h<<16>>16)+261489|0)>>>2)+655&65535),k=k<<16>>16>1310?1310:k,b&32768?b=0-(k&65535)&65535:b=k,e[w>>1]=b,(R|0)>0;)R=R+-1|0;if((U|0)>0)U=U+-1|0;else break}}if(e[m1>>1]|0){S=i+32|0,R=i+12|0,k=S,E=R,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));E=i+4|0,h=e[E>>1]|0,w=i+6|0,e[w>>1]=h;do if(e[i+402>>1]|0){k=e[i>>1]|0,e[i>>1]=0,k=k<<16>>16<32?k:32,q=k<<16>>16,b=q<<10,(b|0)!=(q<<26>>16|0)&&(s[m>>2]=1,b=k<<16>>16>0?32767:-32768),k<<16>>16>1?b=a0(1024,b&65535)|0:b=16384,e[i+2>>1]=b,ie(t,e[l>>1]|0,s[u+60>>2]|0),U6(t,8,0,l+2|0,u,R,m),k=t,q=k+20|0;do _[k>>0]=0,k=k+1|0;while((k|0)<(q|0));if(h=e[l+8>>1]|0,h=h<<16>>16?((h+64&65535)>127?h<<16>>16>0?32767:32768:h<<16>>16<<9)+60416&65535:-32768,e[E>>1]=h,e[i+412>>1]|0&&s[i+408>>2]|0)break;k=S,E=R,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));e[w>>1]=h}while(!1);k=h<<16>>16,h<<16>>16<0?k=~((k^-2)>>1):k=k>>>1,k=k+56536|0,b=k<<16,(b|0)>0?k=0:k=(b|0)<-946077696?-14436:k&65535,e[o>>1]=k,e[o+2>>1]=k,e[o+4>>1]=k,e[o+6>>1]=k,l=((k<<16>>16)*5443|0)>>>15&65535,e[o+8>>1]=l,e[o+10>>1]=l,e[o+12>>1]=l,e[o+14>>1]=l}for(k=((e[636+(f<<1)>>1]|0)*104864|0)>>>15<<16,(k|0)<0?k=~((k>>16^-32)>>5):k=k>>21,f=i+394|0,e[f>>1]=n1(((e[f>>1]|0)*29491|0)>>>15&65535,k&65535,m)|0,o=(D[i>>1]<<16)+65536|0,k=o>>16,u=i+2|0,k=(y(((o<<10>>26|0)==(k|0)?o>>>6:k>>>15^32767)<<16>>16,e[u>>1]|0)|0)>>15,(k|0)>32767&&(s[m>>2]=1,k=32767),h=k&65535,h<<16>>16<=1024?h<<16>>16<-2048?R=-32768:R=k<<4&65535:R=16384,l=i+4|0,S=R<<16>>16,b=y(e[l>>1]|0,S)|0,(b|0)==1073741824?(s[m>>2]=1,U=2147483647):U=b<<1,b=(y(e[i+30>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),I=b&65535,e[s1+18>>1]=I,b=(y(e[i+28>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+16>>1]=b,b=(y(e[i+26>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+14>>1]=b,b=(y(e[i+24>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+12>>1]=b,b=(y(e[i+22>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+10>>1]=b,b=(y(e[i+20>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+8>>1]=b,b=(y(e[i+18>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+6>>1]=b,b=(y(e[i+16>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+4>>1]=b,b=(y(e[i+14>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1+2>>1]=b,b=(y(e[i+12>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[s1>>1]=b,o=i+6|0,S=16384-(R&65535)<<16>>16,b=y(e[o>>1]|0,S)|0,(b|0)!=1073741824?(k=(b<<1)+U|0,(b^U|0)>0&(k^U|0)<0?(s[m>>2]=1,V=(U>>>31)+2147483647|0):V=k):(s[m>>2]=1,V=2147483647),k=I,E=9;h=s1+(E<<1)|0,b=(y(e[i+32+(E<<1)>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),k=n1(k,b&65535,m)|0,e[h>>1]=k,q=k<<16>>16,b=q<<1,(b|0)!=(q<<17>>16|0)&&(s[m>>2]=1,b=k<<16>>16>0?32767:-32768),e[h>>1]=b,b=E+-1|0,!((E|0)<=0);)k=e[s1+(b<<1)>>1]|0,E=b;U=i+374|0,b=((D[U>>1]<<16)+-161021952>>16)*9830>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),b=4096-(b&65535)|0,k=b<<16,(k|0)>268369920?S=32767:S=(k|0)<0?0:b<<19>>16,K=i+8|0,b=j3(K,3)|0,Y2(s1,W,10,m),k=r1,E=W,q=k+20|0;do e[k>>1]=e[E>>1]|0,k=k+2|0,E=E+2|0;while((k|0)<(q|0));for(k=(b<<16>>16)*10|0,E=9;h=r1+(E<<1)|0,w=e[h>>1]|0,b=(y(e[i+214+(E+k<<1)>>1]|0,S)|0)>>15,(b|0)>32767&&(s[m>>2]=1,b=32767),e[h>>1]=n1(w,b&65535,m)|0,(E|0)>0;)E=E+-1|0;A0(W,205,10,m),A0(r1,205,10,m),k=t+20|0,E=W,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));b0(W,s1,10,m),b0(r1,c1,10,m),y0(s1,i1,m),y0(c1,l1,m),k=d,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+22|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+44|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+66|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));z3(i1+2|0,h1,m),b=0,k=32767;do h=e[h1+(b<<1)>>1]|0,h=y(h,h)|0,h>>>0<1073741824?h=32767-(h>>>15)|0:(s[m>>2]=1,h=0),k=(y(h<<16>>16,k<<16>>16)|0)>>15,(k|0)>32767&&(s[m>>2]=1,k=32767),b=b+1|0;while((b|0)!=10);for(f2(k<<16>>16,d1,t1,m),k=(D[d1>>1]<<16)+-983040|0,h=k>>16,h=W1(E1(0,n1(((k<<12>>28|0)==(h|0)?k>>>4:h>>>15^32767)&65535,W1(e[t1>>1]|0,3,m)|0,m)|0,m)|0,1,m)|0,k=(e[U>>1]|0)*29491>>15,(k|0)>32767&&(s[m>>2]=1,k=32767),b=h<<16>>16,h=b*3277>>15,(h|0)>32767&&(s[m>>2]=1,h=32767),e[U>>1]=n1(k&65535,h&65535,m)|0,h=V>>10,w=h+262144|0,(h|0)>-1&(w^h|0)<0&&(s[m>>2]=1,w=(h>>>31)+2147483647|0),t1=b<<4,h=w-t1|0,((h^w)&(w^t1)|0)<0?(s[m>>2]=1,w=(w>>>31)+2147483647|0):w=h,t1=e[f>>1]<<5,h=t1+w|0,(t1^w|0)>-1&(h^w|0)<0&&(s[m>>2]=1,h=(w>>>31)+2147483647|0),b=(x0(h>>>16&65535,h>>>1&32767,m)|0)<<16>>16,u5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(y(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(s[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(c0(l1,w1,c,40,r,1),u5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(y(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(s[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(c0(l1,w1,c+80|0,40,r,1),u5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(y(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(s[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(c0(l1,w1,c+160|0,40,r,1),u5(K,w1,m),k=39;w=w1+(k<<1)|0,h=(y(e[w>>1]|0,b)|0)>>15,(h|0)>32767&&(s[m>>2]=1,h=32767),e[w>>1]=h,(k|0)>0;)k=k+-1|0;if(c0(l1,w1,c+240|0,40,r,1),e[n+14>>1]=20,e[n+16>>1]=0,(a|0)==2){h=e[i>>1]|0,h=h<<16>>16>32?32:h<<16>>16<1?8:h,c=h<<16>>16,w=c<<10,(w|0)!=(c<<26>>16|0)&&(s[m>>2]=1,w=h<<16>>16>0?32767:-32768),e[u>>1]=a0(1024,w&65535)|0,e[i>>1]=0,k=i+32|0,E=i+12|0,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));m=e[l>>1]|0,e[o>>1]=m,e[l>>1]=(m&65535)+65280}if(!(e[m1>>1]|0)){O=H;return}do if(!(e[i+402>>1]|0)){if(e[$>>1]|0)break;O=H;return}while(!1);e[i>>1]=0,e[i+412>>1]=1,O=H}function oe(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0;for(u=O,O=O+16|0,f=u+2|0,l=u,e[l>>1]=0,a=i+212|0,n=(D[a>>1]|0)+10|0,n=(n&65535|0)==80?0:n&65535,e[a>>1]=n,r2(i+52+(n<<16>>16<<1)|0,r|0,20)|0,n=0,a=159;c=e[t+(a<<1)>>1]|0,c=y(c,c)|0,c=(c|0)==1073741824?2147483647:c<<1,r=c+n|0,(c^n|0)>-1&(r^n|0)<0?(s[o>>2]=1,n=(n>>>31)+2147483647|0):n=r,(a|0)>0;)a=a+-1|0;f2(n,f,l,o),n=e[f>>1]|0,c=n<<16>>16,r=c<<10,(r|0)!=(c<<26>>16|0)&&(s[o>>2]=1,r=n<<16>>16>0?32767:-32768),e[f>>1]=r,c=e[l>>1]|0,n=c<<16>>16,c<<16>>16<0?n=~((n^-32)>>5):n=n>>>5,l=i+392|0,c=(D[l>>1]|0)+1|0,c=(c&65535|0)==8?0:c&65535,e[l>>1]=c,e[i+376+(c<<16>>16<<1)>>1]=n+57015+r,O=u}function ne(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0;u=(r|0)==4,c=(r|0)==5,d=(r|0)==6,o=s[i+408>>2]|0;e:do if((r+-4|0)>>>0<3)l=4;else{if((o+-1|0)>>>0<2)switch(r|0){case 2:case 3:case 7:{l=4;break e}default:}e[i>>1]=0,f=0}while(!1);if((l|0)==4){e:do if((o|0)==2){switch(r|0){case 2:case 4:case 6:case 7:break;default:{n=1;break e}}n=2}else n=1;while(!1);f=(D[i>>1]|0)+1&65535,e[i>>1]=f,f=(r|0)!=5&f<<16>>16>50?2:n}a=i+398|0,c&(e[i+412>>1]|0)==0?(e[a>>1]=0,n=0):n=e[a>>1]|0,n=n1(n,1,t)|0,e[a>>1]=n,t=i+404|0,e[t>>1]=0;e:do switch(r|0){case 2:case 4:case 5:case 6:case 7:{if((r|0)==7&(f|0)==0)l=14;else{if(n<<16>>16>30){e[t>>1]=1,e[a>>1]=0,e[i+396>>1]=0;break e}if(n=i+396|0,o=e[n>>1]|0,o<<16>>16){e[n>>1]=(o&65535)+65535;break e}else{e[a>>1]=0;break e}}break}default:l=14}while(!1);return(l|0)==14&&(e[i+396>>1]=7),f?(n=i+400|0,e[n>>1]=0,o=i+402|0,e[o>>1]=0,u?(e[n>>1]=1,f|0):c?(e[n>>1]=1,e[o>>1]=1,f|0):(d&&(e[n>>1]=1,e[t>>1]=0),f|0)):f|0}function T5(i){return i=i|0,i?(e[i>>1]=1,e[i+2>>1]=1,e[i+4>>1]=1,e[i+6>>1]=1,e[i+8>>1]=1,e[i+10>>1]=0,e[i+12>>1]=1,i=0,i|0):(i=-1,i|0)}function c5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0;u=O,O=O+16|0,l=u+2|0,f=u,a=E2(i,5)|0,i=i+10|0,(E1(a,e[i>>1]|0,n)|0)<<16>>16>0&&(a=e[i>>1]|0),a=(y(e[674+(t<<16>>16<<1)>>1]|0,a<<16>>16)|0)>>15,(a|0)>32767&&(s[n>>2]=1,a=32767),e[o>>1]=a,Bi(r,l,f,n),W0(r,e[l>>1]|0,e[f>>1]|0),O=u}function d5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,r<<16>>16||(t<<16>>16?(r=i+12|0,(E1(e[o>>1]|0,e[r>>1]|0,n)|0)<<16>>16>0&&(e[o>>1]=e[r>>1]|0)):r=i+12|0,e[r>>1]=e[o>>1]|0),e[i+10>>1]=e[o>>1]|0,n=i+2|0,e[i>>1]=e[n>>1]|0,t=i+4|0,e[n>>1]=e[t>>1]|0,n=i+6|0,e[t>>1]=e[n>>1]|0,i=i+8|0,e[n>>1]=e[i>>1]|0,e[i>>1]=e[o>>1]|0}function h5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0;if(n=E2(i,5)|0,i=i+10|0,(E1(n,e[i>>1]|0,o)|0)<<16>>16>0&&(n=e[i>>1]|0),n=(y(e[688+(r<<16>>16<<1)>>1]|0,n<<16>>16)|0)>>15,(n|0)<=32767){o=n,o=o&65535,e[t>>1]=o;return}s[o>>2]=1,o=32767,o=o&65535,e[t>>1]=o}function x5(i){return i=i|0,i?(e[i>>1]=1640,e[i+2>>1]=1640,e[i+4>>1]=1640,e[i+6>>1]=1640,e[i+8>>1]=1640,e[i+10>>1]=0,e[i+12>>1]=16384,i=0,i|0):(i=-1,i|0)}function w5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,r<<16>>16||(t<<16>>16?(r=i+12|0,(E1(e[o>>1]|0,e[r>>1]|0,n)|0)<<16>>16>0&&(e[o>>1]=e[r>>1]|0)):r=i+12|0,e[r>>1]=e[o>>1]|0),o=e[o>>1]|0,r=i+10|0,e[r>>1]=o,(E1(o,16384,n)|0)<<16>>16>0?(e[r>>1]=16384,r=16384):r=e[r>>1]|0,n=i+2|0,e[i>>1]=e[n>>1]|0,o=i+4|0,e[n>>1]=e[o>>1]|0,n=i+6|0,e[o>>1]=e[n>>1]|0,i=i+8|0,e[n>>1]=e[i>>1]|0,e[i>>1]=r}function ae(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0;if(u=E2(t,9)|0,c=e[t+16>>1]|0,l=c<<16>>16,t=(l+(e[t+14>>1]|0)|0)>>>1,t=(l|0)<(t<<16>>16|0)?c:t&65535,!(r<<16>>16>5&&u<<16>>16>r<<16>>16))return 0;l=t<<16>>16,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)&65535,o<<16>>16>6&n<<16>>16==0||(l=E1(l,t,f)|0),u=u<<16>>16>l<<16>>16?l:u,c=O2(r)|0,l=c<<16>>16,c<<16>>16<0?(t=0-l<<16,(t|0)<983040?l=r<<16>>16>>(t>>16)&65535:l=0):(t=r<<16>>16,n=t<<l,(n<<16>>16>>l|0)==(t|0)?l=n&65535:l=(t>>>15^32767)&65535),o=y((a0(16383,l)|0)<<16>>16,u<<16>>16)|0,(o|0)==1073741824?(s[f>>2]=1,n=2147483647):n=o<<1,o=E1(20,c,f)|0,l=o<<16>>16,o<<16>>16>0?o=o<<16>>16<31?n>>l:0:(r=0-l<<16>>16,o=n<<r,o=(o>>r|0)==(n|0)?o:n>>31^2147483647),o=(o|0)>32767?32767:o&65535,o=a<<16>>16!=0&o<<16>>16>3072?3072:o<<16>>16,t=0;do n=i+(t<<1)|0,l=y(e[n>>1]|0,o)|0,(l|0)==1073741824?(s[f>>2]=1,l=2147483647):l=l<<1,e[n>>1]=l>>>11,t=t+1|0;while((t|0)!=40);return 0}function se(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0;if(n=s[o+104>>2]|0,a=s[o+96>>2]|0,i>>>0>=8){if(e[t>>1]=(B[r>>0]|0)>>>4&1,e[t+2>>1]=(B[r>>0]|0)>>>5&1,e[t+4>>1]=(B[r>>0]|0)>>>6&1,e[t+6>>1]=(B[r>>0]|0)>>>7&255,n=n+(i<<1)|0,(e[n>>1]|0)>1)i=1,o=1,a=4;else return;for(;f=r+i|0,i=a|1,e[t+(a<<16>>16<<1)>>1]=B[f>>0]&1,e[t+(i<<16>>16<<1)>>1]=(B[f>>0]|0)>>>1&1,l=a|3,e[t+(i+1<<16>>16<<16>>16<<1)>>1]=(B[f>>0]|0)>>>2&1,e[t+(l<<16>>16<<1)>>1]=(B[f>>0]|0)>>>3&1,e[t+(l+1<<16>>16<<16>>16<<1)>>1]=(B[f>>0]|0)>>>4&1,e[t+(l+2<<16>>16<<16>>16<<1)>>1]=(B[f>>0]|0)>>>5&1,e[t+(l+3<<16>>16<<16>>16<<1)>>1]=(B[f>>0]|0)>>>6&1,e[t+(l+4<<16>>16<<16>>16<<1)>>1]=(B[f>>0]|0)>>>7&255,o=o+1<<16>>16,o<<16>>16<(e[n>>1]|0);)i=o<<16>>16,a=a+8<<16>>16;return}if(l=s[(s[o+100>>2]|0)+(i<<2)>>2]|0,e[t+(e[l>>1]<<1)>>1]=(B[r>>0]|0)>>>4&1,e[t+(e[l+2>>1]<<1)>>1]=(B[r>>0]|0)>>>5&1,e[t+(e[l+4>>1]<<1)>>1]=(B[r>>0]|0)>>>6&1,e[t+(e[l+6>>1]<<1)>>1]=(B[r>>0]|0)>>>7&255,f=n+(i<<1)|0,!((e[f>>1]|0)<=1))for(o=a+(i<<1)|0,n=1,i=1,a=4;n=r+n|0,a=a<<16>>16,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=B[n>>0]&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>1&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>2&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>3&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>4&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>5&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>6&1,a=a+1|0,(a|0)<(e[o>>1]|0)&&(e[t+(e[l+(a<<1)>>1]<<1)>>1]=(B[n>>0]|0)>>>7&1,a=a+1|0)))))))),i=i+1<<16>>16,i<<16>>16<(e[f>>1]|0);)n=i<<16>>16}function fe(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0;switch(t<<16>>16){case 0:{for(u=9;l=e[i+(u<<1)>>1]|0,t=l<<16>>16,l<<16>>16<0?t=~((t^-4)>>2):t=t>>>2,f=e[r+(u<<1)>>1]|0,a=f<<16>>16,f<<16>>16<0?f=~((a^-4)>>2):f=a>>>2,e[o+(u<<1)>>1]=n1((l&65535)-t&65535,f&65535,n)|0,(u|0)>0;)u=u+-1|0;return}case 40:{for(f=9;n=e[i+(f<<1)>>1]|0,t=n<<16>>16,n<<16>>16<0?a=~((t^-2)>>1):a=t>>>1,n=e[r+(f<<1)>>1]|0,t=n<<16>>16,n<<16>>16<0?t=~((t^-2)>>1):t=t>>>1,e[o+(f<<1)>>1]=t+a,(f|0)>0;)f=f+-1|0;return}case 80:{for(u=9;l=e[i+(u<<1)>>1]|0,t=l<<16>>16,l<<16>>16<0?l=~((t^-4)>>2):l=t>>>2,t=e[r+(u<<1)>>1]|0,a=t<<16>>16,t<<16>>16<0?f=~((a^-4)>>2):f=a>>>2,e[o+(u<<1)>>1]=n1(l&65535,(t&65535)-f&65535,n)|0,(u|0)>0;)u=u+-1|0;return}case 120:{e[o+18>>1]=e[r+18>>1]|0,e[o+16>>1]=e[r+16>>1]|0,e[o+14>>1]=e[r+14>>1]|0,e[o+12>>1]=e[r+12>>1]|0,e[o+10>>1]=e[r+10>>1]|0,e[o+8>>1]=e[r+8>>1]|0,e[o+6>>1]=e[r+6>>1]|0,e[o+4>>1]=e[r+4>>1]|0,e[o+2>>1]=e[r+2>>1]|0,e[o>>1]=e[r>>1]|0;return}default:return}}function j6(i,r){return i=i|0,r=r|0,i?(r2(i|0,r|0,20)|0,i=0,i|0):(i=-1,i|0)}function q6(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0;c=0;do u=i+(c<<1)|0,o=e[u>>1]|0,f=o&65535,l=f<<16,o=o<<16>>16,(o*5243|0)==1073741824?(s[t>>2]=1,a=2147483647):a=o*10486|0,n=l-a|0,((n^l)&(a^l)|0)<0?(s[t>>2]=1,a=(f>>>15)+2147483647|0):a=n,o=e[r+(c<<1)>>1]|0,n=o*5243|0,(n|0)!=1073741824?(o=(o*10486|0)+a|0,(n^a|0)>0&(o^a|0)<0&&(s[t>>2]=1,o=(a>>>31)+2147483647|0)):(s[t>>2]=1,o=2147483647),e[u>>1]=S1(o,t)|0,c=c+1|0;while((c|0)!=10)}function V6(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+18|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function le(i){i=i|0,e[i+14>>1]=1}function ue(i){i=i|0,e[i+14>>1]=0}function ce(i,r,t,o,n,a,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0;M=O,O=O+160|0,F=M+80|0,T=M,S=s[u+120>>2]|0,I=s[u+124>>2]|0,N=s[u+128>>2]|0,R=s[u+132>>2]|0,m=i+6|0,E=i+8|0,e[E>>1]=e[m>>1]|0,k=i+4|0,e[m>>1]=e[k>>1]|0,b=i+2|0,e[k>>1]=e[b>>1]|0,e[b>>1]=e[i>>1]|0,e[i>>1]=n,u=n<<16>>16<14746?n<<16>>16>9830&1:2,d=i+12|0,n=e[d>>1]|0,w=n<<15;do if((w|0)<=536870911)if((w|0)<-536870912){s[c>>2]=1,n=-2147483648;break}else{n=n<<17;break}else s[c>>2]=1,n=2147483647;while(!1);switch(A=o<<16>>16,h=i+16|0,(S1(n,c)|0)<<16>>16>=o<<16>>16?(w=e[h>>1]|0,w<<16>>16>0&&(w=(w&65535)+65535&65535,e[h>>1]=w),w<<16>>16||(n=(e[i>>1]|0)<9830,n=(e[b>>1]|0)<9830?n?2:1:n&1,(e[k>>1]|0)<9830&&(n=(n&65535)+1&65535),(e[m>>1]|0)<9830&&(n=(n&65535)+1&65535),(e[E>>1]|0)<9830&&(n=(n&65535)+1&65535),w=0,u=n<<16>>16>2?0:u)):(e[h>>1]=2,w=2),b=u<<16>>16,E=i+10|0,b=!(w<<16>>16)&&(b|0)>((e[E>>1]|0)+1|0)?b+65535&65535:u,i=(e[i+14>>1]|0)==1?0:o<<16>>16<10?2:b<<16>>16<2&w<<16>>16>0?(b&65535)+1&65535:b,e[E>>1]=i,e[d>>1]=o,r|0){case 4:case 6:case 7:break;default:if(i<<16>>16<2){for(w=0,u=0,m=a,d=F;;)if(e[m>>1]|0?(u=u<<16>>16,e[T+(u<<1)>>1]=w,n=e[m>>1]|0,u=u+1&65535):n=0,e[d>>1]=n,e[m>>1]=0,w=w+1<<16>>16,w<<16>>16>=40){E=u;break}else m=m+2|0,d=d+2|0;if(b=i<<16>>16==0,b=(r|0)==5?b?S:I:b?N:R,E<<16>>16>0){k=0;do{if(h=e[T+(k<<1)>>1]|0,u=h<<16>>16,i=e[F+(u<<1)>>1]|0,h<<16>>16<40){for(w=i<<16>>16,m=39-h&65535,d=h,u=a+(u<<1)|0,n=b;r=(y(e[n>>1]|0,w)|0)>>>15&65535,e[u>>1]=n1(e[u>>1]|0,r,c)|0,d=d+1<<16>>16,!(d<<16>>16>=40);)u=u+2|0,n=n+2|0;h<<16>>16>0&&(u=b+(m+1<<1)|0,x=36)}else u=b,x=36;if((x|0)==36)for(x=0,n=i<<16>>16,w=0,m=a;r=(y(e[u>>1]|0,n)|0)>>>15&65535,e[m>>1]=n1(e[m>>1]|0,r,c)|0,w=w+1<<16>>16,!(w<<16>>16>=h<<16>>16);)m=m+2|0,u=u+2|0;k=k+1|0}while((k&65535)<<16>>16!=E<<16>>16)}}}if(k=f<<16>>16,b=A<<1,n=l<<16>>16,d=0-n<<16,u=d>>16,l<<16>>16>0){for(w=0,m=t;i=y(e[t+(w<<1)>>1]|0,k)|0,(i|0)==1073741824?(s[c>>2]=1,d=2147483647):d=i<<1,l=y(b,e[a>>1]|0)|0,i=l+d|0,(l^d|0)>-1&(i^d|0)<0&&(s[c>>2]=1,i=(d>>>31)+2147483647|0),l=i<<n,e[m>>1]=S1((l>>n|0)==(i|0)?l:i>>31^2147483647,c)|0,w=w+1|0,(w|0)!=40;)a=a+2|0,m=m+2|0;O=M;return}if((d|0)<2031616){for(w=0,m=t;i=y(e[t+(w<<1)>>1]|0,k)|0,(i|0)==1073741824?(s[c>>2]=1,d=2147483647):d=i<<1,l=y(b,e[a>>1]|0)|0,i=l+d|0,(l^d|0)>-1&(i^d|0)<0&&(s[c>>2]=1,i=(d>>>31)+2147483647|0),e[m>>1]=S1(i>>u,c)|0,w=w+1|0,(w|0)!=40;)a=a+2|0,m=m+2|0;O=M;return}else{for(m=0,d=t;i=y(e[t+(m<<1)>>1]|0,k)|0,(i|0)==1073741824?(s[c>>2]=1,i=2147483647):i=i<<1,l=y(b,e[a>>1]|0)|0,(l^i|0)>-1&(l+i^i|0)<0&&(s[c>>2]=1),e[d>>1]=S1(0,c)|0,m=m+1|0,(m|0)!=40;)a=a+2|0,d=d+2|0;O=M;return}}function H6(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,i=0,i|0):(i=-1,i|0)}function de(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;if(!(t<<16>>16<=0))for(n=i+10|0,u=i+8|0,d=i+4|0,m=i+6|0,w=i+2|0,a=e[d>>1]|0,f=e[m>>1]|0,l=e[i>>1]|0,c=e[w>>1]|0,h=0;k=e[n>>1]|0,b=e[u>>1]|0,e[n>>1]=b,E=e[r>>1]|0,e[u>>1]=E,k=((E<<16>>16)*7699|0)+((y(l<<16>>16,-7667)|0)+(((a<<16>>16)*15836|0)+((f<<16>>16)*15836>>15))+((y(c<<16>>16,-7667)|0)>>15))+(y(b<<16>>16,-15398)|0)+((k<<16>>16)*7699|0)|0,b=k<<3,k=(b>>3|0)==(k|0)?b:k>>31^2147483647,b=k<<1,e[r>>1]=S1((b>>1|0)==(k|0)?b:k>>31^2147483647,o)|0,l=e[d>>1]|0,e[i>>1]=l,c=e[m>>1]|0,e[w>>1]=c,a=k>>>16&65535,e[d>>1]=a,f=(k>>>1)-(k>>16<<15)&65535,e[m>>1]=f,h=h+1<<16>>16,!(h<<16>>16>=t<<16>>16);)r=r+2|0}function he(i){return i=i|0,i?(e[i>>1]=0,i=0):i=-1,i|0}function W6(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0;if(l=o<<16>>16,a=r+(l+-1<<1)|0,l=l+-2|0,u=e[a>>1]|0,o<<16>>16<2)o=t<<16>>16;else for(o=t<<16>>16,f=0,r=r+(l<<1)|0;t=(y(e[r>>1]|0,o)|0)>>15,(t|0)>32767&&(s[n>>2]=1,t=32767),e[a>>1]=E1(e[a>>1]|0,t&65535,n)|0,a=a+-2|0,f=f+1<<16>>16,!((f<<16>>16|0)>(l|0));)r=r+-2|0;if(o=(y(e[i>>1]|0,o)|0)>>15,(o|0)<=32767){l=o,l=l&65535,f=e[a>>1]|0,n=E1(f,l,n)|0,e[a>>1]=n,e[i>>1]=u;return}s[n>>2]=1,l=32767,l=l&65535,f=e[a>>1]|0,n=E1(f,l,n)|0,e[a>>1]=n,e[i>>1]=u}function we(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;o2(i+104|0,0,340)|0,r=i+102|0,t=i,o=t+100|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return x3(r)|0,he(i+100|0)|0,o=0,o|0}function me(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0;if(S=O,O=O+96|0,k=S+22|0,b=S,E=S+44|0,r2(i+124|0,t|0,320)|0,d=E+22|0,m=i+100|0,w=i+80|0,h=i+102|0,(r&-2|0)==6){for(c=0;;){X0(o,702,k),X0(o,722,b),u=i+104+(c+10<<1)|0,L2(k,u,i,40),f=E,a=k,r=f+22|0;do e[f>>1]=e[a>>1]|0,f=f+2|0,a=a+2|0;while((f|0)<(r|0));f=d,r=f+22|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(r|0));c0(b,E,E,22,d,0),r=0,f=21;do{if(a=e[E+(f<<16>>16<<1)>>1]|0,a=y(a,a)|0,(a|0)==1073741824){R=7;break}l=a<<1,a=l+r|0,(l^r|0)>-1&(a^r|0)<0?(s[n>>2]=1,r=(r>>>31)+2147483647|0):r=a,f=f+-1<<16>>16}while(f<<16>>16>-1);for((R|0)==7&&(R=0,s[n>>2]=1),l=r>>>16&65535,a=20,r=0,f=20;;){if(a=y(e[E+(a+1<<1)>>1]|0,e[E+(a<<1)>>1]|0)|0,(a|0)==1073741824){R=13;break}if(I=a<<1,a=I+r|0,(I^r|0)>-1&(a^r|0)<0?(s[n>>2]=1,r=(r>>>31)+2147483647|0):r=a,a=(f&65535)+-1<<16>>16,a<<16>>16>-1)a=a<<16>>16,f=f+-1|0;else break}if((R|0)==13&&(R=0,s[n>>2]=1),r=r>>16,(r|0)<1?r=0:r=a0((r*26214|0)>>>15&65535,l)|0,W6(m,i,r,40,n),r=t+(c<<1)|0,c0(b,i,r,40,w,1),L6(h,u,r,29491,40,n),r=(c<<16)+2621440|0,(r|0)<10485760)c=r>>16,o=o+22|0;else break}f=i+104|0,a=i+424|0,r=f+20|0;do _[f>>0]=_[a>>0]|0,f=f+1|0,a=a+1|0;while((f|0)<(r|0));O=S;return}else{for(c=0;;){X0(o,742,k),X0(o,762,b),u=i+104+(c+10<<1)|0,L2(k,u,i,40),f=E,a=k,r=f+22|0;do e[f>>1]=e[a>>1]|0,f=f+2|0,a=a+2|0;while((f|0)<(r|0));f=d,r=f+22|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(r|0));c0(b,E,E,22,d,0),r=0,f=21;do{if(a=e[E+(f<<16>>16<<1)>>1]|0,a=y(a,a)|0,(a|0)==1073741824){R=22;break}I=a<<1,a=I+r|0,(I^r|0)>-1&(a^r|0)<0?(s[n>>2]=1,r=(r>>>31)+2147483647|0):r=a,f=f+-1<<16>>16}while(f<<16>>16>-1);for((R|0)==22&&(R=0,s[n>>2]=1),l=r>>>16&65535,a=20,r=0,f=20;;){if(a=y(e[E+(a+1<<1)>>1]|0,e[E+(a<<1)>>1]|0)|0,(a|0)==1073741824){R=28;break}if(I=a<<1,a=I+r|0,(I^r|0)>-1&(a^r|0)<0?(s[n>>2]=1,r=(r>>>31)+2147483647|0):r=a,a=(f&65535)+-1<<16>>16,a<<16>>16>-1)a=a<<16>>16,f=f+-1|0;else break}if((R|0)==28&&(R=0,s[n>>2]=1),r=r>>16,(r|0)<1?r=0:r=a0((r*26214|0)>>>15&65535,l)|0,W6(m,i,r,40,n),r=t+(c<<1)|0,c0(b,i,r,40,w,1),L6(h,u,r,29491,40,n),r=(c<<16)+2621440|0,(r|0)<10485760)c=r>>16,o=o+22|0;else break}f=i+104|0,a=i+424|0,r=f+20|0;do _[f>>0]=_[a>>0]|0,f=f+1|0,a=a+1|0;while((f|0)<(r|0));O=S;return}}function pe(i,r){i=i|0,r=r|0;var t=0,o=0;return!i||(s[i>>2]=0,t=s0(1764)|0,!t)?(i=-1,i|0):!((Q3(t)|0)<<16>>16)&&(o=t+1748|0,(H6(o)|0)<<16>>16==0)?(C5(t,0)|0,we(t+1304|0)|0,H6(o)|0,s[t+1760>>2]=0,s[i>>2]=t,i=0,i|0):(r=s[t>>2]|0,r?(K1(r),s[t>>2]=0,i=-1,i|0):(i=-1,i|0))}function ke(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function be(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=O,O=O+208|0,E=R+88|0,b=R,k=i+1164|0,a=s[i+1256>>2]|0,(o+-5|0)>>>0<2){if(h=a+16|0,(e[h>>1]|0)>0)for(w=s[(s[i+1260>>2]|0)+32>>2]|0,m=0,a=0;;){if(d=w+(m<<1)|0,u=e[d>>1]|0,u<<16>>16>0){for(l=t,c=0,f=0;f=D[l>>1]|f<<1&131070,c=c+1<<16>>16,!(c<<16>>16>=u<<16>>16);)l=l+2|0;f=f&65535}else f=0;if(e[E+(m<<1)>>1]=f,a=a+1<<16>>16,a<<16>>16<(e[h>>1]|0))t=t+(e[d>>1]<<1)|0,m=a<<16>>16;else break}}else if(w=a+(r<<1)|0,(e[w>>1]|0)>0)for(h=s[(s[i+1260>>2]|0)+(r<<2)>>2]|0,d=0,a=0;;){if(m=h+(d<<1)|0,u=e[m>>1]|0,u<<16>>16>0){for(l=t,c=0,f=0;f=D[l>>1]|f<<1&131070,c=c+1<<16>>16,!(c<<16>>16>=u<<16>>16);)l=l+2|0;f=f&65535}else f=0;if(e[E+(d<<1)>>1]=f,a=a+1<<16>>16,a<<16>>16<(e[w>>1]|0))t=t+(e[m>>1]<<1)|0,d=a<<16>>16;else break}$3(i,r,E,o,n,b),me(i+1304|0,r,n,b,k),de(i+1748|0,n,160,k),a=0;do i=n+(a<<1)|0,e[i>>1]=D[i>>1]&65528,a=a+1|0;while((a|0)!=160);O=R}function ve(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;if(a=s[o+100>>2]|0,f=(D[(s[o+96>>2]|0)+(i<<1)>>1]|0)+65535|0,o=f&65535,n=o<<16>>16>-1,i>>>0<8){if(!n)return;for(a=s[a+(i<<2)>>2]|0,n=f<<16>>16;e[t+(e[a+(n<<1)>>1]<<1)>>1]=(B[r+(n>>3)>>0]|0)>>>(n&7^7)&1,o=o+-1<<16>>16,o<<16>>16>-1;)n=o<<16>>16;return}else{if(!n)return;for(n=f<<16>>16;e[t+(n<<1)>>1]=(B[r+(n>>3)>>0]|0)>>>(n&7^7)&1,o=o+-1<<16>>16,o<<16>>16>-1;)n=o<<16>>16;return}}function Ee(i,r,t){return i=i|0,r=r|0,t=t|0,i=Si(i,t,31764)|0,((_i(r)|0|i)<<16>>16!=0)<<31>>31|0}function ge(i,r){i=i|0,r=r|0,Di(i),Ri(r)}function ye(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0;if(m=O,O=O+512|0,l=m+8|0,u=m+4|0,c=m,s[c>>2]=0,d=f<<16>>16==3,!((f&65535)<2|d&1)){if(f<<16>>16!=2)return n=-1,O=m,n|0;h4(i,t,o,l+2|0,c),i=s[c>>2]|0,s[a>>2]=i,d4(r,i,u),r=s[u>>2]|0,e[l>>1]=r,e[l+490>>1]=(r|0)==3?-1:t&65535,_[n>>0]=r,r=1;do l=l+1|0,_[n+r>>0]=_[l>>0]|0,r=r+1|0;while((r|0)!=492);return l=492,O=m,l|0}if(h4(i,t,o,l,c),d4(r,s[c>>2]|0,u),o=s[u>>2]|0,(o|0)!=3){if(r=s[c>>2]|0,s[a>>2]=r,(r|0)==8){switch(o|0){case 1:{e[l+70>>1]=0;break}case 2:{c=l+70|0,e[c>>1]=D[c>>1]|0|1;break}default:}e[l+72>>1]=t&1,e[l+74>>1]=t>>>1&1,e[l+76>>1]=t>>>2&1,r=8}}else s[a>>2]=15,r=15;if(d)return Ye(r,l,n,(s[i+4>>2]|0)+2392|0),n=e[3404+(s[a>>2]<<16>>16<<1)>>1]|0,O=m,n|0;switch(f<<16>>16){case 0:return Xe(r,l,n,(s[i+4>>2]|0)+2392|0),n=e[3404+(s[a>>2]<<16>>16<<1)>>1]|0,O=m,n|0;case 1:return We(r,l,n,(s[i+4>>2]|0)+2392|0),n=e[3436+(s[a>>2]<<16>>16<<1)>>1]|0,O=m,n|0;default:return n=-1,O=m,n|0}return 0}function B5(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0;for(F=O,O=O+480|0,A=F,a=240,c=n,u=i,l=A,f=0;;){if(N=((y(e[c>>1]|0,e[u>>1]|0)|0)+16384|0)>>>15,e[l>>1]=N,N=N<<16,f=(y(N>>15,N>>16)|0)+f|0,(f|0)<0){d=4;break}if(a=a+-1|0,(a&65535)<<16>>16)c=c+2|0,u=u+2|0,l=l+2|0;else{a=0;break}}if((d|0)==4){if(f=a&65535,l=240-a|0,!(f<<16>>16))a=0;else for(c=f,u=n+(l<<1)|0,a=i+(l<<1)|0,f=A+(l<<1)|0;;)if(e[f>>1]=((y(e[u>>1]|0,e[a>>1]|0)|0)+16384|0)>>>15,c=c+-1<<16>>16,c<<16>>16)u=u+2|0,a=a+2|0,f=f+2|0;else{a=0;break}do{for(u=a&65535,a=120,l=A,f=0;N=(e[l>>1]|0)>>>2,S=l+2|0,e[l>>1]=N,N=N<<16>>16,N=y(N,N)|0,I=(e[S>>1]|0)>>>2,e[S>>1]=I,I=I<<16>>16,f=((y(I,I)|0)+N<<1)+f|0,a=a+-1<<16>>16,a<<16>>16;)l=l+4|0;a=u+4|0}while((f|0)<1)}if(N=f+1|0,I=(B1(N)|0)<<16>>16,N=N<<I,e[t>>1]=N>>>16,e[o>>1]=(N>>>1)-(N>>16<<15),N=A+478|0,c=r<<16>>16,r<<16>>16<=0)return A=I-a|0,A=A&65535,O=F,A|0;for(b=A+476|0,E=I+1|0,R=239-c|0,S=A+(236-c<<1)|0,r=c,t=t+(c<<1)|0,o=o+(c<<1)|0;;){if(d=y((R>>>1)+65535&65535,-2)|0,u=A+(d+236<<1)|0,d=S+(d<<1)|0,n=240-r|0,k=n+-1|0,l=A+(k<<1)|0,i=k>>>1&65535,n=A+(n+-2<<1)|0,c=y(e[N>>1]|0,e[l>>1]|0)|0,!(i<<16>>16))d=n,u=b;else for(h=b,w=N;f=l+-4|0,m=w+-4|0,c=(y(e[h>>1]|0,e[n>>1]|0)|0)+c|0,i=i+-1<<16>>16,c=(y(e[m>>1]|0,e[f>>1]|0)|0)+c|0,i<<16>>16;)n=l+-6|0,h=w+-6|0,l=f,w=m;if(k&1&&(c=(y(e[u>>1]|0,e[d>>1]|0)|0)+c|0),k=c<<E,e[t>>1]=k>>>16,e[o>>1]=(k>>>1)-(k>>16<<15),(r&65535)+-1<<16>>16<<16>>16>0)R=R+1|0,S=S+2|0,r=r+-1|0,t=t+-2|0,o=o+-2|0;else break}return A=I-a|0,A=A&65535,O=F,A|0}function X6(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0;z=O,O=O+3440|0,C=z+3420|0,F=z+3400|0,T=z+3224|0,M=z,N=z+3320|0,x=z+3240|0,A=z+24|0,W2(t,i,N,2,l),c4(N,r,x,T,5,F,5,l),N2(t,x,A,l),u4(10,5,5,N,A,F,T,M,l),r=o,l=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(l|0));e[a>>1]=65535,e[a+2>>1]=65535,e[a+4>>1]=65535,e[a+6>>1]=65535,e[a+8>>1]=65535,w=0,h=M,k=C;do{i=e[h>>1]|0,h=h+2|0,u=(i*6554|0)>>>15,c=u<<16>>16,r=o+(i<<1)|0,l=e[r>>1]|0,(e[x+(i<<1)>>1]|0)>0?(e[r>>1]=l+4096,e[k>>1]=8192,d=u):(e[r>>1]=l+61440,e[k>>1]=-8192,d=c+8|0),k=k+2|0,m=d&65535,r=i-(u<<2)-c<<16>>16,u=a+(r<<1)|0,l=e[u>>1]|0,i=l<<16>>16;do if(l<<16>>16>=0)if(c=d<<16>>16,(c^i)&8)if(r=a+(r+5<<1)|0,(i&7)>>>0>(c&7)>>>0){e[r>>1]=m;break}else{e[r>>1]=l,e[u>>1]=m;break}else if(r=a+(r+5<<1)|0,(i|0)>(c|0)){e[r>>1]=l,e[u>>1]=m;break}else{e[r>>1]=m;break}else e[u>>1]=m;while(!1);w=w+1<<16>>16}while(w<<16>>16<10);for(k=C+2|0,w=C+4|0,d=C+6|0,c=C+8|0,u=C+10|0,r=C+12|0,l=C+14|0,i=C+16|0,b=C+18|0,E=40,R=t+(0-(e[M>>1]|0)<<1)|0,S=t+(0-(e[M+2>>1]|0)<<1)|0,I=t+(0-(e[M+4>>1]|0)<<1)|0,N=t+(0-(e[M+6>>1]|0)<<1)|0,A=t+(0-(e[M+8>>1]|0)<<1)|0,F=t+(0-(e[M+10>>1]|0)<<1)|0,T=t+(0-(e[M+12>>1]|0)<<1)|0,x=t+(0-(e[M+14>>1]|0)<<1)|0,o=t+(0-(e[M+16>>1]|0)<<1)|0,h=t+(0-(e[M+18>>1]|0)<<1)|0,m=n;q=(y(e[C>>1]|0,e[R>>1]|0)|0)>>7,U=(y(e[k>>1]|0,e[S>>1]|0)|0)>>7,V=(y(e[w>>1]|0,e[I>>1]|0)|0)>>7,K=(y(e[d>>1]|0,e[N>>1]|0)|0)>>7,G=(y(e[c>>1]|0,e[A>>1]|0)|0)>>7,j=(y(e[u>>1]|0,e[F>>1]|0)|0)>>7,M=(y(e[r>>1]|0,e[T>>1]|0)|0)>>7,t=(y(e[l>>1]|0,e[x>>1]|0)|0)>>>7,n=(y(e[i>>1]|0,e[o>>1]|0)|0)>>>7,e[m>>1]=(q+128+U+V+K+G+j+M+t+n+((y(e[b>>1]|0,e[h>>1]|0)|0)>>>7)|0)>>>8,E=E+-1<<16>>16,E<<16>>16;)R=R+2|0,S=S+2|0,I=I+2|0,N=N+2|0,A=A+2|0,F=F+2|0,T=T+2|0,x=x+2|0,o=o+2|0,h=h+2|0,m=m+2|0;r=0;do l=a+(r<<1)|0,i=e[l>>1]|0,(r|0)<5?i=(D[f+((i&7)<<1)>>1]|i&8)&65535:i=e[f+((i&7)<<1)>>1]|0,e[l>>1]=i,r=r+1|0;while((r|0)!=10);O=z}function _e(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0;if(r1=O,O=O+3456|0,V=r1+3448|0,G=r1+3360|0,z=r1+3368|0,w=r1+3280|0,K=r1+3200|0,j=r1,q=(o&65535)<<17,W=t<<16>>16,U=t<<16>>16<40,U){o=q>>16,t=W;do c=(y(e[r+(t-W<<1)>>1]|0,o)|0)>>15,(c|0)>32767&&(s[l>>2]=1,c=32767),C=r+(t<<1)|0,e[C>>1]=n1(e[C>>1]|0,c&65535,l)|0,t=t+1|0;while((t&65535)<<16>>16!=40)}W2(r,i,z,1,l),p5(z,K,w,8),N2(r,K,j,l),C=G+2|0,e[G>>1]=0,e[C>>1]=1,i=1,c=0,m=1,w=0,d=-1;do{x=e[2830+(w<<1)>>1]|0,M=x<<16>>16,T=0;do{for(A=e[2834+(T<<1)>>1]|0,F=A<<16>>16,N=i,S=M,R=m,I=x,E=d;;){for(u=e[z+(S<<1)>>1]|0,k=e[j+(S*80|0)+(S<<1)>>1]|0,t=F,m=1,b=A,i=A,d=-1;o=n1(u,e[z+(t<<1)>>1]|0,l)|0,o=o<<16>>16,o=(y(o,o)|0)>>>15,h=(e[j+(S*80|0)+(t<<1)>>1]<<15)+32768+((e[j+(t*80|0)+(t<<1)>>1]|0)+k<<14)|0,((y(o<<16>>16,m<<16>>16)|0)-(y(h>>16,d<<16>>16)|0)<<1|0)>0&&(m=h>>>16&65535,i=b,d=o&65535),h=t+5|0,b=h&65535,!(b<<16>>16>=40);)t=h<<16>>16;if(((y(d<<16>>16,R<<16>>16)|0)-(y(m<<16>>16,E<<16>>16)|0)<<1|0)>0?(e[G>>1]=I,e[C>>1]=i,c=I):(i=N,m=R,d=E),h=S+5|0,I=h&65535,I<<16>>16>=40)break;N=i,S=h<<16>>16,R=m,E=d}T=T+1|0}while((T|0)!=4);w=w+1|0}while((w|0)!=2);k=i,b=c,o=n,t=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(t|0));for(m=b,t=0,h=0,o=0;;){switch(c=m<<16>>16,u=e[K+(c<<1)>>1]|0,i=(c*6554|0)>>>15,m=i<<16,w=m>>15,d=c-(w+(i<<3)<<16>>17)|0,d<<16>>16|0){case 0:{w=m>>10,i=1;break}case 1:{(t&65535)<<16>>16?(w=i<<22>>16|16,i=1):i=0;break}case 2:{w=i<<22>>16|32,i=1;break}case 3:{w=i<<17>>16|1,i=0;break}case 4:{w=i<<22>>16|48,i=1;break}default:w=i,i=d&65535}if(w=w&65535,d=n+(c<<1)|0,u<<16>>16>0?(e[d>>1]=8191,e[V+(t<<1)>>1]=32767,c=i<<16>>16,i<<16>>16<0?(c=0-c<<16,(c|0)<983040?c=1>>>(c>>16)&65535:c=0):(j=1<<c,c=(j<<16>>16>>c|0)==1?j&65535:32767),o=n1(o,c,l)|0):(e[d>>1]=-8192,e[V+(t<<1)>>1]=-32768),c=n1(h,w,l)|0,t=t+1|0,(t|0)==2){h=c;break}m=e[G+(t<<1)>>1]|0,h=c}e[f>>1]=o,w=V+2|0,m=e[V>>1]|0,i=0,d=r+(0-(b<<16>>16)<<1)|0,c=r+(0-(k<<16>>16)<<1)|0;do o=y(e[d>>1]|0,m)|0,d=d+2|0,(o|0)!=1073741824&&(i1=o<<1,!((o|0)>0&(i1|0)<0))?u=i1:(s[l>>2]=1,u=2147483647),t=y(e[w>>1]|0,e[c>>1]|0)|0,c=c+2|0,(t|0)!=1073741824?(o=(t<<1)+u|0,(t^u|0)>0&(o^u|0)<0&&(s[l>>2]=1,o=(u>>>31)+2147483647|0)):(s[l>>2]=1,o=2147483647),e[a+(i<<1)>>1]=S1(o,l)|0,i=i+1|0;while((i|0)!=40);if(!U)return O=r1,h|0;t=q>>16,o=W;do u=(y(e[n+(o-W<<1)>>1]|0,t)|0)>>15,(u|0)>32767&&(s[l>>2]=1,u=32767),a=n+(o<<1)|0,e[a>>1]=n1(e[a>>1]|0,u&65535,l)|0,o=o+1|0;while((o&65535)<<16>>16!=40);return O=r1,h|0}function Re(i,r,t,o,n,a,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0;if(N=O,O=O+3456|0,k=N+3360|0,b=N+3368|0,E=N+3280|0,R=N+3200|0,S=N,I=n<<16>>16,w=I<<1,(w|0)==(I<<17>>16|0)?h=w:(s[c>>2]=1,h=n<<16>>16>0?32767:-32768),I=o<<16>>16,d=o<<16>>16<40,d){n=h<<16>>16,m=I;do o=t+(m<<1)|0,w=(y(e[t+(m-I<<1)>>1]|0,n)|0)>>15,(w|0)>32767&&(s[c>>2]=1,w=32767),e[o>>1]=n1(e[o>>1]|0,w&65535,c)|0,m=m+1|0;while((m&65535)<<16>>16!=40)}if(W2(t,r,b,1,c),p5(b,R,E,8),N2(t,R,S,c),Se(i,b,S,u,k),w=De(i,k,R,a,t,f,l,c)|0,!d)return O=N,w|0;m=h<<16>>16,n=I;do o=a+(n<<1)|0,d=(y(e[a+(n-I<<1)>>1]|0,m)|0)>>15,(d|0)>32767&&(s[c>>2]=1,d=32767),e[o>>1]=n1(e[o>>1]|0,d&65535,c)|0,n=n+1|0;while((n&65535)<<16>>16!=40);return O=N,w|0}function Se(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0;A=n+2|0,e[n>>1]=0,e[A>>1]=1,I=i<<16>>16<<1,a=1,N=0,i=-1;do{S=(N<<3)+I<<16>>16,u=e[o+(S<<1)>>1]|0,S=e[o+((S|1)<<1)>>1]|0,f=u<<16>>16;e:do if(u<<16>>16<40){if(R=S<<16>>16,S<<16>>16<40)E=a;else for(;;)if((i<<16>>16|0)<(0-(a<<16>>16)|0)?(e[n>>1]=u,e[A>>1]=S,l=1,i=-1):l=a,a=f+5|0,u=a&65535,u<<16>>16>=40){a=l;break e}else f=a<<16>>16,a=l;for(;;){for(k=e[t+(f*80|0)+(f<<1)>>1]|0,h=D[r+(f<<1)>>1]|0,w=R,a=1,b=S,l=S,c=-1;m=(D[r+(w<<1)>>1]|0)+h<<16>>16,m=(y(m,m)|0)>>>15,d=(e[t+(f*80|0)+(w<<1)>>1]<<15)+32768+((e[t+(w*80|0)+(w<<1)>>1]|0)+k<<14)|0,((y(m<<16>>16,a<<16>>16)|0)-(y(d>>16,c<<16>>16)|0)<<1|0)>0&&(a=d>>>16&65535,l=b,c=m&65535),d=w+5|0,b=d&65535,!(b<<16>>16>=40);)w=d<<16>>16;if(((y(c<<16>>16,E<<16>>16)|0)-(y(a<<16>>16,i<<16>>16)|0)<<1|0)>0?(e[n>>1]=u,e[A>>1]=l,i=c):a=E,f=f+5|0,u=f&65535,u<<16>>16>=40)break;f=f<<16>>16,E=a}}while(!1);N=N+1|0}while((N|0)!=2)}function De(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;u=o,c=u+80|0;do e[u>>1]=0,u=u+2|0;while((u|0)<(c|0));u=e[r>>1]|0,w=(u*6554|0)>>>15,c=w<<16>>16,m=(748250>>>((u+(y(c,-5)|0)<<16>>16)+((i<<16>>16)*5|0)|0)&1|0)==0,d=(e[t+(u<<1)>>1]|0)>0,h=d?32767:-32768,e[o+(u<<1)>>1]=d?8191:-8192,u=r+2|0,i=e[u>>1]|0,o=o+(i<<1)|0,(e[t+(i<<1)>>1]|0)>0?(e[o>>1]=8191,t=32767,o=(d&1|2)&65535):(e[o>>1]=-8192,t=-32768,o=d&1),w=((i*6554|0)>>>15<<3)+(m?w:c+64|0)&65535,e[f>>1]=o,m=0,d=n+(0-(e[r>>1]|0)<<1)|0,o=n+(0-(e[u>>1]|0)<<1)|0;do u=y(h,e[d>>1]|0)|0,d=d+2|0,(u|0)==1073741824?(s[l>>2]=1,i=2147483647):i=u<<1,c=y(t,e[o>>1]|0)|0,o=o+2|0,(c|0)!=1073741824?(u=(c<<1)+i|0,(c^i|0)>0&(u^i|0)<0&&(s[l>>2]=1,u=(i>>>31)+2147483647|0)):(s[l>>2]=1,u=2147483647),e[a+(m<<1)>>1]=S1(u,l)|0,m=m+1|0;while((m|0)!=40);return w|0}function Ae(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0;if(l1=O,O=O+3440|0,i1=l1+3360|0,W=l1+3280|0,s1=l1+3200|0,r1=l1,h1=(o&65535)<<17,t1=t<<16>>16,c1=t<<16>>16<40,c1){t=h1>>16,u=t1;do o=(y(e[r+(u-t1<<1)>>1]|0,t)|0)>>15,(o|0)>32767&&(s[l>>2]=1,o=32767),q=r+(u<<1)|0,e[q>>1]=n1(e[q>>1]|0,o&65535,l)|0,u=u+1|0;while((u&65535)<<16>>16!=40)}for(W2(r,i,i1,1,l),p5(i1,s1,W,6),N2(r,s1,r1,l),q=1,c=2,d=1,o=0,u=1,i=-1,m=1;;){for(U=2,k=2;;){for(G=0,K=0,V=m,j=k;;){if(K<<16>>16<40)for(x=V<<16>>16,M=V<<16>>16<40,C=j<<16>>16,z=j<<16>>16<40,F=K<<16>>16,T=K;;){if((e[W+(F<<1)>>1]|0)>-1){if(I=e[r1+(F*80|0)+(F<<1)>>1]|0,M)for(N=D[i1+(F<<1)>>1]|0,S=x,h=1,A=V,t=V,k=0,w=-1;E=(D[i1+(S<<1)>>1]|0)+N|0,R=E<<16>>16,R=(y(R,R)|0)>>>15,b=(e[r1+(F*80|0)+(S<<1)>>1]<<15)+32768+((e[r1+(S*80|0)+(S<<1)>>1]|0)+I<<14)|0,((y(R<<16>>16,h<<16>>16)|0)-(y(b>>16,w<<16>>16)|0)<<1|0)>0&&(h=b>>>16&65535,t=A,k=E&65535,w=R&65535),b=S+5|0,A=b&65535,!(A<<16>>16>=40);)S=b<<16>>16;else h=1,t=V,k=0;if(z)for(N=k&65535,A=t<<16>>16,S=(h<<16>>16<<14)+32768|0,R=C,k=1,I=j,w=j,h=-1;;)if(E=(D[i1+(R<<1)>>1]|0)+N<<16>>16,E=(y(E,E)|0)>>>15,b=S+(e[r1+(R*80|0)+(R<<1)>>1]<<12)+((e[r1+(F*80|0)+(R<<1)>>1]|0)+(e[r1+(A*80|0)+(R<<1)>>1]|0)<<13)|0,((y(E<<16>>16,k<<16>>16)|0)-(y(b>>16,h<<16>>16)|0)<<1|0)>0&&(k=b>>>16&65535,w=I,h=E&65535),b=R+5|0,I=b&65535,I<<16>>16>=40){S=k,R=h;break}else R=b<<16>>16;else S=1,w=j,R=-1;k=y(R<<16>>16,u<<16>>16)|0,(k|0)==1073741824?(s[l>>2]=1,b=2147483647):b=k<<1,k=y(S<<16>>16,i<<16>>16)|0,(k|0)==1073741824?(s[l>>2]=1,h=2147483647):h=k<<1,k=b-h|0,((k^b)&(h^b)|0)<0&&(s[l>>2]=1,k=(b>>>31)+2147483647|0),A=(k|0)>0,c=A?w:c,d=A?t:d,o=A?T:o,u=A?S:u,i=A?R:i}if(k=F+5|0,T=k&65535,T<<16>>16>=40)break;F=k<<16>>16}if(G=G+1<<16>>16,G<<16>>16>=3)break;z=j,j=V,V=K,K=z}if(t=U+2|0,k=t&65535,k<<16>>16>=5)break;U=t&65535}if(t=q+2|0,m=t&65535,m<<16>>16<4)q=t&65535;else{k=c,c=d;break}}t=n,u=t+80|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(u|0));switch(R=o<<16>>16,i=e[s1+(R<<1)>>1]|0,o=(R*6554|0)>>>15,t=o<<16,u=R-(((t>>16)*327680|0)>>>16)|0,u<<16>>16|0){case 1:{o=t>>12;break}case 2:{o=t>>8,u=2;break}case 3:{o=o<<20>>16|8,u=1;break}case 4:{o=o<<24>>16|128,u=2;break}default:}switch(t=n+(R<<1)|0,i<<16>>16>0?(e[t>>1]=8191,A=32767,d=65536<<(u<<16>>16)>>>16&65535):(e[t>>1]=-8192,A=-32768,d=0),b=c<<16>>16,c=e[s1+(b<<1)>>1]|0,t=(b*6554|0)>>>15,u=t<<16,i=b-(((u>>16)*327680|0)>>>16)|0,i<<16>>16|0){case 1:{t=u>>12;break}case 2:{t=u>>8,i=2;break}case 3:{t=t<<20>>16|8,i=1;break}case 4:{t=t<<24>>16|128,i=2;break}default:}switch(u=n+(b<<1)|0,c<<16>>16>0?(e[u>>1]=8191,E=32767,d=(65536<<(i<<16>>16)>>>16)+(d&65535)&65535):(e[u>>1]=-8192,E=-32768),m=t+o|0,h=k<<16>>16,c=e[s1+(h<<1)>>1]|0,o=(h*6554|0)>>>15,t=o<<16,u=h-(((t>>16)*327680|0)>>>16)|0,u<<16>>16|0){case 1:{t=t>>12;break}case 2:{t=t>>8,u=2;break}case 3:{t=o<<20>>16|8,u=1;break}case 4:{t=o<<24>>16|128,u=2;break}default:t=o}o=n+(h<<1)|0,c<<16>>16>0?(e[o>>1]=8191,k=32767,o=(65536<<(u<<16>>16)>>>16)+(d&65535)&65535):(e[o>>1]=-8192,k=-32768,o=d),w=m+t|0,e[f>>1]=o,d=0,m=r+(0-R<<1)|0,i=r+(0-b<<1)|0,c=r+(0-h<<1)|0;do o=y(e[m>>1]|0,A)|0,m=m+2|0,(o|0)!=1073741824&&(d1=o<<1,!((o|0)>0&(d1|0)<0))?u=d1:(s[l>>2]=1,u=2147483647),o=y(e[i>>1]|0,E)|0,i=i+2|0,(o|0)!=1073741824?(t=(o<<1)+u|0,(o^u|0)>0&(t^u|0)<0&&(s[l>>2]=1,t=(u>>>31)+2147483647|0)):(s[l>>2]=1,t=2147483647),u=y(e[c>>1]|0,k)|0,c=c+2|0,(u|0)!=1073741824?(o=(u<<1)+t|0,(u^t|0)>0&(o^t|0)<0&&(s[l>>2]=1,o=(t>>>31)+2147483647|0)):(s[l>>2]=1,o=2147483647),e[a+(d<<1)>>1]=S1(o,l)|0,d=d+1|0;while((d|0)!=40);if(o=w&65535,!c1)return O=l1,o|0;u=h1>>16,t=t1;do i=(y(e[n+(t-t1<<1)>>1]|0,u)|0)>>15,(i|0)>32767&&(s[l>>2]=1,i=32767),a=n+(t<<1)|0,e[a>>1]=n1(e[a>>1]|0,i&65535,l)|0,t=t+1|0;while((t&65535)<<16>>16!=40);return O=l1,o|0}function Me(i,r,t,o,n,a,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0;if(L1=O,O=O+3456|0,v1=L1+3448|0,$=L1+3360|0,l1=L1+3368|0,w1=L1+3280|0,H=L1+3200|0,m1=L1,P1=(o&65535)<<17,U1=t<<16>>16,F1=t<<16>>16<40,F1){t=P1>>16,c=U1;do o=(y(e[r+(c-U1<<1)>>1]|0,t)|0)>>15,(o|0)>32767&&(s[u>>2]=1,o=32767),t1=r+(c<<1)|0,e[t1>>1]=n1(e[t1>>1]|0,o&65535,u)|0,c=c+1|0;while((c&65535)<<16>>16!=40)}W2(r,i,l1,1,u),p5(l1,H,w1,4),N2(r,H,m1,u),h1=$+2|0,e[$>>1]=0,d1=$+4|0,e[h1>>1]=1,t1=$+6|0,e[d1>>1]=2,e[t1>>1]=3,h=3,m=2,d=1,o=0,t=1,c=-1,w=3;do{for(i1=0,W=0,r1=w,s1=1,c1=2;;){if(W<<16>>16<40)for(j=s1<<16>>16,G=s1<<16>>16<40,K=c1<<16>>16,V=c1<<16>>16<40,U=r1<<16>>16,q=r1<<16>>16<40,z=W<<16>>16,C=m,x=d,T=t,M=W;;){if((e[w1+(z<<1)>>1]|0)>-1){if(b=e[m1+(z*80|0)+(z<<1)>>1]|0,G)for(k=D[l1+(z<<1)>>1]|0,E=j,A=1,m=s1,d=s1,I=0,N=-1;S=(D[l1+(E<<1)>>1]|0)+k|0,R=S<<16>>16,R=(y(R,R)|0)>>>15,F=(e[m1+(z*80|0)+(E<<1)>>1]<<15)+32768+((e[m1+(E*80|0)+(E<<1)>>1]|0)+b<<14)|0,((y(R<<16>>16,A<<16>>16)|0)-(y(F>>16,N<<16>>16)|0)<<1|0)>0&&(A=F>>>16&65535,d=m,I=S&65535,N=R&65535),F=E+5|0,m=F&65535,!(m<<16>>16>=40);)E=F<<16>>16;else A=1,d=s1,I=0;if(V)for(t=I&65535,i=d<<16>>16,b=(A<<16>>16<<14)+32768|0,E=K,F=1,k=c1,m=c1,N=0,I=-1;S=(D[l1+(E<<1)>>1]|0)+t|0,R=S<<16>>16,R=(y(R,R)|0)>>>15,A=b+(e[m1+(E*80|0)+(E<<1)>>1]<<12)+((e[m1+(z*80|0)+(E<<1)>>1]|0)+(e[m1+(i*80|0)+(E<<1)>>1]|0)<<13)|0,((y(R<<16>>16,F<<16>>16)|0)-(y(A>>16,I<<16>>16)|0)<<1|0)>0&&(F=A>>>16&65535,m=k,N=S&65535,I=R&65535),A=E+5|0,k=A&65535,!(k<<16>>16>=40);)E=A<<16>>16;else F=1,m=c1,N=0;if(q)for(b=N&65535,k=m<<16>>16,i=d<<16>>16,R=(F&65535)<<16|32768,S=U,t=1,E=r1,A=r1,F=-1;I=(D[l1+(S<<1)>>1]|0)+b<<16>>16,I=(y(I,I)|0)>>>15,N=(e[m1+(S*80|0)+(S<<1)>>1]<<12)+R+((e[m1+(i*80|0)+(S<<1)>>1]|0)+(e[m1+(k*80|0)+(S<<1)>>1]|0)+(e[m1+(z*80|0)+(S<<1)>>1]|0)<<13)|0,((y(I<<16>>16,t<<16>>16)|0)-(y(N>>16,F<<16>>16)|0)<<1|0)>0&&(t=N>>>16&65535,A=E,F=I&65535),N=S+5|0,E=N&65535,!(E<<16>>16>=40);)S=N<<16>>16;else t=1,A=r1,F=-1;((y(F<<16>>16,T<<16>>16)|0)-(y(t<<16>>16,c<<16>>16)|0)<<1|0)>0?(e[$>>1]=M,e[h1>>1]=d,e[d1>>1]=m,e[t1>>1]=A,h=A,o=M,c=F):(m=C,d=x,t=T)}else m=C,d=x,t=T;if(S=z+5|0,M=S&65535,M<<16>>16>=40)break;z=S<<16>>16,C=m,x=d,T=t}if(i1=i1+1<<16>>16,i1<<16>>16>=4)break;U=c1,q=r1,c1=s1,s1=W,r1=U,W=q}w=w+1<<16>>16}while(w<<16>>16<5);F=h,A=m,N=d,I=o,o=n,t=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(t|0));for(i=I,t=0,c=0,o=0;;){switch(m=i<<16>>16,w=e[H+(m<<1)>>1]|0,i=m*13108>>16,d=m-((i*327680|0)>>>16)|0,i=e[l+(i<<1)>>1]|0,d<<16>>16|0){case 1:{h=i<<16>>16<<3&65535;break}case 2:{h=i<<16>>16<<6&65535;break}case 3:{h=i<<16>>16<<10&65535;break}case 4:{h=((i&65535)<<10|512)&65535,d=3;break}default:h=i}if(i=n+(m<<1)|0,w<<16>>16>0?(e[i>>1]=8191,i=32767,o=(65536<<(d<<16>>16)>>>16)+(o&65535)&65535):(e[i>>1]=-8192,i=-32768),e[v1+(t<<1)>>1]=i,c=(h&65535)+(c&65535)|0,t=t+1|0,(t|0)==4){S=c;break}i=e[$+(t<<1)>>1]|0}e[f>>1]=o,b=v1+2|0,E=v1+4|0,R=v1+6|0,i=e[v1>>1]|0,k=0,d=r+(0-(I<<16>>16)<<1)|0,m=r+(0-(N<<16>>16)<<1)|0,w=r+(0-(A<<16>>16)<<1)|0,h=r+(0-(F<<16>>16)<<1)|0;do o=y(e[d>>1]|0,i)|0,d=d+2|0,(o|0)!=1073741824&&(q1=o<<1,!((o|0)>0&(q1|0)<0))?c=q1:(s[u>>2]=1,c=2147483647),o=y(e[b>>1]|0,e[m>>1]|0)|0,m=m+2|0,(o|0)!=1073741824?(t=(o<<1)+c|0,(o^c|0)>0&(t^c|0)<0&&(s[u>>2]=1,t=(c>>>31)+2147483647|0)):(s[u>>2]=1,t=2147483647),o=y(e[E>>1]|0,e[w>>1]|0)|0,w=w+2|0,(o|0)!=1073741824?(c=(o<<1)+t|0,(o^t|0)>0&(c^t|0)<0&&(s[u>>2]=1,c=(t>>>31)+2147483647|0)):(s[u>>2]=1,c=2147483647),t=y(e[R>>1]|0,e[h>>1]|0)|0,h=h+2|0,(t|0)!=1073741824?(o=(t<<1)+c|0,(t^c|0)>0&(o^c|0)<0&&(s[u>>2]=1,o=(c>>>31)+2147483647|0)):(s[u>>2]=1,o=2147483647),e[a+(k<<1)>>1]=S1(o,u)|0,k=k+1|0;while((k|0)!=40);if(o=S&65535,((U1<<16)+-2621440|0)>-1|F1^1)return O=L1,o|0;c=P1>>16,t=U1;do i=(y(e[n+(t-U1<<1)>>1]|0,c)|0)>>15,(i|0)>32767&&(s[u>>2]=1,i=32767),a=n+(t<<1)|0,e[a>>1]=n1(e[a>>1]|0,i&65535,u)|0,t=t+1|0;while((t&65535)<<16>>16!=40);return O=L1,o|0}function Y6(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0;q=O,O=O+3440|0,b=q+3424|0,j=q+3408|0,G=q+3240|0,E=q+3224|0,C=q+3328|0,k=q+3248|0,z=q+24|0,U=q+16|0,V=q,Te(t,i,C,2,4,4,f),c4(C,r,k,G,4,j,4,f),N2(t,k,z,f),u4(8,4,4,C,z,j,G,E,f),r=o,i=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(i|0));e[V>>1]=-1,e[U>>1]=-1,x=V+2|0,e[x>>1]=-1,M=U+2|0,e[M>>1]=-1,C=V+4|0,e[C>>1]=-1,z=U+4|0,e[z>>1]=-1,G=V+6|0,e[G>>1]=-1,j=U+6|0,e[j>>1]=-1,w=0;do{d=e[E+(w<<1)>>1]|0,r=d>>>2,u=r&65535,i=d&3,c=(e[k+(d<<1)>>1]|0)>0,d=o+(d<<1)|0,h=c&1^1,e[d>>1]=(D[d>>1]|0)+(c?8191:57345),e[b+(w<<1)>>1]=c?32767:-32768,c=V+(i<<1)|0,d=e[c>>1]|0;do if(d<<16>>16>=0)if(m=U+(i<<1)|0,l=(d<<16>>16|0)<=(r<<16>>16|0),r=V+((i|4)<<1)|0,(h&65535|0)==(D[m>>1]&1|0))if(l){e[r>>1]=u;break}else{e[r>>1]=d,e[c>>1]=u,e[m>>1]=h;break}else if(l){e[r>>1]=d,e[c>>1]=u,e[m>>1]=h;break}else{e[r>>1]=u;break}else e[c>>1]=u,e[U+(i<<1)>>1]=h;while(!1);w=w+1|0}while((w|0)!=8);R=b+2|0,S=b+4|0,I=b+6|0,N=b+8|0,A=b+10|0,F=b+12|0,T=b+14|0,b=e[b>>1]|0,w=0,m=t+(0-(e[E>>1]|0)<<1)|0,d=t+(0-(e[E+2>>1]|0)<<1)|0,c=t+(0-(e[E+4>>1]|0)<<1)|0,u=t+(0-(e[E+6>>1]|0)<<1)|0,r=t+(0-(e[E+8>>1]|0)<<1)|0,i=t+(0-(e[E+10>>1]|0)<<1)|0,l=t+(0-(e[E+12>>1]|0)<<1)|0,t=t+(0-(e[E+14>>1]|0)<<1)|0;do h=y(e[m>>1]|0,b)|0,m=m+2|0,(h|0)!=1073741824&&(K=h<<1,!((h|0)>0&(K|0)<0))?h=K:(s[f>>2]=1,h=2147483647),k=y(e[R>>1]|0,e[d>>1]|0)|0,d=d+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0?(s[f>>2]=1,h=(h>>>31)+2147483647|0):h=o):(s[f>>2]=1,h=2147483647),k=y(e[S>>1]|0,e[c>>1]|0)|0,c=c+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(s[f>>2]=1,o=(h>>>31)+2147483647|0)):(s[f>>2]=1,o=2147483647),k=y(e[I>>1]|0,e[u>>1]|0)|0,u=u+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(s[f>>2]=1,h=(o>>>31)+2147483647|0)):(s[f>>2]=1,h=2147483647),k=y(e[N>>1]|0,e[r>>1]|0)|0,r=r+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(s[f>>2]=1,o=(h>>>31)+2147483647|0)):(s[f>>2]=1,o=2147483647),k=y(e[A>>1]|0,e[i>>1]|0)|0,i=i+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(s[f>>2]=1,h=(o>>>31)+2147483647|0)):(s[f>>2]=1,h=2147483647),k=y(e[F>>1]|0,e[l>>1]|0)|0,l=l+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(s[f>>2]=1,o=(h>>>31)+2147483647|0)):(s[f>>2]=1,o=2147483647),k=y(e[T>>1]|0,e[t>>1]|0)|0,t=t+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(s[f>>2]=1,h=(o>>>31)+2147483647|0)):(s[f>>2]=1,h=2147483647),e[n+(w<<1)>>1]=S1(h,f)|0,w=w+1|0;while((w|0)!=40);if(e[a>>1]=e[U>>1]|0,e[a+2>>1]=e[M>>1]|0,e[a+4>>1]=e[z>>1]|0,e[a+6>>1]=e[j>>1]|0,i=e[V>>1]|0,r=e[V+8>>1]|0,l=e[x>>1]|0,e[a+8>>1]=r<<1&2|i&1|l<<2&4|(((r>>1)*327680|0)+(i>>>1<<16)+(y(l>>1,1638400)|0)|0)>>>13&65528,l=e[C>>1]|0,i=e[V+12>>1]|0,r=e[V+10>>1]|0,e[a+10>>1]=i<<1&2|l&1|r<<2&4|(((i>>1)*327680|0)+(l>>>1<<16)+(y(r>>1,1638400)|0)|0)>>>13&65528,r=e[V+14>>1]|0,l=e[G>>1]|0,i=l<<16>>16>>>1,!(r&2)){n=i,f=r<<16>>16,U=f>>1,U=U*327680|0,n=n<<16,U=n+U|0,U=U<<5,U=U>>16,U=U|12,U=U*2622|0,U=U>>>16,n=l&65535,n=n&1,f=f<<17,f=f&131072,U=U<<18,f=U|f,f=f>>>16,n=f|n,n=n&65535,a=a+12|0,e[a>>1]=n,O=q;return}n=4-(i<<16>>16)|0,f=r<<16>>16,U=f>>1,U=U*327680|0,n=n<<16,U=n+U|0,U=U<<5,U=U>>16,U=U|12,U=U*2622|0,U=U>>>16,n=l&65535,n=n&1,f=f<<17,f=f&131072,U=U<<18,f=U|f,f=f>>>16,n=f|n,n=n&65535,a=a+12|0,e[a>>1]=n,O=q}function G6(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(b=t<<16>>16,a=0-b|0,t=n+(a<<2)|0,n=((b-(o<<16>>16)|0)>>>2)+1&65535,!(n<<16>>16<=0)){if(b=r<<16>>16>>>1&65535,!(b<<16>>16)){for(;s[t>>2]=0,s[t+4>>2]=0,s[t+8>>2]=0,s[t+12>>2]=0,n<<16>>16>1;)t=t+16|0,n=n+-1<<16>>16;return}for(k=i+(a<<1)|0;;){for(c=k+4|0,m=e[c>>1]|0,l=e[k>>1]|0,d=m,u=b,w=i,h=k,k=k+8|0,f=0,a=0,o=0,r=0;R=e[w>>1]|0,E=(y(l<<16>>16,R)|0)+f|0,f=e[h+2>>1]|0,a=(y(f,R)|0)+a|0,l=(y(d<<16>>16,R)|0)+o|0,o=e[h+6>>1]|0,d=(y(o,R)|0)+r|0,r=e[w+2>>1]|0,f=E+(y(r,f)|0)|0,a=a+(y(m<<16>>16,r)|0)|0,c=c+4|0,o=l+(y(r,o)|0)|0,l=e[c>>1]|0,r=d+(y(l<<16>>16,r)|0)|0,u=u+-1<<16>>16,!!(u<<16>>16);)R=m,d=l,m=e[h+8>>1]|0,w=w+4|0,h=h+4|0,l=R;if(s[t>>2]=f<<1,s[t+4>>2]=a<<1,s[t+8>>2]=o<<1,s[t+12>>2]=r<<1,n<<16>>16<=1)break;t=t+16|0,n=n+-1<<16>>16}}}function Pe(i,r,t,o,n,a,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0;N=O,O=O+16|0,S=N+2|0,I=N;do if(n<<16>>16>0){for(k=o<<16>>16,E=0,m=0,o=0,d=0,b=0;;)if(c=e[i+(E<<1)>>1]|0,w=c<<16>>16,m=(y(w,w)|0)+m|0,w=e[r+(E<<1)>>1]|0,o=(y(w,w)|0)+o|0,d=(y(e[t+(E<<1)>>1]|0,w)|0)+d|0,w=y(w,k)|0,(w|0)==1073741824?(s[u>>2]=1,h=2147483647):h=w<<1,w=h<<1,w=(E1(c,S1((w>>1|0)==(h|0)?w:h>>31^2147483647,u)|0,u)|0)<<16>>16,w=y(w,w)|0,(w|0)!=1073741824?(c=(w<<1)+b|0,(w^b|0)>0&(c^b|0)<0&&(s[u>>2]=1,c=(b>>>31)+2147483647|0)):(s[u>>2]=1,c=2147483647),E=E+1|0,(E&65535)<<16>>16==n<<16>>16){b=c;break}else b=c;if(m=m<<1,o=o<<1,d=d<<1,(m|0)>=0){if((m|0)<400){c=b,R=14;break}}else s[u>>2]=1,m=2147483647;h=B1(m)|0,w=h<<16>>16,h<<16>>16>0?(c=m<<w,(c>>w|0)!=(m|0)&&(c=m>>31^2147483647)):(c=0-w<<16,(c|0)<2031616?c=m>>(c>>16):c=0),e[a>>1]=c>>>16,m=o,k=d,c=b,o=15-(h&65535)&65535}else o=0,d=0,c=0,R=14;while(!1);if((R|0)==14&&(e[a>>1]=0,m=o,k=d,o=-15),e[f>>1]=o,(m|0)<0&&(s[u>>2]=1,m=2147483647),w=B1(m)|0,d=w<<16>>16,w<<16>>16>0?(o=m<<d,(o>>d|0)!=(m|0)&&(o=m>>31^2147483647)):(o=0-d<<16,(o|0)<2031616?o=m>>(o>>16):o=0),e[a+2>>1]=o>>>16,e[f+2>>1]=15-(w&65535),m=B1(k)|0,d=m<<16>>16,m<<16>>16>0?(o=k<<d,(o>>d|0)!=(k|0)&&(o=k>>31^2147483647)):(o=0-d<<16,(o|0)<2031616?o=k>>(o>>16):o=0),e[a+4>>1]=o>>>16,e[f+4>>1]=2-(m&65535),m=B1(c)|0,o=m<<16>>16,m<<16>>16>0?(d=c<<o,(d>>o|0)!=(c|0)&&(d=c>>31^2147483647)):(o=0-o<<16,(o|0)<2031616?d=c>>(o>>16):d=0),o=d>>>16&65535,c=15-(m&65535)&65535,e[a+6>>1]=o,e[f+6>>1]=c,(d>>16|0)<=0){u=0,e[l>>1]=u,O=N;return}if(d=e[a>>1]|0,!(d<<16>>16)){u=0,e[l>>1]=u,O=N;return}o=a0(W1(d,1,u)|0,o)|0,o=(o&65535)<<16,d=((E1(c,e[f>>1]|0,u)|0)&65535)+3|0,c=d&65535,d=d<<16>>16,c<<16>>16>0?c=c<<16>>16<31?o>>d:0:(f=0-d<<16>>16,c=o<<f,c=(c>>f|0)==(o|0)?c:o>>31^2147483647),f2(c,S,I,u),I=ti((D[S>>1]|0)+65509&65535,e[I>>1]|0,u)|0,S=I<<13,u=S1((S>>13|0)==(I|0)?S:I>>31^2147483647,u)|0,e[l>>1]=u,O=N}function U5(i,r,t,o,n,a,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0;switch(N=O,O=O+80|0,R=N,e[f>>1]=e[a>>1]|0,e[l>>1]=e[a+2>>1]|0,h=e[a+4>>1]|0,h<<16>>16==-32768?h=32767:h=0-(h&65535)&65535,e[f+2>>1]=h,e[l+2>>1]=(D[a+6>>1]|0)+1,i|0){case 0:case 5:{E=0,w=0,m=0,b=0;break}default:E=0,w=1,m=1,b=1}for(;;)if(k=(e[n+(E<<1)>>1]|0)>>>3,e[R+(E<<1)>>1]=k,k=k<<16>>16,h=y(k,k)|0,(h|0)!=1073741824?(a=(h<<1)+w|0,(h^w|0)>0&(a^w|0)<0?(s[d>>2]=1,w=(w>>>31)+2147483647|0):w=a):(s[d>>2]=1,w=2147483647),h=y(e[r+(E<<1)>>1]|0,k)|0,(h|0)!=1073741824?(a=(h<<1)+m|0,(h^m|0)>0&(a^m|0)<0?(s[d>>2]=1,m=(m>>>31)+2147483647|0):m=a):(s[d>>2]=1,m=2147483647),h=y(e[o+(E<<1)>>1]|0,k)|0,(h|0)!=1073741824?(a=(h<<1)+b|0,(h^b|0)>0&(a^b|0)<0&&(s[d>>2]=1,a=(b>>>31)+2147483647|0)):(s[d>>2]=1,a=2147483647),E=E+1|0,(E|0)==40){o=a,k=m;break}else b=a;switch(m=B1(w)|0,a=m<<16>>16,m<<16>>16>0?(h=w<<a,(h>>a|0)!=(w|0)&&(h=w>>31^2147483647)):(h=0-a<<16,(h|0)<2031616?h=w>>(h>>16):h=0),n=f+4|0,e[n>>1]=h>>>16,r=l+4|0,e[r>>1]=-3-(m&65535),w=B1(k)|0,a=w<<16>>16,w<<16>>16>0?(h=k<<a,(h>>a|0)!=(k|0)&&(h=k>>31^2147483647)):(h=0-a<<16,(h|0)<2031616?h=k>>(h>>16):h=0),a=h>>>16,e[f+6>>1]=(a|0)==32768?32767:0-a&65535,e[l+6>>1]=7-(w&65535),w=B1(o)|0,a=w<<16>>16,w<<16>>16>0?(h=o<<a,(h>>a|0)!=(o|0)&&(h=o>>31^2147483647)):(h=0-a<<16,(h|0)<2031616?h=o>>(h>>16):h=0),e[f+8>>1]=h>>>16,e[l+8>>1]=7-(w&65535),i|0){case 0:case 5:{h=0,m=0;break}default:{O=N;return}}do m=(y(e[R+(h<<1)>>1]|0,e[t+(h<<1)>>1]|0)|0)+m|0,h=h+1|0;while((h|0)!=40);if(a=m<<1,h=B1(a)|0,w=h<<16>>16,h<<16>>16>0?(m=a<<w,(m>>w|0)==(a|0)?(S=m,I=40):(S=a>>31^2147483647,I=40)):(m=0-w<<16,(m|0)<2031616&&(S=a>>(m>>16),I=40)),(I|0)==40&&(S>>16|0)>=1){d=W1(S>>>16&65535,1,d)|0,e[u>>1]=a0(d,e[n>>1]|0)|0,e[c>>1]=65528-(h&65535)-(D[r>>1]|0),O=N;return}e[u>>1]=0,e[c>>1]=0,O=N}function K6(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;a=0,n=0;do f=e[i+(a<<1)>>1]|0,n=(y(f,f)|0)+n|0,a=a+1|0;while((a|0)!=40);(n|0)<0&&(s[o>>2]=1,n=2147483647),o=B1(n)|0,i=o<<16>>16,o<<16>>16>0?(a=n<<i,(a>>i|0)==(n|0)?n=a:n=n>>31^2147483647):(i=0-i<<16,(i|0)<2031616?n=n>>(i>>16):n=0),e[t>>1]=n>>>16,e[r>>1]=16-(o&65535)}function Ne(i,r,t,o,n,a,f,l,u,c,d,m,w){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0;var h=0,k=0,b=0,E=0;if(k=O,O=O+16|0,h=k,c>>>0<2){f=Re(d,i,r,t,o,f,l,h,s[m+76>>2]|0,w)|0,w=s[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,s[u>>2]=w+4,e[w+2>>1]=f,O=k;return}switch(c|0){case 2:{f=_e(i,r,t,o,f,l,h,w)|0,w=s[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,s[u>>2]=w+4,e[w+2>>1]=f,O=k;return}case 3:{f=Ae(i,r,t,o,f,l,h,w)|0,w=s[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,s[u>>2]=w+4,e[w+2>>1]=f,O=k;return}default:{if((c&-2|0)==4){f=Me(i,r,t,o,f,l,h,s[m+36>>2]|0,w)|0,w=s[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,s[u>>2]=w+4,e[w+2>>1]=f,O=k;return}if((c|0)!=6){if(d=n<<16>>16,d=(d<<17>>17|0)==(d|0)?d<<1:d>>>15^32767,n=t<<16>>16<40,!n){X6(i,a,r,f,l,s[u>>2]|0,s[m+36>>2]|0,w),s[u>>2]=(s[u>>2]|0)+20,O=k;return}h=t<<16>>16,c=d<<16>>16,o=h;do E=(y(e[r+(o-h<<1)>>1]|0,c)|0)>>>15&65535,b=r+(o<<1)|0,e[b>>1]=n1(e[b>>1]|0,E,w)|0,o=o+1|0;while((o&65535)<<16>>16!=40);if(X6(i,a,r,f,l,s[u>>2]|0,s[m+36>>2]|0,w),s[u>>2]=(s[u>>2]|0)+20,!n){O=k;return}n=t<<16>>16,c=d<<16>>16,h=n;do o=(y(e[f+(h-n<<1)>>1]|0,c)|0)>>15,(o|0)>32767&&(s[w>>2]=1,o=32767),E=f+(h<<1)|0,e[E>>1]=n1(e[E>>1]|0,o&65535,w)|0,h=h+1|0;while((h&65535)<<16>>16!=40);O=k;return}if(m=o<<16>>16,m=(m<<17>>17|0)==(m|0)?m<<1:m>>>15^32767,d=t<<16>>16<40,!d){Y6(i,a,r,f,l,s[u>>2]|0,w),s[u>>2]=(s[u>>2]|0)+14,O=k;return}h=t<<16>>16,c=m<<16>>16,o=h;do n=(y(e[r+(o-h<<1)>>1]|0,c)|0)>>15,(n|0)>32767&&(s[w>>2]=1,n=32767),E=r+(o<<1)|0,e[E>>1]=n1(e[E>>1]|0,n&65535,w)|0,o=o+1|0;while((o&65535)<<16>>16!=40);if(Y6(i,a,r,f,l,s[u>>2]|0,w),s[u>>2]=(s[u>>2]|0)+14,!d){O=k;return}n=t<<16>>16,c=m<<16>>16,h=n;do o=(y(e[f+(h-n<<1)>>1]|0,c)|0)>>15,(o|0)>32767&&(s[w>>2]=1,o=32767),E=f+(h<<1)|0,e[E>>1]=n1(e[E>>1]|0,o&65535,w)|0,h=h+1|0;while((h&65535)<<16>>16!=40);O=k;return}}}function Ie(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(4)|0,!r)?(i=-1,i|0):(fi(r)|0)<<16>>16?(a4(r),K1(r),i=-1,i|0):(n4(s[r>>2]|0)|0,s[i>>2]=r,i=0,i|0)}function Oe(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(a4(r),K1(s[i>>2]|0),s[i>>2]=0))}function Z6(i){return i=i|0,i?(n4(s[i>>2]|0)|0,i=0,i|0):(i=-1,i|0)}function Le(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b,E,R,S,I){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,I=I|0;var N=0,A=0,F=0,T=0;for(A=O,O=O+16|0,T=A+2|0,F=A,e[w>>1]=li(s[i>>2]|0,t,n,f,u,a,40,o,h,F,T,I)|0,i=e[T>>1]|0,o=s[E>>2]|0,s[E>>2]=o+2,e[o>>1]=i,G2(f,e[w>>1]|0,e[h>>1]|0,40,e[F>>1]|0,I),m5(f,a,m,40),e[k>>1]=ii(t,u,m,b,40,I)|0,e[R>>1]=32767,c<<16>>16&&(N=e[k>>1]|0,N<<16>>16>15565)?N=Ii(r,N,I)|0:N=0,t>>>0<2?(T=e[k>>1]|0,e[k>>1]=T<<16>>16>13926?13926:T,N<<16>>16&&(e[R>>1]=15565)):(N<<16>>16&&(e[R>>1]=15565,e[k>>1]=15565),(t|0)==7&&(F=l4(7,e[R>>1]|0,k,0,0,S,I)|0,T=s[E>>2]|0,s[E>>2]=T+2,e[T>>1]=F)),w=e[k>>1]|0,N=0;F=y(e[m>>1]|0,w)|0,e[d>>1]=(D[u>>1]|0)-(F>>>14),F=(y(e[f>>1]|0,w)|0)>>>14,T=l+(N<<1)|0,e[T>>1]=(D[T>>1]|0)-F,N=N+1|0,(N|0)!=40;)f=f+2|0,u=u+2|0,d=d+2|0,m=m+2|0;O=A}function Ce(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,a=0;return a=O,O=O+16|0,n=a,!i||(s[i>>2]=0,t=s0(2532)|0,s[n>>2]=t,!t)?(i=-1,O=a,i|0):(k4(t+2392|0),s[t+2188>>2]=0,s[(s[n>>2]|0)+2192>>2]=0,s[(s[n>>2]|0)+2196>>2]=0,s[(s[n>>2]|0)+2200>>2]=0,s[(s[n>>2]|0)+2204>>2]=0,s[(s[n>>2]|0)+2208>>2]=0,s[(s[n>>2]|0)+2212>>2]=0,s[(s[n>>2]|0)+2220>>2]=0,o=s[n>>2]|0,s[o+2216>>2]=r,s[o+2528>>2]=0,t=o,!((Ie(o+2196|0)|0)<<16>>16)&&!((ji(o+2192|0)|0)<<16>>16)&&!((Ze(o+2200|0)|0)<<16>>16)&&!((ui(o+2204|0)|0)<<16>>16)&&!((Ai(o+2208|0)|0)<<16>>16)&&!((Li(o+2212|0)|0)<<16>>16)&&!((xe(o+2220|0,s[o+2432>>2]|0)|0)<<16>>16)&&!((oi(o+2188|0)|0)<<16>>16)?(Q6(o)|0,s[i>>2]=t,i=0,O=a,i|0):(z5(n),i=-1,O=a,i|0))}function z5(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(ni(r+2188|0),qi((s[i>>2]|0)+2192|0),Qe((s[i>>2]|0)+2200|0),Oe((s[i>>2]|0)+2196|0),di((s[i>>2]|0)+2204|0),Pi((s[i>>2]|0)+2208|0),Fi((s[i>>2]|0)+2212|0),Ue((s[i>>2]|0)+2220|0),K1(s[i>>2]|0),s[i>>2]=0))}function Q6(i){i=i|0;var r=0,t=0,o=0,n=0;if(!i)return n=-1,n|0;s[i+652>>2]=i+320,s[i+640>>2]=i+240,s[i+644>>2]=i+160,s[i+648>>2]=i+80,s[i+1264>>2]=i+942,s[i+1912>>2]=i+1590,o=i+1938|0,s[i+2020>>2]=o,s[i+2384>>2]=i+2304,r=i+2028|0,s[i+2024>>2]=i+2108,s[i+2528>>2]=0,o2(i|0,0,640)|0,o2(i+1282|0,0,308)|0,o2(i+656|0,0,286)|0,t=i+2224|0,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));o=r,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));r=i+1268|0,o=t,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));return e[r>>1]=40,e[i+1270>>1]=40,e[i+1272>>1]=40,e[i+1274>>1]=40,e[i+1276>>1]=40,ai(s[i+2188>>2]|0)|0,g4(s[i+2192>>2]|0)|0,Z6(s[i+2196>>2]|0)|0,$e(s[i+2200>>2]|0)|0,ci(s[i+2204>>2]|0)|0,Mi(s[i+2208>>2]|0)|0,Ci(s[i+2212>>2]|0)|0,Be(s[i+2220>>2]|0,s[i+2432>>2]|0)|0,e[i+2388>>1]=0,n=0,n|0}function Fe(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0,Z1=0,z1=0,f0=0,j1=0,Q1=0,l0=0,t0=0,H1=0,m0=0,j0=0,Y0=0,v0=0,d0=0,u0=0,O1=0;if(O1=O,O=O+1184|0,l1=O1,d=O1+1096|0,m=O1+1008|0,u=O1+904|0,m0=O1+928|0,j0=O1+824|0,H=O1+744|0,v0=O1+664|0,d0=O1+584|0,F1=O1+328|0,l0=O1+504|0,t0=O1+424|0,Y0=O1+344|0,u0=O1+248|0,v1=O1+168|0,z1=O1+88|0,j1=O1+68|0,Q1=O1+48|0,f0=O1+28|0,H1=O1+24|0,L1=O1+22|0,q1=O1+20|0,$=O1+16|0,w1=O1+12|0,m1=O1+10|0,U1=O1+8|0,P1=O1+6|0,Z1=O1+4|0,s[l1>>2]=o,t1=i+2528|0,f=i+652|0,_0(s[f>>2]|0,t|0,320)|0,s[n>>2]=r,c=i+2216|0,s[c>>2]|0?(o=xi(s[i+2212>>2]|0,s[f>>2]|0,t1)|0,d1=i+2220|0,t=d1,o=qe(s[d1>>2]|0,o,n,t1)|0):(t=i+2220|0,o=0),d1=i+2392|0,si(s[i+2188>>2]|0,r,s[i+644>>2]|0,s[i+648>>2]|0,d,d1,t1),l=i+2192|0,Vi(s[l>>2]|0,r,s[n>>2]|0,d,m,u,l1,t1),je(s[t>>2]|0,u,s[f>>2]|0,t1),(s[n>>2]|0)==8){ze(s[t>>2]|0,o,s[(s[l>>2]|0)+40>>2]|0,(s[i+2200>>2]|0)+32|0,l1,t1),o2(i+1282|0,0,308)|0,f=i+2244|0,h=f+20|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=i+2284|0,h=f+20|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=s[i+2020>>2]|0,h=f+80|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=i+2028|0,h=f+80|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));g4(s[l>>2]|0)|0,f=s[l>>2]|0,t=u,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=(s[l>>2]|0)+20|0,t=u,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));Z6(s[i+2196>>2]|0)|0,e[i+2388>>1]=0,h1=0}else h1=Ni(s[i+2208>>2]|0,s[l>>2]|0,t1)|0;if(r1=i+640|0,l=i+2264|0,f=i+1264|0,t=i+2204|0,o=i+2212|0,s1=i+1268|0,c1=i+1278|0,X5(r,2842,2862,2882,d,0,s[r1>>2]|0,l,s[f>>2]|0,t1),r>>>0>1?(V5(s[t>>2]|0,s[o>>2]|0,r,s[f>>2]|0,$,s1,c1,0,s[c>>2]|0,t1),X5(r,2842,2862,2882,d,80,s[r1>>2]|0,l,s[f>>2]|0,t1),V5(s[t>>2]|0,s[o>>2]|0,r,(s[f>>2]|0)+160|0,$+2|0,s1,c1,1,s[c>>2]|0,t1)):(X5(r,2842,2862,2882,d,80,s[r1>>2]|0,l,s[f>>2]|0,t1),V5(s[t>>2]|0,s[o>>2]|0,r,s[f>>2]|0,$,s1,c1,1,s[c>>2]|0,t1),e[$+2>>1]=e[$>>1]|0),s[c>>2]|0&&Ti(s[o>>2]|0,$,t1),(s[n>>2]|0)==8)return d0=i+656|0,u0=i+976|0,_0(d0|0,u0|0,286)|0,u0=i+320|0,_0(i|0,u0|0,320)|0,O=O1,0;for(F=i+2224|0,T=i+2244|0,x=i+2284|0,M=i+2388|0,C=i+2020|0,z=i+1916|0,j=i+1912|0,G=i+2024|0,K=i+2384|0,V=i+2196|0,U=i+2208|0,q=i+2464|0,i1=i+2200|0,W=i+2224|0,I=i+2244|0,N=i+1270|0,A=i+1280|0,S=0,c=0,u=0,b=0,E=0,l=0,R=-1;;){w=R,R=R+1<<16>>16,b=1-(b<<16>>16)|0,o=b&65535,k=(b&65535|0)!=0,t=s[n>>2]|0,f=(t|0)==0;do if(k)if(f){f=j1,t=F,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=Q1,t=T,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=f0,t=x,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));e[H1>>1]=e[M>>1]|0,r=(s[r1>>2]|0)+(S<<1)|0,f=20;break}else{r=(s[r1>>2]|0)+(S<<1)|0,f=19;break}else r=(s[r1>>2]|0)+(S<<1)|0,f?f=20:f=19;while(!1);if((f|0)==19)Y5(t,2842,2862,2882,d,m,r,x,I,s[C>>2]|0,z,(s[j>>2]|0)+(S<<1)|0,s[G>>2]|0,m0,l0,s[K>>2]|0);else if((f|0)==20&&(Y5(0,2842,2862,2882,d,m,r,x,Q1,s[C>>2]|0,z,(s[j>>2]|0)+(S<<1)|0,s[G>>2]|0,m0,l0,s[K>>2]|0),k)){f=z1,t=s[G>>2]|0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0))}f=t0,t=l0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));switch(Le(s[V>>2]|0,s[U>>2]|0,s[n>>2]|0,E,$,s[G>>2]|0,(s[j>>2]|0)+(S<<1)|0,t0,m0,h1,j0,v0,w1,m1,U1,F1,l1,Z1,s[q>>2]|0,t1),w<<16>>16){case-1:{(e[c1>>1]|0)>0&&(e[N>>1]=e[w1>>1]|0);break}case 2:{(e[A>>1]|0)>0&&(e[s1>>1]=e[w1>>1]|0);break}default:}Ne(j0,s[G>>2]|0,e[w1>>1]|0,e[M>>1]|0,e[U1>>1]|0,t0,H,d0,l1,s[n>>2]|0,R,d1,t1),Je(s[i1>>2]|0,s[n>>2]|0,l0,(s[j>>2]|0)+(S<<1)|0,H,m0,j0,v0,d0,F1,o,e[Z1>>1]|0,L1,q1,U1,P1,l1,d1,t1),Oi(s[U>>2]|0,e[U1>>1]|0,t1),r=s[n>>2]|0;do if(r)k5(s[r1>>2]|0,r,E,e[U1>>1]|0,e[P1>>1]|0,m,a,m0,H,v0,d0,W,x,I,s[j>>2]|0,M,t1);else if(k){f=Y0,t=m0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=u0,t=d0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=v1,t=H,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));u=e[w1>>1]|0,c=e[m1>>1]|0,k5(s[r1>>2]|0,0,E,e[U1>>1]|0,e[P1>>1]|0,m,a,m0,H,v0,d0,j1,x,Q1,s[j>>2]|0,M,t1),e[M>>1]=e[H1>>1]|0,l=E;break}else{f=x,t=f0,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));k=l<<16>>16,G2((s[j>>2]|0)+(k<<1)|0,u,c,40,1,t1),m5((s[j>>2]|0)+(k<<1)|0,z1,v0,40),k5(s[r1>>2]|0,s[n>>2]|0,l,e[L1>>1]|0,e[q1>>1]|0,m+-22|0,a,Y0,v1,v0,u0,W,x,I,s[j>>2]|0,H1,t1),Y5(s[n>>2]|0,2842,2862,2882,d,m,(s[r1>>2]|0)+(S<<1)|0,x,I,s[C>>2]|0,z,(s[j>>2]|0)+(S<<1)|0,s[G>>2]|0,m0,l0,s[K>>2]|0),G2((s[j>>2]|0)+(S<<1)|0,e[w1>>1]|0,e[m1>>1]|0,40,1,t1),m5((s[j>>2]|0)+(S<<1)|0,s[G>>2]|0,v0,40),k5(s[r1>>2]|0,s[n>>2]|0,E,e[U1>>1]|0,e[P1>>1]|0,m,a,m0,H,v0,d0,W,x,I,s[j>>2]|0,M,t1);break}while(!1);if(r=S+40|0,E=r&65535,E<<16>>16>=160)break;S=r<<16>>16,d=d+22|0,m=m+22|0}return _0(i+1282|0,i+1602|0,308)|0,d0=i+656|0,u0=i+976|0,_0(d0|0,u0|0,286)|0,u0=i+320|0,_0(i|0,u0|0,320)|0,O=O1,0}function m5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;if(h=o<<16>>16,o<<16>>16>1)w=1;else return;for(;;){if(n=e[i>>1]|0,l=r+(w+-1<<1)|0,o=y(e[r+(w<<1)>>1]|0,n)|0,c=e[l>>1]|0,n=y(c<<16>>16,n)|0,f=(w+131071|0)>>>1,u=f&65535,a=e[i+2>>1]|0,!(u<<16>>16))r=l,f=c;else{d=(f<<1)+131070&131070,m=w-d|0,f=i;do b=(y(c<<16>>16,a)|0)+o|0,k=f,f=f+4|0,o=e[l+-2>>1]|0,a=(y(o,a)|0)+n|0,n=e[f>>1]|0,l=l+-4|0,o=b+(y(n,o)|0)|0,c=e[l>>1]|0,n=a+(y(c<<16>>16,n)|0)|0,u=u+-1<<16>>16,a=e[k+6>>1]|0;while(u<<16>>16);f=r+(m+-3<<1)|0,i=i+(d+2<<1)|0,r=f,f=e[f>>1]|0}if(o=(y(f<<16>>16,a)|0)+o|0,e[t>>1]=n>>>12,e[t+2>>1]=o>>>12,o=(w<<16)+131072>>16,(o|0)<(h|0))t=t+4|0,i=i+(1-w<<1)|0,w=o;else break}}function N2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0;for(T=O,O=O+80|0,F=T,f=20,a=i,n=1;A=e[a>>1]|0,A=(y(A,A)|0)+n|0,n=e[a+2>>1]|0,n=A+(y(n,n)|0)|0,f=f+-1<<16>>16,f<<16>>16;)a=a+4|0;if(n=n<<1,(n|0)<0)for(a=20,n=i,o=F;;)if(e[o>>1]=(e[n>>1]|0)>>>1,e[o+2>>1]=(e[n+2>>1]|0)>>>1,a=a+-1<<16>>16,a<<16>>16)n=n+4|0,o=o+4|0;else{A=F;break}else for(n=t2(n>>1,o)|0,(n|0)<16777215?n=((n>>9)*32440|0)>>>15<<16>>16:n=32440,f=20,a=i,o=F;;)if(e[o>>1]=((y(e[a>>1]|0,n)|0)+32|0)>>>6,e[o+2>>1]=((y(e[a+2>>1]|0,n)|0)+32|0)>>>6,f=f+-1<<16>>16,f<<16>>16)a=a+4|0,o=o+4|0;else{A=F;break}for(f=20,a=A,o=t+3198|0,n=0;N=e[a>>1]|0,N=(y(N,N)|0)+n|0,e[o>>1]=(N+16384|0)>>>15,I=e[a+2>>1]|0,n=(y(I,I)|0)+N|0,e[o+-82>>1]=(n+16384|0)>>>15,f=f+-1<<16>>16,f<<16>>16;)a=a+4|0,o=o+-164|0;for(N=r+78|0,I=1;;){if(n=39-I|0,i=t+3120+(n<<1)|0,o=t+(n*80|0)+78|0,n=r+(n<<1)|0,u=F+(I<<1)|0,a=65575-I|0,l=a&65535,f=e[A>>1]|0,!(l<<16>>16))l=N,a=0;else{for(b=a+65535&65535,R=b*41|0,S=(y(I,-40)|0)-R|0,E=0-I|0,R=E-R|0,E=E-b|0,k=I+b|0,h=e[u>>1]|0,m=A,w=N,c=t+((38-I|0)*80|0)+78|0,a=0,d=0;u=u+2|0,a=(y(h<<16>>16,f)|0)+a|0,m=m+2|0,h=e[u>>1]|0,d=(y(h<<16>>16,f)|0)+d|0,M=n,n=n+-2|0,f=e[n>>1]|0,x=e[w>>1]<<1,M=(y((y(x,e[M>>1]|0)|0)>>16,(a<<1)+32768>>16)|0)>>>15&65535,e[o>>1]=M,e[i>>1]=M,f=(y((y(x,f)|0)>>16,(d<<1)+32768>>16)|0)>>>15&65535,e[i+-2>>1]=f,e[c>>1]=f,l=l+-1<<16>>16,f=e[m>>1]|0,l<<16>>16;)w=w+-2|0,i=i+-82|0,o=o+-82|0,c=c+-82|0;u=F+(k+1<<1)|0,l=r+(38-b<<1)|0,n=r+(E+38<<1)|0,i=t+3040+(R+38<<1)|0,o=t+3040+(S+38<<1)|0}if(M=(y(e[u>>1]|0,f)|0)+a|0,M=(y((M<<1)+32768>>16,(y(e[l>>1]<<1,e[n>>1]|0)|0)>>16)|0)>>>15&65535,e[i>>1]=M,e[o>>1]=M,o=(I<<16)+131072|0,(o|0)<2621440)I=o>>16;else break}O=T}function Te(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0;if(k=O,O=O+160|0,h=k,n<<16>>16>0){m=a&65535,w=0,l=5;do{if((w|0)<40)for(d=w,c=w&65535,a=0;;){if(c<<16>>16<40){c=c<<16>>16,u=0;do u=(y(e[i+(c-d<<1)>>1]|0,e[r+(c<<1)>>1]|0)|0)+u|0,c=c+1|0;while((c&65535)<<16>>16!=40)}else u=0;if(u=u<<1,s[h+(d<<2)>>2]=u,u=v2(u)|0,a=(u|0)>(a|0)?u:a,u=d+m|0,c=u&65535,c<<16>>16>=40)break;d=u<<16>>16}else a=0;l=(a>>1)+l|0,w=w+1|0}while((w&65535)<<16>>16!=n<<16>>16)}else l=5;if(o=((B1(l)|0)&65535)-(o&65535)|0,a=o<<16>>16,u=0-a<<16,l=(u|0)<2031616,u=u>>16,(o&65535)<<16>>16>0)if(l){l=0;do o=s[h+(l<<2)>>2]|0,r=o<<a,e[t+(l<<1)>>1]=S1((r>>a|0)==(o|0)?r:o>>31^2147483647,f)|0,l=l+1|0;while((l|0)!=40);O=k;return}else{l=0;do o=s[h+(l<<2)>>2]|0,r=o<<a,e[t+(l<<1)>>1]=S1((r>>a|0)==(o|0)?r:o>>31^2147483647,f)|0,l=l+1|0;while((l|0)!=40);O=k;return}else if(l){l=0;do e[t+(l<<1)>>1]=S1(s[h+(l<<2)>>2]>>u,f)|0,l=l+1|0;while((l|0)!=40);O=k;return}else{l=0;do e[t+(l<<1)>>1]=S1(0,f)|0,l=l+1|0;while((l|0)!=40);O=k;return}}function W2(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0;F=O,O=O+160|0,A=F,S=i+2|0,I=e[i>>1]|0,N=0,n=5;do{for(R=N,l=0;;){if(d=r+(R<<1)|0,E=40-R|0,a=(E+131071|0)>>>1&65535,u=r+(R+1<<1)|0,f=y(e[d>>1]<<1,I)|0,!(a<<16>>16))a=S;else{for(b=131111-R+131070&131070,k=R+b|0,h=S,w=i,m=d;c=m+4|0,d=w+4|0,f=(y(e[u>>1]<<1,e[h>>1]|0)|0)+f|0,a=a+-1<<16>>16,f=(y(e[c>>1]<<1,e[d>>1]|0)|0)+f|0,a<<16>>16;)u=m+6|0,h=w+6|0,w=d,m=c;u=r+(k+3<<1)|0,a=i+(b+3<<1)|0}if(E&1||(f=(y(e[u>>1]<<1,e[a>>1]|0)|0)+f|0),s[A+(R<<2)>>2]=f,f=(f|0)<0?0-f|0:f,l=(f|0)>(l|0)?f:l,f=R+5|0,(f&65535)<<16>>16<40)R=f<<16>>16;else break}n=(l>>1)+n|0,N=N+1|0}while((N|0)!=5);if(o=((B1(n)|0)&65535)-(o&65535)|0,f=o<<16>>16,n=0-f<<16,l=n>>16,(o&65535)<<16>>16>0){for(a=20,n=A;A=s[n>>2]|0,o=A<<f,e[t>>1]=(((o>>f|0)==(A|0)?o:A>>31^2147483647)+32768|0)>>>16,A=s[n+4>>2]|0,o=A<<f,e[t+2>>1]=(((o>>f|0)==(A|0)?o:A>>31^2147483647)+32768|0)>>>16,a=a+-1<<16>>16,a<<16>>16;)t=t+4|0,n=n+8|0;O=F;return}if((n|0)<2031616){for(a=20,n=A;e[t>>1]=((s[n>>2]>>l)+32768|0)>>>16,e[t+2>>1]=((s[n+4>>2]>>l)+32768|0)>>>16,a=a+-1<<16>>16,a<<16>>16;)t=t+4|0,n=n+8|0;O=F;return}else{e[t>>1]=0,A=t+4|0,e[t+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,e[A+2>>1]=0,O=F;return}}function $6(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;return f=(a0(16383,r)|0)<<16>>16,r=y(f,r<<16>>16)|0,(r|0)==1073741824?(s[o>>2]=1,n=2147483647):n=r<<1,a=(y(f,t<<16>>16)|0)>>15,r=n+(a<<1)|0,(n^a|0)>0&(r^n|0)<0&&(s[o>>2]=1,r=(n>>>31)+2147483647|0),n=2147483647-r|0,t=n>>16,r=y(t,f)|0,(r|0)==1073741824?(s[o>>2]=1,a=2147483647):a=r<<1,f=(y((n>>>1)-(t<<15)<<16>>16,f)|0)>>15,r=a+(f<<1)|0,(a^f|0)>0&(r^a|0)<0&&(s[o>>2]=1,r=(a>>>31)+2147483647|0),a=r>>16,f=i>>16,t=y(a,f)|0,t=(t|0)==1073741824?2147483647:t<<1,n=(y((r>>>1)-(a<<15)<<16>>16,f)|0)>>15,o=(n<<1)+t|0,o=(n^t|0)>0&(o^t|0)<0?(t>>>31)+2147483647|0:o,f=(y(a,(i>>>1)-(f<<15)<<16>>16)|0)>>15,i=o+(f<<1)|0,i=(o^f|0)>0&(i^o|0)<0?(o>>>31)+2147483647|0:i,o=i<<2,((o>>2|0)==(i|0)?o:i>>31^2147483647)|0}function xe(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,a=0;if(!i||(s[i>>2]=0,t=s0(192)|0,!t))return a=-1,a|0;o=t+176|0,e[o>>1]=0,e[o+2>>1]=0,e[o+4>>1]=0,e[o+6>>1]=0,e[o+8>>1]=0,e[o+10>>1]=0,o=t,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+20|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+40|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+60|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+80|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+100|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+120|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+140|0,n=r,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=t+160|0,a=o+20|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(a|0));return e[t+188>>1]=7,e[t+190>>1]=32767,s[i>>2]=t,a=0,a|0}function Be(i,r){i=i|0,r=r|0;var t=0,o=0,n=0;if(!i)return n=-1,n|0;t=i+176|0,e[t>>1]=0,e[t+2>>1]=0,e[t+4>>1]=0,e[t+6>>1]=0,e[t+8>>1]=0,e[t+10>>1]=0,t=i,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+20|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+40|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+60|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+80|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+100|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+120|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+140|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+160|0,n=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(n|0));return e[i+188>>1]=7,e[i+190>>1]=32767,n=1,n|0}function Ue(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function ze(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0;if(M=O,O=O+112|0,F=M+80|0,T=M+60|0,x=M+40|0,A=M,!(r<<16>>16)&&(f=i+178|0,(e[f>>1]|0)!=0)){x=i+180|0,a=i+182|0,t=f,x=e[x>>1]|0,o=s[n>>2]|0,T=o+2|0,e[o>>1]=x,a=e[a>>1]|0,x=o+4|0,e[T>>1]=a,T=i+184|0,T=e[T>>1]|0,a=o+6|0,e[x>>1]=T,x=i+186|0,x=e[x>>1]|0,i=o+8|0,e[a>>1]=x,t=e[t>>1]|0,o=o+10|0,s[n>>2]=o,e[i>>1]=t,O=M;return}b=A+36|0,E=A+32|0,R=A+28|0,S=A+24|0,I=A+20|0,N=A+16|0,w=A+12|0,h=A+8|0,k=A+4|0,r=A,f=r+40|0;do s[r>>2]=0,r=r+4|0;while((r|0)<(f|0));for(m=7,r=0;;){for(d=e[i+160+(m<<1)>>1]|0,f=d<<16>>16,d<<16>>16<0?f=~((f^-4)>>2):f=f>>>2,r=n1(r,f&65535,a)|0,u=m*10|0,d=9;c=A+(d<<2)|0,l=s[c>>2]|0,C=e[i+(d+u<<1)>>1]|0,f=C+l|0,(C^l|0)>-1&(f^l|0)<0&&(s[a>>2]=1,f=(l>>>31)+2147483647|0),s[c>>2]=f,(d|0)>0;)d=d+-1|0;if((m|0)>0)m=m+-1|0;else break}f=r<<16>>16,r<<16>>16<0?f=~((f^-2)>>1):f=f>>>1,e[T+18>>1]=(s[b>>2]|0)>>>3,e[T+16>>1]=(s[E>>2]|0)>>>3,e[T+14>>1]=(s[R>>2]|0)>>>3,e[T+12>>1]=(s[S>>2]|0)>>>3,e[T+10>>1]=(s[I>>2]|0)>>>3,e[T+8>>1]=(s[N>>2]|0)>>>3,e[T+6>>1]=(s[w>>2]|0)>>>3,e[T+4>>1]=(s[h>>2]|0)>>>3,e[T+2>>1]=(s[k>>2]|0)>>>3,e[T>>1]=(s[A>>2]|0)>>>3,r=i+178|0,f=(((f<<16)+167772160|0)>>>16)+128|0,e[r>>1]=f,f=f<<16,(f|0)<0?f=~((f>>16^-256)>>8):f=f>>24,e[r>>1]=f,(f|0)<=63?(f|0)<0&&(e[r>>1]=0,f=0):(e[r>>1]=63,f=63),C=E1(f<<8&65535,11560,a)|0,C=C<<16>>16>0?0:C<<16>>16<-14436?-14436:C,e[o>>1]=C,e[o+2>>1]=C,e[o+4>>1]=C,e[o+6>>1]=C,C=((C<<16>>16)*5443|0)>>>15&65535,e[o+8>>1]=C,e[o+10>>1]=C,e[o+12>>1]=C,e[o+14>>1]=C,Y2(T,F,10,a),A0(F,205,10,a),b0(F,T,10,a),o=i+182|0,C=i+180|0,y4(t,8,T,x,o,C,a),a=o,o=r,C=e[C>>1]|0,t=s[n>>2]|0,x=t+2|0,e[t>>1]=C,a=e[a>>1]|0,C=t+4|0,e[x>>1]=a,x=i+184|0,x=e[x>>1]|0,a=t+6|0,e[C>>1]=x,i=i+186|0,i=e[i>>1]|0,C=t+8|0,e[a>>1]=i,i=e[o>>1]|0,t=t+10|0,s[n>>2]=t,e[C>>1]=i,O=M}function je(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0;c=O,O=O+16|0,f=c+2|0,u=c,l=i+176|0,a=(D[l>>1]|0)+1|0,a=(a&65535|0)==8?0:a&65535,e[l>>1]=a,a=i+((a<<16>>16)*10<<1)|0,n=a+20|0;do e[a>>1]=e[r>>1]|0,a=a+2|0,r=r+2|0;while((a|0)<(n|0));for(r=0,n=160;;){if(a=e[t>>1]|0,r=(y(a<<1,a)|0)+r|0,(r|0)<0){r=2147483647;break}if(n=n+-1<<16>>16,n<<16>>16)t=t+2|0;else break}f2(r,f,u,o),r=e[f>>1]|0,f=r<<16>>16,t=f<<10,(t|0)!=(f<<26>>16|0)&&(s[o>>2]=1,t=r<<16>>16>0?32767:-32768),e[i+160+(e[l>>1]<<1)>>1]=(((e[u>>1]|0)>>>5)+t<<16)+-558432256>>17,O=c}function qe(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;a=i+190|0,f=n1(e[a>>1]|0,1,o)|0,e[a>>1]=f,n=i+188|0;do if(r<<16>>16)e[n>>1]=7,i=0;else{if(i=e[n>>1]|0,!(i<<16>>16)){e[a>>1]=0,s[t>>2]=8,i=1;break}a=(i&65535)+65535&65535,e[n>>1]=a,(n1(f,a,o)|0)<<16>>16<30&&(s[t>>2]=8),i=0}while(!1);return i|0}function Ve(i,r,t,o,n,a,f,l){return i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,a<<16>>16?f<<16>>16?(o=o<<16>>16,o=(((t&65535)-o<<16)+-327680|0)>0?o+5&65535:t,n=n<<16>>16,t=i<<16>>16,o=(((n-(o&65535)<<16)+-262144|0)>0?n+65532&65535:o)<<16>>16,n=o*196608|0,i=n+-393216>>16,a=((r&65535)<<16)+(t*196608|0)>>16,i-a&32768?(n+196608>>16|0)>(a|0)?(r=a+3-i|0,r=r&65535,r|0):(r=t+11-o|0,r=r&65535,r|0):(r=t+5-o|0,r=r&65535,r|0)):(l=(i&65535)-(o&65535)<<16,r=(r<<16>>16)+2+(l>>15)+(l>>16)|0,r=r&65535,r|0):(a=i<<16>>16,((a<<16)+-5570560|0)<65536?(r=(a*3|0)+-58+(r<<16>>16)|0,r=r&65535,r|0):(r=a+112|0,r=r&65535,r|0))}function He(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,n=i<<16>>16;do if(o<<16>>16)n=((((n-(t&65535)|0)*393216|0)+196608|0)>>>16)+(r&65535)|0;else if(i<<16>>16<95){n=((n*393216|0)+-6881280>>16)+(r<<16>>16)|0;break}else{n=n+368|0;break}while(!1);return n&65535|0}function We(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0;if(n=s[o+96>>2]|0,i>>>0<8){if(u=(s[o+100>>2]|0)+(i<<2)|0,l=s[u>>2]|0,_[t>>0]=e[r+(e[l>>1]<<1)>>1]<<4|i|e[r+(e[l+2>>1]<<1)>>1]<<5|e[r+(e[l+4>>1]<<1)>>1]<<6|e[r+(e[l+6>>1]<<1)>>1]<<7,l=n+(i<<1)|0,o=e[l>>1]|0,(o+-7|0)>4)for(n=4,f=4,i=1;c=e[r+(e[(s[u>>2]|0)+(n<<1)>>1]<<1)>>1]|0,o=t+(i<<16>>16)|0,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+((f|1)<<16>>16<<1)>>1]<<1)>>1]<<1|c&65535,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+((f|2)<<16>>16<<1)>>1]<<1)>>1]<<2|c,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+((f|3)<<16>>16<<1)>>1]<<1)>>1]<<3|c,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+(f+4<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<4|c,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+(f+5<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<5|c,_[o>>0]=c,c=D[r+(e[(s[u>>2]|0)+(f+6<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<6|c,_[o>>0]=c,a=f+8<<16>>16,i=i+1<<16>>16,_[o>>0]=D[r+(e[(s[u>>2]|0)+(f+7<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<7|c,n=a<<16>>16,o=e[l>>1]|0,!((n|0)>=(o+-7|0));)f=a;else a=4,i=1;if(l=o+4&7,!l)return;for(n=t+(i<<16>>16)|0,_[n>>0]=0,o=0,f=0,i=0;f=(D[r+(e[(s[u>>2]|0)+(a<<16>>16<<1)>>1]<<1)>>1]&255)<<o|f&255,_[n>>0]=f,i=i+1<<16>>16,o=i<<16>>16,!((o|0)>=(l|0));)a=a+1<<16>>16;return}if((i|0)==15){_[t>>0]=15;return}if(_[t>>0]=e[r>>1]<<4|i|e[r+2>>1]<<5|e[r+4>>1]<<6|e[r+6>>1]<<7,o=n+(i<<1)|0,i=e[o>>1]|0,n=((i&65535)<<16)+262144>>16,u=n&-8,f=(u+524281|0)>>>3&65535,f<<16>>16>0){for(n=((n&-8)+524281|0)>>>3,l=((n<<3)+524280&524280)+12|0,a=1,i=r+8|0;_[t+(a<<16>>16)>>0]=D[i+2>>1]<<1|D[i>>1]|D[i+4>>1]<<2|D[i+6>>1]<<3|D[i+8>>1]<<4|D[i+10>>1]<<5|D[i+12>>1]<<6|D[i+14>>1]<<7,f<<16>>16>1;)f=f+-1<<16>>16,a=a+1<<16>>16,i=i+16|0;i=e[o>>1]|0,a=(n<<16)+65536>>16}else l=4,a=1;if(i=(0-u|4)+(i&65535)<<16,f=i>>16,!!f){if(a=t+a|0,_[a>>0]=0,(i|0)>0)i=0,n=0,o=0;else return;do n=n&255|e[r+(l+i<<1)>>1]<<i,_[a>>0]=n,o=o+1<<16>>16,i=o<<16>>16;while((i|0)<(f|0))}}function Xe(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0;if(d=s[o+100>>2]|0,c=s[o+96>>2]|0,_[t>>0]=i&15,c=c+(i<<1)|0,n=e[c>>1]|0,i>>>0>=8){if(l=((n&65535)<<16)+-458752|0,(l|0)>0){for(u=1,f=r;r=f+16|0,o=u+1<<16>>16,_[t+(u<<16>>16)>>0]=D[f+14>>1]|D[f+12>>1]<<1|((D[f+2>>1]<<6|D[f>>1]<<7|D[f+4>>1]<<5|D[f+6>>1]<<4)&240|D[f+8>>1]<<3|D[f+10>>1]<<2)&252,l=l+-524288&-65536,!((l|0)<=0);)u=o,f=r;n=e[c>>1]|0}else o=1;if(u=n&7,n=t+(o<<16>>16)|0,_[n>>0]=0,u)a=0,f=0,l=0,o=r;else return;for(;f=f&255|e[o>>1]<<7-a,_[n>>0]=f,l=l+1<<16>>16,a=l<<16>>16,!((a|0)>=(u|0));)o=o+2|0;return}if(f=n<<16>>16,n<<16>>16>7)for(n=d+(i<<2)|0,o=0,u=0,a=1;m=D[r+(e[(s[n>>2]|0)+(o<<1)>>1]<<1)>>1]<<7,f=t+(a<<16>>16)|0,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|1)<<16>>16<<1)>>1]<<1)>>1]<<6|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|2)<<16>>16<<1)>>1]<<1)>>1]<<5|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|3)<<16>>16<<1)>>1]<<1)>>1]<<4|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|4)<<16>>16<<1)>>1]<<1)>>1]<<3|m&240,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|5)<<16>>16<<1)>>1]<<1)>>1]<<2|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|6)<<16>>16<<1)>>1]<<1)>>1]<<1|m,_[f>>0]=m,l=u+8<<16>>16,a=a+1<<16>>16,_[f>>0]=m&254|D[r+(e[(s[n>>2]|0)+((u|7)<<16>>16<<1)>>1]<<1)>>1],o=l<<16>>16,f=e[c>>1]|0,!((o|0)>=(f+-7|0));)u=l;else l=0,a=1;if(c=f&7,u=t+(a<<16>>16)|0,_[u>>0]=0,!!c)for(a=d+(i<<2)|0,n=0,o=0,f=0;o=(D[r+(e[(s[a>>2]|0)+(l<<16>>16<<1)>>1]<<1)>>1]&255)<<7-n|o&255,_[u>>0]=o,f=f+1<<16>>16,n=f<<16>>16,!((n|0)>=(c|0));)l=l+1<<16>>16}function Ye(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0;if(d=s[o+100>>2]|0,c=s[o+96>>2]|0,_[t>>0]=i<<3,c=c+(i<<1)|0,n=e[c>>1]|0,i>>>0>=8){if(l=((n&65535)<<16)+-458752|0,(l|0)>0){for(u=1,f=r;r=f+16|0,o=u+1<<16>>16,_[t+(u<<16>>16)>>0]=D[f+14>>1]|D[f+12>>1]<<1|((D[f+2>>1]<<6|D[f>>1]<<7|D[f+4>>1]<<5|D[f+6>>1]<<4)&240|D[f+8>>1]<<3|D[f+10>>1]<<2)&252,l=l+-524288&-65536,!((l|0)<=0);)u=o,f=r;n=e[c>>1]|0}else o=1;if(u=n&7,n=t+(o<<16>>16)|0,_[n>>0]=0,u)a=0,f=0,l=0,o=r;else return;for(;f=f&255|e[o>>1]<<7-a,_[n>>0]=f,l=l+1<<16>>16,a=l<<16>>16,!((a|0)>=(u|0));)o=o+2|0;return}if(f=n<<16>>16,n<<16>>16>7)for(n=d+(i<<2)|0,o=0,u=0,a=1;m=D[r+(e[(s[n>>2]|0)+(o<<1)>>1]<<1)>>1]<<7,f=t+(a<<16>>16)|0,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|1)<<16>>16<<1)>>1]<<1)>>1]<<6|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|2)<<16>>16<<1)>>1]<<1)>>1]<<5|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|3)<<16>>16<<1)>>1]<<1)>>1]<<4|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|4)<<16>>16<<1)>>1]<<1)>>1]<<3|m&240,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|5)<<16>>16<<1)>>1]<<1)>>1]<<2|m,_[f>>0]=m,m=D[r+(e[(s[n>>2]|0)+((u|6)<<16>>16<<1)>>1]<<1)>>1]<<1|m,_[f>>0]=m,l=u+8<<16>>16,a=a+1<<16>>16,_[f>>0]=m&254|D[r+(e[(s[n>>2]|0)+((u|7)<<16>>16<<1)>>1]<<1)>>1],o=l<<16>>16,f=e[c>>1]|0,!((o|0)>=(f+-7|0));)u=l;else l=0,a=1;if(c=f&7,u=t+(a<<16>>16)|0,_[u>>0]=0,!!c)for(a=d+(i<<2)|0,n=0,o=0,f=0;o=(D[r+(e[(s[a>>2]|0)+(l<<16>>16<<1)>>1]<<1)>>1]&255)<<7-n|o&255,_[u>>0]=o,f=f+1<<16>>16,n=f<<16>>16,!((n|0)>=(c|0));)l=l+1<<16>>16}function Ge(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(16)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,e[r+12>>1]=0,e[r+14>>1]=0,s[i>>2]=r,i=0,i|0)}function J6(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,e[i+12>>1]=0,e[i+14>>1]=0,i=0,i|0):(i=-1,i|0)}function e4(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function Ke(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0;l=r<<16>>16<2722?0:r<<16>>16<5444?1:2,f=K2(t,1,n)|0,c=i+4|0,t<<16>>16>200&&f<<16>>16>(e[c>>1]|0)?(e[i>>1]=8,a=1,u=5):(f=e[i>>1]|0,f<<16>>16&&(a=f+-1<<16>>16,e[i>>1]=a,a=a<<16>>16!=0,u=5)),(u|0)==5&&(l&65535)<2&a&&(l=(l&65535)+1&65535),u=i+6|0,e[u>>1]=r,a=E2(u,5)|0,l<<16>>16!=0|a<<16>>16>5443?a=0:a<<16>>16<0?a=16384:(a=a<<16>>16,a=(((a<<18>>18|0)==(a|0)?a<<2:a>>>15^32767)<<16>>16)*24660>>15,(a|0)>32767&&(s[n>>2]=1,a=32767),a=16384-a&65535),f=i+2|0,e[f>>1]|0||(a=W1(a,1,n)|0),e[o>>1]=a,e[f>>1]=a,e[c>>1]=t,o=i+12|0,e[i+14>>1]=e[o>>1]|0,t=i+10|0,e[o>>1]=e[t>>1]|0,i=i+8|0,e[t>>1]=e[i>>1]|0,e[i>>1]=e[u>>1]|0}function Ze(i){i=i|0;var r=0,t=0,o=0,n=0,a=0,f=0;if(!i||(s[i>>2]=0,r=s0(68)|0,o=r,!r))return i=-1,i|0;if(s[r+28>>2]=0,n=r+64|0,s[n>>2]=0,a=r+32|0,!((s2(a)|0)<<16>>16)&&(f=r+48|0,(s2(f)|0)<<16>>16==0)&&!((Ge(n)|0)<<16>>16)){t=r+32|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(t|0));return s2(a)|0,s2(f)|0,J6(s[n>>2]|0)|0,s[i>>2]=o,i=0,i|0}return e4(n),K1(r),i=-1,i|0}function Qe(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(e4(r+64|0),K1(s[i>>2]|0),s[i>>2]=0))}function $e(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;r=i+32|0,t=i,o=t+32|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return s2(r)|0,s2(i+48|0)|0,J6(s[i+64>>2]|0)|0,o=0,o|0}function Je(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b,E,R,S){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0;var I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0;if(G=O,O=O+48|0,N=G+34|0,F=G+32|0,x=G+30|0,T=G+28|0,A=G+18|0,I=G+8|0,M=G+6|0,C=G+4|0,z=G+2|0,j=G,r){d=i+32|0,I2(d,r,n,N,F,M,C,S);do if((r|0)!=7)if(U5(r,a,f,l,u,c,A,I,j,z,S),(r|0)==5){Ei(s[i+64>>2]|0,t,o,n,A,I,e[M>>1]|0,e[C>>1]|0,e[N>>1]|0,e[F>>1]|0,40,e[j>>1]|0,e[z>>1]|0,m,k,b,x,T,E,R,S);break}else{i=yi(r,e[N>>1]|0,e[F>>1]|0,A,I,m,k,b,x,T,R,S)|0,a=s[E>>2]|0,s[E>>2]=a+2,e[a>>1]=i;break}else e[b>>1]=ei(f,u,S)|0,i=gi(7,e[N>>1]|0,e[F>>1]|0,b,x,T,s[R+68>>2]|0,S)|0,a=s[E>>2]|0,s[E>>2]=a+2,e[a>>1]=i;while(!1);W0(d,e[x>>1]|0,e[T>>1]|0),O=G;return}if(!(d<<16>>16)){I2(i+48|0,0,n,N,F,M,C,S),U5(0,a,f,l,u,c,A,I,j,z,S),K6(a,M,C,S),a=vi(i+32|0,e[i>>1]|0,e[i+2>>1]|0,i+8|0,i+18|0,e[i+4>>1]|0,e[i+6>>1]|0,n,e[N>>1]|0,e[F>>1]|0,I,A,e[M>>1]|0,e[C>>1]|0,m,w,h,k,b,S)|0,e[s[i+28>>2]>>1]=a,O=G;return}d=s[E>>2]|0,s[E>>2]=d+2,s[i+28>>2]=d,d=i+48|0,t=i+32|0,w=t,w=D[w>>1]|D[w+2>>1]<<16,t=t+4|0,t=D[t>>1]|D[t+2>>1]<<16,E=d,h=E,e[h>>1]=w,e[h+2>>1]=w>>>16,E=E+4|0,e[E>>1]=t,e[E+2>>1]=t>>>16,E=i+40|0,t=E,t=D[t>>1]|D[t+2>>1]<<16,E=E+4|0,E=D[E>>1]|D[E+2>>1]<<16,h=i+56|0,w=h,e[w>>1]=t,e[w+2>>1]=t>>>16,h=h+4|0,e[h>>1]=E,e[h+2>>1]=E>>>16,h=i+2|0,I2(d,0,n,i,h,M,C,S),U5(0,a,f,l,u,c,i+18|0,i+8|0,j,z,S),l=(D[z>>1]|0)+1|0,E=e[j>>1]|0,w=l<<16>>16,(l&65535)<<16>>16<0?(R=0-w<<16,(R|0)<983040?R=E<<16>>16>>(R>>16)&65535:R=0):(E=E<<16>>16,R=E<<w,(R<<16>>16>>w|0)==(E|0)?R=R&65535:R=(E>>>15^32767)&65535),e[b>>1]=R,K6(a,i+4|0,i+6|0,S),bi(d,e[i>>1]|0,e[h>>1]|0,e[z>>1]|0,e[j>>1]|0,S),O=G}function ei(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;for(n=10,t=i,o=r,i=0;i=(y(e[o>>1]>>1,e[t>>1]|0)|0)+i|0,i=i+(y(e[o+2>>1]>>1,e[t+2>>1]|0)|0)|0,i=i+(y(e[o+4>>1]>>1,e[t+4>>1]|0)|0)|0,i=i+(y(e[o+6>>1]>>1,e[t+6>>1]|0)|0)|0,n=n+-1<<16>>16,n<<16>>16;)t=t+8|0,o=o+8|0;if(t=i<<1,n=B1(t|1)|0,a=n<<16>>16,t=(n<<16>>16<17?t>>17-a:t<<a+-17)&65535,t<<16>>16<1)return r=0,r|0;for(n=20,o=r,i=0;r=e[o>>1]>>1,r=((y(r,r)|0)>>>2)+i|0,i=e[o+2>>1]>>1,i=r+((y(i,i)|0)>>>2)|0,n=n+-1<<16>>16,n<<16>>16;)o=o+4|0;return i=i<<3,n=B1(i)|0,r=n<<16>>16,t=a0(t,(n<<16>>16<16?i>>16-r:i<<r+-16)&65535)|0,r=(a<<16)+327680-(r<<16)|0,i=r>>16,(r|0)>65536?i=t<<16>>16>>i+-1:i=t<<16>>16<<1-i,r=i&65535,r|0}function ii(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;if(s[a>>2]=0,d=n<<16>>16,u=d>>>2&65535,w=u<<16>>16==0,w)l=0;else for(c=u,f=t,l=0;h=e[f>>1]|0,h=(y(h,h)|0)+l|0,l=e[f+2>>1]|0,l=h+(y(l,l)|0)|0,h=e[f+4>>1]|0,h=l+(y(h,h)|0)|0,l=e[f+6>>1]|0,l=h+(y(l,l)|0)|0,c=c+-1<<16>>16,c<<16>>16;)f=f+8|0;if((l>>>31^1)&(l|0)<1073741824)d=l<<1|1,h=B1(d)|0,m=h,h=S1(d<<(h<<16>>16),a)|0;else{if(l=d>>>1&65535,!(l<<16>>16))l=1;else{for(f=l,c=t,l=0;h=e[c>>1]>>2,h=(y(h,h)|0)+l|0,l=e[c+2>>1]>>2,l=h+(y(l,l)|0)|0,f=f+-1<<16>>16,f<<16>>16;)c=c+4|0;l=l<<1|1}h=(B1(l)|0)<<16>>16,m=h+65532&65535,h=S1(l<<h,a)|0}s[a>>2]=0;do if(!(n<<16>>16))l=1,k=14;else{for(d=n,c=r,l=t,n=0;b=y(e[l>>1]|0,e[c>>1]|0)|0,f=b+n|0,!((b^n|0)>0&(f^n|0)<0);)if(d=d+-1<<16>>16,d<<16>>16)c=c+2|0,l=l+2|0,n=f;else{k=13;break}if((k|0)==13){l=f<<1|1,k=14;break}if(s[a>>2]=1,w)l=1;else{for(l=r,f=0;f=(y(e[t>>1]>>2,e[l>>1]|0)|0)+f|0,f=f+(y(e[t+2>>1]>>2,e[l+2>>1]|0)|0)|0,f=f+(y(e[t+4>>1]>>2,e[l+4>>1]|0)|0)|0,f=f+(y(e[t+6>>1]>>2,e[l+6>>1]|0)|0)|0,u=u+-1<<16>>16,u<<16>>16;)l=l+8|0,t=t+8|0;l=f<<1|1}t=(B1(l)|0)<<16>>16,f=t+65532&65535,t=S1(l<<t,a)|0}while(!1);return(k|0)==14&&(t=B1(l)|0,f=t,t=S1(l<<(t<<16>>16),a)|0),e[o>>1]=h,l=m<<16>>16,e[o+2>>1]=15-l,e[o+4>>1]=t,f=f<<16>>16,e[o+6>>1]=15-f,t<<16>>16<4?(b=0,b|0):(f=W1(a0(t<<16>>16>>>1&65535,h)|0,f-l&65535,a)|0,f=f<<16>>16>19661?19661:f,(i|0)!=7?(b=f,b|0):(b=f&65532,b|0))}function i4(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0;if(u=(o&65535)+65535&65535,u<<16>>16>n<<16>>16)for(m=o+-1<<16>>16<<16>>16,o=-2147483648;;)if(c=s[i+(0-m<<2)>>2]|0,l=c<<1,c=(l>>1|0)==(c|0)?l:c>>31^2147483647,l=s[i+(~m<<2)>>2]|0,d=c-l|0,((d^c)&(c^l)|0)<0&&(s[f>>2]=1,d=(c>>>31)+2147483647|0),c=s[i+(1-m<<2)>>2]|0,l=d-c|0,((l^d)&(c^d)|0)<0&&(s[f>>2]=1,l=(d>>>31)+2147483647|0),d=v2(l)|0,o=(d|0)<(o|0)?o:d,u=u+-1<<16>>16,u<<16>>16<=n<<16>>16){n=o;break}else m=m+-1|0;else n=-2147483648;if(i=t<<16>>16>0,i){for(o=0,l=r,u=0;d=e[l>>1]|0,d=y(d,d)|0,(d|0)!=1073741824?(c=(d<<1)+u|0,(d^u|0)>0&(c^u|0)<0?(s[f>>2]=1,u=(u>>>31)+2147483647|0):u=c):(s[f>>2]=1,u=2147483647),o=o+1<<16>>16,!(o<<16>>16>=t<<16>>16);)l=l+2|0;if(i)for(i=0,m=r,o=r+-2|0,l=0;d=y(e[o>>1]|0,e[m>>1]|0)|0,(d|0)!=1073741824?(c=(d<<1)+l|0,(d^l|0)>0&(c^l|0)<0?(s[f>>2]=1,l=(l>>>31)+2147483647|0):l=c):(s[f>>2]=1,l=2147483647),i=i+1<<16>>16,!(i<<16>>16>=t<<16>>16);)m=m+2|0,o=o+2|0;else l=0}else u=0,l=0;if(o=u<<1,o=(o>>1|0)==(u|0)?o:u>>31^2147483647,t=l<<1,t=(t>>1|0)==(l|0)?t:l>>31^2147483647,u=o-t|0,((u^o)&(t^o)|0)<0&&(s[f>>2]=1,u=(o>>>31)+2147483647|0),i=v2(u)|0,m=((B1(n)|0)&65535)+65535|0,u=m<<16>>16,(m&65535)<<16>>16>0?(o=n<<u,(o>>u|0)!=(n|0)&&(o=n>>31^2147483647)):(u=0-u<<16,(u|0)<2031616?o=n>>(u>>16):o=0),d=B1(i)|0,l=d<<16>>16,d<<16>>16>0?(u=i<<l,(u>>l|0)==(i|0)||(u=i>>31^2147483647),w=33):(u=0-l<<16,(u|0)<2031616?(u=i>>(u>>16),w=33):c=0),(w|0)==33&&(u>>>0>65535?c=a0(o>>>16&65535,u>>>16&65535)|0:c=0),u=d&65535,w=(m&65535)-u|0,o=w&65535,!(w&32768))return f=W1(c,o,f)|0,e[a>>1]=f,0;if(o<<16>>16!=-32768){if(f=u-m|0,l=f<<16>>16,(f&65535)<<16>>16<0)return l=0-l<<16,(l|0)>=983040?(f=0,e[a>>1]=f,0):(f=c<<16>>16>>(l>>16)&65535,e[a>>1]=f,0)}else l=32767;return o=c<<16>>16,u=o<<l,(u<<16>>16>>l|0)==(o|0)?(f=u&65535,e[a>>1]=f,0):(f=(o>>>15^32767)&65535,e[a>>1]=f,0)}function a2(i,r,t,o){return i=i|0,r=r|0,t=t|0,o=o|0,t<<16>>16&&(r=r<<16>>16<<1&65535),r<<16>>16<0&&(i=i+-2|0,r=(r&65535)+6&65535),t=r<<16>>16,o=6-t<<16>>16,r=(y(e[3468+(t<<1)>>1]|0,e[i>>1]|0)|0)+16384|0,r=r+(y(e[3468+(o<<1)>>1]|0,e[i+2>>1]|0)|0)|0,r=r+(y(e[3468+(t+6<<1)>>1]|0,e[i+-2>>1]|0)|0)|0,r=r+(y(e[3468+(o+6<<1)>>1]|0,e[i+4>>1]|0)|0)|0,r=(y(e[3468+(t+12<<1)>>1]|0,e[i+-4>>1]|0)|0)+r|0,r=r+(y(e[3468+(o+12<<1)>>1]|0,e[i+6>>1]|0)|0)|0,t=r+(y(e[3468+(t+18<<1)>>1]|0,e[i+-6>>1]|0)|0)|0,(t+(y(e[3468+(o+18<<1)>>1]|0,e[i+8>>1]|0)|0)|0)>>>15&65535|0}function v2(i){return i=i|0,i=i-(i>>>31)|0,i>>31^i|0}function j5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0;if(i<<16>>16)n=3518,a=3538,o=t;else return;for(;o=o+2|0,r=r+2|0,u=e[r>>1]|0,l=e[n>>1]|0,t=y(l,u)|0,t=(t|0)==1073741824?2147483647:t<<1,u=(y(e[a>>1]|0,u)|0)>>15,f=(u<<1)+t|0,f=(t^u|0)>0&(f^t|0)<0?(t>>>31)+2147483647|0:f,l=(y(l,e[o>>1]|0)|0)>>15,t=f+(l<<1)|0,t=(f^l|0)>0&(t^f|0)<0?(f>>>31)+2147483647|0:t,e[r>>1]=t>>>16,e[o>>1]=(t>>>1)-(t>>16<<15),i=i+-1<<16>>16,i<<16>>16;)n=n+2|0,a=a+2|0}function ti(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;return o=i&65535,n=o<<16,r=r<<16>>16,i=(r<<1)+n|0,(r^n|0)>0&(i^n|0)<0?(s[t>>2]=1,n=(o>>>15)+2147483647|0,n|0):(n=i,n|0)}function ri(i){i=i|0;var r=0,t=0,o=0;if(!i||(s[i>>2]=0,r=s0(22)|0,!r))return o=-1,o|0;e[r>>1]=4096,t=r+2|0,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return s[i>>2]=r,o=0,o|0}function t4(i){i=i|0;var r=0;if(!i)return r=-1,r|0;e[i>>1]=4096,i=i+2|0,r=i+20|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function r4(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function q5(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0;for(q=O,O=O+96|0,V=q+66|0,U=q+44|0,K=q+22|0,l=q,C=r+2|0,G=t+2|0,j=(e[G>>1]<<1)+(D[C>>1]<<16)|0,f=v2(j)|0,f=$6(f,e[r>>1]|0,e[t>>1]|0,a)|0,(j|0)>0&&(f=o4(f)|0),x=f>>16,e[n>>1]=S1(f,a)|0,S=f>>20,z=V+2|0,e[z>>1]=S,j=U+2|0,e[j>>1]=(f>>>5)-(S<<15),S=y(x,x)|0,S=(S|0)==1073741824?2147483647:S<<1,x=(y((f>>>1)-(x<<15)<<16>>16,x)|0)>>15,M=x<<1,T=M+S|0,T=(x^S|0)>0&(T^S|0)<0?(S>>>31)+2147483647|0:T,M=T+M|0,M=2147483647-(v2((T^x|0)>0&(M^T|0)<0?(T>>>31)+2147483647|0:M)|0)|0,T=M>>16,x=e[r>>1]|0,S=y(T,x)|0,S=(S|0)==1073741824?2147483647:S<<1,x=(y((M>>>1)-(T<<15)<<16>>16,x)|0)>>15,M=(x<<1)+S|0,M=(x^S|0)>0&(M^S|0)<0?(S>>>31)+2147483647|0:M,T=(y(e[t>>1]|0,T)|0)>>15,S=M+(T<<1)|0,S=(M^T|0)>0&(S^M|0)<0?(M>>>31)+2147483647|0:S,M=B1(S)|0,S=S<<(M<<16>>16),T=K+2|0,x=l+2|0,u=S,S=(S>>>1)-(S>>16<<15)|0,I=l+4|0,N=K+4|0,A=2,F=2;;){for(R=u>>>16,f=R&65535,k=S&65535,b=F+-1|0,d=V+(b<<1)|0,E=U+(b<<1)|0,h=1,w=d,m=E,c=C,l=G,u=0;i1=e[c>>1]|0,W=((y(e[m>>1]|0,i1)|0)>>15)+u|0,u=e[w>>1]|0,u=W+(y(u,i1)|0)+((y(u,e[l>>1]|0)|0)>>15)|0,h=h+1<<16>>16,!((h<<16>>16|0)>=(F|0));)w=w+-2|0,m=m+-2|0,c=c+2|0,l=l+2|0;if(W=(D[r+(F<<1)>>1]<<16)+(u<<5)+(e[t+(F<<1)>>1]<<1)|0,u=$6(v2(W)|0,f,k,a)|0,(W|0)>0&&(u=o4(u)|0),l=M<<16>>16,M<<16>>16>0?(f=u<<l,(f>>l|0)!=(u|0)&&(f=u>>31^2147483647)):(l=0-l<<16,(l|0)<2031616?f=u>>(l>>16):f=0),h=f>>16,(F|0)<5&&(e[n+(b<<1)>>1]=(f+32768|0)>>>16),W=(f>>>16)-(f>>>31)|0,((W<<16>>31^W)&65535)<<16>>16>32750){f=16;break}for(m=(f>>>1)-(h<<15)<<16>>16,w=1,u=E,l=T,c=x;i1=(y(e[u>>1]|0,h)|0)>>15,E=e[d>>1]|0,W=(y(E,m)|0)>>15,E=y(E,h)|0,W=E+i1+(e[U+(w<<1)>>1]|0)+(e[V+(w<<1)>>1]<<15)+W|0,e[l>>1]=W>>>15,e[c>>1]=W&32767,w=w+1|0,(w&65535)<<16>>16!=A<<16>>16;)d=d+-2|0,u=u+-2|0,l=l+2|0,c=c+2|0;if(e[N>>1]=f>>20,e[I>>1]=(f>>>5)-(e[K+(F<<1)>>1]<<15),i1=y(h,h)|0,i1=(i1|0)==1073741824?2147483647:i1<<1,f=(y(m,h)|0)>>15,W=f<<1,l=W+i1|0,l=(f^i1|0)>0&(l^i1|0)<0?(i1>>>31)+2147483647|0:l,W=l+W|0,W=2147483647-(v2((l^f|0)>0&(W^l|0)<0?(l>>>31)+2147483647|0:W)|0)|0,l=W>>16,f=R<<16>>16,f=((y(l,S<<16>>16)|0)>>15)+(y(l,f)|0)+((y((W>>>1)-(l<<15)<<16>>16,f)|0)>>15)<<1,l=(B1(f)|0)<<16>>16,f=f<<l,W=F<<1,_0(z|0,T|0,W|0)|0,_0(j|0,x|0,W|0)|0,F=F+1|0,(F|0)>=11){f=20;break}else M=l+(M&65535)&65535,u=f,S=(f>>1)-(f>>16<<15)|0,I=I+2|0,N=N+2|0,A=A+1<<16>>16}if((f|0)==16){f=o+22|0;do e[o>>1]=e[i>>1]|0,o=o+2|0,i=i+2|0;while((o|0)<(f|0));return W=n,i1=W,e[i1>>1]=0,e[i1+2>>1]=0,W=W+4|0,e[W>>1]=0,e[W+2>>1]=0,O=q,0}else if((f|0)==20)return e[o>>1]=4096,W=((e[j>>1]|0)+8192+(e[z>>1]<<15)|0)>>>14&65535,e[o+2>>1]=W,e[i+2>>1]=W,W=((e[U+4>>1]|0)+8192+(e[V+4>>1]<<15)|0)>>>14&65535,e[o+4>>1]=W,e[i+4>>1]=W,W=((e[U+6>>1]|0)+8192+(e[V+6>>1]<<15)|0)>>>14&65535,e[o+6>>1]=W,e[i+6>>1]=W,W=((e[U+8>>1]|0)+8192+(e[V+8>>1]<<15)|0)>>>14&65535,e[o+8>>1]=W,e[i+8>>1]=W,W=((e[U+10>>1]|0)+8192+(e[V+10>>1]<<15)|0)>>>14&65535,e[o+10>>1]=W,e[i+10>>1]=W,W=((e[U+12>>1]|0)+8192+(e[V+12>>1]<<15)|0)>>>14&65535,e[o+12>>1]=W,e[i+12>>1]=W,W=((e[U+14>>1]|0)+8192+(e[V+14>>1]<<15)|0)>>>14&65535,e[o+14>>1]=W,e[i+14>>1]=W,W=((e[U+16>>1]|0)+8192+(e[V+16>>1]<<15)|0)>>>14&65535,e[o+16>>1]=W,e[i+16>>1]=W,W=((e[U+18>>1]|0)+8192+(e[V+18>>1]<<15)|0)>>>14&65535,e[o+18>>1]=W,e[i+18>>1]=W,W=((e[U+20>>1]|0)+8192+(e[V+20>>1]<<15)|0)>>>14&65535,e[o+20>>1]=W,e[i+20>>1]=W,O=q,0;return 0}function N0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,o=i>>16,e[r>>1]=o,e[t>>1]=(i>>>1)-(o<<15)}function o4(i){return i=i|0,((i|0)==-2147483648?2147483647:0-i|0)|0}function oi(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(4)|0,!r)?(i=-1,i|0):(s[r>>2]=0,(ri(r)|0)<<16>>16?(r4(r),K1(r),i=-1,i|0):(t4(s[r>>2]|0)|0,s[i>>2]=r,i=0,i|0))}function ni(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(r4(r),K1(s[i>>2]|0),s[i>>2]=0))}function ai(i){return i=i|0,i?(t4(s[i>>2]|0)|0,i=0,i|0):(i=-1,i|0)}function si(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0;if(d=O,O=O+64|0,c=d+48|0,u=d+22|0,l=d,(r|0)==7){t=s[a+116>>2]|0,B5(o,10,l,u,s[a+112>>2]|0,f)|0,j5(10,l,u,f),q5(s[i>>2]|0,l,u,n+22|0,c,f)|0,B5(o,10,l,u,t,f)|0,j5(10,l,u,f),q5(s[i>>2]|0,l,u,n+66|0,c,f)|0,O=d;return}else{B5(t,10,l,u,s[a+108>>2]|0,f)|0,j5(10,l,u,f),q5(s[i>>2]|0,l,u,n+66|0,c,f)|0,O=d;return}}function V5(i,r,t,o,n,a,f,l,u,c){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,(t|0)==6){e[n>>1]=hi(i,r,o,20,143,80,a,f,l,u,c)|0;return}if(e[f>>1]=0,e[f+2>>1]=0,t>>>0<2){e[n>>1]=H5(r,t,o,20,143,160,l,u,c)|0;return}if(t>>>0<6){e[n>>1]=H5(r,t,o,20,143,80,l,u,c)|0;return}else{e[n>>1]=H5(r,t,o,18,143,80,l,u,c)|0;return}}function fi(i){i=i|0;var r=0;return i|0&&(s[i>>2]=0,r=s0(2)|0,(r|0)!=0)?(e[r>>1]=0,s[i>>2]=r,r=0):r=-1,r|0}function n4(i){return i=i|0,i?(e[i>>1]=0,i=0):i=-1,i|0}function a4(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function li(i,r,t,o,n,a,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0;w1=O,O=O+240|0,R=w1+160|0,S=w1+80|0,s1=w1,r1=e[3558+(r*18|0)>>1]|0,l1=e[3558+(r*18|0)+2>>1]|0,w=e[3558+(r*18|0)+4>>1]|0,c1=e[3558+(r*18|0)+6>>1]|0,b=e[3558+(r*18|0)+12>>1]|0,k=e[3558+(r*18|0)+14>>1]|0,h=e[3558+(r*18|0)+16>>1]|0;e:do switch(l<<16>>16){case 0:case 80:if(r>>>0<2&l<<16>>16==80){h1=(D[i>>1]|0)-(b&65535)|0,h1=(h1<<16>>16|0)<(h<<16>>16|0)?h:h1&65535,W=k<<16>>16,d1=(h1&65535)+W&65535,t1=d1<<16>>16>143,h1=t1?143-W&65535:h1,d1=t1?143:d1,t1=1;break e}else{h1=(D[t+((l<<16>>16!=0&1)<<1)>>1]|0)-(D[3558+(r*18|0)+8>>1]|0)|0,h1=(h1<<16>>16|0)<(h<<16>>16|0)?h:h1&65535,W=e[3558+(r*18|0)+10>>1]|0,d1=(h1&65535)+W&65535,t1=d1<<16>>16>143,h1=t1?143-W&65535:h1,d1=t1?143:d1,t1=0;break e}default:h1=(D[i>>1]|0)-(b&65535)|0,h1=(h1<<16>>16|0)<(h<<16>>16|0)?h:h1&65535,W=k<<16>>16,d1=(h1&65535)+W&65535,t1=d1<<16>>16>143,h1=t1?143-W&65535:h1,d1=t1?143:d1,t1=1}while(!1);if(i1=h1&65535,l=i1+65532|0,E=l&65535,q=(d1&65535)+4&65535,W=l<<16>>16,l=0-(l&65535)|0,b=l&65535,m5(o+(l<<16>>16<<1)|0,a,R,f),l=f<<16>>16,x=l>>>1&65535,I=x<<16>>16==0,I)f=1;else{for(f=x,h=R,t=S,k=0;U=e[h>>1]|0,e[t>>1]=U>>>2,U=(y(U,U)|0)+k|0,k=e[h+2>>1]|0,e[t+2>>1]=k>>>2,k=U+(y(k,k)|0)|0,f=f+-1<<16>>16,f<<16>>16;)h=h+4|0,t=t+4|0;f=(k|0)<33554433}U=f?0:2,T=f?R:S,N=f?R:S;e:do if(E<<16>>16<=q<<16>>16){if(A=l+-1|0,G=T+(A<<1)|0,K=a+(A<<1)|0,V=T+(l+-2<<1)|0,C=A>>>1,z=C&65535,F=z<<16>>16==0,j=f?12:14,C=(C<<1)+131070&131070,t=l+-3-C|0,M=T+(t<<1)|0,C=T+(l+-4-C<<1)|0,a=a+(t<<1)|0,!I)for(I=W;;){for(S=x,R=N,h=n,k=0,f=0;S=S+-1<<16>>16,l=e[R>>1]|0,k=(y(l,e[h>>1]|0)|0)+k|0,l=(y(l,l)|0)+f|0,f=e[R+2>>1]|0,k=k+(y(f,e[h+2>>1]|0)|0)|0,f=l+(y(f,f)|0)|0,S<<16>>16;)R=R+4|0,h=h+4|0;if(R=t2(f<<1,m)|0,f=R>>16,h=k<<1>>16,S=y(f,h)|0,S=(S|0)==1073741824?2147483647:S<<1,h=(y((R>>>1)-(f<<15)<<16>>16,h)|0)>>15,R=(h<<1)+S|0,R=(h^S|0)>0&(R^S|0)<0?(S>>>31)+2147483647|0:R,f=(y(f,k&32767)|0)>>15,S=R+(f<<1)|0,e[s1+(I-W<<1)>>1]=(R^f|0)>0&(S^R|0)<0?(R>>>31)+65535|0:S,E<<16>>16!=q<<16>>16){if(b=b+-1<<16>>16,S=e[o+(b<<16>>16<<1)>>1]|0,F)R=A,f=V,k=K,h=G;else for(R=z,f=V,k=K,h=G;;)if(I=(y(e[k>>1]|0,S)|0)>>j,e[h>>1]=I+(D[f>>1]|0),I=(y(e[k+-2>>1]|0,S)|0)>>j,e[h+-2>>1]=I+(D[f+-2>>1]|0),R=R+-1<<16>>16,R<<16>>16)f=f+-4|0,k=k+-4|0,h=h+-4|0;else{R=t,f=C,k=a,h=M;break}I=(y(e[k>>1]|0,S)|0)>>j,e[h>>1]=I+(D[f>>1]|0),e[T+(R+-1<<1)>>1]=S>>U}if(E=E+1<<16>>16,E<<16>>16>q<<16>>16)break e;I=E<<16>>16}if(F)for(f=T+(l+-2<<1)|0,k=W;;){if(t2(0,m)|0,e[s1+(k-W<<1)>>1]=0,E<<16>>16!=q<<16>>16&&(b=b+-1<<16>>16,n=e[o+(b<<16>>16<<1)>>1]|0,z=(y(e[K>>1]|0,n)|0)>>j,e[G>>1]=z+(D[V>>1]|0),e[f>>1]=n>>U),E=E+1<<16>>16,E<<16>>16>q<<16>>16)break e;k=E<<16>>16}for(R=T+(t+-1<<1)|0,f=W;;){if(t2(0,m)|0,e[s1+(f-W<<1)>>1]=0,E<<16>>16!=q<<16>>16){for(b=b+-1<<16>>16,f=e[o+(b<<16>>16<<1)>>1]|0,k=z,h=V,t=K,l=G;n=(y(e[t>>1]|0,f)|0)>>j,e[l>>1]=n+(D[h>>1]|0),n=(y(e[t+-2>>1]|0,f)|0)>>j,e[l+-2>>1]=n+(D[h+-2>>1]|0),k=k+-1<<16>>16,k<<16>>16;)h=h+-4|0,t=t+-4|0,l=l+-4|0;n=(y(e[a>>1]|0,f)|0)>>j,e[M>>1]=n+(D[C>>1]|0),e[R>>1]=f>>U}if(E=E+1<<16>>16,E<<16>>16>q<<16>>16)break;f=E<<16>>16}}while(!1);if(E=h1<<16>>16,t=i1+1&65535,t<<16>>16>d1<<16>>16)a=h1;else for(b=h1,l=e[s1+(E-W<<1)>>1]|0;;)if(k=e[s1+((t<<16>>16)-W<<1)>>1]|0,h=k<<16>>16<l<<16>>16,b=h?b:t,t=t+1<<16>>16,t<<16>>16>d1<<16>>16){a=b;break}else l=h?l:k;e:do if(!(t1<<16>>16)&&a<<16>>16>r1<<16>>16)t=a,w=0;else{if(!(r>>>0<4&t1<<16>>16!=0)){if(b=s1+((a<<16>>16)-W<<1)|0,k=a2(b,w,l1,m)|0,t=(w&65535)+1&65535,t<<16>>16<=c1<<16>>16)for(;h=a2(b,t,l1,m)|0,l=h<<16>>16>k<<16>>16,w=l?t:w,t=t+1<<16>>16,!(t<<16>>16>c1<<16>>16);)k=l?h:k;if((r+-7|0)>>>0<2){c1=w<<16>>16==-3,t=(c1<<31>>31)+a<<16>>16,w=c1?3:w;break}switch(w<<16>>16){case-2:{t=a+-1<<16>>16,w=1;break e}case 2:{t=a+1<<16>>16,w=-1;break e}default:{t=a;break e}}}if(r1=e[i>>1]|0,r1=((r1<<16>>16)-E|0)>5?E+5&65535:r1,l=d1<<16>>16,r1=(l-(r1<<16>>16)|0)>4?l+65532&65535:r1,l=a<<16>>16,t=r1<<16>>16,(l|0)==(t+-1|0)||a<<16>>16==r1<<16>>16){if(b=s1+(l-W<<1)|0,l=a2(b,w,l1,m)|0,t=(w&65535)+1&65535,t<<16>>16<=c1<<16>>16)for(;k=a2(b,t,l1,m)|0,h=k<<16>>16>l<<16>>16,w=h?t:w,t=t+1<<16>>16,!(t<<16>>16>c1<<16>>16);)l=h?k:l;if((r+-7|0)>>>0<2){c1=w<<16>>16==-3,t=(c1<<31>>31)+a<<16>>16,w=c1?3:w;break}switch(w<<16>>16){case-2:{t=a+-1<<16>>16,w=1;break e}case 2:{t=a+1<<16>>16,w=-1;break e}default:{t=a;break e}}}if((l|0)==(t+-2|0)){if(t=s1+(l-W<<1)|0,l=a2(t,0,l1,m)|0,(r|0)!=8){for(w=0,b=1;k=a2(t,b,l1,m)|0,h=k<<16>>16>l<<16>>16,w=h?b:w,b=b+1<<16>>16,!(b<<16>>16>c1<<16>>16);)l=h?k:l;if((r+-7|0)>>>0>=2)switch(w<<16>>16){case-2:{t=a+-1<<16>>16,w=1;break e}case 2:{t=a+1<<16>>16,w=-1;break e}default:{t=a;break e}}}else w=0;c1=w<<16>>16==-3,t=(c1<<31>>31)+a<<16>>16,w=c1?3:w;break}if((l|0)==(t+1|0)){if(b=s1+(l-W<<1)|0,t=a2(b,w,l1,m)|0,l=(w&65535)+1&65535,l<<16>>16<=0)for(;h=a2(b,l,l1,m)|0,k=h<<16>>16>t<<16>>16,w=k?l:w,l=l+1<<16>>16,!(l<<16>>16>0);)t=k?h:t;if((r+-7|0)>>>0<2){c1=w<<16>>16==-3,t=(c1<<31>>31)+a<<16>>16,w=c1?3:w;break}switch(w<<16>>16){case-2:{t=a+-1<<16>>16,w=1;break e}case 2:{t=a+1<<16>>16,w=-1;break e}default:{t=a;break e}}}else t=a,w=0}while(!1);return(r+-7|0)>>>0>1?(c1=i,i=Ve(t,w,e[i>>1]|0,h1,d1,t1,r>>>0<4&1,m)|0,e[d>>1]=i,e[c1>>1]=t,e[c>>1]=l1,e[u>>1]=w,O=w1,t|0):(m=He(t,w,h1,t1,m)|0,e[d>>1]=m,e[i>>1]=t,e[c>>1]=l1,e[u>>1]=w,O=w1,t|0)}function H5(i,r,t,o,n,a,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0;C=O,O=O+1200|0,x=C+1188|0,T=C+580|0,M=C+578|0,F=C+576|0,S=C,N=C+582|0,A=(l|0)!=0;do if(A)if(r>>>0<2){G5(i,1,u);break}else{G5(i,0,u);break}while(!1);I=n<<16>>16,m=0-I|0,d=t+(m<<1)|0,m=m&65535,b=a<<16>>16;do if(m<<16>>16<a<<16>>16){for(k=m,h=d,m=0;E=e[h>>1]|0,m=(y(E<<1,E)|0)+m|0,!((m|0)<0);)if(k=k+1<<16>>16,k<<16>>16>=a<<16>>16){R=14;break}else h=h+2|0;if((R|0)==14){if((m|0)<1048576){R=15;break}_0(N|0,d|0,b+I<<1|0)|0,E=0;break}if(c=b+I|0,w=c>>>1,k=w&65535,!(k<<16>>16))m=N;else{for(E=((w<<1)+131070&131070)+2|0,b=E-I|0,h=N;e[h>>1]=(e[d>>1]|0)>>>3,e[h+2>>1]=(e[d+2>>1]|0)>>>3,k=k+-1<<16>>16,k<<16>>16;)d=d+4|0,h=h+4|0;d=t+(b<<1)|0,m=N+(E<<1)|0}c&1&&(e[m>>1]=(e[d>>1]|0)>>>3),E=3}else R=15;while(!1);if((R|0)==15){if(E=b+I|0,m=E>>>1,w=m&65535,!(w<<16>>16))m=N;else{for(b=((m<<1)+131070&131070)+2|0,h=b-I|0,k=N;e[k>>1]=e[d>>1]<<3,e[k+2>>1]=e[d+2>>1]<<3,w=w+-1<<16>>16,w<<16>>16;)d=d+4|0,k=k+4|0;d=t+(h<<1)|0,m=N+(b<<1)|0}E&1&&(e[m>>1]=e[d>>1]<<3),E=-3}return b=S+(I<<2)|0,h=N+(I<<1)|0,G6(h,a,n,o,b),c=(r|0)==7&1,m=o<<16>>16,d=m<<2,(d|0)!=(m<<18>>16|0)&&(s[u>>2]=1,d=o<<16>>16>0?32767:-32768),k=W5(i,b,h,E,c,a,n,d&65535,x,l,u)|0,m=m<<1,w=W5(i,b,h,E,c,a,d+65535&65535,m&65535,T,l,u)|0,m=W5(i,b,h,E,c,a,m+65535&65535,o,M,l,u)|0,f<<16>>16==1&A&&(i4(b,h,a,n,o,F,u)|0,w4(i,e[F>>1]|0)),d=e[x>>1]|0,c=e[T>>1]|0,((d<<16>>16)*55706>>16|0)>=(c<<16>>16|0)?(T=d,x=k,T=T<<16>>16,T=T*55706|0,T=T>>16,M=e[M>>1]|0,M=M<<16>>16,M=(T|0)<(M|0),M=M?m:x,O=C,M|0):(e[x>>1]=c,T=c,x=w,T=T<<16>>16,T=T*55706|0,T=T>>16,M=e[M>>1]|0,M=M<<16>>16,M=(T|0)<(M|0),M=M?m:x,O=C,M|0)}function W5(i,r,t,o,n,a,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0;if(f<<16>>16<l<<16>>16)l=-2147483648,h=f;else for(h=f,m=-2147483648,w=r+(0-(f<<16>>16)<<2)|0,r=f;;)if(f=s[w>>2]|0,b=(f|0)<(m|0),r=b?r:h,m=b?m:f,h=h+-1<<16>>16,h<<16>>16<l<<16>>16){l=m,h=r;break}else w=w+4|0;if(r=a<<16>>16>>>2&65535,!(r<<16>>16))r=0;else{for(m=r,f=t+(0-(h<<16>>16)<<1)|0,r=0;b=e[f>>1]|0,b=(y(b,b)|0)+r|0,r=e[f+2>>1]|0,r=b+(y(r,r)|0)|0,b=e[f+4>>1]|0,b=r+(y(b,b)|0)|0,r=e[f+6>>1]|0,r=b+(y(r,r)|0)|0,m=m+-1<<16>>16,m<<16>>16;)f=f+8|0;r=r<<1}if(c&&m4(i,l,r,d),r=t2(r,d)|0,f=n<<16>>16!=0,f&&(r=(r|0)>1073741823?2147483647:r<<1),n=l>>16,i=r>>16,d=y(i,n)|0,d=(d|0)==1073741824?2147483647:d<<1,r=(y((r>>>1)-(i<<15)<<16>>16,n)|0)>>15,b=(r<<1)+d|0,b=(r^d|0)>0&(b^d|0)<0?(d>>>31)+2147483647|0:b,n=(y(i,(l>>>1)-(n<<15)<<16>>16)|0)>>15,r=b+(n<<1)|0,r=(b^n|0)>0&(r^b|0)<0?(b>>>31)+2147483647|0:r,!f)return e[u>>1]=r,h|0;if(f=o<<16>>16,o<<16>>16>0?o<<16>>16<31?(f=r>>f,k=16):f=0:(k=0-f<<16>>16,f=r<<k,f=(f>>k|0)==(r|0)?f:r>>31^2147483647,k=16),(k|0)==16){if((f|0)>65535)return e[u>>1]=32767,h|0;if((f|0)<-65536)return e[u>>1]=-32768,h|0}return e[u>>1]=f>>>1,h|0}function ui(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(6)|0,!r)?(i=-1,i|0):(e[r>>1]=40,e[r+2>>1]=0,e[r+4>>1]=0,s[i>>2]=r,i=0,i|0)}function ci(i){return i=i|0,i?(e[i>>1]=40,e[i+2>>1]=0,e[i+4>>1]=0,i=0,i|0):(i=-1,i|0)}function di(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function hi(i,r,t,o,n,a,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0;if(j=O,O=O+1200|0,I=j+1186|0,N=j+1184|0,z=j+1182|0,S=j,F=j+576|0,A=n<<16>>16,C=F+(A<<1)|0,m=(0-A&65535)<<16>>16<a<<16>>16,m){b=0-n<<16>>16<<16>>16,w=0;do k=e[t+(b<<1)>>1]|0,k=y(k,k)|0,(k|0)!=1073741824?(h=(k<<1)+w|0,(k^w|0)>0&(h^w|0)<0?(s[d>>2]=1,w=(w>>>31)+2147483647|0):w=h):(s[d>>2]=1,w=2147483647),b=b+1|0;while((b&65535)<<16>>16!=a<<16>>16)}else w=0;if((2147483646-w&w|0)>=0)if((w|0)==2147483647){if(m){w=0-n<<16>>16<<16>>16;do e[F+(w+A<<1)>>1]=W1(e[t+(w<<1)>>1]|0,3,d)|0,w=w+1|0;while((w&65535)<<16>>16!=a<<16>>16)}}else E=14;else s[d>>2]=1,E=14;do if((E|0)==14){if((1048575-w&w|0)<0?(s[d>>2]=1,w=(w>>>31)+2147483647|0):w=w+-1048576|0,(w|0)>=0){if(!m)break;M=0-n<<16>>16<<16>>16,_0(F+(A+M<<1)|0,t+(M<<1)|0,(((a+n<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(m){w=0-n<<16>>16<<16>>16;do M=e[t+(w<<1)>>1]|0,e[F+(w+A<<1)>>1]=(M<<19>>19|0)==(M|0)?M<<3:M>>>15^32767,w=w+1|0;while((w&65535)<<16>>16!=a<<16>>16)}}while(!1);x=S+(A<<2)|0,G6(C,a,n,o,x),b=e[i>>1]|0,M=i+4|0,T=l+(u<<16>>16<<1)|0;e:do if(n<<16>>16<o<<16>>16)R=n;else{if((e[M>>1]|0)<=0)for(t=n,l=-2147483648,k=n,E=3402;;)if(N0(s[S+(A-(t<<16>>16)<<2)>>2]|0,I,N,d),h=e[N>>1]|0,w=e[E>>1]|0,b=y(w,e[I>>1]|0)|0,(b|0)==1073741824?(s[d>>2]=1,m=2147483647):m=b<<1,R=(y(w,h<<16>>16)|0)>>15,b=m+(R<<1)|0,(m^R|0)>0&(b^m|0)<0&&(s[d>>2]=1,b=(m>>>31)+2147483647|0),h=(b|0)<(l|0),k=h?k:t,t=t+-1<<16>>16,t<<16>>16<o<<16>>16){R=k;break e}else l=h?l:b,E=E+-2|0;for(l=n,m=-2147483648,k=n,R=2902+(A+123-(b<<16>>16)<<1)|0,t=3402;;)if(N0(s[S+(A-(l<<16>>16)<<2)>>2]|0,I,N,d),E=e[N>>1]|0,h=e[t>>1]|0,b=y(h,e[I>>1]|0)|0,(b|0)==1073741824?(s[d>>2]=1,w=2147483647):w=b<<1,E=(y(h,E<<16>>16)|0)>>15,b=w+(E<<1)|0,(w^E|0)>0&(b^w|0)<0&&(s[d>>2]=1,b=(w>>>31)+2147483647|0),N0(b,I,N,d),E=e[N>>1]|0,h=e[R>>1]|0,b=y(h,e[I>>1]|0)|0,(b|0)==1073741824?(s[d>>2]=1,w=2147483647):w=b<<1,E=(y(h,E<<16>>16)|0)>>15,b=w+(E<<1)|0,(w^E|0)>0&(b^w|0)<0&&(s[d>>2]=1,b=(w>>>31)+2147483647|0),h=(b|0)<(m|0),k=h?k:l,l=l+-1<<16>>16,l<<16>>16<o<<16>>16){R=k;break}else m=h?m:b,R=R+-2|0,t=t+-2|0}while(!1);if(a<<16>>16>0)for(l=0,t=C,E=F+(A-(R<<16>>16)<<1)|0,k=0,w=0;b=e[E>>1]|0,h=y(b,e[t>>1]|0)|0,(h|0)!=1073741824?(m=(h<<1)+k|0,(h^k|0)>0&(m^k|0)<0?(s[d>>2]=1,k=(k>>>31)+2147483647|0):k=m):(s[d>>2]=1,k=2147483647),m=y(b,b)|0,(m|0)!=1073741824?(h=(m<<1)+w|0,(m^w|0)>0&(h^w|0)<0?(s[d>>2]=1,w=(w>>>31)+2147483647|0):w=h):(s[d>>2]=1,w=2147483647),l=l+1<<16>>16,!(l<<16>>16>=a<<16>>16);)t=t+2|0,E=E+2|0;else k=0,w=0;return h=(c|0)==0,h||(G5(r,0,d),m4(r,k,w,d)),m=(S1(w,d)|0)<<16>>16,(m*13107|0)==1073741824?(s[d>>2]=1,w=2147483647):w=m*26214|0,m=k-w|0,((m^k)&(w^k)|0)<0&&(s[d>>2]=1,m=(k>>>31)+2147483647|0),c=S1(m,d)|0,e[T>>1]=c,c<<16>>16>0?(m=f+6|0,e[f+8>>1]=e[m>>1]|0,c=f+4|0,e[m>>1]=e[c>>1]|0,m=f+2|0,e[c>>1]=e[m>>1]|0,e[m>>1]=e[f>>1]|0,e[f>>1]=R,e[i>>1]=E2(f,5)|0,e[i+2>>1]=32767,m=32767):(e[i>>1]=R,i=i+2|0,m=((e[i>>1]|0)*29491|0)>>>15&65535,e[i>>1]=m),e[M>>1]=((E1(m,9830,d)|0)&65535)>>>15^1,h||(E1(u,1,d)|0)<<16>>16?(O=j,R|0):(i4(x,C,a,n,o,z,d)|0,w4(r,e[z>>1]|0),O=j,R|0)}function X5(i,r,t,o,n,a,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0;c=O,O=O+48|0,m=c+22|0,d=c,r=i>>>0<6?r:t,t=a<<16>>16>0?22:0,i=n+(t<<1)|0,X0(i,r,m),X0(i,o,d),i=a<<16>>16,a=u+(i<<1)|0,L2(m,f+(i<<1)|0,a,40),c0(d,a,a,40,l,1),t=n+(((t<<16)+720896|0)>>>16<<1)|0,X0(t,r,m),X0(t,o,d),i=(i<<16)+2621440>>16,u=u+(i<<1)|0,L2(m,f+(i<<1)|0,u,40),c0(d,u,u,40,l,1),O=c}function wi(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(12)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,s[i>>2]=r,i=0,i|0)}function mi(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,i=0,i|0):(i=-1,i|0)}function s4(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function pi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0;if(m=i+10|0,n=e[m>>1]|0,w=i+8|0,o=e[w>>1]|0,!(t<<16>>16)){i=o,d=n,e[m>>1]=d,e[w>>1]=i;return}for(l=i+4|0,u=i+6|0,c=i+2|0,f=e[u>>1]|0,d=e[l>>1]|0,a=t,t=n;h=(y(e[i>>1]|0,-3733)|0)+(((d<<16>>16)*7807|0)+((f<<16>>16)*7807>>15))|0,e[i>>1]=d,h=h+((y(e[c>>1]|0,-3733)|0)>>15)|0,e[c>>1]=f,h=((t<<16>>16)*1899|0)+h+(y(o<<16>>16,-3798)|0)|0,t=e[r>>1]|0,h=h+((t<<16>>16)*1899|0)|0,e[r>>1]=(h+2048|0)>>>12,n=h>>>12,d=n&65535,e[l>>1]=d,f=(h<<3)-(n<<15)&65535,e[u>>1]=f,a=a+-1<<16>>16,a<<16>>16;)h=o,r=r+2|0,o=t,t=h;e[m>>1]=o,e[w>>1]=t}function ki(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0;if(n=e[(s[o+88>>2]|0)+(i<<1)>>1]|0,!!(n<<16>>16))for(l=t,f=s[(s[o+92>>2]|0)+(i<<2)>>2]|0;;){if(t=e[f>>1]|0,!(t<<16>>16))t=0;else{for(i=e[r>>1]|0,a=t,o=l+((t<<16>>16)+-1<<1)|0;t=i<<16>>16,e[o>>1]=t&1,a=a+-1<<16>>16,a<<16>>16;)i=t>>>1&65535,o=o+-2|0;t=e[f>>1]|0}if(r=r+2|0,n=n+-1<<16>>16,n<<16>>16)l=l+(t<<16>>16<<1)|0,f=f+2|0;else break}}function bi(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0;if(d=O,O=O+16|0,u=d+2|0,c=d,f=n<<16>>16,n<<16>>16<1){a=-5443,c=-32768,W0(i,c,a),O=d;return}if(l=x0(14,t,a)|0,(f|0)<(l<<16>>16|0)?t=o:(t=(o&65535)+1&65535,n=f>>>1&65535),o=a0(n,l&65535)|0,e[c>>1]=o,f2(o<<16>>16,u,c,a),e[u>>1]=((((t&65535)-(r&65535)<<16)+-65536|0)>>>16)+(D[u>>1]|0),o=K2(e[c>>1]|0,5,a)|0,f=e[u>>1]|0,o=((f&65535)<<10)+(o&65535)&65535,o<<16>>16>18284){a=3037,c=18284,W0(i,c,a),O=d;return}n=e[c>>1]|0,f=f<<16>>16,(f*24660|0)==1073741824?(s[a>>2]=1,t=2147483647):t=f*49320|0,c=(n<<16>>16)*24660>>15,f=t+(c<<1)|0,(t^c|0)>0&(f^t|0)<0&&(s[a>>2]=1,f=(t>>>31)+2147483647|0),c=f<<13,a=S1((c>>13|0)==(f|0)?c:f>>31^2147483647,a)|0,c=o,W0(i,c,a),O=d}function vi(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b,E,R,S,I){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,I=I|0;var N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0,Z1=0,z1=0,f0=0,j1=0;for(j1=O,O=O+80|0,L1=j1+66|0,Z1=j1+64|0,z1=j1+62|0,f0=j1+60|0,r1=j1+40|0,s1=j1+20|0,i1=j1,e[L1>>1]=r,e[Z1>>1]=u,e[z1>>1]=c,q=x0(14,t,I)|0,U1=q&65535,e[f0>>1]=U1,W=x0(14,c,I)|0,U=(D[o>>1]|0)+65523|0,e[i1>>1]=U,C=(D[o+2>>1]|0)+65522|0,z=i1+2|0,e[z>>1]=C,j=((r&65535)<<16)+-720896|0,T=j>>16,j=(j>>>15)+15+(D[o+4>>1]|0)|0,G=i1+4|0,e[G>>1]=j,K=(D[o+6>>1]|0)+T|0,V=i1+6|0,e[V>>1]=K,T=T+1+(D[o+8>>1]|0)|0,x=i1+8|0,e[x>>1]=T,N=(D[d>>1]|0)+65523&65535,e[i1+10>>1]=N,M=(D[d+2>>1]|0)+65522&65535,e[i1+12>>1]=M,A=((u&65535)<<16)+-720896|0,o=A>>16,A=(A>>>15)+15+(D[d+4>>1]|0)&65535,e[i1+14>>1]=A,F=(D[d+6>>1]|0)+o&65535,e[i1+16>>1]=F,o=o+1+(D[d+8>>1]|0)&65535,e[i1+18>>1]=o,P1=(a&65535)-(w&65535)<<16,u=P1>>16,(P1|0)>0?(c=f,t=h<<16>>16>>u&65535):(c=f<<16>>16>>0-u&65535,t=h),(K2(t,1,I)|0)<<16>>16>c<<16>>16?t=1:t=(((c<<16>>16)+3>>2|0)>(t<<16>>16|0))<<31>>31,d=U+t&65535,e[i1>>1]=d,P1=C+t&65535,e[z>>1]=P1,F1=j+t&65535,e[G>>1]=F1,v1=K+t&65535,e[V>>1]=v1,H=T+t&65535,e[x>>1]=H,u=o<<16>>16>d<<16>>16?o:d,u=F<<16>>16>u<<16>>16?F:u,u=A<<16>>16>u<<16>>16?A:u,u=M<<16>>16>u<<16>>16?M:u,u=N<<16>>16>u<<16>>16?N:u,u=H<<16>>16>u<<16>>16?H:u,u=v1<<16>>16>u<<16>>16?v1:u,u=F1<<16>>16>u<<16>>16?F1:u,u=(P1<<16>>16>u<<16>>16?P1:u)+1&65535,o=0;;){if(t=u-(d&65535)|0,d=t&65535,c=D[n>>1]<<16,t=t<<16>>16,d<<16>>16>0?d=d<<16>>16<31?c>>t:0:(P1=0-t<<16>>16,d=c<<P1,d=(d>>P1|0)==(c|0)?d:c>>31^2147483647),P1=d>>16,e[r1+(o<<1)>>1]=P1,e[s1+(o<<1)>>1]=(d>>>1)-(P1<<15),o=o+1|0,(o|0)==5){t=5,c=m;break}d=e[i1+(o<<1)>>1]|0,n=n+2|0}for(;o=u-(N&65535)|0,N=o&65535,d=D[c>>1]<<16,o=o<<16>>16,N<<16>>16>0?d=N<<16>>16<31?d>>o:0:(F1=0-o<<16>>16,P1=d<<F1,d=(P1>>F1|0)==(d|0)?P1:d>>31^2147483647),P1=d>>16,e[r1+(t<<1)>>1]=P1,e[s1+(t<<1)>>1]=(d>>>1)-(P1<<15),d=t+1|0,(d&65535)<<16>>16!=10;)N=e[i1+(d<<1)>>1]|0,t=d,c=c+2|0;c1=q<<16>>16,h1=e[r1>>1]|0,d1=e[s1>>1]|0,t1=e[r1+2>>1]|0,l1=e[s1+2>>1]|0,w1=e[r1+4>>1]|0,m1=e[s1+4>>1]|0,$=e[r1+6>>1]|0,H=e[s1+6>>1]|0,v1=e[r1+8>>1]|0,F1=e[s1+8>>1]|0,P1=k&65535,w=W<<16>>16,a=e[r1+10>>1]|0,F=e[s1+10>>1]|0,A=e[r1+12>>1]|0,n=e[s1+12>>1]|0,t=e[r1+14>>1]|0,c=e[s1+14>>1]|0,o=e[r1+16>>1]|0,N=e[s1+16>>1]|0,T=e[r1+18>>1]|0,s1=e[s1+18>>1]|0,u=2147483647,r1=0,d=0,x=782;do i1=e[x>>1]|0,K=(y(c1,e[x+2>>1]|0)|0)>>>15<<16,m=K>>16,j=i1<<1,U=(y(j,i1)|0)>>16,h=y(U,h1)|0,(h|0)==1073741824?(s[I>>2]=1,V=2147483647):V=h<<1,W=(y(d1,U)|0)>>15,h=V+(W<<1)|0,(V^W|0)>0&(h^V|0)<0&&(s[I>>2]=1,h=(V>>>31)+2147483647|0),U=y(t1,i1)|0,(U|0)==1073741824?(s[I>>2]=1,V=2147483647):V=U<<1,W=(y(l1,i1)|0)>>15,U=V+(W<<1)|0,(V^W|0)>0&(U^V|0)<0&&(s[I>>2]=1,U=(V>>>31)+2147483647|0),K=(y(K>>15,m)|0)>>16,V=y(w1,K)|0,(V|0)==1073741824?(s[I>>2]=1,G=2147483647):G=V<<1,W=(y(m1,K)|0)>>15,V=G+(W<<1)|0,(G^W|0)>0&(V^G|0)<0&&(s[I>>2]=1,V=(G>>>31)+2147483647|0),K=y($,m)|0,(K|0)==1073741824?(s[I>>2]=1,G=2147483647):G=K<<1,W=(y(H,m)|0)>>15,K=G+(W<<1)|0,(G^W|0)>0&(K^G|0)<0?(s[I>>2]=1,W=(G>>>31)+2147483647|0):W=K,G=(y(j,m)|0)>>16,K=y(v1,G)|0,(K|0)==1073741824?(s[I>>2]=1,j=2147483647):j=K<<1,q=(y(F1,G)|0)>>15,K=j+(q<<1)|0,(j^q|0)>0&(K^j|0)<0&&(s[I>>2]=1,K=(j>>>31)+2147483647|0),G=e[x+4>>1]|0,j=e[x+6>>1]|0,x=x+8|0,(i1-P1&65535)<<16>>16<1&&(q1=G<<16>>16,G<<16>>16<=k<<16>>16)&&(C=(y(j<<16>>16,w)|0)>>>15<<16,i1=C>>16,M=q1<<1,j=(y(M,q1)|0)>>16,G=y(a,j)|0,(G|0)==1073741824?(s[I>>2]=1,z=2147483647):z=G<<1,q=(y(F,j)|0)>>15,G=z+(q<<1)|0,(z^q|0)>0&(G^z|0)<0&&(s[I>>2]=1,G=(z>>>31)+2147483647|0),j=y(A,q1)|0,(j|0)==1073741824?(s[I>>2]=1,z=2147483647):z=j<<1,q=(y(n,q1)|0)>>15,j=z+(q<<1)|0,(z^q|0)>0&(j^z|0)<0?(s[I>>2]=1,q=(z>>>31)+2147483647|0):q=j,z=(y(C>>15,i1)|0)>>16,j=y(t,z)|0,(j|0)==1073741824?(s[I>>2]=1,C=2147483647):C=j<<1,m=(y(c,z)|0)>>15,j=C+(m<<1)|0,(C^m|0)>0&(j^C|0)<0?(s[I>>2]=1,m=(C>>>31)+2147483647|0):m=j,j=y(o,i1)|0,(j|0)==1073741824?(s[I>>2]=1,z=2147483647):z=j<<1,C=(y(N,i1)|0)>>15,j=z+(C<<1)|0,(z^C|0)>0&(j^z|0)<0?(s[I>>2]=1,f=(z>>>31)+2147483647|0):f=j,z=(y(M,i1)|0)>>16,j=y(T,z)|0,(j|0)==1073741824?(s[I>>2]=1,C=2147483647):C=j<<1,i1=(y(s1,z)|0)>>15,j=C+(i1<<1)|0,(C^i1|0)>0&(j^C|0)<0&&(s[I>>2]=1,j=(C>>>31)+2147483647|0),i1=U+h+V+W+K+G+q+m+f+j|0,W=(i1|0)<(u|0),u=W?i1:u,d=W?r1:d),r1=r1+1<<16>>16;while(r1<<16>>16<256);return k=(d&65535)<<18>>16,f4(i,782+(k<<1)|0,U1,r,b,E,I),I2(i,0,l,Z1,z1,L1,f0,I),l=(x0(14,e[z1>>1]|0,I)|0)&65535,f4(i,782+((k|2)<<1)|0,l,e[Z1>>1]|0,R,S,I),O=j1,d|0}function f4(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0;if(d=O,O=O+16|0,u=d+2|0,c=d,e[n>>1]=e[r>>1]|0,l=e[r+2>>1]|0,t=y(t<<16>>16<<1,l)|0,n=10-(o&65535)|0,r=n&65535,n=n<<16>>16,r<<16>>16>0?r=r<<16>>16<31?t>>n:0:(n=0-n<<16>>16,r=t<<n,r=(r>>n|0)==(t|0)?r:t>>31^2147483647),e[a>>1]=r>>>16,f2(l,u,c,f),e[u>>1]=(D[u>>1]|0)+65524,n=K2(e[c>>1]|0,5,f)|0,o=e[u>>1]|0,n=((o&65535)<<10)+(n&65535)&65535,t=e[c>>1]|0,o=o<<16>>16,(o*24660|0)==1073741824?(s[f>>2]=1,r=2147483647):r=o*49320|0,c=(t<<16>>16)*24660>>15,o=r+(c<<1)|0,!((r^c|0)>0&(o^r|0)<0)){f=o,f=f<<13,f=f+32768|0,f=f>>>16,f=f&65535,W0(i,n,f),O=d;return}s[f>>2]=1,f=(r>>>31)+2147483647|0,f=f<<13,f=f+32768|0,f=f>>>16,f=f&65535,W0(i,n,f),O=d}function Ei(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b,E,R,S,I,N){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,I=I|0,N=N|0;var A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0,Z1=0,z1=0,f0=0,j1=0,Q1=0,l0=0,t0=0,H1=0;for(H1=O,O=O+80|0,Q1=H1+72|0,l0=H1+70|0,t0=H1+68|0,f0=H1+66|0,j1=H1+56|0,v1=H1+24|0,H=H1+12|0,m1=H1+48|0,$=H1+40|0,h1=H1+34|0,t1=H1+22|0,s1=H1+6|0,c1=H1,l4(5,h,k,s1,c1,s[I+72>>2]|0,N)|0,T=x0(14,c,N)|0,d1=I+68|0,r1=s[d1>>2]|0,w1=u<<16>>16,l1=w1+65526|0,h=(D[a>>1]|0)+65523&65535,e[j1>>1]=h,I=(D[a+2>>1]|0)+65522&65535,e[j1+2>>1]=I,L1=l1<<16>>16,Z1=((l1<<17>>17|0)==(L1|0)?l1<<1:L1>>>15^32767)+15+(D[a+4>>1]|0)&65535,e[j1+4>>1]=Z1,z1=(D[a+6>>1]|0)+L1&65535,e[j1+6>>1]=z1,a=L1+1+(D[a+8>>1]|0)&65535,e[j1+8>>1]=a,I=I<<16>>16>h<<16>>16?I:h,I=Z1<<16>>16>I<<16>>16?Z1:I,I=z1<<16>>16>I<<16>>16?z1:I,I=(n1(a<<16>>16>I<<16>>16?a:I,1,N)|0)&65535,a=h,h=0;c=I-(a&65535)|0,a=c&65535,F=D[n+(h<<1)>>1]<<16,c=c<<16>>16,a<<16>>16>0?c=a<<16>>16<31?F>>c:0:(z1=0-c<<16>>16,c=F<<z1,c=(c>>z1|0)==(F|0)?c:F>>31^2147483647),N0(c,v1+(h<<1)|0,H+(h<<1)|0,N),c=h+1|0,(c|0)!=5;)a=e[j1+(c<<1)>>1]|0,h=c;for(i1=v1+2|0,W=H+2|0,z1=T<<16>>16,F1=v1+4|0,P1=H+4|0,q1=v1+6|0,U1=H+6|0,L1=v1+8|0,Z1=H+8|0,C=0,a=2147483647,n=0,c=0;;){q=e[s1+(n<<1)>>1]|0,T=y(q,q)|0,T>>>0>1073741823?(s[N>>2]=1,T=32767):T=T>>>15,I=e[H>>1]|0,F=T<<16>>16,T=y(F,e[v1>>1]|0)|0,(T|0)==1073741824?(s[N>>2]=1,h=2147483647):h=T<<1,U=(y(I<<16>>16,F)|0)>>15,T=h+(U<<1)|0,(h^U|0)>0&(T^h|0)<0&&(s[N>>2]=1,T=(h>>>31)+2147483647|0),I=e[W>>1]|0,F=y(e[i1>>1]|0,q)|0,(F|0)!=1073741824?(h=(F<<1)+T|0,(F^T|0)>0&(h^T|0)<0&&(s[N>>2]=1,h=(T>>>31)+2147483647|0)):(s[N>>2]=1,h=2147483647),T=(y(I<<16>>16,q)|0)>>15,(T|0)>32767&&(s[N>>2]=1,T=32767),U=T<<16,T=(U>>15)+h|0,(U>>16^h|0)>0&(T^h|0)<0?(s[N>>2]=1,U=(h>>>31)+2147483647|0):U=T,K=(U>>>31)+2147483647|0,V=n&65535,T=C,j=0,G=r1;do{F=(y(e[G>>1]|0,z1)|0)>>15,G=G+6|0,(F|0)>32767&&(s[N>>2]=1,F=32767),z=F<<16>>16,F=y(z,z)|0,(F|0)==1073741824?(s[N>>2]=1,M=2147483647):M=F<<1,N0(M,Q1,l0,N),F=y(z,q)|0,(F|0)==1073741824?(s[N>>2]=1,M=2147483647):M=F<<1,N0(M,t0,f0,N),h=e[P1>>1]|0,x=e[l0>>1]|0,F=e[F1>>1]|0,I=e[Q1>>1]|0,C=y(I,F)|0,(C|0)!=1073741824?(M=(C<<1)+U|0,(C^U|0)>0&(M^U|0)<0&&(s[N>>2]=1,M=K)):(s[N>>2]=1,M=2147483647),C=(y(x<<16>>16,F)|0)>>15,(C|0)>32767&&(s[N>>2]=1,C=32767),x=C<<16,C=(x>>15)+M|0,(x>>16^M|0)>0&(C^M|0)<0&&(s[N>>2]=1,C=(M>>>31)+2147483647|0),M=(y(I,h<<16>>16)|0)>>15,(M|0)>32767&&(s[N>>2]=1,M=32767),x=M<<16,M=(x>>15)+C|0,(x>>16^C|0)>0&(M^C|0)<0&&(s[N>>2]=1,M=(C>>>31)+2147483647|0),F=e[U1>>1]|0,C=y(e[q1>>1]|0,z)|0,(C|0)!=1073741824?(x=(C<<1)+M|0,(C^M|0)>0&(x^M|0)<0&&(s[N>>2]=1,x=(M>>>31)+2147483647|0)):(s[N>>2]=1,x=2147483647),F=(y(F<<16>>16,z)|0)>>15,(F|0)>32767&&(s[N>>2]=1,F=32767),z=F<<16,F=(z>>15)+x|0,(z>>16^x|0)>0&(F^x|0)<0&&(s[N>>2]=1,F=(x>>>31)+2147483647|0),I=e[Z1>>1]|0,x=e[f0>>1]|0,h=e[L1>>1]|0,A=e[t0>>1]|0,C=y(A,h)|0;do if((C|0)==1073741824)s[N>>2]=1,C=2147483647;else{if(M=(C<<1)+F|0,!((C^F|0)>0&(M^F|0)<0)){C=M;break}s[N>>2]=1,C=(F>>>31)+2147483647|0}while(!1);M=(y(x<<16>>16,h)|0)>>15,(M|0)>32767&&(s[N>>2]=1,M=32767),z=M<<16,M=(z>>15)+C|0,(z>>16^C|0)>0&(M^C|0)<0&&(s[N>>2]=1,M=(C>>>31)+2147483647|0),F=(y(A,I<<16>>16)|0)>>15,(F|0)>32767&&(s[N>>2]=1,F=32767),z=F<<16,F=(z>>15)+M|0,(z>>16^M|0)>0&(F^M|0)<0&&(s[N>>2]=1,F=(M>>>31)+2147483647|0),z=(F|0)<(a|0),T=z?j:T,c=z?V:c,a=z?F:a,j=j+1<<16>>16}while(j<<16>>16<32);if(n=n+1|0,(n|0)==3){F=T,n=c;break}else C=T}if(W=(F<<16>>16)*3|0,a=e[r1+(W<<1)>>1]|0,e[E>>1]=e[r1+(W+1<<1)>>1]|0,e[R>>1]=e[r1+(W+2<<1)>>1]|0,a=y(a<<16>>16,z1)|0,(a|0)==1073741824?(s[N>>2]=1,T=2147483647):T=a<<1,W=9-w1|0,r1=W&65535,W=W<<16>>16,i1=r1<<16>>16>0,i1?T=r1<<16>>16<31?T>>W:0:(U=0-W<<16>>16,q=T<<U,T=(q>>U|0)==(T|0)?q:T>>31^2147483647),e[b>>1]=T>>>16,q=n<<16>>16,s1=e[s1+(q<<1)>>1]|0,e[k>>1]=s1,c1=e[c1+(q<<1)>>1]|0,Pe(r,t,o,s1,d,m1,$,h1,N),Ke(i,e[h1>>1]|0,e[b>>1]|0,t1,N),!((e[m1>>1]|0)!=0&(e[t1>>1]|0)>0)){N=F,E=s[S>>2]|0,b=E+2|0,e[E>>1]=c1,E=E+4|0,s[S>>2]=E,e[b>>1]=N,O=H1;return}z=m1+6|0,e[z>>1]=l,M=$+6|0,e[M>>1]=f,u=((E1(w,u,N)|0)&65535)+10|0,I=u<<16>>16,(u&65535)<<16>>16<0?(c=0-I<<16,(c|0)<983040?m=m<<16>>16>>(c>>16)&65535:m=0):(c=m<<16>>16,h=c<<I,(h<<16>>16>>I|0)==(c|0)?m=h&65535:m=(c>>>15^32767)&65535),a=e[k>>1]|0,T=e[t1>>1]|0,d1=s[d1>>2]|0,h=e[b>>1]|0,t1=10-w1|0,I=t1<<16>>16,(t1&65535)<<16>>16<0?(c=0-I<<16,(c|0)<983040?l=h<<16>>16>>(c>>16)&65535:l=0):(c=h<<16>>16,h=c<<I,(h<<16>>16>>I|0)==(c|0)?l=h&65535:l=(c>>>15^32767)&65535),n=a<<16>>16,c=y(n,n)|0,c>>>0>1073741823?(s[N>>2]=1,a=32767):a=c>>>15,F=n1(32767-(T&65535)&65535,1,N)|0,T=T<<16>>16,c=y(e[m1+2>>1]|0,T)|0,(c|0)==1073741824?(s[N>>2]=1,c=2147483647):c=c<<1,t1=c<<1,c=y(((t1>>1|0)==(c|0)?t1:c>>31^2147418112)>>16,a<<16>>16)|0,(c|0)==1073741824?(s[N>>2]=1,C=2147483647):C=c<<1,x=(D[$+2>>1]|0)+65521|0,I=x&65535,c=y(e[m1+4>>1]|0,T)|0,(c|0)==1073741824?(s[N>>2]=1,a=2147483647):a=c<<1,c=a<<1,c=(y(((c>>1|0)==(a|0)?c:a>>31^2147418112)>>16,n)|0)>>15,(c|0)>32767&&(s[N>>2]=1,c=32767),e[F1>>1]=c,a=l1&65535,e[Q1>>1]=a,a=n1(e[$+4>>1]|0,a,N)|0,c=y(e[z>>1]|0,T)|0,(c|0)==1073741824?(s[N>>2]=1,c=2147483647):c=c<<1,A=c<<1,e[q1>>1]=((A>>1|0)==(c|0)?A:c>>31^2147418112)>>>16,A=((w1<<17>>17|0)==(w1|0)?w1<<1:w1>>>15^32767)+65529&65535,e[Q1>>1]=A,A=n1(e[M>>1]|0,A,N)|0,c=(y(e[z>>1]|0,F<<16>>16)|0)>>15,(c|0)>32767&&(s[N>>2]=1,c=32767),e[L1>>1]=c,F=n1(A,1,N)|0,h=y(e[m1>>1]|0,T)|0,(h|0)==1073741824?(s[N>>2]=1,c=2147483647):c=h<<1,M=e6(c,Q1,N)|0,n=(D[Q1>>1]|0)+47|0,e[Q1>>1]=n,n=(D[$>>1]|0)-(n&65535)|0,T=n+31&65535,T=I<<16>>16>T<<16>>16?I:T,T=a<<16>>16>T<<16>>16?a:T,T=A<<16>>16>T<<16>>16?A:T,T=(F<<16>>16>T<<16>>16?F:T)<<16>>16,h=T-(x&65535)|0,c=h&65535,h=h<<16>>16,c<<16>>16>0?U=c<<16>>16<31?C>>h:0:($=0-h<<16>>16,U=C<<$,U=(U>>$|0)==(C|0)?U:C>>31^2147483647),I=T-(a&65535)|0,c=I&65535,h=D[F1>>1]<<16,I=I<<16>>16,c<<16>>16>0?h=c<<16>>16<31?h>>I:0:(m1=0-I<<16>>16,$=h<<m1,h=($>>m1|0)==(h|0)?$:h>>31^2147483647),N0(h,F1,P1,N),A=T-(A&65535)|0,h=A&65535,I=D[q1>>1]<<16,A=A<<16>>16,h<<16>>16>0?h=h<<16>>16<31?I>>A:0:($=0-A<<16>>16,h=I<<$,h=(h>>$|0)==(I|0)?h:I>>31^2147483647),N0(h,q1,U1,N),A=T-(F&65535)|0,h=A&65535,I=D[L1>>1]<<16,A=A<<16>>16,h<<16>>16>0?h=h<<16>>16<31?I>>A:0:($=0-A<<16>>16,h=I<<$,h=(h>>$|0)==(I|0)?h:I>>31^2147483647),N0(h,L1,Z1,N),A=T+65505|0,e[Q1>>1]=A,A=A-(n&65535)|0,h=W1(A&65535,1,N)|0,I=h<<16>>16,h<<16>>16>0?I=h<<16>>16<31?M>>I:0:($=0-I<<16>>16,I=M<<$,I=(I>>$|0)==(M|0)?I:M>>31^2147483647);do if(!(A&1))C=I;else{if(N0(I,v1,H,N),h=e[H>>1]|0,I=e[v1>>1]|0,(I*23170|0)==1073741824?(s[N>>2]=1,A=2147483647):A=I*46340|0,v1=(h<<16>>16)*23170>>15,I=A+(v1<<1)|0,!((A^v1|0)>0&(I^A|0)<0)){C=I;break}s[N>>2]=1,C=(A>>>31)+2147483647|0}while(!1);for(z=(U>>>31)+2147483647|0,M=2147483647,x=0,I=0,j=d1;h=(y(e[j>>1]|0,z1)|0)>>15,j=j+6|0,(h|0)>32767&&(s[N>>2]=1,h=32767),A=h&65535,!(A<<16>>16>=l<<16>>16);){a=h<<16>>16,h=y(a,a)|0,(h|0)==1073741824?(s[N>>2]=1,c=2147483647):c=h<<1,N0(c,l0,t0,N),h=(E1(A,m,N)|0)<<16>>16,h=y(h,h)|0,(h|0)==1073741824?(s[N>>2]=1,h=2147483647):h=h<<1,N0(h,f0,j1,N),A=e[P1>>1]|0,c=y(e[F1>>1]|0,a)|0;do if((c|0)==1073741824)s[N>>2]=1,c=2147483647;else{if(h=(c<<1)+U|0,!((c^U|0)>0&(h^U|0)<0)){c=h;break}s[N>>2]=1,c=z}while(!1);h=(y(A<<16>>16,a)|0)>>15,(h|0)>32767&&(s[N>>2]=1,h=32767),v1=h<<16,h=(v1>>15)+c|0,(v1>>16^c|0)>0&(h^c|0)<0&&(s[N>>2]=1,h=(c>>>31)+2147483647|0),n=e[U1>>1]|0,F=e[t0>>1]|0,a=e[q1>>1]|0,T=e[l0>>1]|0,c=y(T,a)|0;do if((c|0)==1073741824)s[N>>2]=1,A=2147483647;else{if(A=(c<<1)+h|0,!((c^h|0)>0&(A^h|0)<0))break;s[N>>2]=1,A=(h>>>31)+2147483647|0}while(!1);c=(y(F<<16>>16,a)|0)>>15,(c|0)>32767&&(s[N>>2]=1,c=32767),v1=c<<16,c=(v1>>15)+A|0,(v1>>16^A|0)>0&(c^A|0)<0&&(s[N>>2]=1,c=(A>>>31)+2147483647|0),h=(y(T,n<<16>>16)|0)>>15,(h|0)>32767&&(s[N>>2]=1,h=32767),v1=h<<16,h=(v1>>15)+c|0,(v1>>16^c|0)>0&(h^c|0)<0&&(s[N>>2]=1,h=(c>>>31)+2147483647|0),h=e6(h,Q1,N)|0,A=W1(e[Q1>>1]|0,1,N)|0,c=A<<16>>16,A<<16>>16>0?A=A<<16>>16<31?h>>c:0:(v1=0-c<<16>>16,A=h<<v1,A=(A>>v1|0)==(h|0)?A:h>>31^2147483647),h=A-C|0,((h^A)&(A^C)|0)<0&&(s[N>>2]=1,h=(A>>>31)+2147483647|0),h=(S1(h,N)|0)<<16>>16,h=y(h,h)|0,(h|0)==1073741824?(s[N>>2]=1,A=2147483647):A=h<<1,T=e[Z1>>1]|0,a=e[j1>>1]|0,F=e[L1>>1]|0,n=e[f0>>1]|0,c=y(n,F)|0;do if((c|0)==1073741824)s[N>>2]=1,h=2147483647;else{if(h=(c<<1)+A|0,!((c^A|0)>0&(h^A|0)<0))break;s[N>>2]=1,h=(A>>>31)+2147483647|0}while(!1);if(c=(y(a<<16>>16,F)|0)>>15,(c|0)>32767&&(s[N>>2]=1,c=32767),v1=c<<16,c=(v1>>15)+h|0,(v1>>16^h|0)>0&(c^h|0)<0&&(s[N>>2]=1,c=(h>>>31)+2147483647|0),h=(y(n,T<<16>>16)|0)>>15,(h|0)>32767&&(s[N>>2]=1,h=32767),v1=h<<16,h=(v1>>15)+c|0,(v1>>16^c|0)>0&(h^c|0)<0&&(s[N>>2]=1,h=(c>>>31)+2147483647|0),c=(h|0)<(M|0),I=c?x:I,x=x+1<<16>>16,x<<16>>16>=32)break;M=c?h:M}t0=(I<<16>>16)*3|0,A=e[d1+(t0<<1)>>1]|0,e[E>>1]=e[d1+(t0+1<<1)>>1]|0,e[R>>1]=e[d1+(t0+2<<1)>>1]|0,A=y(A<<16>>16,z1)|0,(A|0)==1073741824?(s[N>>2]=1,A=2147483647):A=A<<1,i1?A=r1<<16>>16<31?A>>W:0:(E=0-W<<16>>16,N=A<<E,A=(N>>E|0)==(A|0)?N:A>>31^2147483647),e[b>>1]=A>>>16,N=I,E=s[S>>2]|0,b=E+2|0,e[E>>1]=c1,E=E+4|0,s[S>>2]=E,e[b>>1]=N,O=H1}function gi(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0;for(w=(i|0)==7,u=e[o>>1]|0,w?(u=u<<16>>16>>>1&65535,m=x0(r,t,l)|0,r=m<<16,i=r>>16,(m<<20>>20|0)==(i|0)?i=r>>12:i=i>>>15^32767):(m=x0(r,t,l)|0,r=m<<16,i=r>>16,(m<<21>>21|0)==(i|0)?i=r>>11:i=i>>>15^32767),m=i<<16>>16,l=u<<16>>16,r=l-((y(m,e[f>>1]|0)|0)>>>15&65535)|0,r=(r&32768|0?0-r|0:r)&65535,c=1,i=0,d=f;d=d+6|0,u=l-((y(e[d>>1]|0,m)|0)>>>15&65535)|0,t=u<<16,u=(t|0)<0?0-(t>>16)|0:u,t=(u<<16>>16|0)<(r<<16>>16|0),i=t?c:i,c=c+1<<16>>16,!(c<<16>>16>=32);)r=t?u&65535:r;return d=(i<<16>>16)*196608>>16,e[o>>1]=(y(e[f+(d<<1)>>1]|0,m)|0)>>>15<<(w&1),e[n>>1]=e[f+(d+1<<1)>>1]|0,e[a>>1]=e[f+(d+2<<1)>>1]|0,i|0}function l4(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0;for(l=E1(e[t>>1]|0,e[a>>1]|0,f)|0,l=(l&65535)-((l&65535)>>>15&65535)|0,l=(l<<16>>31^l)&65535,c=0,d=1;u=e[a+(d<<1)>>1]|0,u<<16>>16>r<<16>>16?u=l:(u=E1(e[t>>1]|0,u,f)|0,u=(u&65535)-((u&65535)>>>15&65535)|0,u=(u<<16>>31^u)&65535,w=u<<16>>16<l<<16>>16,u=w?u:l,c=w?d&65535:c),d=d+1|0,(d|0)!=16;)l=u;if((i|0)!=5)return l=e[a+(c<<16>>16<<1)>>1]|0,(i|0)==7?(e[t>>1]=l&65532,c|0):(e[t>>1]=l,c|0);switch(u=c<<16>>16,c<<16>>16){case 0:{l=0;break}case 15:{m=8;break}default:(e[a+(u+1<<1)>>1]|0)>r<<16>>16?m=8:l=u+65535&65535}return(m|0)==8&&(l=u+65534&65535),e[n>>1]=l,w=l<<16>>16,e[o>>1]=e[a+(w<<1)>>1]|0,w=w+1|0,e[n+2>>1]=w,w=w<<16>>16,e[o+2>>1]=e[a+(w<<1)>>1]|0,w=w+1|0,e[n+4>>1]=w,e[o+4>>1]=e[a+(w<<16>>16<<1)>>1]|0,e[t>>1]=e[a+(u<<1)>>1]|0,c|0}function yi(i,r,t,o,n,a,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0;switch(U=O,O=O+32|0,k=U+20|0,b=U+10|0,h=U,i|0){case 3:case 4:case 6:{d=d+84|0,V=128;break}default:d=d+80|0,V=64}for(K=s[d>>2]|0,w=x0(14,t,m)|0,G=r<<16>>16,j=G+65525|0,i=(D[n>>1]|0)+65523&65535,e[h>>1]=i,r=(D[n+2>>1]|0)+65522&65535,e[h+2>>1]=r,z=j<<16>>16,z=n1(e[n+4>>1]|0,((j<<17>>17|0)==(z|0)?j<<1:z>>>15^32767)+15&65535,m)|0,e[h+4>>1]=z,j=n1(e[n+6>>1]|0,j&65535,m)|0,e[h+6>>1]=j,n=n1(e[n+8>>1]|0,G+65526&65535,m)|0,e[h+8>>1]=n,r=r<<16>>16>i<<16>>16?r:i,r=z<<16>>16>r<<16>>16?z:r,r=j<<16>>16>r<<16>>16?j:r,r=(n<<16>>16>r<<16>>16?n:r)+1&65535,n=0;t=r-(i&65535)|0,d=t&65535,i=D[o+(n<<1)>>1]<<16,t=t<<16>>16,d<<16>>16>0?d=d<<16>>16<31?i>>t:0:(j=0-t<<16>>16,d=i<<j,d=(d>>j|0)==(i|0)?d:i>>31^2147483647),N0(d,k+(n<<1)|0,b+(n<<1)|0,m),d=n+1|0,(d|0)!=5;)i=e[h+(d<<1)>>1]|0,n=d;for(j=w<<16>>16,N=e[k>>1]|0,A=e[b>>1]|0,F=e[k+2>>1]|0,T=e[b+2>>1]|0,x=e[k+4>>1]|0,M=e[b+4>>1]|0,C=e[k+6>>1]|0,z=e[b+6>>1]|0,I=e[k+8>>1]|0,E=e[b+8>>1]|0,r=2147483647,R=0,d=0,S=K;n=e[S>>1]|0,n<<16>>16>a<<16>>16?w=r:(w=(y(e[S+2>>1]|0,j)|0)>>15,(w|0)>32767&&(s[m>>2]=1,w=32767),b=n<<16>>16,n=y(b,b)|0,n>>>0>1073741823?(s[m>>2]=1,h=32767):h=n>>>15,t=w<<16>>16,w=y(t,t)|0,w>>>0>1073741823?(s[m>>2]=1,k=32767):k=w>>>15,o=(y(t,b)|0)>>15,(o|0)>32767&&(s[m>>2]=1,o=32767),w=h<<16>>16,h=y(N,w)|0,(h|0)==1073741824?(s[m>>2]=1,n=2147483647):n=h<<1,w=(y(A,w)|0)>>15,h=n+(w<<1)|0,(n^w|0)>0&(h^n|0)<0&&(s[m>>2]=1,h=(n>>>31)+2147483647|0),w=y(F,b)|0,(w|0)==1073741824?(s[m>>2]=1,n=2147483647):n=w<<1,b=(y(T,b)|0)>>15,w=n+(b<<1)|0,(n^b|0)>0&(w^n|0)<0&&(s[m>>2]=1,w=(n>>>31)+2147483647|0),n=w+h|0,(w^h|0)>-1&(n^h|0)<0&&(s[m>>2]=1,n=(h>>>31)+2147483647|0),w=k<<16>>16,h=y(x,w)|0,(h|0)==1073741824?(s[m>>2]=1,i=2147483647):i=h<<1,b=(y(M,w)|0)>>15,h=i+(b<<1)|0,(i^b|0)>0&(h^i|0)<0&&(s[m>>2]=1,h=(i>>>31)+2147483647|0),w=h+n|0,(h^n|0)>-1&(w^n|0)<0?(s[m>>2]=1,i=(n>>>31)+2147483647|0):i=w,w=y(C,t)|0,(w|0)==1073741824?(s[m>>2]=1,h=2147483647):h=w<<1,b=(y(z,t)|0)>>15,w=h+(b<<1)|0,(h^b|0)>0&(w^h|0)<0&&(s[m>>2]=1,w=(h>>>31)+2147483647|0),n=w+i|0,(w^i|0)>-1&(n^i|0)<0?(s[m>>2]=1,h=(i>>>31)+2147483647|0):h=n,n=o<<16>>16,w=y(I,n)|0,(w|0)==1073741824?(s[m>>2]=1,i=2147483647):i=w<<1,b=(y(E,n)|0)>>15,w=i+(b<<1)|0,(i^b|0)>0&(w^i|0)<0?(s[m>>2]=1,n=(i>>>31)+2147483647|0):n=w,w=n+h|0,(n^h|0)>-1&(w^h|0)<0&&(s[m>>2]=1,w=(h>>>31)+2147483647|0),b=(w|0)<(r|0),w=b?w:r,d=b?R:d),S=S+8|0,R=R+1<<16>>16,!((R<<16>>16|0)>=(V|0));)r=w;return a=d<<16>>16,a=((a<<18>>18|0)==(a|0)?a<<2:a>>>15^32767)<<16>>16,e[f>>1]=e[K+(a<<1)>>1]|0,r=e[K+(a+1<<1)>>1]|0,e[u>>1]=e[K+(a+2<<1)>>1]|0,e[c>>1]=e[K+(a+3<<1)>>1]|0,r=y(r<<16>>16,j)|0,(r|0)==1073741824?(s[m>>2]=1,i=2147483647):i=r<<1,t=10-G|0,r=t&65535,t=t<<16>>16,r<<16>>16>0?(m=r<<16>>16<31?i>>t:0,m=m>>>16,m=m&65535,e[l>>1]=m,O=U,d|0):(u=0-t<<16>>16,m=i<<u,m=(m>>u|0)==(i|0)?m:i>>31^2147483647,m=m>>>16,m=m&65535,e[l>>1]=m,O=U,d|0)}function u4(i,r,t,o,n,a,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0,v1=0,F1=0,P1=0,q1=0,U1=0,L1=0,Z1=0,z1=0,f0=0,j1=0,Q1=0,l0=0,t0=0,H1=0,m0=0,j0=0,Y0=0,v0=0,d0=0,u0=0,O1=0,p0=0,g2=0,y2=0,I0=0,l2=0;if(l2=O,O=O+160|0,I0=l2,d=i<<16>>16,g2=i<<16>>16==10,y2=e[f+(e[a>>1]<<1)>>1]|0,i<<16>>16>0)for(u=0,c=l;e[c>>1]=u,u=u+1<<16>>16,!(u<<16>>16>=i<<16>>16);)c=c+2|0;if(t<<16>>16<=1){O=l2;return}for(O1=a+2|0,p0=y2<<16>>16,v0=o+(p0<<1)|0,d0=n+(p0*80|0)+(p0<<1)|0,u0=a+6|0,H=r&65535,v1=a+4|0,F1=a+10|0,P1=a+8|0,q1=a+14|0,U1=a+12|0,L1=a+18|0,Z1=a+16|0,z1=l+2|0,f0=l+4|0,j1=l+6|0,Q1=l+8|0,l0=l+10|0,t0=l+12|0,H1=l+14|0,m0=l+16|0,j0=l+18|0,Y0=i<<16>>16>2,m1=a+(d+-1<<1)|0,l1=1,$=1,r1=0,s1=0,w1=-1;;){if(t1=e[f+(e[O1>>1]<<1)>>1]|0,d1=t1<<16>>16,r=(D[o+(d1<<1)>>1]|0)+(D[v0>>1]|0)|0,c=(e[n+(p0*80|0)+(d1<<1)>>1]<<13)+32768+((e[n+(d1*80|0)+(d1<<1)>>1]|0)+(e[d0>>1]|0)<<12)|0,d=e[u0>>1]|0,d<<16>>16<40){for(d=d<<16>>16,m=I0;c1=(e[n+(d*80|0)+(d<<1)>>1]|0)>>>1,W=e[n+(d*80|0)+(p0<<1)>>1]|0,h1=e[n+(d*80|0)+(d1<<1)>>1]|0,e[m>>1]=r+(D[o+(d<<1)>>1]|0),e[m+2>>1]=(W+2+c1+h1|0)>>>2,d=d+H|0,(d&65535)<<16>>16<40;)d=d<<16>>16,m=m+4|0;x=e[u0>>1]|0}else x=d;r=e[v1>>1]|0,T=c>>12,d=r<<16>>16;e:do if(r<<16>>16<40){if(F=x<<16>>16,x<<16>>16<40)m=1,h=r,b=x,k=0,w=-1;else for(;;)if(d=d+H|0,(d&65535)<<16>>16<40)d=d<<16>>16;else{m=1,h1=r,c1=x,d=0;break e}for(;;){for(A=((e[n+(d*80|0)+(d<<1)>>1]|0)+T>>1)+(e[n+(d*80|0)+(p0<<1)>>1]|0)+(e[n+(d*80|0)+(d1<<1)>>1]|0)|0,N=D[o+(d<<1)>>1]|0,S=F,I=x,R=I0,E=k;c=(D[R>>1]|0)+N|0,u=c<<16>>16,u=(y(u,u)|0)>>>15,k=(A+(e[n+(d*80|0)+(S<<1)>>1]|0)>>2)+(e[R+2>>1]|0)>>1,(y(u<<16>>16,m<<16>>16)|0)>(y(k,w<<16>>16)|0)?(m=k&65535,h=r,b=I,k=c&65535,w=u&65535):k=E,c=S+H|0,I=c&65535,!(I<<16>>16>=40);)S=c<<16>>16,R=R+4|0,E=k;if(d=d+H|0,r=d&65535,r<<16>>16<40)d=d<<16>>16;else{h1=h,c1=b,d=k;break}}}else m=1,h1=r,c1=x,d=0;while(!1);if(h=m<<16>>16<<15,m=e[F1>>1]|0,m<<16>>16<40){for(c=h1<<16>>16,u=c1<<16>>16,r=d&65535,m=m<<16>>16,d=I0;U=e[n+(m*80|0)+(m<<1)>>1]>>1,V=e[n+(m*80|0)+(p0<<1)>>1]|0,q=e[n+(m*80|0)+(d1<<1)>>1]|0,i1=e[n+(m*80|0)+(c<<1)>>1]|0,W=e[n+(m*80|0)+(u<<1)>>1]|0,e[d>>1]=(D[o+(m<<1)>>1]|0)+r,e[d+2>>1]=(V+2+U+q+i1+W|0)>>>2,m=m+H|0,(m&65535)<<16>>16<40;)m=m<<16>>16,d=d+4|0;U=e[F1>>1]|0}else U=m;w=e[P1>>1]|0,m=w<<16>>16;e:do if(w<<16>>16<40){if(M=h1<<16>>16,C=c1<<16>>16,z=U<<16>>16,x=h+32768|0,U<<16>>16<40)k=1,h=w,r=U,b=w,d=0,w=-1;else for(;;)if(m=m+H|0,(m&65535)<<16>>16<40)m=m<<16>>16;else{m=1,W=w,i1=U,d=0;break e}for(;;){for(u=D[o+(m<<1)>>1]|0,T=(e[n+(m*80|0)+(d1<<1)>>1]|0)+(e[n+(m*80|0)+(p0<<1)>>1]|0)+(e[n+(m*80|0)+(M<<1)>>1]|0)+(e[n+(m*80|0)+(C<<1)>>1]|0)|0,F=x+(e[n+(m*80|0)+(m<<1)>>1]<<11)|0,N=z,S=U,A=I0;;)if(E=(D[A>>1]|0)+u|0,c=F+(e[A+2>>1]<<14)+(T+(e[n+(m*80|0)+(N<<1)>>1]|0)<<12)|0,R=E<<16>>16,R=(y(R,R)|0)>>>15,(y(R<<16>>16,k<<16>>16)|0)>(y(c>>16,w<<16>>16)|0)?(k=c>>>16&65535,I=b,r=S,d=E&65535,w=R&65535):I=h,h=N+H|0,S=h&65535,S<<16>>16>=40){h=I;break}else N=h<<16>>16,h=I,A=A+4|0;if(m=m+H|0,b=m&65535,b<<16>>16<40)m=m<<16>>16;else{m=k,W=h,i1=r;break}}}else m=1,W=w,i1=U,d=0;while(!1);if(k=m<<16>>16<<15,m=e[q1>>1]|0,m<<16>>16<40){for(c=h1<<16>>16,u=c1<<16>>16,w=W<<16>>16,h=i1<<16>>16,r=d&65535,m=m<<16>>16,d=I0;j=e[n+(m*80|0)+(m<<1)>>1]>>1,z=e[n+(p0*80|0)+(m<<1)>>1]|0,G=e[n+(d1*80|0)+(m<<1)>>1]|0,K=e[n+(c*80|0)+(m<<1)>>1]|0,V=e[n+(u*80|0)+(m<<1)>>1]|0,U=e[n+(w*80|0)+(m<<1)>>1]|0,q=e[n+(h*80|0)+(m<<1)>>1]|0,e[d>>1]=(D[o+(m<<1)>>1]|0)+r,e[d+2>>1]=(z+4+j+G+K+V+U+q|0)>>>3,m=m+H|0,(m&65535)<<16>>16<40;)m=m<<16>>16,d=d+4|0;r=e[q1>>1]|0}else r=m;if(b=e[U1>>1]|0,b<<16>>16<40)for(U=h1<<16>>16,j=c1<<16>>16,z=W<<16>>16,C=i1<<16>>16,M=r<<16>>16,x=r<<16>>16<40,G=k+32768|0,V=b<<16>>16,u=1,I=b,S=r,K=b,h=0,m=-1;;){if(x)for(k=D[o+(V<<1)>>1]|0,d=(e[n+(V*80|0)+(d1<<1)>>1]|0)+(e[n+(V*80|0)+(p0<<1)>>1]|0)+(e[n+(V*80|0)+(U<<1)>>1]|0)+(e[n+(V*80|0)+(j<<1)>>1]|0)+(e[n+(V*80|0)+(z<<1)>>1]|0)+(e[n+(V*80|0)+(C<<1)>>1]|0)|0,w=G+(e[n+(V*80|0)+(V<<1)>>1]<<10)|0,R=M,b=r,F=S,T=I0;A=(D[T>>1]|0)+k|0,S=w+(e[T+2>>1]<<14)+(d+(e[n+(V*80|0)+(R<<1)>>1]|0)<<11)|0,N=A<<16>>16,N=(y(N,N)|0)>>>15,(y(N<<16>>16,u<<16>>16)|0)>(y(S>>16,m<<16>>16)|0)?(u=S>>>16&65535,I=K,S=b,h=A&65535,m=N&65535):S=F,E=R+H|0,b=E&65535,!(b<<16>>16>=40);)R=E<<16>>16,F=S,T=T+4|0;if(b=V+H|0,K=b&65535,K<<16>>16>=40){q=S;break}else V=b<<16>>16}else u=1,I=b,q=r,h=0,m=-1;if(g2){if(R=u<<16>>16<<15,m=e[L1>>1]|0,m<<16>>16<40){for(d=h1<<16>>16,r=c1<<16>>16,c=W<<16>>16,u=i1<<16>>16,k=I<<16>>16,b=q<<16>>16,w=h&65535,m=m<<16>>16,h=I0;z=e[n+(m*80|0)+(m<<1)>>1]>>1,C=e[n+(p0*80|0)+(m<<1)>>1]|0,j=e[n+(d1*80|0)+(m<<1)>>1]|0,G=e[n+(d*80|0)+(m<<1)>>1]|0,K=e[n+(r*80|0)+(m<<1)>>1]|0,V=e[n+(c*80|0)+(m<<1)>>1]|0,U=e[n+(u*80|0)+(m<<1)>>1]|0,r1=e[n+(k*80|0)+(m<<1)>>1]|0,s1=e[n+(b*80|0)+(m<<1)>>1]|0,e[h>>1]=(D[o+(m<<1)>>1]|0)+w,e[h+2>>1]=(C+4+z+j+G+K+V+U+r1+s1|0)>>>3,m=m+H|0,(m&65535)<<16>>16<40;)m=m<<16>>16,h=h+4|0;U=e[L1>>1]|0}else U=m;if(k=e[Z1>>1]|0,k<<16>>16<40)for(z=h1<<16>>16,C=c1<<16>>16,M=W<<16>>16,c=i1<<16>>16,j=I<<16>>16,G=q<<16>>16,K=U<<16>>16,V=U<<16>>16<40,x=R+32768|0,d=k<<16>>16,u=1,b=k,h=U,r=k,m=-1;;){if(V)for(R=D[o+(d<<1)>>1]|0,w=(e[n+(d1*80|0)+(d<<1)>>1]|0)+(e[n+(p0*80|0)+(d<<1)>>1]|0)+(e[n+(z*80|0)+(d<<1)>>1]|0)+(e[n+(C*80|0)+(d<<1)>>1]|0)+(e[n+(M*80|0)+(d<<1)>>1]|0)+(e[n+(c*80|0)+(d<<1)>>1]|0)+(e[n+(j*80|0)+(d<<1)>>1]|0)+(e[n+(G*80|0)+(d<<1)>>1]|0)|0,k=x+(e[n+(d*80|0)+(d<<1)>>1]<<9)|0,T=K,N=U,F=I0;A=(D[F>>1]|0)+R<<16>>16,A=(y(A,A)|0)>>>15,S=k+(e[F+2>>1]<<13)+(w+(e[n+(d*80|0)+(T<<1)>>1]|0)<<10)|0,(y(A<<16>>16,u<<16>>16)|0)>(y(S>>16,m<<16>>16)|0)&&(u=S>>>16&65535,b=r,h=N,m=A&65535),E=T+H|0,N=E&65535,!(N<<16>>16>=40);)T=E<<16>>16,F=F+4|0;if(k=d+H|0,r=k&65535,r<<16>>16>=40)break;d=k<<16>>16}else u=1,b=k,h=U,m=-1}else b=r1,h=s1;if((y(m<<16>>16,l1<<16>>16)|0)>(y(u<<16>>16,w1<<16>>16)|0)?(e[l>>1]=y2,e[z1>>1]=t1,e[f0>>1]=h1,e[j1>>1]=c1,e[Q1>>1]=W,e[l0>>1]=i1,e[t0>>1]=I,e[H1>>1]=q,g2&&(e[m0>>1]=b,e[j0>>1]=h)):(u=l1,m=w1),d=e[O1>>1]|0,Y0)for(r=1,c=2;e[a+(r<<1)>>1]=e[a+(c<<1)>>1]|0,c=c+1|0,(c&65535)<<16>>16!=i<<16>>16;)r=r+1|0;if(e[m1>>1]=d,$=$+1<<16>>16,$<<16>>16>=t<<16>>16)break;l1=u,r1=b,s1=h,w1=m}O=l2}function p5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0;for(l=39;f=i+(l<<1)|0,a=e[f>>1]|0,n=r+(l<<1)|0,a<<16>>16>-1?e[n>>1]=32767:(e[n>>1]=-32767,a<<16>>16==-32768?a=32767:a=0-(a&65535)&65535,e[f>>1]=a),e[t+(l<<1)>>1]=a,(l|0)>0;)l=l+-1|0;if(c=8-(o<<16>>16)|0,(c|0)>0)u=0,n=0;else return;do{for(o=0,i=0,f=32767;r=e[t+(o<<1)>>1]|0,l=r<<16>>16>-1?r<<16>>16<f<<16>>16:0,n=l?i:n,a=o+5|0,i=a&65535,!(i<<16>>16>=40);)o=a<<16>>16,f=l?r:f;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));u=0;do{for(r=1,i=1,a=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<a<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,a=l?o:a;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));u=0;do{for(r=2,i=2,a=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<a<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,a=l?o:a;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));for(u=0;;){for(r=3,i=3,a=32767;;)if(o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<a<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,i<<16>>16>=40){a=n;break}else r=f<<16>>16,a=l?o:a;if(e[t+(a<<16>>16<<1)>>1]=-1,u=u+1<<16>>16,(u<<16>>16|0)>=(c|0)){n=0;break}else n=a}do{for(r=4,i=4,u=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<u<<16>>16:0,a=l?i:a,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,u=l?o:u;e[t+(a<<16>>16<<1)>>1]=-1,n=n+1<<16>>16}while((n<<16>>16|0)<(c|0))}function c4(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0;for(A=O,O=O+80|0,N=A,w=40,h=r,k=i,c=256,d=256;u=e[h>>1]|0,h=h+2|0,u=y(u,u)|0,(u|0)!=1073741824?(m=(u<<1)+c|0,(u^c|0)>0&(m^c|0)<0?(s[l>>2]=1,c=(c>>>31)+2147483647|0):c=m):(s[l>>2]=1,c=2147483647),I=e[k>>1]|0,d=(y(I<<1,I)|0)+d|0,w=w+-1<<16>>16,w<<16>>16;)k=k+2|0;for(I=t2(c,l)|0,R=I<<5,I=((R>>5|0)==(I|0)?R:I>>31^2147418112)>>16,R=(t2(d,l)|0)<<5>>16,S=39,b=r+78|0,E=N+78|0,u=t+78|0;k=y(e[b>>1]|0,I)|0,b=b+-2|0,h=k<<1,r=i+(S<<1)|0,c=e[r>>1]|0,w=y(c<<16>>16,R)|0,(w|0)!=1073741824?(m=(w<<1)+h|0,(w^h|0)>0&(m^h|0)<0&&(s[l>>2]=1,m=(k>>>30&1)+2147483647|0)):(s[l>>2]=1,m=2147483647),d=m<<10,d=S1((d>>10|0)==(m|0)?d:m>>31^2147483647,l)|0,d<<16>>16>-1?e[u>>1]=32767:(e[u>>1]=-32767,d<<16>>16==-32768?d=32767:d=0-(d&65535)&65535,c<<16>>16==-32768?m=32767:m=0-(c&65535)&65535,e[r>>1]=m),u=u+-2|0,e[E>>1]=d,!((S|0)<=0);)S=S+-1|0,E=E+-2|0;if(r=n<<16>>16,n<<16>>16<=0){e[a+(r<<1)>>1]=e[a>>1]|0,O=A;return}for(k=f&65535,h=0,w=-1,u=0;;){if((h|0)<40)for(d=h,m=h&65535,c=-1;l=e[N+(d<<1)>>1]|0,f=l<<16>>16>c<<16>>16,c=f?l:c,u=f?m:u,d=d+k|0,m=d&65535,!(m<<16>>16>=40);)d=d<<16>>16;else c=-1;if(e[o+(h<<1)>>1]=u,c<<16>>16>w<<16>>16?e[a>>1]=h:c=w,h=h+1|0,(h&65535)<<16>>16==n<<16>>16)break;w=c}if(u=e[a>>1]|0,e[a+(r<<1)>>1]=u,n<<16>>16>1)c=1;else{O=A;return}do o=u+1<<16>>16,u=o<<16>>16>=n<<16>>16?0:o,e[a+(c<<1)>>1]=u,e[a+(c+r<<1)>>1]=u,c=c+1|0;while((c&65535)<<16>>16!=n<<16>>16);O=A}function _i(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(12)|0,!r)?(i=-1,i|0):(e[r>>1]=8,s[i>>2]=r,e[r+2>>1]=3,e[r+4>>1]=0,s[r+8>>2]=0,i=0,i|0)}function Ri(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function d4(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;do if((r|0)==8){if(o=i+2|0,n=(e[o>>1]|0)+-1<<16>>16,e[o>>1]=n,r=i+8|0,!(s[r>>2]|0)){s[t>>2]=1,e[o>>1]=3;break}if(a=i+4|0,n<<16>>16>2&(e[a>>1]|0)>0){s[t>>2]=2,e[a>>1]=(e[a>>1]|0)+-1<<16>>16;break}if(n<<16>>16){s[t>>2]=3;break}else{s[t>>2]=2,e[o>>1]=e[i>>1]|0;break}}else e[i+2>>1]=e[i>>1]|0,s[t>>2]=0,r=i+8|0;while(!1);s[r>>2]=s[t>>2]}function Si(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;return!i||(s[i>>2]=0,t=s0(12)|0,o=t,!t)?(i=-1,i|0):(s[t>>2]=0,n=t+4|0,s[n>>2]=0,a=t+8|0,s[a>>2]=r,!((wi(t)|0)<<16>>16)&&!((Ce(n,s[a>>2]|0)|0)<<16>>16)?(mi(s[t>>2]|0)|0,Q6(s[n>>2]|0)|0,s[i>>2]=o,i=0,i|0):(s4(t),z5(n),K1(t),i=-1,i|0))}function Di(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(s4(r),z5((s[i>>2]|0)+4|0),K1(s[i>>2]|0),s[i>>2]=0))}function h4(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0;u=O,O=O+448|0,f=u+320|0,l=u,o2(o|0,0,488)|0,a=0;do c=t+(a<<1)|0,e[c>>1]=(D[c>>1]|0)&65528,a=a+1|0;while((a|0)!=160);pi(s[i>>2]|0,t,160),c=i+4|0,Fe(s[c>>2]|0,r,t,f,n,l)|0,ki(s[n>>2]|0,f,o,(s[c>>2]|0)+2392|0),O=u}function Y5(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0;var E=0,R=0,S=0;S=O,O=O+48|0,E=S+22|0,R=S,X0(n,(i&-2|0)==6?t:r,E),X0(n,o,R),t=d,r=E,n=t+22|0;do e[t>>1]=e[r>>1]|0,t=t+2|0,r=r+2|0;while((t|0)<(n|0));c0(a,d,w,40,c,0),c0(R,w,w,40,c,0),L2(a,f,k,40),t=m,r=k,n=t+80|0;do e[t>>1]=e[r>>1]|0,t=t+2|0,r=r+2|0;while((t|0)<(n|0));c0(a,m,b,40,l,0),L2(E,b,h,40),c0(R,h,h,40,u,0),O=S}function k5(i,r,t,o,n,a,f,l,u,c,d,m,w,h,k,b,E){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0;var R=0,S=0,I=0,N=0,A=0;for((r|0)==7?(I=11,r=o<<16>>16>>>1&65535,R=2):(I=13,r=o,R=1),e[b>>1]=o<<16>>16<13017?o:13017,S=t<<16>>16,k=k+(S<<1)|0,b=r<<16>>16,n=n<<16>>16,t=20,r=u,E=k;u=E+2|0,A=y(e[E>>1]|0,b)|0,N=y(e[u>>1]|0,b)|0,A=(y(e[r>>1]|0,n)|0)+A<<1,N=(y(e[r+2>>1]|0,n)|0)+N<<1<<R,e[E>>1]=((A<<R)+32768|0)>>>16,e[u>>1]=(N+32768|0)>>>16,t=t+-1<<16>>16,t<<16>>16;)r=r+4|0,E=E+4|0;for(r=o<<16>>16,c0(a,k,f+(S<<1)|0,40,m,1),t=30,E=0;N=t+S|0,e[w+(E<<1)>>1]=(D[i+(N<<1)>>1]|0)-(D[f+(N<<1)>>1]|0),N=y(e[c+(t<<1)>>1]|0,r)|0,A=(y(e[d+(t<<1)>>1]|0,n)|0)>>I,e[h+(E<<1)>>1]=(D[l+(t<<1)>>1]|0)-(N>>>14)-A,E=E+1|0,(E|0)!=10;)t=t+1|0}function Ai(i){i=i|0;var r=0;return!i||(s[i>>2]=0,r=s0(16)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,e[r+12>>1]=0,e[r+14>>1]=0,s[i>>2]=r,i=0,i|0)}function Mi(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,e[i+12>>1]=0,e[i+14>>1]=0,i=0,i|0):(i=-1,i|0)}function Pi(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function Ni(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0;return o=D[r+6>>1]|0,t=D[r+8>>1]|0,n=o-t|0,n=(n&65535|0)!=32767?n&65535:32767,a=D[r+10>>1]|0,t=t-a|0,n=(t<<16>>16|0)<(n<<16>>16|0)?t&65535:n,t=D[r+12>>1]|0,a=a-t|0,n=(a<<16>>16|0)<(n<<16>>16|0)?a&65535:n,a=D[r+14>>1]|0,t=t-a|0,n=(t<<16>>16|0)<(n<<16>>16|0)?t&65535:n,a=a-(D[r+16>>1]|0)|0,t=e[r+2>>1]|0,f=D[r+4>>1]|0,r=(t&65535)-f|0,r=(r&65535|0)!=32767?r&65535:32767,o=f-o|0,((a<<16>>16|0)<(n<<16>>16|0)?a&65535:n)<<16>>16<1500||(((o<<16>>16|0)<(r<<16>>16|0)?o&65535:r)<<16>>16|0)<((t<<16>>16>32e3?600:t<<16>>16>30500?800:1100)|0)?(a=(e[i>>1]|0)+1<<16>>16,f=a<<16>>16>11,e[i>>1]=f?12:a,f&1|0):(e[i>>1]=0,0)}function Ii(i,r,t){return i=i|0,r=r|0,t=t|0,r=W1(r,3,t)|0,r=n1(r,e[i+2>>1]|0,t)|0,r=n1(r,e[i+4>>1]|0,t)|0,r=n1(r,e[i+6>>1]|0,t)|0,r=n1(r,e[i+8>>1]|0,t)|0,r=n1(r,e[i+10>>1]|0,t)|0,r=n1(r,e[i+12>>1]|0,t)|0,(n1(r,e[i+14>>1]|0,t)|0)<<16>>16>15565|0}function Oi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;t=i+4|0,e[i+2>>1]=e[t>>1]|0,o=i+6|0,e[t>>1]=e[o>>1]|0,t=i+8|0,e[o>>1]=e[t>>1]|0,o=i+10|0,e[t>>1]=e[o>>1]|0,t=i+12|0,e[o>>1]=e[t>>1]|0,i=i+14|0,e[t>>1]=e[i>>1]|0,e[i>>1]=r<<16>>16>>>3}function Li(i){i=i|0;var r=0,t=0,o=0;if(!i||(s[i>>2]=0,r=s0(128)|0,!r))return o=-1,o|0;t=r+72|0,o=t+46|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return e[r>>1]=150,e[r+36>>1]=150,e[r+18>>1]=150,e[r+54>>1]=0,e[r+2>>1]=150,e[r+38>>1]=150,e[r+20>>1]=150,e[r+56>>1]=0,e[r+4>>1]=150,e[r+40>>1]=150,e[r+22>>1]=150,e[r+58>>1]=0,e[r+6>>1]=150,e[r+42>>1]=150,e[r+24>>1]=150,e[r+60>>1]=0,e[r+8>>1]=150,e[r+44>>1]=150,e[r+26>>1]=150,e[r+62>>1]=0,e[r+10>>1]=150,e[r+46>>1]=150,e[r+28>>1]=150,e[r+64>>1]=0,e[r+12>>1]=150,e[r+48>>1]=150,e[r+30>>1]=150,e[r+66>>1]=0,e[r+14>>1]=150,e[r+50>>1]=150,e[r+32>>1]=150,e[r+68>>1]=0,e[r+16>>1]=150,e[r+52>>1]=150,e[r+34>>1]=150,e[r+70>>1]=0,e[r+118>>1]=13106,e[r+120>>1]=0,e[r+122>>1]=0,e[r+124>>1]=0,e[r+126>>1]=13106,s[i>>2]=r,o=0,o|0}function Ci(i){i=i|0;var r=0,t=0;if(!i)return t=-1,t|0;r=i+72|0,t=r+46|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(t|0));return e[i>>1]=150,e[i+36>>1]=150,e[i+18>>1]=150,e[i+54>>1]=0,e[i+2>>1]=150,e[i+38>>1]=150,e[i+20>>1]=150,e[i+56>>1]=0,e[i+4>>1]=150,e[i+40>>1]=150,e[i+22>>1]=150,e[i+58>>1]=0,e[i+6>>1]=150,e[i+42>>1]=150,e[i+24>>1]=150,e[i+60>>1]=0,e[i+8>>1]=150,e[i+44>>1]=150,e[i+26>>1]=150,e[i+62>>1]=0,e[i+10>>1]=150,e[i+46>>1]=150,e[i+28>>1]=150,e[i+64>>1]=0,e[i+12>>1]=150,e[i+48>>1]=150,e[i+30>>1]=150,e[i+66>>1]=0,e[i+14>>1]=150,e[i+50>>1]=150,e[i+32>>1]=150,e[i+68>>1]=0,e[i+16>>1]=150,e[i+52>>1]=150,e[i+34>>1]=150,e[i+70>>1]=0,e[i+118>>1]=13106,e[i+120>>1]=0,e[i+122>>1]=0,e[i+124>>1]=0,e[i+126>>1]=13106,t=0,t|0}function Fi(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function w4(i,r){i=i|0,r=r|0,e[i+118>>1]=r}function m4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0;t=S1(t,o)|0,!(t<<16>>16<=0)&&(t=t<<16>>16,(t*21298|0)==1073741824?(s[o>>2]=1,n=2147483647):n=t*42596|0,t=r-n|0,((t^r)&(n^r)|0)<0&&(s[o>>2]=1,t=(r>>>31)+2147483647|0),!((t|0)<=0)&&(i=i+104|0,e[i>>1]=D[i>>1]|0|16384))}function G5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;i=i+104|0,o=W1(e[i>>1]|0,1,t)|0,e[i>>1]=o,r<<16>>16&&(e[i>>1]=(W1(o,1,t)|0)&65535|8192)}function Ti(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;if(n=i+112|0,o=E1(e[n>>1]|0,e[r>>1]|0,t)|0,o=(o&65535)-((o&65535)>>>15&65535)|0,o=((o<<16>>31^o)&65535)<<16>>16<4,a=e[r>>1]|0,e[n>>1]=a,r=r+2|0,a=E1(a,e[r>>1]|0,t)|0,a=(a&65535)-((a&65535)>>>15&65535)|0,o=((a<<16>>31^a)&65535)<<16>>16<4?o?2:1:o&1,e[n>>1]=e[r>>1]|0,n=i+102|0,e[n>>1]=W1(e[n>>1]|0,1,t)|0,r=i+110|0,(n1(e[r>>1]|0,o,t)|0)<<16>>16<=3){e[r>>1]=o;return}e[n>>1]=D[n>>1]|0|16384,e[r>>1]=o}function xi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0;M=O,O=O+352|0,c=M+24|0,T=M,f=0,n=0;do o=e[r+(f+-40<<1)>>1]|0,o=y(o,o)|0,(o|0)!=1073741824?(a=(o<<1)+n|0,(o^n|0)>0&(a^n|0)<0?(s[t>>2]=1,n=(n>>>31)+2147483647|0):n=a):(s[t>>2]=1,n=2147483647),f=f+1|0;while((f|0)!=160);d=n,(343039-d&d|0)<0?(s[t>>2]=1,n=(d>>>31)+2147483647|0):n=d+-343040|0,(n|0)<0&&(F=i+102|0,e[F>>1]=D[F>>1]&16383),u=d+-15e3|0,m=(14999-d&d|0)<0,m?(s[t>>2]=1,a=(d>>>31)+2147483647|0):a=u,(a|0)<0&&(F=i+108|0,e[F>>1]=D[F>>1]&16383),o=i+72|0,l=i+74|0,a=e[o>>1]|0,f=e[l>>1]|0,n=0;do F=n<<2,N=E1((e[r+(F<<1)>>1]|0)>>>2&65535,((a<<16>>16)*21955|0)>>>15&65535,t)|0,R=((N<<16>>16)*21955|0)>>>15&65535,E=n1(a,R,t)|0,I=F|1,A=E1((e[r+(I<<1)>>1]|0)>>>2&65535,((f<<16>>16)*6390|0)>>>15&65535,t)|0,S=((A<<16>>16)*6390|0)>>>15&65535,a=n1(f,S,t)|0,e[c+(F<<1)>>1]=n1(E,a,t)|0,e[c+(I<<1)>>1]=E1(E,a,t)|0,I=F|2,a=E1((e[r+(I<<1)>>1]|0)>>>2&65535,R,t)|0,N=n1(N,((a<<16>>16)*21955|0)>>>15&65535,t)|0,F=F|3,f=E1((e[r+(F<<1)>>1]|0)>>>2&65535,S,t)|0,A=n1(A,((f<<16>>16)*6390|0)>>>15&65535,t)|0,e[c+(I<<1)>>1]=n1(N,A,t)|0,e[c+(F<<1)>>1]=E1(N,A,t)|0,n=n+1|0;while((n|0)!=40);e[o>>1]=a,e[l>>1]=f,f=i+76|0,a=i+80|0,n=0;do F=n<<2,p4(c+(F<<1)|0,c+((F|2)<<1)|0,f,t),p4(c+((F|1)<<1)|0,c+((F|3)<<1)|0,a,t),n=n+1|0;while((n|0)!=40);f=i+84|0,a=i+86|0,n=i+92|0,o=0;do F=o<<3,X2(c+(F<<1)|0,c+((F|4)<<1)|0,f,t),X2(c+((F|2)<<1)|0,c+((F|6)<<1)|0,a,t),X2(c+((F|3)<<1)|0,c+((F|7)<<1)|0,n,t),o=o+1|0;while((o|0)!=20);f=i+88|0,a=i+90|0,n=0;do F=n<<4,X2(c+(F<<1)|0,c+((F|8)<<1)|0,f,t),X2(c+((F|4)<<1)|0,c+((F|12)<<1)|0,a,t),n=n+1|0;while((n|0)!=10);b=i2(c,i+70|0,32,40,4,1,15,t)|0,e[T+16>>1]=b,E=i2(c,i+68|0,16,20,8,7,16,t)|0,e[T+14>>1]=E,R=i2(c,i+66|0,16,20,8,3,16,t)|0,e[T+12>>1]=R,S=i2(c,i+64|0,16,20,8,2,16,t)|0,e[T+10>>1]=S,I=i2(c,i+62|0,16,20,8,6,16,t)|0,e[T+8>>1]=I,N=i2(c,i+60|0,8,10,16,4,16,t)|0,e[T+6>>1]=N,A=i2(c,i+58|0,8,10,16,12,16,t)|0,e[T+4>>1]=A,F=i2(c,i+56|0,8,10,16,8,16,t)|0,e[T+2>>1]=F,k=i2(c,i+54|0,8,10,16,0,16,t)|0,e[T>>1]=k,f=0,o=0;do a=i+(o<<1)|0,r=O2(e[a>>1]|0)|0,a=e[a>>1]|0,n=r<<16>>16,r<<16>>16<0?(l=0-n<<16,(l|0)<983040?l=a<<16>>16>>(l>>16)&65535:l=0):(l=a<<16>>16,a=l<<n,(a<<16>>16>>n|0)==(l|0)?l=a&65535:l=(l>>>15^32767)&65535),a=a0(W1(e[T+(o<<1)>>1]|0,1,t)|0,l)|0,h=E1(r,5,t)|0,n=h<<16>>16,h<<16>>16<0?(l=0-n<<16,(l|0)<983040?l=a<<16>>16>>(l>>16):l=0):(a=a<<16>>16,l=a<<n,(l<<16>>16>>n|0)!=(a|0)&&(l=a>>>15^32767)),l=l<<16>>16,l=y(l,l)|0,(l|0)!=1073741824?(a=(l<<1)+f|0,(l^f|0)>0&(a^f|0)<0?(s[t>>2]=1,f=(f>>>31)+2147483647|0):f=a):(s[t>>2]=1,f=2147483647),o=o+1|0;while((o|0)!=9);h=f<<6,f=(((h>>6|0)==(f|0)?h:f>>31^2147418112)>>16)*3641>>15,(f|0)>32767&&(s[t>>2]=1,f=32767),h=e[i>>1]|0,l=h<<16>>16,w=e[i+2>>1]|0,a=(w<<16>>16)+l|0,(w^h)<<16>>16>-1&(a^l|0)<0&&(s[t>>2]=1,a=(l>>>31)+2147483647|0),h=e[i+4>>1]|0,l=h+a|0,(h^a|0)>-1&(l^a|0)<0&&(s[t>>2]=1,l=(a>>>31)+2147483647|0),h=e[i+6>>1]|0,a=h+l|0,(h^l|0)>-1&(a^l|0)<0&&(s[t>>2]=1,a=(l>>>31)+2147483647|0),h=e[i+8>>1]|0,l=h+a|0,(h^a|0)>-1&(l^a|0)<0&&(s[t>>2]=1,l=(a>>>31)+2147483647|0),h=e[i+10>>1]|0,a=h+l|0,(h^l|0)>-1&(a^l|0)<0&&(s[t>>2]=1,a=(l>>>31)+2147483647|0),h=e[i+12>>1]|0,l=h+a|0,(h^a|0)>-1&(l^a|0)<0&&(s[t>>2]=1,l=(a>>>31)+2147483647|0),h=e[i+14>>1]|0,a=h+l|0,(h^l|0)>-1&(a^l|0)<0&&(s[t>>2]=1,a=(l>>>31)+2147483647|0),h=e[i+16>>1]|0,l=h+a|0,(h^a|0)>-1&(l^a|0)<0&&(s[t>>2]=1,l=(a>>>31)+2147483647|0),w=l<<13,w=((w>>13|0)==(l|0)?w:l>>31^2147418112)>>>16&65535,l=(y((E1(w,0,t)|0)<<16>>16,-2808)|0)>>15,(l|0)>32767&&(s[t>>2]=1,l=32767),c=n1(l&65535,1260,t)|0,h=i+100|0,l=W1(e[h>>1]|0,1,t)|0,(f<<16>>16|0)>((c<<16>>16<720?720:c<<16>>16)|0)&&(l=(l&65535|16384)&65535),e[h>>1]=l,m&&(s[t>>2]=1,u=(d>>>31)+2147483647|0),n=e[i+118>>1]|0,m=i+126|0,l=e[m>>1]|0,o=l<<16>>16<19660,o=n<<16>>16<l<<16>>16?o?2621:6553:o?2621:655,r=l&65535,f=r<<16,l=y(o,l<<16>>16)|0,(l|0)==1073741824?(s[t>>2]=1,l=2147483647):l=l<<1,a=f-l|0,((a^f)&(l^f)|0)<0&&(s[t>>2]=1,a=(r>>>15)+2147483647|0),f=y(o,n<<16>>16)|0;do if((f|0)==1073741824)s[t>>2]=1,l=2147483647;else{if(l=a+(f<<1)|0,!((a^f|0)>0&(l^a|0)<0))break;s[t>>2]=1,l=(a>>>31)+2147483647|0}while(!1);r=S1(l,t)|0,d=(u|0)>-1,e[m>>1]=d?r<<16>>16<13106?13106:r:13106,r=i+106|0,e[r>>1]=W1(e[r>>1]|0,1,t)|0,a=i+108|0,l=W1(e[a>>1]|0,1,t)|0,e[a>>1]=l,f=e[m>>1]|0;e:do if(d){do if(f<<16>>16>19660)e[r>>1]=D[r>>1]|16384;else{if(f<<16>>16>16383)break;f=i+116|0,l=0;break e}while(!1);e[a>>1]=l&65535|16384,x=62}else x=62;while(!1);do if((x|0)==62){if(l=i+116|0,f<<16>>16<=22936){f=l,l=0;break}f=l,l=n1(e[l>>1]|0,1,t)|0}while(!1);e[f>>1]=l,(e[r>>1]&32640)!=32640?(c=(e[a>>1]&32767)==32767,e[i+122>>1]=c&1,c&&(x=67)):(e[i+122>>1]=1,x=67);do if((x|0)==67){if(f=i+98|0,(e[f>>1]|0)>=5)break;e[f>>1]=5}while(!1);c=i+102|0;do if((e[c>>1]&24576)==24576)x=71;else{if((e[i+104>>1]&31744)==31744){x=71;break}if(e[h>>1]&32640)a=k,f=0,l=0;else{e[i+98>>1]=20,a=32767;break}for(;;){o=e[i+18+(f<<1)>>1]|0,n=a<<16>>16>o<<16>>16,u=n?a:o,a=n?o:a,u=u<<16>>16<184?184:u,a=a<<16>>16<184?184:a,o=O2(a)|0,n=o<<16>>16;do if(o<<16>>16<0){if(r=0-n<<16,(r|0)>=983040){r=0;break}r=a<<16>>16>>(r>>16)&65535}else{if(r=a<<16>>16,a=r<<n,(a<<16>>16>>n|0)==(r|0)){r=a&65535;break}r=(r>>>15^32767)&65535}while(!1);if(u=a0(W1(u,1,t)|0,r)|0,l=n1(l,W1(u,E1(8,o,t)|0,t)|0,t)|0,f=f+1|0,(f|0)==9)break;a=e[T+(f<<1)>>1]|0}if(l<<16>>16>1e3){e[i+98>>1]=20,a=32767;break}a=e[h>>1]|0,f=i+98|0,l=e[f>>1]|0;do if(!(a&16384))x=86;else{if(!(l<<16>>16)){l=a;break}l=E1(l,1,t)|0,e[f>>1]=l,x=86}while(!1);if((x|0)==86){if(l<<16>>16==20){a=32767;break}l=e[h>>1]|0}a=l&16384?3276:16383}while(!1);for((x|0)==71&&(e[i+98>>1]=20,a=32767),f=k,l=0;u=i+18+(l<<1)|0,r=$5(a,E1(f,e[u>>1]|0,t)|0,t)|0,e[u>>1]=n1(e[u>>1]|0,r,t)|0,l=l+1|0,(l|0)!=9;)f=e[T+(l<<1)>>1]|0;do if(e[h>>1]&30720)x=95;else{if(e[c>>1]&30720){x=95;break}e[i+114>>1]|0?x=95:(n=2097,o=1638,r=2)}while(!1);do if((x|0)==95){if(!(e[i+98>>1]|0)&&!(e[i+114>>1]|0)){n=1867,o=491,r=2;break}n=1638,o=0,r=0}while(!1);a=0;do f=i+(a<<1)|0,l=E1(e[i+36+(a<<1)>>1]|0,e[f>>1]|0,t)|0,l<<16>>16<0?(l=$5(n,l,t)|0,l=n1(-2,n1(e[f>>1]|0,l,t)|0,t)|0,l=l<<16>>16<40?40:l):(l=$5(o,l,t)|0,l=n1(r,n1(e[f>>1]|0,l,t)|0,t)|0,l=l<<16>>16>16e3?16e3:l),e[f>>1]=l,a=a+1|0;while((a|0)!=9);if(e[i+36>>1]=k,e[i+38>>1]=F,e[i+40>>1]=A,e[i+42>>1]=N,e[i+44>>1]=I,e[i+46>>1]=S,e[i+48>>1]=R,e[i+50>>1]=E,e[i+52>>1]=b,f=w<<16>>16>100,a=f?7:4,f=f?4:5,!d)return e[i+94>>1]=0,e[i+96>>1]=0,e[i+114>>1]=0,e[i+116>>1]=0,t=0,i=i+120|0,e[i>>1]=t,O=M,t|0;n=i+114|0,o=e[n>>1]|0;do if((e[i+116>>1]|0)<=100){if(o<<16>>16)break;o=e[h>>1]|0;do if(!(o&16368)){if((e[m>>1]|0)>21298)o=1;else break;return i=i+120|0,e[i>>1]=o,O=M,o|0}while(!1);return n=i+94|0,o&16384?(t=n1(e[n>>1]|0,1,t)|0,e[n>>1]=t,(t<<16>>16|0)<(f|0)?(t=1,i=i+120|0,e[i>>1]=t,O=M,t|0):(e[i+96>>1]=a,t=1,i=i+120|0,e[i>>1]=t,O=M,t|0)):(e[n>>1]=0,o=i+96|0,n=e[o>>1]|0,n<<16>>16<=0?(t=0,i=i+120|0,e[i>>1]=t,O=M,t|0):(e[o>>1]=E1(n,1,t)|0,t=1,i=i+120|0,e[i>>1]=t,O=M,t|0))}else{if(o<<16>>16>=250)break;e[n>>1]=250,o=250}while(!1);return e[i+94>>1]=4,e[n>>1]=E1(o,1,t)|0,t=1,i=i+120|0,e[i>>1]=t,O=M,t|0}function p4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;n=(e[t>>1]|0)*21955>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),a=E1(e[i>>1]|0,n&65535,o)|0,n=(a<<16>>16)*21955>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),f=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=a,t=t+2|0,n=(e[t>>1]|0)*6390>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),a=E1(e[r>>1]|0,n&65535,o)|0,n=(a<<16>>16)*6390>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),n=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=a,e[i>>1]=W1(n1(f,n,o)|0,1,o)|0,e[r>>1]=W1(E1(f,n,o)|0,1,o)|0}function X2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0;n=(e[t>>1]|0)*13363>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),a=E1(e[r>>1]|0,n&65535,o)|0,n=(a<<16>>16)*13363>>15,(n|0)>32767&&(s[o>>2]=1,n=32767),n=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=a,e[r>>1]=W1(E1(e[i>>1]|0,n,o)|0,1,o)|0,e[i>>1]=W1(n1(e[i>>1]|0,n,o)|0,1,o)|0}function i2(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;if(t<<16>>16<o<<16>>16){m=n<<16>>16,u=a<<16>>16,w=t<<16>>16,c=0;do h=e[i+((y(w,m)|0)+u<<1)>>1]|0,h=(h&65535)-((h&65535)>>>15&65535)|0,h=(h<<16>>31^h)<<16,d=(h>>15)+c|0,(h>>16^c|0)>0&(d^c|0)<0?(s[l>>2]=1,c=(c>>>31)+2147483647|0):c=d,w=w+1|0;while((w&65535)<<16>>16!=o<<16>>16);w=c}else w=0;if(c=e[r>>1]|0,h=E1(16,f,l)|0,u=h<<16>>16,h<<16>>16>0?(o=c<<u,(o>>u|0)!=(c|0)&&(o=c>>31^2147483647)):(u=0-u<<16,(u|0)<2031616?o=c>>(u>>16):o=0),u=o+w|0,(o^w|0)>-1&(u^w|0)<0&&(s[l>>2]=1,u=(w>>>31)+2147483647|0),h=f<<16>>16,f=f<<16>>16>0,f?(o=w<<h,(o>>h|0)!=(w|0)&&(o=w>>31^2147483647)):(o=0-h<<16,(o|0)<2031616?o=w>>(o>>16):o=0),e[r>>1]=o>>>16,t<<16>>16>0){m=n<<16>>16,c=a<<16>>16,d=0;do a=e[i+((y(d,m)|0)+c<<1)>>1]|0,a=(a&65535)-((a&65535)>>>15&65535)|0,a=(a<<16>>31^a)<<16,o=(a>>15)+u|0,(a>>16^u|0)>0&(o^u|0)<0?(s[l>>2]=1,u=(u>>>31)+2147483647|0):u=o,d=d+1|0;while((d&65535)<<16>>16!=t<<16>>16)}return f?(o=u<<h,(o>>h|0)==(u|0)?(l=o,l=l>>>16,l=l&65535,l|0):(l=u>>31^2147483647,l=l>>>16,l=l&65535,l|0)):(o=0-h<<16,(o|0)>=2031616?(l=0,l=l>>>16,l=l&65535,l|0):(l=u>>(o>>16),l=l>>>16,l=l&65535,l|0))}function n1(i,r,t){return i=i|0,r=r|0,t=t|0,i=(r<<16>>16)+(i<<16>>16)|0,(i|0)<=32767?(i|0)<-32768&&(s[t>>2]=1,i=-32768):(s[t>>2]=1,i=32767),i&65535|0}function K5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0;F=O,O=O+32|0,N=F+12|0,A=F,e[N>>1]=1024,e[A>>1]=1024,u=e[i+2>>1]|0,f=e[i+20>>1]|0,o=((f+u|0)>>>2)+64512|0,e[N+2>>1]=o,f=((u-f|0)>>>2)+1024|0,e[A+2>>1]=f,u=e[i+4>>1]|0,n=e[i+18>>1]|0,o=((n+u|0)>>>2)-o|0,e[N+4>>1]=o,f=((u-n|0)>>>2)+f|0,e[A+4>>1]=f,n=e[i+6>>1]|0,u=e[i+16>>1]|0,o=((u+n|0)>>>2)-o|0,e[N+6>>1]=o,f=((n-u|0)>>>2)+f|0,e[A+6>>1]=f,u=e[i+8>>1]|0,n=e[i+14>>1]|0,o=((n+u|0)>>>2)-o|0,e[N+8>>1]=o,f=((u-n|0)>>>2)+f|0,e[A+8>>1]=f,n=e[i+10>>1]|0,u=e[i+12>>1]|0,o=((u+n|0)>>>2)-o|0,e[N+10>>1]=o,e[A+10>>1]=((n-u|0)>>>2)+f,f=e[3454]|0,u=f<<16>>16,i=e[N+2>>1]|0,n=(i<<16>>16<<14)+(u<<10)|0,E=n&-65536,n=(n>>>1)-(n>>16<<15)<<16,I=(((y(n>>16,u)|0)>>15)+(y(E>>16,u)|0)<<2)+-16777216|0,I=(e[N+4>>1]<<14)+I|0,l=I>>16,I=(I>>>1)-(l<<15)<<16,E=(((y(I>>16,u)|0)>>15)+(y(l,u)|0)<<2)-((n>>15)+E)|0,E=(e[N+6>>1]<<14)+E|0,n=E>>16,E=(E>>>1)-(n<<15)<<16,l=(((y(E>>16,u)|0)>>15)+(y(n,u)|0)<<2)-((I>>15)+(l<<16))|0,l=(e[N+8>>1]<<14)+l|0,I=l>>16,n=(o<<16>>3)+((((y((l>>>1)-(I<<15)<<16>>16,u)|0)>>15)+(y(I,u)|0)<<1)-((E>>15)+(n<<16)))|0,E=N+4|0,u=N,I=0,l=0,o=0,b=N+10|0,n=(n+33554432|0)>>>0<67108863?n>>>10&65535:(n|0)>33554431?32767:-32768;e:for(;;){for(R=i<<16>>16<<14,k=u+6|0,h=u+8|0,w=l<<16>>16;;){if((w|0)>=60)break e;if(u=(w&65535)+1<<16>>16,c=e[6908+(u<<16>>16<<1)>>1]|0,S=c<<16>>16,l=R+(S<<10)|0,a=l&-65536,l=(l>>>1)-(l>>16<<15)<<16,d=(((y(l>>16,S)|0)>>15)+(y(a>>16,S)|0)<<2)+-16777216|0,m=e[E>>1]|0,d=(m<<16>>16<<14)+d|0,M=d>>16,d=(d>>>1)-(M<<15)<<16,a=(((y(d>>16,S)|0)>>15)+(y(M,S)|0)<<2)-((l>>15)+a)|0,l=e[k>>1]|0,a=(l<<16>>16<<14)+a|0,i=a>>16,a=(a>>>1)-(i<<15)<<16,M=(((y(a>>16,S)|0)>>15)+(y(i,S)|0)<<2)-((d>>15)+(M<<16))|0,d=e[h>>1]|0,M=(d<<16>>16<<14)+M|0,x=M>>16,i=(((y((M>>>1)-(x<<15)<<16>>16,S)|0)>>15)+(y(x,S)|0)<<1)-((a>>15)+(i<<16))|0,a=e[b>>1]|0,i=(a<<16>>16<<13)+i|0,i=(i+33554432|0)>>>0<67108863?i>>>10&65535:(i|0)>33554431?32767:-32768,(y(i<<16>>16,n<<16>>16)|0)<1){S=u,u=m;break}else w=w+1|0,f=c,n=i}for(E=a<<16>>16<<13,b=u<<16>>16<<14,m=l<<16>>16<<14,h=d<<16>>16<<14,a=c<<16>>16,w=4;;)if(x=(f<<16>>16>>>1)+(a>>>1)|0,a=x<<16,k=a>>16,a=R+(a>>6)|0,M=a&-65536,a=(a>>>1)-(a>>16<<15)<<16,d=b+((((y(a>>16,k)|0)>>15)+(y(M>>16,k)|0)<<2)+-16777216)|0,u=d>>16,d=(d>>>1)-(u<<15)<<16,M=m+((((y(d>>16,k)|0)>>15)+(y(u,k)|0)<<2)-((a>>15)+M))|0,a=M>>16,M=(M>>>1)-(a<<15)<<16,u=h+((((y(M>>16,k)|0)>>15)+(y(a,k)|0)<<2)-((d>>15)+(u<<16)))|0,d=u>>16,x=x&65535,a=E+((((y((u>>>1)-(d<<15)<<16>>16,k)|0)>>15)+(y(d,k)|0)<<1)-((M>>15)+(a<<16)))|0,a=(a+33554432|0)>>>0<67108863?a>>>10&65535:(a|0)>33554431?32767:-32768,M=(y(a<<16>>16,i<<16>>16)|0)<1,k=M?c:x,i=M?i:a,f=M?x:f,n=M?a:n,w=w+-1<<16>>16,a=k<<16>>16,w<<16>>16)c=k;else{c=a,l=f,f=k;break}if(u=o<<16>>16,a=i<<16>>16,i=(n&65535)-a|0,n=i<<16,n&&(M=(i&65535)-(i>>>15&1)|0,M=M<<16>>31^M,i=(O2(M&65535)|0)<<16>>16,i=(y((a0(16383,M<<16>>16<<i&65535)|0)<<16>>16,(l&65535)-c<<16>>16)|0)>>19-i,(n|0)<0&&(i=0-(i<<16>>16)|0),f=c-((y(i<<16>>16,a)|0)>>>10)&65535),e[r+(u<<1)>>1]=f,n=I<<16>>16?N:A,x=f<<16>>16,i=e[n+2>>1]|0,a=(i<<16>>16<<14)+(x<<10)|0,M=a&-65536,a=(a>>>1)-(a>>16<<15)<<16,R=(((y(a>>16,x)|0)>>15)+(y(M>>16,x)|0)<<2)+-16777216|0,R=(e[n+4>>1]<<14)+R|0,E=R>>16,R=(R>>>1)-(E<<15)<<16,M=(((y(R>>16,x)|0)>>15)+(y(E,x)|0)<<2)-((a>>15)+M)|0,M=(e[n+6>>1]<<14)+M|0,a=M>>16,M=(M>>>1)-(a<<15)<<16,E=(((y(M>>16,x)|0)>>15)+(y(a,x)|0)<<2)-((R>>15)+(E<<16))|0,E=(e[n+8>>1]<<14)+E|0,R=E>>16,o=o+1<<16>>16,a=(((y((E>>>1)-(R<<15)<<16>>16,x)|0)>>15)+(y(R,x)|0)<<1)-((M>>15)+(a<<16))|0,a=(e[n+10>>1]<<13)+a|0,o<<16>>16<10)E=n+4|0,u=n,I=I^1,l=S,b=n+10|0,n=(a+33554432|0)>>>0<67108863?a>>>10&65535:(a|0)>33554431?32767:-32768;else{T=13;break}}if((T|0)==13){O=F;return}e[r>>1]=e[t>>1]|0,e[r+2>>1]=e[t+2>>1]|0,e[r+4>>1]=e[t+4>>1]|0,e[r+6>>1]=e[t+6>>1]|0,e[r+8>>1]=e[t+8>>1]|0,e[r+10>>1]=e[t+10>>1]|0,e[r+12>>1]=e[t+12>>1]|0,e[r+14>>1]=e[t+14>>1]|0,e[r+16>>1]=e[t+16>>1]|0,e[r+18>>1]=e[t+18>>1]|0,O=F}function a0(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,a=0,f=0,l=0;return n=r<<16>>16,i<<16>>16<1||i<<16>>16>r<<16>>16?(n=0,n|0):i<<16>>16==r<<16>>16?(n=32767,n|0):(o=n<<1,t=n<<2,a=i<<16>>16<<3,i=(a|0)<(t|0),a=a-(i?0:t)|0,i=i?0:4,f=(a|0)<(o|0),a=a-(f?0:o)|0,r=(a|0)<(n|0),i=(r&1|(f?i:i|2))<<3^8,r=a-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),a=(r|0)<(o|0),f=r-(a?0:o)|0,r=(f|0)<(n|0),i=(r&1^1|(a?i:i|2))<<16>>13,r=f-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),a=(r|0)<(o|0),f=r-(a?0:o)|0,r=(f|0)<(n|0),i=(r&1^1|(a?i:i|2))<<16>>13,r=f-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),l=(r|0)<(o|0),a=r-(l?0:o)|0,f=(a|0)<(n|0),r=(f&1^1|(l?i:i|2))<<16>>13,i=a-(f?0:n)<<3,(i|0)>=(t|0)&&(i=i-t|0,r=r&65528|4),l=(i|0)<(o|0),l=((i-(l?0:o)|0)>=(n|0)|(l?r:r|2))&65535,l|0)}function s2(i){return i=i|0,i?(e[i>>1]=-14336,e[i+8>>1]=-2381,e[i+2>>1]=-14336,e[i+10>>1]=-2381,e[i+4>>1]=-14336,e[i+12>>1]=-2381,e[i+6>>1]=-14336,e[i+14>>1]=-2381,i=0,i|0):(i=-1,i|0)}function I2(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;for(h=O,O=O+16|0,m=h+2|0,w=h,u=0,c=10;d=e[t>>1]|0,d=((y(d,d)|0)>>>3)+u|0,u=e[t+2>>1]|0,u=d+((y(u,u)|0)>>>3)|0,d=e[t+4>>1]|0,d=u+((y(d,d)|0)>>>3)|0,u=e[t+6>>1]|0,u=d+((y(u,u)|0)>>>3)|0,c=c+-1<<16>>16,c<<16>>16;)t=t+8|0;if(c=u<<4,c=(c|0)<0?2147483647:c,(r|0)==7){f2(((S1(c,l)|0)<<16>>16)*52428|0,m,w,l),d=D[m>>1]<<16,c=e[w>>1]<<1,r=e[i+8>>1]|0,u=(r<<16>>16)*88|0,r<<16>>16>-1&(u|0)<-783741?(s[l>>2]=1,t=2147483647):t=u+783741|0,r=(e[i+10>>1]|0)*74|0,u=r+t|0,(r^t|0)>-1&(u^t|0)<0?(s[l>>2]=1,t=(t>>>31)+2147483647|0):t=u,r=(e[i+12>>1]|0)*44|0,u=r+t|0,(r^t|0)>-1&(u^t|0)<0?(s[l>>2]=1,t=(t>>>31)+2147483647|0):t=u,i=(e[i+14>>1]|0)*24|0,u=i+t|0,(i^t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0),i=d+-1966080+c|0,t=u-i|0,((t^u)&(u^i)|0)<0&&(s[l>>2]=1,t=(u>>>31)+2147483647|0),l=t>>17,e[o>>1]=l,l=(t>>2)-(l<<15)|0,l=l&65535,e[n>>1]=l,O=h;return}switch(d=B1(c)|0,u=d<<16>>16,d<<16>>16>0?(t=c<<u,(t>>u|0)==(c|0)?c=t:c=c>>31^2147483647):(u=0-u<<16,(u|0)<2031616?c=c>>(u>>16):c=0),E4(c,d,m,w),m=y(e[m>>1]|0,-49320)|0,u=(y(e[w>>1]|0,-24660)|0)>>15,u=u&65536|0?u|-65536:u,w=u<<1,t=w+m|0,(w^m|0)>-1&(t^w|0)<0&&(s[l>>2]=1,t=(u>>>30&1)+2147483647|0),r|0){case 6:{u=t+2134784|0,(t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 5:{e[f>>1]=c>>>16,e[a>>1]=-11-(d&65535),u=t+2183936|0,(t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 4:{u=t+2085632|0,(t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 3:{u=t+2065152|0,(t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0);break}default:u=t+2134784|0,(t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0)}do if((u|0)<=2097151)if((u|0)<-2097152){s[l>>2]=1,t=-2147483648;break}else{t=u<<10;break}else s[l>>2]=1,t=2147483647;while(!1);if(f=(e[i>>1]|0)*11142|0,u=f+t|0,(f^t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0),f=(e[i+2>>1]|0)*9502|0,t=f+u|0,(f^u|0)>-1&(t^u|0)<0&&(s[l>>2]=1,t=(u>>>31)+2147483647|0),f=(e[i+4>>1]|0)*5570|0,u=f+t|0,(f^t|0)>-1&(u^t|0)<0&&(s[l>>2]=1,u=(t>>>31)+2147483647|0),i=(e[i+6>>1]|0)*3112|0,t=i+u|0,(i^u|0)>-1&(t^u|0)<0&&(s[l>>2]=1,t=(u>>>31)+2147483647|0),t=y(t>>16,(r|0)==4?10878:10886)|0,(t|0)<0?t=~((t^-256)>>8):t=t>>8,e[o>>1]=t>>>16,(t|0)<0?u=~((t^-2)>>1):u=t>>1,o=t>>16<<15,t=u-o|0,((t^u)&(o^u)|0)>=0){l=t,l=l&65535,e[n>>1]=l,O=h;return}s[l>>2]=1,l=(u>>>31)+2147483647|0,l=l&65535,e[n>>1]=l,O=h}function W0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;n=i+4|0,e[i+6>>1]=e[n>>1]|0,a=i+12|0,e[i+14>>1]=e[a>>1]|0,o=i+2|0,e[n>>1]=e[o>>1]|0,n=i+10|0,e[a>>1]=e[n>>1]|0,e[o>>1]=e[i>>1]|0,o=i+8|0,e[n>>1]=e[o>>1]|0,e[o>>1]=r,e[i>>1]=t}function Bi(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0;a=n1(0,e[i+8>>1]|0,o)|0,a=n1(a,e[i+10>>1]|0,o)|0,a=n1(a,e[i+12>>1]|0,o)|0,a=n1(a,e[i+14>>1]|0,o)|0,n=a<<16>>16>>2,n=(a<<16>>16<0?n|49152:n)&65535,e[r>>1]=n<<16>>16<-2381?-2381:n,r=n1(0,e[i>>1]|0,o)|0,r=n1(r,e[i+2>>1]|0,o)|0,r=n1(r,e[i+4>>1]|0,o)|0,o=n1(r,e[i+6>>1]|0,o)|0,i=o<<16>>16>>2,i=(o<<16>>16<0?i|49152:i)&65535,e[t>>1]=i<<16>>16<-14336?-14336:i}function k4(i){i=i|0,s[i>>2]=6892,s[i+4>>2]=8180,s[i+8>>2]=21e3,s[i+12>>2]=9716,s[i+16>>2]=22024,s[i+20>>2]=12788,s[i+24>>2]=24072,s[i+28>>2]=26120,s[i+32>>2]=28168,s[i+36>>2]=6876,s[i+40>>2]=7452,s[i+44>>2]=8140,s[i+48>>2]=20980,s[i+52>>2]=16884,s[i+56>>2]=17908,s[i+60>>2]=7980,s[i+64>>2]=8160,s[i+68>>2]=6678,s[i+72>>2]=6646,s[i+76>>2]=6614,s[i+80>>2]=29704,s[i+84>>2]=28680,s[i+88>>2]=3720,s[i+92>>2]=8,s[i+96>>2]=4172,s[i+100>>2]=44,s[i+104>>2]=3436,s[i+108>>2]=30316,s[i+112>>2]=30796,s[i+116>>2]=31276,s[i+120>>2]=7472,s[i+124>>2]=7552,s[i+128>>2]=7632,s[i+132>>2]=7712}function E2(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0;if(m=O,O=O+48|0,c=m+18|0,d=m,u=r<<16>>16,_0(d|0,i|0,u<<1|0)|0,r<<16>>16>0)t=0,o=0;else return d=u>>1,d=c+(d<<1)|0,d=e[d>>1]|0,d=d<<16>>16,d=i+(d<<1)|0,d=e[d>>1]|0,O=m,d|0;do{for(l=0,f=-32767;n=e[d+(l<<1)>>1]|0,a=n<<16>>16<f<<16>>16,o=a?o:l&65535,l=l+1|0,(l&65535)<<16>>16!=r<<16>>16;)f=a?f:n;e[d+(o<<16>>16<<1)>>1]=-32768,e[c+(t<<1)>>1]=o,t=t+1|0}while((t&65535)<<16>>16!=r<<16>>16);return d=u>>1,d=c+(d<<1)|0,d=e[d>>1]|0,d=d<<16>>16,d=i+(d<<1)|0,d=e[d>>1]|0,O=m,d|0}function b4(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0;a=O,O=O+32|0,f=a,x=r+2|0,T=f+2|0,e[f>>1]=((e[r>>1]|0)>>>1)+((e[i>>1]|0)>>>1),F=r+4|0,A=f+4|0,e[T>>1]=((e[x>>1]|0)>>>1)+((e[i+2>>1]|0)>>>1),N=r+6|0,I=f+6|0,e[A>>1]=((e[F>>1]|0)>>>1)+((e[i+4>>1]|0)>>>1),S=r+8|0,R=f+8|0,e[I>>1]=((e[N>>1]|0)>>>1)+((e[i+6>>1]|0)>>>1),E=r+10|0,b=f+10|0,e[R>>1]=((e[S>>1]|0)>>>1)+((e[i+8>>1]|0)>>>1),k=r+12|0,h=f+12|0,e[b>>1]=((e[E>>1]|0)>>>1)+((e[i+10>>1]|0)>>>1),w=r+14|0,m=f+14|0,e[h>>1]=((e[k>>1]|0)>>>1)+((e[i+12>>1]|0)>>>1),d=r+16|0,c=f+16|0,e[m>>1]=((e[w>>1]|0)>>>1)+((e[i+14>>1]|0)>>>1),u=r+18|0,l=f+18|0,e[c>>1]=((e[d>>1]|0)>>>1)+((e[i+16>>1]|0)>>>1),e[l>>1]=((e[u>>1]|0)>>>1)+((e[i+18>>1]|0)>>>1),y0(f,o,n),y0(r,o+22|0,n),e[f>>1]=((e[t>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[T>>1]=((e[t+2>>1]|0)>>>1)+((e[x>>1]|0)>>>1),e[A>>1]=((e[t+4>>1]|0)>>>1)+((e[F>>1]|0)>>>1),e[I>>1]=((e[t+6>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[R>>1]=((e[t+8>>1]|0)>>>1)+((e[S>>1]|0)>>>1),e[b>>1]=((e[t+10>>1]|0)>>>1)+((e[E>>1]|0)>>>1),e[h>>1]=((e[t+12>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[m>>1]=((e[t+14>>1]|0)>>>1)+((e[w>>1]|0)>>>1),e[c>>1]=((e[t+16>>1]|0)>>>1)+((e[d>>1]|0)>>>1),e[l>>1]=((e[t+18>>1]|0)>>>1)+((e[u>>1]|0)>>>1),y0(f,o+44|0,n),y0(t,o+66|0,n),O=a}function Ui(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0;a=O,O=O+32|0,f=a,x=r+2|0,T=f+2|0,e[f>>1]=((e[r>>1]|0)>>>1)+((e[i>>1]|0)>>>1),F=r+4|0,A=f+4|0,e[T>>1]=((e[x>>1]|0)>>>1)+((e[i+2>>1]|0)>>>1),N=r+6|0,I=f+6|0,e[A>>1]=((e[F>>1]|0)>>>1)+((e[i+4>>1]|0)>>>1),S=r+8|0,R=f+8|0,e[I>>1]=((e[N>>1]|0)>>>1)+((e[i+6>>1]|0)>>>1),E=r+10|0,b=f+10|0,e[R>>1]=((e[S>>1]|0)>>>1)+((e[i+8>>1]|0)>>>1),k=r+12|0,h=f+12|0,e[b>>1]=((e[E>>1]|0)>>>1)+((e[i+10>>1]|0)>>>1),w=r+14|0,m=f+14|0,e[h>>1]=((e[k>>1]|0)>>>1)+((e[i+12>>1]|0)>>>1),d=r+16|0,c=f+16|0,e[m>>1]=((e[w>>1]|0)>>>1)+((e[i+14>>1]|0)>>>1),u=r+18|0,l=f+18|0,e[c>>1]=((e[d>>1]|0)>>>1)+((e[i+16>>1]|0)>>>1),e[l>>1]=((e[u>>1]|0)>>>1)+((e[i+18>>1]|0)>>>1),y0(f,o,n),e[f>>1]=((e[t>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[T>>1]=((e[t+2>>1]|0)>>>1)+((e[x>>1]|0)>>>1),e[A>>1]=((e[t+4>>1]|0)>>>1)+((e[F>>1]|0)>>>1),e[I>>1]=((e[t+6>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[R>>1]=((e[t+8>>1]|0)>>>1)+((e[S>>1]|0)>>>1),e[b>>1]=((e[t+10>>1]|0)>>>1)+((e[E>>1]|0)>>>1),e[h>>1]=((e[t+12>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[m>>1]=((e[t+14>>1]|0)>>>1)+((e[w>>1]|0)>>>1),e[c>>1]=((e[t+16>>1]|0)>>>1)+((e[d>>1]|0)>>>1),e[l>>1]=((e[t+18>>1]|0)>>>1)+((e[u>>1]|0)>>>1),y0(f,o+44|0,n),O=a}function v4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0;n=O,O=O+32|0,a=n,V=e[i>>1]|0,e[a>>1]=V-(V>>>2)+((e[r>>1]|0)>>>2),V=i+2|0,j=e[V>>1]|0,U=r+2|0,K=a+2|0,e[K>>1]=j-(j>>>2)+((e[U>>1]|0)>>>2),j=i+4|0,M=e[j>>1]|0,G=r+4|0,z=a+4|0,e[z>>1]=M-(M>>>2)+((e[G>>1]|0)>>>2),M=i+6|0,F=e[M>>1]|0,C=r+6|0,x=a+6|0,e[x>>1]=F-(F>>>2)+((e[C>>1]|0)>>>2),F=i+8|0,I=e[F>>1]|0,T=r+8|0,A=a+8|0,e[A>>1]=I-(I>>>2)+((e[T>>1]|0)>>>2),I=i+10|0,E=e[I>>1]|0,N=r+10|0,S=a+10|0,e[S>>1]=E-(E>>>2)+((e[N>>1]|0)>>>2),E=i+12|0,h=e[E>>1]|0,R=r+12|0,b=a+12|0,e[b>>1]=h-(h>>>2)+((e[R>>1]|0)>>>2),h=i+14|0,d=e[h>>1]|0,k=r+14|0,w=a+14|0,e[w>>1]=d-(d>>>2)+((e[k>>1]|0)>>>2),d=i+16|0,l=e[d>>1]|0,m=r+16|0,c=a+16|0,e[c>>1]=l-(l>>>2)+((e[m>>1]|0)>>>2),l=i+18|0,q=e[l>>1]|0,u=r+18|0,f=a+18|0,e[f>>1]=q-(q>>>2)+((e[u>>1]|0)>>>2),y0(a,t,o),e[a>>1]=((e[i>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[K>>1]=((e[V>>1]|0)>>>1)+((e[U>>1]|0)>>>1),e[z>>1]=((e[j>>1]|0)>>>1)+((e[G>>1]|0)>>>1),e[x>>1]=((e[M>>1]|0)>>>1)+((e[C>>1]|0)>>>1),e[A>>1]=((e[F>>1]|0)>>>1)+((e[T>>1]|0)>>>1),e[S>>1]=((e[I>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[b>>1]=((e[E>>1]|0)>>>1)+((e[R>>1]|0)>>>1),e[w>>1]=((e[h>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[c>>1]=((e[d>>1]|0)>>>1)+((e[m>>1]|0)>>>1),e[f>>1]=((e[l>>1]|0)>>>1)+((e[u>>1]|0)>>>1),y0(a,t+22|0,o),q=e[r>>1]|0,e[a>>1]=q-(q>>>2)+((e[i>>1]|0)>>>2),i=e[U>>1]|0,e[K>>1]=i-(i>>>2)+((e[V>>1]|0)>>>2),i=e[G>>1]|0,e[z>>1]=i-(i>>>2)+((e[j>>1]|0)>>>2),i=e[C>>1]|0,e[x>>1]=i-(i>>>2)+((e[M>>1]|0)>>>2),i=e[T>>1]|0,e[A>>1]=i-(i>>>2)+((e[F>>1]|0)>>>2),i=e[N>>1]|0,e[S>>1]=i-(i>>>2)+((e[I>>1]|0)>>>2),i=e[R>>1]|0,e[b>>1]=i-(i>>>2)+((e[E>>1]|0)>>>2),i=e[k>>1]|0,e[w>>1]=i-(i>>>2)+((e[h>>1]|0)>>>2),i=e[m>>1]|0,e[c>>1]=i-(i>>>2)+((e[d>>1]|0)>>>2),i=e[u>>1]|0,e[f>>1]=i-(i>>>2)+((e[l>>1]|0)>>>2),y0(a,t+44|0,o),y0(r,t+66|0,o),O=n}function zi(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0;n=O,O=O+32|0,a=n,V=e[i>>1]|0,e[a>>1]=V-(V>>>2)+((e[r>>1]|0)>>>2),V=i+2|0,j=e[V>>1]|0,U=r+2|0,K=a+2|0,e[K>>1]=j-(j>>>2)+((e[U>>1]|0)>>>2),j=i+4|0,M=e[j>>1]|0,G=r+4|0,z=a+4|0,e[z>>1]=M-(M>>>2)+((e[G>>1]|0)>>>2),M=i+6|0,F=e[M>>1]|0,C=r+6|0,x=a+6|0,e[x>>1]=F-(F>>>2)+((e[C>>1]|0)>>>2),F=i+8|0,I=e[F>>1]|0,T=r+8|0,A=a+8|0,e[A>>1]=I-(I>>>2)+((e[T>>1]|0)>>>2),I=i+10|0,E=e[I>>1]|0,N=r+10|0,S=a+10|0,e[S>>1]=E-(E>>>2)+((e[N>>1]|0)>>>2),E=i+12|0,h=e[E>>1]|0,R=r+12|0,b=a+12|0,e[b>>1]=h-(h>>>2)+((e[R>>1]|0)>>>2),h=i+14|0,d=e[h>>1]|0,k=r+14|0,w=a+14|0,e[w>>1]=d-(d>>>2)+((e[k>>1]|0)>>>2),d=i+16|0,l=e[d>>1]|0,m=r+16|0,c=a+16|0,e[c>>1]=l-(l>>>2)+((e[m>>1]|0)>>>2),l=i+18|0,q=e[l>>1]|0,u=r+18|0,f=a+18|0,e[f>>1]=q-(q>>>2)+((e[u>>1]|0)>>>2),y0(a,t,o),e[a>>1]=((e[i>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[K>>1]=((e[V>>1]|0)>>>1)+((e[U>>1]|0)>>>1),e[z>>1]=((e[j>>1]|0)>>>1)+((e[G>>1]|0)>>>1),e[x>>1]=((e[M>>1]|0)>>>1)+((e[C>>1]|0)>>>1),e[A>>1]=((e[F>>1]|0)>>>1)+((e[T>>1]|0)>>>1),e[S>>1]=((e[I>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[b>>1]=((e[E>>1]|0)>>>1)+((e[R>>1]|0)>>>1),e[w>>1]=((e[h>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[c>>1]=((e[d>>1]|0)>>>1)+((e[m>>1]|0)>>>1),e[f>>1]=((e[l>>1]|0)>>>1)+((e[u>>1]|0)>>>1),y0(a,t+22|0,o),r=e[r>>1]|0,e[a>>1]=r-(r>>>2)+((e[i>>1]|0)>>>2),i=e[U>>1]|0,e[K>>1]=i-(i>>>2)+((e[V>>1]|0)>>>2),i=e[G>>1]|0,e[z>>1]=i-(i>>>2)+((e[j>>1]|0)>>>2),i=e[C>>1]|0,e[x>>1]=i-(i>>>2)+((e[M>>1]|0)>>>2),i=e[T>>1]|0,e[A>>1]=i-(i>>>2)+((e[F>>1]|0)>>>2),i=e[N>>1]|0,e[S>>1]=i-(i>>>2)+((e[I>>1]|0)>>>2),i=e[R>>1]|0,e[b>>1]=i-(i>>>2)+((e[E>>1]|0)>>>2),i=e[k>>1]|0,e[w>>1]=i-(i>>>2)+((e[h>>1]|0)>>>2),i=e[m>>1]|0,e[c>>1]=i-(i>>>2)+((e[d>>1]|0)>>>2),i=e[u>>1]|0,e[f>>1]=i-(i>>>2)+((e[l>>1]|0)>>>2),y0(a,t+44|0,o),O=n}function t2(i,r){i=i|0,r=r|0;var t=0,o=0;return(i|0)<1?(r=1073741823,r|0):(t=(B1(i)|0)<<16>>16,r=30-t|0,i=i<<t>>(r&1^1),t=(i>>25<<16)+-1048576>>16,o=e[7030+(t<<1)>>1]|0,r=(o<<16)-(y(o-(D[7030+(t+1<<1)>>1]|0)<<16>>15,i>>>10&32767)|0)>>(r<<16>>17)+1,r|0)}function f2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,o=B1(i)|0,E4(i<<(o<<16>>16),o,r,t)}function E4(i,r,t,o){if(i=i|0,r=r|0,t=t|0,o=o|0,(i|0)<1){e[t>>1]=0,t=0,e[o>>1]=t;return}else{e[t>>1]=30-(r&65535),t=(i>>25<<16)+-2097152>>16,r=e[7128+(t<<1)>>1]|0,t=((r<<16)-(y(i>>>9&65534,r-(D[7128+(t+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535,e[o>>1]=t;return}}function Z5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;for(o=i+2|0,t=e[o>>1]|0,e[r>>1]=t,n=i+4|0,e[r+2>>1]=(D[n>>1]|0)-(D[i>>1]|0),e[r+4>>1]=(D[i+6>>1]|0)-(D[o>>1]|0),o=i+8|0,e[r+6>>1]=(D[o>>1]|0)-(D[n>>1]|0),e[r+8>>1]=(D[i+10>>1]|0)-(D[i+6>>1]|0),n=i+12|0,e[r+10>>1]=(D[n>>1]|0)-(D[o>>1]|0),e[r+12>>1]=(D[i+14>>1]|0)-(D[i+10>>1]|0),e[r+14>>1]=(D[i+16>>1]|0)-(D[n>>1]|0),e[r+16>>1]=(D[i+18>>1]|0)-(D[i+14>>1]|0),e[r+18>>1]=16384-(D[i+16>>1]|0),i=10,n=r;t=t<<16>>16,r=(t<<16)+-120782848|0,(r|0)>0?r=1843-((r>>16)*12484>>16)|0:r=3427-((t*56320|0)>>>16)|0,o=n+2|0,e[n>>1]=r<<3,i=i+-1<<16>>16,!!(i<<16>>16);)t=e[o>>1]|0,n=o}function Q5(i,r,t){return i=i|0,r=r|0,t=t|0,t=r<<16>>16,r<<16>>16>31?(r=0,r|0):r<<16>>16>0?((1<<t+-1&i|0)!=0&1)+(r<<16>>16<31?i>>t:0)|0:(t=0-t<<16>>16,r=i<<t,r=(r>>t|0)==(i|0)?r:i>>31^2147483647,r|0)}function y0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;for(b=O,O=O+48|0,h=b+24|0,k=b,m=h+4|0,s[h>>2]=16777216,o=0-(e[i>>1]|0)|0,w=h+8|0,s[m>>2]=o<<10,n=e[i+4>>1]|0,u=o>>6,s[w>>2]=33554432-(((y((o<<9)-(u<<15)<<16>>16,n)|0)>>15)+(y(u,n)|0)<<2),u=h+4|0,n=(s[u>>2]|0)-(n<<10)|0,s[u>>2]=n,u=h+12|0,o=h+4|0,s[u>>2]=n,t=e[i+8>>1]|0,a=n,c=1;l=u+-4|0,f=s[l>>2]|0,d=f>>16,s[u>>2]=a+n-(((y((f>>>1)-(d<<15)<<16>>16,t)|0)>>15)+(y(d,t)|0)<<2),(c|0)!=2;)a=s[u+-12>>2]|0,u=l,n=f,c=c+1|0;for(s[o>>2]=(s[o>>2]|0)-(t<<10),t=h+16|0,o=s[h+8>>2]|0,s[t>>2]=o,l=e[i+12>>1]|0,n=o,u=1;f=t+-4|0,a=s[f>>2]|0,d=a>>16,s[t>>2]=n+o-(((y((a>>>1)-(d<<15)<<16>>16,l)|0)>>15)+(y(d,l)|0)<<2),(u|0)!=3;)n=s[t+-12>>2]|0,t=f,o=a,u=u+1|0;for(t=h+4|0,s[t>>2]=(s[t>>2]|0)-(l<<10),t=h+20|0,n=s[h+12>>2]|0,s[t>>2]=n,o=e[i+16>>1]|0,a=n,u=1;l=t+-4|0,f=s[l>>2]|0,d=f>>16,s[t>>2]=a+n-(((y((f>>>1)-(d<<15)<<16>>16,o)|0)>>15)+(y(d,o)|0)<<2),(u|0)!=4;)a=s[t+-12>>2]|0,t=l,n=f,u=u+1|0;for(u=h+4|0,s[u>>2]=(s[u>>2]|0)-(o<<10),s[k>>2]=16777216,u=0-(e[i+2>>1]|0)|0,d=k+8|0,s[k+4>>2]=u<<10,o=e[i+6>>1]|0,c=u>>6,s[d>>2]=33554432-(((y((u<<9)-(c<<15)<<16>>16,o)|0)>>15)+(y(c,o)|0)<<2),c=k+4|0,o=(s[c>>2]|0)-(o<<10)|0,s[c>>2]=o,c=k+12|0,u=k+4|0,s[c>>2]=o,l=e[i+10>>1]|0,n=o,t=1;f=c+-4|0,a=s[f>>2]|0,E=a>>16,s[c>>2]=n+o-(((y((a>>>1)-(E<<15)<<16>>16,l)|0)>>15)+(y(E,l)|0)<<2),(t|0)!=2;)n=s[c+-12>>2]|0,c=f,o=a,t=t+1|0;for(s[u>>2]=(s[u>>2]|0)-(l<<10),u=k+16|0,o=s[k+8>>2]|0,s[u>>2]=o,l=e[i+14>>1]|0,n=o,t=1;f=u+-4|0,a=s[f>>2]|0,E=a>>16,s[u>>2]=n+o-(((y((a>>>1)-(E<<15)<<16>>16,l)|0)>>15)+(y(E,l)|0)<<2),(t|0)!=3;)n=s[u+-12>>2]|0,u=f,o=a,t=t+1|0;for(t=k+4|0,s[t>>2]=(s[t>>2]|0)-(l<<10),t=k+20|0,l=s[k+12>>2]|0,s[t>>2]=l,o=e[i+18>>1]|0,f=l,u=1;n=t+-4|0,a=s[n>>2]|0,E=a>>16,s[t>>2]=f+l-(((y((a>>>1)-(E<<15)<<16>>16,o)|0)>>15)+(y(E,o)|0)<<2),(u|0)!=4;)f=s[t+-12>>2]|0,t=n,l=a,u=u+1|0;f=(s[k+4>>2]|0)-(o<<10)|0,c=h+20|0,l=k+20|0,u=s[h+16>>2]|0,i=(s[c>>2]|0)+u|0,s[c>>2]=i,c=s[k+16>>2]|0,E=(s[l>>2]|0)-c|0,s[l>>2]=E,l=s[h+12>>2]|0,u=u+l|0,s[h+16>>2]=u,a=s[k+12>>2]|0,c=c-a|0,s[k+16>>2]=c,o=s[w>>2]|0,l=l+o|0,s[h+12>>2]=l,n=s[d>>2]|0,w=a-n|0,s[k+12>>2]=w,a=s[m>>2]|0,d=o+a|0,s[h+8>>2]=d,m=n-f|0,s[k+8>>2]=m,h=a+(s[h>>2]|0)|0,k=f-(s[k>>2]|0)|0,e[r>>1]=4096,h=h+4096|0,e[r+2>>1]=(h+k|0)>>>13,e[r+20>>1]=(h-k|0)>>>13,k=d+4096|0,e[r+4>>1]=(k+m|0)>>>13,e[r+18>>1]=(k-m|0)>>>13,k=l+4096|0,e[r+6>>1]=(k+w|0)>>>13,e[r+16>>1]=(k-w|0)>>>13,k=u+4096|0,e[r+8>>1]=(k+c|0)>>>13,e[r+14>>1]=(k-c|0)>>>13,k=i+4096|0,e[r+10>>1]=(k+E|0)>>>13,e[r+12>>1]=(k-E|0)>>>13,O=b}function ji(i){i=i|0;var r=0,t=0,o=0,n=0,a=0;if(!i||(s[i>>2]=0,r=s0(44)|0,!r)||(t=r+40|0,(Wi(t)|0)<<16>>16))return a=-1,a|0;o=r,n=7452,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));o=r+20|0,n=7452,a=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(a|0));return _4(s[t>>2]|0)|0,s[i>>2]=r,a=0,a|0}function g4(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;r=i,t=7452,o=r+20|0;do e[r>>1]=e[t>>1]|0,r=r+2|0,t=t+2|0;while((r|0)<(o|0));r=i+20|0,t=7452,o=r+20|0;do e[r>>1]=e[t>>1]|0,r=r+2|0,t=t+2|0;while((r|0)<(o|0));return _4(s[i+40>>2]|0)|0,o=0,o|0}function qi(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(Xi(r+40|0),K1(s[i>>2]|0),s[i>>2]=0))}function Vi(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0;if(w=O,O=O+64|0,m=w+44|0,u=w+24|0,c=w+4|0,d=w,(r|0)==7?(K5(o+22|0,u,i,l),K5(o+66|0,a,u,l),Ui(i,u,a,o,l),(t|0)==8?o=6:(Hi(s[i+40>>2]|0,u,a,c,m,s[f>>2]|0,l),b4(i+20|0,c,m,n,l),n=(s[f>>2]|0)+10|0,o=7)):(K5(o+66|0,a,i,l),zi(i,a,o,l),(t|0)==8?o=6:(y4(s[i+40>>2]|0,r,a,m,s[f>>2]|0,d,l),v4(i+20|0,m,n,l),n=(s[f>>2]|0)+6|0,o=7)),(o|0)==6){o=i,n=o+20|0;do e[o>>1]=e[a>>1]|0,o=o+2|0,a=a+2|0;while((o|0)<(n|0));O=w;return}else if((o|0)==7){s[f>>2]=n,o=i,n=o+20|0;do e[o>>1]=e[a>>1]|0,o=o+2|0,a=a+2|0;while((o|0)<(n|0));o=i+20|0,a=m,n=o+20|0;do e[o>>1]=e[a>>1]|0,o=o+2|0,a=a+2|0;while((o|0)<(n|0));O=w;return}}function b0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;if(t<<16>>16>0)o=0;else return;do a=e[i+(o<<1)>>1]|0,f=a>>8,n=e[7194+(f<<1)>>1]|0,e[r+(o<<1)>>1]=((y((e[7194+(f+1<<1)>>1]|0)-n|0,a&255)|0)>>>8)+n,o=o+1|0;while((o&65535)<<16>>16!=t<<16>>16)}function Y2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;if(o=(t<<16>>16)+-1|0,t=o&65535,!(t<<16>>16<=-1))for(n=63,f=r+(o<<1)|0,a=i+(o<<1)|0;;){for(i=e[a>>1]|0,r=n;o=r<<16>>16,n=e[7194+(o<<1)>>1]|0,i<<16>>16>n<<16>>16;)r=r+-1<<16>>16;if(e[f>>1]=(((y(e[7324+(o<<1)>>1]|0,(i<<16>>16)-(n<<16>>16)|0)|0)+2048|0)>>>12)+(o<<8),t=t+-1<<16>>16,t<<16>>16>-1)n=r,f=f+-2|0,a=a+-2|0;else break}}function $5(i,r,t){return i=i|0,r=r|0,t=t|0,i=(y(r<<16>>16,i<<16>>16)|0)+16384>>15,i=i|0-(i&65536),(i|0)<=32767?(i|0)<-32768&&(s[t>>2]=1,i=-32768):(s[t>>2]=1,i=32767),i&65535|0}function B1(i){i=i|0;var r=0;e:do if(i|0&&(r=i-(i>>>31)|0,r=r>>31^r,(r&1073741824|0)==0)){for(i=r,r=0;;){if(i&536870912){i=7;break}if(i&268435456){i=8;break}if(i&134217728){i=9;break}if(r=r+4<<16>>16,i=i<<4,i&1073741824)break e}if((i|0)==7){r=r|1;break}else if((i|0)==8){r=r|2;break}else if((i|0)==9){r=r|3;break}}else r=0;while(!1);return r|0}function O2(i){i=i|0;var r=0,t=0;if(!(i<<16>>16))return t=0,t|0;if(r=(i&65535)-((i&65535)>>>15&65535)|0,r=(r<<16>>31^r)<<16,i=r>>16,!(i&16384))t=r,r=0;else return t=0,t|0;for(;;){if(i&8192){i=r,t=7;break}if(i&4096){i=r,t=8;break}if(i&2048){i=r,t=9;break}if(r=r+4<<16>>16,t=t<<4,i=t>>16,i&16384){i=r,t=10;break}}return(t|0)==7?(t=i|1,t|0):(t|0)==8?(t=i|2,t|0):(t|0)==9?(t=i|3,t|0):(t|0)==10?i|0:0}function x0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;return r=r<<16>>16,(r&134217727|0)==33554432?(s[t>>2]=1,r=2147483647):r=r<<6,o=r>>>16&31,a=e[7792+(o<<1)>>1]|0,n=a<<16,r=y(a-(D[7792+(o+1<<1)>>1]|0)<<16>>16,r>>>1&32767)|0,(r|0)==1073741824?(s[t>>2]=1,o=2147483647):o=r<<1,r=n-o|0,((r^n)&(o^n)|0)>=0?(a=r,i=i&65535,i=30-i|0,i=i&65535,t=Q5(a,i,t)|0,t|0):(s[t>>2]=1,a=(a>>>15&1)+2147483647|0,i=i&65535,i=30-i|0,i=i&65535,t=Q5(a,i,t)|0,t|0)}function G2(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;if(w=O,O=O+48|0,m=w,d=0-(t&65535)|0,d=n<<16>>16?d<<1&131070:d,t=d&65535,d=(t<<16>>16<0?d+6|0:d)<<16>>16,a=6-d|0,e[m>>1]=e[7858+(d<<1)>>1]|0,e[m+2>>1]=e[7858+(a<<1)>>1]|0,e[m+4>>1]=e[7858+(d+6<<1)>>1]|0,e[m+6>>1]=e[7858+(a+6<<1)>>1]|0,e[m+8>>1]=e[7858+(d+12<<1)>>1]|0,e[m+10>>1]=e[7858+(a+12<<1)>>1]|0,e[m+12>>1]=e[7858+(d+18<<1)>>1]|0,e[m+14>>1]=e[7858+(a+18<<1)>>1]|0,e[m+16>>1]=e[7858+(d+24<<1)>>1]|0,e[m+18>>1]=e[7858+(a+24<<1)>>1]|0,e[m+20>>1]=e[7858+(d+30<<1)>>1]|0,e[m+22>>1]=e[7858+(a+30<<1)>>1]|0,e[m+24>>1]=e[7858+(d+36<<1)>>1]|0,e[m+26>>1]=e[7858+(a+36<<1)>>1]|0,e[m+28>>1]=e[7858+(d+42<<1)>>1]|0,e[m+30>>1]=e[7858+(a+42<<1)>>1]|0,e[m+32>>1]=e[7858+(d+48<<1)>>1]|0,e[m+34>>1]=e[7858+(a+48<<1)>>1]|0,e[m+36>>1]=e[7858+(d+54<<1)>>1]|0,e[m+38>>1]=e[7858+(a+54<<1)>>1]|0,a=o<<16>>16>>>1&65535,!(a<<16>>16)){O=w;return}for(d=i+((t<<16>>16>>15<<16>>16)-(r<<16>>16)<<1)|0;;){for(c=d+2|0,f=e[c>>1]|0,r=f,o=d,l=5,u=m,n=16384,t=16384;k=e[u>>1]|0,b=(y(k,r<<16>>16)|0)+t|0,h=e[c+-2>>1]|0,t=(y(h,k)|0)+n|0,k=o,o=o+4|0,E=e[u+2>>1]|0,t=t+(y(E,f<<16>>16)|0)|0,n=e[o>>1]|0,E=b+(y(n,E)|0)|0,c=c+-4|0,b=e[u+4>>1]|0,h=E+(y(b,h)|0)|0,r=e[c>>1]|0,b=t+(y(r<<16>>16,b)|0)|0,t=e[u+6>>1]|0,n=b+(y(t,n)|0)|0,f=e[k+6>>1]|0,t=h+(y(f<<16>>16,t)|0)|0,!(l<<16>>16<=1);)l=l+-1<<16>>16,u=u+8|0;if(e[i>>1]=n>>>15,e[i+2>>1]=t>>>15,a=a+-1<<16>>16,a<<16>>16)d=d+4|0,i=i+4|0;else break}O=w}function y4(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0;if(M=O,O=O+144|0,E=M+120|0,A=M+100|0,T=M+80|0,x=M+60|0,F=M+40|0,h=M+20|0,k=M,Y2(t,E,10,f),Z5(E,A,f),(r|0)==8)for(e[a>>1]=0,u=2147483647,b=0;;){d=b*10|0,t=0,c=0;do N=(D[7980+(c+d<<1)>>1]|0)+(D[8140+(c<<1)>>1]|0)|0,e[k+(c<<1)>>1]=N,N=(D[E+(c<<1)>>1]|0)-(N&65535)|0,e[h+(c<<1)>>1]=N,N=N<<16,t=(y(N>>15,N>>16)|0)+t|0,c=c+1|0;while((c|0)!=10);if((t|0)<(u|0)){R=x,w=h,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));R=T,w=k,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));R=i,w=7980+(d<<1)|0,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));e[a>>1]=b}else t=u;if(b=b+1|0,(b|0)==8)break;u=t}else{t=0;do N=y(e[8160+(t<<1)>>1]|0,e[i+(t<<1)>>1]|0)|0,N=(N>>>15)+(D[8140+(t<<1)>>1]|0)|0,e[T+(t<<1)>>1]=N,e[x+(t<<1)>>1]=(D[E+(t<<1)>>1]|0)-N,t=t+1|0;while((t|0)!=10)}do if(r>>>0>=2)if(N=x+2|0,I=x+4|0,S=D[x>>1]|0,R=e[A>>1]<<1,E=D[N>>1]|0,h=e[A+2>>1]<<1,w=D[I>>1]|0,m=e[A+4>>1]<<1,(r|0)==5){for(k=2147483647,a=0,t=0,b=17908;c=(y(S-(D[b>>1]|0)<<16>>16,R)|0)>>16,c=y(c,c)|0,d=(y(E-(D[b+2>>1]|0)<<16>>16,h)|0)>>16,c=(y(d,d)|0)+c|0,d=(y(w-(D[b+4>>1]|0)<<16>>16,m)|0)>>16,d=c+(y(d,d)|0)|0,c=(d|0)<(k|0),t=c?a:t,a=a+1<<16>>16,!(a<<16>>16>=512);)k=c?d:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[x>>1]=e[17908+(d<<1)>>1]|0,e[N>>1]=e[17908+(d+1<<1)>>1]|0,e[I>>1]=e[17908+(d+2<<1)>>1]|0,e[n>>1]=t,d=x+6|0,c=x+8|0,S=x+10|0,b=D[d>>1]|0,a=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(y(a,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=y(u,u)|0,r=(y(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(y(r,r)|0)+u|0,r=(y(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(y(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=512);)l=u?r:l,R=R+6|0;l=(t<<16>>16)*3|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+(l+1<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=x+12|0,e[n+4>>1]=J5(l,12788,A+12|0,512)|0,E=N,b=I,t=S,u=x;break}else{for(k=2147483647,a=0,t=0,b=8180;c=(y(S-(D[b>>1]|0)<<16>>16,R)|0)>>16,c=y(c,c)|0,d=(y(E-(D[b+2>>1]|0)<<16>>16,h)|0)>>16,c=(y(d,d)|0)+c|0,d=(y(w-(D[b+4>>1]|0)<<16>>16,m)|0)>>16,d=c+(y(d,d)|0)|0,c=(d|0)<(k|0),t=c?a:t,a=a+1<<16>>16,!(a<<16>>16>=256);)k=c?d:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[x>>1]=e[8180+(d<<1)>>1]|0,e[N>>1]=e[8180+(d+1<<1)>>1]|0,e[I>>1]=e[8180+(d+2<<1)>>1]|0,e[n>>1]=t,d=x+6|0,c=x+8|0,S=x+10|0,b=D[d>>1]|0,a=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(y(a,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=y(u,u)|0,r=(y(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(y(r,r)|0)+u|0,r=(y(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(y(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=512);)l=u?r:l,R=R+6|0;l=(t<<16>>16)*3|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+(l+1<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=x+12|0,e[n+4>>1]=J5(l,12788,A+12|0,512)|0,E=N,b=I,t=S,u=x;break}else{for(I=x+2|0,N=x+4|0,d=D[x>>1]|0,c=e[A>>1]<<1,u=D[I>>1]|0,l=e[A+2>>1]<<1,r=D[N>>1]|0,m=e[A+4>>1]<<1,k=2147483647,a=0,t=0,b=8180;h=(y(c,d-(D[b>>1]|0)<<16>>16)|0)>>16,h=y(h,h)|0,w=(y(l,u-(D[b+2>>1]|0)<<16>>16)|0)>>16,h=(y(w,w)|0)+h|0,w=(y(m,r-(D[b+4>>1]|0)<<16>>16)|0)>>16,w=h+(y(w,w)|0)|0,h=(w|0)<(k|0),t=h?a:t,a=a+1<<16>>16,!(a<<16>>16>=256);)k=h?w:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[x>>1]=e[8180+(d<<1)>>1]|0,e[I>>1]=e[8180+(d+1<<1)>>1]|0,e[N>>1]=e[8180+(d+2<<1)>>1]|0,e[n>>1]=t,d=x+6|0,c=x+8|0,S=x+10|0,b=D[d>>1]|0,a=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(y(a,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=y(u,u)|0,r=(y(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(y(r,r)|0)+u|0,r=(y(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(y(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=256);)l=u?r:l,R=R+12|0;l=(t<<16>>16)*6|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+((l|1)<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=x+12|0,e[n+4>>1]=J5(l,16884,A+12|0,128)|0,E=I,b=N,t=S,u=x}while(!1);R=i,w=x,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));e[F>>1]=(D[T>>1]|0)+(D[u>>1]|0),e[F+2>>1]=(D[T+2>>1]|0)+(D[E>>1]|0),e[F+4>>1]=(D[T+4>>1]|0)+(D[b>>1]|0),e[F+6>>1]=(D[T+6>>1]|0)+(D[d>>1]|0),e[F+8>>1]=(D[T+8>>1]|0)+(D[c>>1]|0),e[F+10>>1]=(D[T+10>>1]|0)+(D[t>>1]|0),e[F+12>>1]=(D[T+12>>1]|0)+(D[l>>1]|0),e[F+14>>1]=(D[T+14>>1]|0)+(D[x+14>>1]|0),e[F+16>>1]=(D[T+16>>1]|0)+(D[x+16>>1]|0),e[F+18>>1]=(D[T+18>>1]|0)+(D[x+18>>1]|0),A0(F,205,10,f),b0(F,o,10,f),O=M}function J5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0;if(R=i+2|0,S=i+4|0,I=i+6|0,o<<16>>16>0)for(d=D[i>>1]|0,m=e[t>>1]<<1,w=D[R>>1]|0,h=e[t+2>>1]<<1,k=D[S>>1]|0,b=e[t+4>>1]<<1,E=D[I>>1]|0,n=e[t+6>>1]<<1,l=2147483647,u=0,t=0,c=r;a=(y(m,d-(D[c>>1]|0)<<16>>16)|0)>>16,a=y(a,a)|0,f=(y(h,w-(D[c+2>>1]|0)<<16>>16)|0)>>16,a=(y(f,f)|0)+a|0,f=(y(b,k-(D[c+4>>1]|0)<<16>>16)|0)>>16,f=a+(y(f,f)|0)|0,a=(y(n,E-(D[c+6>>1]|0)<<16>>16)|0)>>16,a=f+(y(a,a)|0)|0,f=(a|0)<(l|0),t=f?u:t,u=u+1<<16>>16,!(u<<16>>16>=o<<16>>16);)l=f?a:l,c=c+8|0;else t=0;return o=t<<16>>16<<2,E=o|1,e[i>>1]=e[r+(o<<1)>>1]|0,e[R>>1]=e[r+(E<<1)>>1]|0,e[S>>1]=e[r+(E+1<<1)>>1]|0,e[I>>1]=e[r+((o|3)<<1)>>1]|0,t|0}function Hi(i,r,t,o,n,a,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0;for(V=O,O=O+192|0,c=V+160|0,u=V+140|0,M=V+120|0,C=V+100|0,z=V+80|0,j=V+60|0,l=V+40|0,G=V+20|0,K=V,Y2(r,c,10,f),Y2(t,u,10,f),Z5(c,M,f),Z5(u,C,f),d=0,t=z,r=j,m=l;x=(((e[i+(d<<1)>>1]|0)*21299|0)>>>15)+(D[20980+(d<<1)>>1]|0)|0,e[t>>1]=x,e[r>>1]=(D[c>>1]|0)-x,e[m>>1]=(D[u>>1]|0)-x,d=d+1|0,(d|0)!=10;)c=c+2|0,u=u+2|0,t=t+2|0,r=r+2|0,m=m+2|0;for(e[a>>1]=b5(j,l,21e3,e[M>>1]|0,e[M+2>>1]|0,e[C>>1]|0,e[C+2>>1]|0,128)|0,e[a+2>>1]=b5(j+4|0,l+4|0,22024,e[M+4>>1]|0,e[M+6>>1]|0,e[C+4>>1]|0,e[C+6>>1]|0,256)|0,A=j+8|0,F=l+8|0,T=j+10|0,x=l+10|0,t=e[A>>1]|0,w=e[M+8>>1]<<1,h=e[T>>1]|0,k=e[M+10>>1]<<1,b=e[F>>1]|0,E=e[C+8>>1]<<1,R=e[x>>1]|0,S=e[C+10>>1]<<1,u=2147483647,I=0,m=0,N=24072,r=0;c=e[N>>1]|0,d=(y(t-c<<16>>16,w)|0)>>16,d=y(d,d)|0,c=(y(c+t<<16>>16,w)|0)>>16,c=y(c,c)|0,U=e[N+2>>1]|0,q=(y(h-U<<16>>16,k)|0)>>16,d=(y(q,q)|0)+d|0,U=(y(U+h<<16>>16,k)|0)>>16,c=(y(U,U)|0)+c|0,(d|0)<(u|0)|(c|0)<(u|0)?(q=e[N+4>>1]|0,U=(y(b-q<<16>>16,E)|0)>>16,U=(y(U,U)|0)+d|0,q=(y(q+b<<16>>16,E)|0)>>16,q=(y(q,q)|0)+c|0,c=e[N+6>>1]|0,d=(y(R-c<<16>>16,S)|0)>>16,d=U+(y(d,d)|0)|0,c=(y(c+R<<16>>16,S)|0)>>16,c=q+(y(c,c)|0)|0,q=(d|0)<(u|0),d=q?d:u,U=(c|0)<(d|0),d=U?c:d,m=q|U?I:m,r=U?1:q?0:r):d=u,I=I+1<<16>>16,!(I<<16>>16>=256);)u=d,N=N+8|0;for(d=m<<16>>16,c=d<<2,m=c|1,u=24072+(m<<1)|0,t=e[24072+(c<<1)>>1]|0,r<<16>>16?(e[A>>1]=0-(t&65535),e[T>>1]=0-(D[u>>1]|0),e[F>>1]=0-(D[24072+(m+1<<1)>>1]|0),e[x>>1]=0-(D[24072+((c|3)<<1)>>1]|0),r=d<<1&65534|1):(e[A>>1]=t,e[T>>1]=e[u>>1]|0,e[F>>1]=e[24072+(m+1<<1)>>1]|0,e[x>>1]=e[24072+((c|3)<<1)>>1]|0,r=d<<1),e[a+4>>1]=r,e[a+6>>1]=b5(j+12|0,l+12|0,26120,e[M+12>>1]|0,e[M+14>>1]|0,e[C+12>>1]|0,e[C+14>>1]|0,256)|0,e[a+8>>1]=b5(j+16|0,l+16|0,28168,e[M+16>>1]|0,e[M+18>>1]|0,e[C+16>>1]|0,e[C+18>>1]|0,64)|0,u=0,c=G,d=K,t=z,r=j;U=D[t>>1]|0,e[c>>1]=U+(D[r>>1]|0),q=e[l>>1]|0,e[d>>1]=U+(q&65535),e[i+(u<<1)>>1]=q,u=u+1|0,(u|0)!=10;)c=c+2|0,d=d+2|0,t=t+2|0,r=r+2|0,l=l+2|0;A0(G,205,10,f),A0(K,205,10,f),b0(G,o,10,f),b0(K,n,10,f),O=V}function b5(i,r,t,o,n,a,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0;if(h=e[i>>1]|0,I=i+2|0,b=e[I>>1]|0,R=e[r>>1]|0,N=r+2|0,S=e[N>>1]|0,l<<16>>16>0)for(w=o<<16>>16<<1,m=n<<16>>16<<1,d=a<<16>>16<<1,n=f<<16>>16<<1,a=2147483647,u=0,o=0,c=t;f=(y(w,h-(e[c>>1]|0)|0)|0)>>16,f=y(f,f)|0,(f|0)<(a|0)&&(k=(y(m,b-(e[c+2>>1]|0)|0)|0)>>16,k=(y(k,k)|0)+f|0,(k|0)<(a|0))&&(E=(y(d,R-(e[c+4>>1]|0)|0)|0)>>16,E=(y(E,E)|0)+k|0,(E|0)<(a|0))?(f=(y(n,S-(e[c+6>>1]|0)|0)|0)>>16,f=(y(f,f)|0)+E|0,A=(f|0)<(a|0),f=A?f:a,o=A?u:o):f=a,u=u+1<<16>>16,!(u<<16>>16>=l<<16>>16);)a=f,c=c+8|0;else o=0;return A=o<<16>>16<<2,l=A|1,e[i>>1]=e[t+(A<<1)>>1]|0,e[I>>1]=e[t+(l<<1)>>1]|0,e[r>>1]=e[t+(l+1<<1)>>1]|0,e[N>>1]=e[t+((A|3)<<1)>>1]|0,o|0}function Wi(i){i=i|0;var r=0,t=0,o=0;if(!i||(s[i>>2]=0,r=s0(20)|0,!r))return o=-1,o|0;t=r,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return s[i>>2]=r,o=0,o|0}function _4(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+20|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function Xi(i){i=i|0;var r=0;i&&(r=s[i>>2]|0,r&&(K1(r),s[i>>2]=0))}function A0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0;if(!(t<<16>>16<=0))for(n=r<<16>>16,a=r&65535,f=0;o=e[i>>1]|0,o<<16>>16<r<<16>>16?(e[i>>1]=r,o=(r<<16>>16)+n|0):o=(o&65535)+a|0,f=f+1<<16>>16,!(f<<16>>16>=t<<16>>16);)r=o&65535,i=i+2|0}function L2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0;if(n=o<<16>>16,o=n>>>2&65535,!!(o<<16>>16))for(w=n+-1|0,S=i+20|0,k=r+(n+-4<<1)|0,b=r+(n+-3<<1)|0,E=r+(n+-2<<1)|0,R=r+(w<<1)|0,h=r+(n+-11<<1)|0,w=t+(w<<1)|0;;){for(r=e[S>>1]|0,f=5,l=S,u=h,c=h+-2|0,d=h+-4|0,m=h+-6|0,a=2048,i=2048,n=2048,t=2048;a=(y(e[u>>1]|0,r)|0)+a|0,i=(y(e[c>>1]|0,r)|0)+i|0,n=(y(e[d>>1]|0,r)|0)+n|0,r=(y(e[m>>1]|0,r)|0)+t|0,t=e[l+-2>>1]|0,a=a+(y(e[u+2>>1]|0,t)|0)|0,i=i+(y(e[c+2>>1]|0,t)|0)|0,n=n+(y(e[d+2>>1]|0,t)|0)|0,l=l+-4|0,t=r+(y(e[m+2>>1]|0,t)|0)|0,f=f+-1<<16>>16,r=e[l>>1]|0,f<<16>>16;)u=u+4|0,c=c+4|0,d=d+4|0,m=m+4|0;if(u=(y(e[R>>1]|0,r)|0)+a|0,c=(y(e[E>>1]|0,r)|0)+i|0,d=(y(e[b>>1]|0,r)|0)+n|0,m=(y(e[k>>1]|0,r)|0)+t|0,e[w>>1]=u>>>12,e[w+-2>>1]=c>>>12,e[w+-4>>1]=d>>>12,e[w+-6>>1]=m>>>12,o=o+-1<<16>>16,o<<16>>16)k=k+-8|0,b=b+-8|0,E=E+-8|0,R=R+-8|0,h=h+-8|0,w=w+-8|0;else break}}function S1(i,r){i=i|0,r=r|0;var t=0;return t=i+32768|0,(i|0)>-1&(t^i|0)<0&&(s[r>>2]=1,t=(i>>>31)+2147483647|0),t>>>16&65535|0}function W1(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;return o=r<<16>>16,r<<16>>16?r<<16>>16>0?(i=i<<16>>16>>(r<<16>>16>15?15:o)&65535,i|0):(n=0-o|0,r=i<<16>>16,n=(n&65535)<<16>>16>15?15:n<<16>>16,o=r<<n,(o<<16>>16>>n|0)==(r|0)?(n=o&65535,n|0):(s[t>>2]=1,n=i<<16>>16>0?32767:-32768,n|0)):i|0}function K2(i,r,t){return i=i|0,r=r|0,t=t|0,r<<16>>16>15?(r=0,r|0):(t=W1(i,r,t)|0,r<<16>>16>0?t+((1<<(r<<16>>16)+-1&i<<16>>16|0)!=0&1)<<16>>16|0:(r=t,r|0))}function e6(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0;return(i|0)<1?(e[r>>1]=0,t=0,t|0):(n=(B1(i)|0)&65534,a=n&65535,n=n<<16>>16,a<<16>>16>0?(o=i<<n,(o>>n|0)!=(i|0)&&(o=i>>31^2147483647)):(n=0-n<<16,(n|0)<2031616?o=i>>(n>>16):o=0),e[r>>1]=a,r=o>>>25&63,r=r>>>0>15?r+-16|0:r,a=e[30216+(r<<1)>>1]|0,i=a<<16,o=y(a-(D[30216+(r+1<<1)>>1]|0)<<16>>16,o>>>10&32767)|0,(o|0)==1073741824?(s[t>>2]=1,n=2147483647):n=o<<1,o=i-n|0,((o^i)&(n^i)|0)>=0?(t=o,t|0):(s[t>>2]=1,t=(a>>>15&1)+2147483647|0,t|0))}function E1(i,r,t){return i=i|0,r=r|0,t=t|0,i=(i<<16>>16)-(r<<16>>16)|0,(i+32768|0)>>>0<=65535?(t=i,t=t&65535,t|0):(s[t>>2]=1,t=(i|0)>32767?32767:-32768,t=t&65535,t|0)}function c0(i,r,t,o,n,a){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,a=a|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0;x=O,O=O+48|0,w=x,u=w,f=n,l=u+20|0;do e[u>>1]=e[f>>1]|0,u=u+2|0,f=f+2|0;while((u|0)<(l|0));for(m=w+18|0,E=i+2|0,R=i+4|0,h=r+20|0,S=i+6|0,I=i+8|0,N=i+10|0,A=i+12|0,F=i+14|0,T=i+16|0,k=i+18|0,b=i+20|0,l=e[m>>1]|0,f=5,c=r,d=t,u=w+20|0;z=e[i>>1]|0,C=(y(z,e[c>>1]|0)|0)+2048|0,z=(y(e[c+2>>1]|0,z)|0)+2048|0,w=l<<16>>16,C=C-(y(w,e[E>>1]|0)|0)|0,M=e[R>>1]|0,w=z-(y(w,M)|0)|0,z=e[m+-2>>1]|0,M=C-(y(z,M)|0)|0,C=e[S>>1]|0,z=w-(y(C,z)|0)|0,w=e[m+-4>>1]|0,C=M-(y(w,C)|0)|0,M=e[I>>1]|0,w=z-(y(M,w)|0)|0,z=e[m+-6>>1]|0,M=C-(y(z,M)|0)|0,C=e[N>>1]|0,z=w-(y(z,C)|0)|0,w=e[m+-8>>1]|0,C=M-(y(w,C)|0)|0,M=e[A>>1]|0,w=z-(y(M,w)|0)|0,z=e[m+-10>>1]|0,M=C-(y(z,M)|0)|0,C=e[F>>1]|0,z=w-(y(C,z)|0)|0,w=e[m+-12>>1]|0,C=M-(y(w,C)|0)|0,M=e[T>>1]|0,w=z-(y(w,M)|0)|0,z=e[m+-14>>1]|0,M=C-(y(z,M)|0)|0,C=e[k>>1]|0,z=w-(y(C,z)|0)|0,w=e[m+-16>>1]|0,C=M-(y(w,C)|0)|0,M=e[b>>1]|0,w=z-(y(M,w)|0)|0,M=C-(y(e[m+-18>>1]|0,M)|0)|0,M=(M+134217728|0)>>>0<268435455?M>>>12&65535:(M|0)>134217727?32767:-32768,w=w-(y(e[E>>1]|0,M<<16>>16)|0)|0,m=u+2|0,e[u>>1]=M,e[d>>1]=M,l=(w+134217728|0)>>>0<268435455?w>>>12&65535:(w|0)>134217727?32767:-32768,e[m>>1]=l,e[d+2>>1]=l,f=f+-1<<16>>16,f<<16>>16;)c=c+4|0,d=d+4|0,u=u+4|0;if(o=(o<<16>>16)+-10|0,u=o>>>1&65535,u<<16>>16)for(w=t+18|0,l=r+16|0,m=e[w>>1]|0,c=h,f=t+20|0;;){M=e[i>>1]|0,d=(y(M,e[c>>1]|0)|0)+2048|0,M=(y(e[l+6>>1]|0,M)|0)+2048|0,l=e[E>>1]|0,C=m<<16>>16,d=d-(y(C,l)|0)|0,z=e[R>>1]|0,C=M-(y(C,z)|0)|0,M=e[w+-2>>1]|0,z=d-(y(M,z)|0)|0,d=e[S>>1]|0,M=C-(y(d,M)|0)|0,C=e[w+-4>>1]|0,d=z-(y(C,d)|0)|0,z=e[I>>1]|0,C=M-(y(z,C)|0)|0,M=e[w+-6>>1]|0,z=d-(y(M,z)|0)|0,d=e[N>>1]|0,M=C-(y(M,d)|0)|0,C=e[w+-8>>1]|0,d=z-(y(C,d)|0)|0,z=e[A>>1]|0,C=M-(y(z,C)|0)|0,M=e[w+-10>>1]|0,z=d-(y(M,z)|0)|0,d=e[F>>1]|0,M=C-(y(d,M)|0)|0,C=e[w+-12>>1]|0,d=z-(y(C,d)|0)|0,z=e[T>>1]|0,C=M-(y(C,z)|0)|0,M=e[w+-14>>1]|0,z=d-(y(M,z)|0)|0,d=e[k>>1]|0,M=C-(y(d,M)|0)|0,C=e[w+-16>>1]|0,d=z-(y(C,d)|0)|0,z=e[b>>1]|0,C=M-(y(z,C)|0)|0,z=d-(y(e[w+-18>>1]|0,z)|0)|0,d=c+4|0,z=(z+134217728|0)>>>0<268435455?z>>>12&65535:(z|0)>134217727?32767:-32768,l=C-(y(l,z<<16>>16)|0)|0,w=f+2|0,e[f>>1]=z;do if((l+134217728|0)>>>0>=268435455)if(f=f+4|0,(l|0)>134217727){e[w>>1]=32767,l=32767;break}else{e[w>>1]=-32768,l=-32768;break}else l=l>>>12&65535,e[w>>1]=l,f=f+4|0;while(!1);if(u=u+-1<<16>>16,u<<16>>16)z=c,m=l,c=d,l=z;else break}if(!(a<<16>>16)){O=x;return}u=n,f=t+(o<<1)|0,l=u+20|0;do e[u>>1]=e[f>>1]|0,u=u+2|0,f=f+2|0;while((u|0)<(l|0));O=x}function X0(i,r,t){i=i|0,r=r|0,t=t|0,e[t>>1]=e[i>>1]|0,e[t+2>>1]=((y(e[r>>1]|0,e[i+2>>1]|0)|0)+16384|0)>>>15,e[t+4>>1]=((y(e[r+2>>1]|0,e[i+4>>1]|0)|0)+16384|0)>>>15,e[t+6>>1]=((y(e[r+4>>1]|0,e[i+6>>1]|0)|0)+16384|0)>>>15,e[t+8>>1]=((y(e[r+6>>1]|0,e[i+8>>1]|0)|0)+16384|0)>>>15,e[t+10>>1]=((y(e[r+8>>1]|0,e[i+10>>1]|0)|0)+16384|0)>>>15,e[t+12>>1]=((y(e[r+10>>1]|0,e[i+12>>1]|0)|0)+16384|0)>>>15,e[t+14>>1]=((y(e[r+12>>1]|0,e[i+14>>1]|0)|0)+16384|0)>>>15,e[t+16>>1]=((y(e[r+14>>1]|0,e[i+16>>1]|0)|0)+16384|0)>>>15,e[t+18>>1]=((y(e[r+16>>1]|0,e[i+18>>1]|0)|0)+16384|0)>>>15,e[t+20>>1]=((y(e[r+18>>1]|0,e[i+20>>1]|0)|0)+16384|0)>>>15}function s0(i){i=i|0;var r=0,t=0,o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0,z=0,j=0,G=0,K=0,V=0,U=0,q=0,i1=0,W=0,r1=0,s1=0,c1=0,h1=0,d1=0,t1=0,l1=0,w1=0,m1=0,$=0,H=0;do if(i>>>0<245){if(R=i>>>0<11?16:i+11&-8,i=R>>>3,m=s[26]|0,u=m>>>i,u&3){o=(u&1^1)+i|0,r=o<<1,t=144+(r<<2)|0,r=144+(r+2<<2)|0,n=s[r>>2]|0,a=n+8|0,f=s[a>>2]|0;do if((t|0)==(f|0))s[26]=m&~(1<<o);else{if(f>>>0>=(s[30]|0)>>>0&&(d=f+12|0,(s[d>>2]|0)==(n|0))){s[d>>2]=t,s[r>>2]=f;break}b1()}while(!1);$=o<<3,s[n+4>>2]=$|3,$=n+($|4)|0,s[$>>2]=s[$>>2]|1;break}if(r=s[28]|0,R>>>0>r>>>0){if(u){n=2<<i,n=u<<i&(n|0-n),n=(n&0-n)+-1|0,a=n>>>12&16,n=n>>>a,o=n>>>5&8,n=n>>>o,t=n>>>2&4,n=n>>>t,f=n>>>1&2,n=n>>>f,l=n>>>1&1,l=(o|a|t|f|l)+(n>>>l)|0,n=l<<1,f=144+(n<<2)|0,n=144+(n+2<<2)|0,t=s[n>>2]|0,a=t+8|0,o=s[a>>2]|0;do if((f|0)==(o|0))s[26]=m&~(1<<l),w=r;else{if(o>>>0>=(s[30]|0)>>>0&&(c=o+12|0,(s[c>>2]|0)==(t|0))){s[c>>2]=f,s[n>>2]=o,w=s[28]|0;break}b1()}while(!1);$=l<<3,r=$-R|0,s[t+4>>2]=R|3,u=t+R|0,s[t+(R|4)>>2]=r|1,s[t+$>>2]=r,w&&(t=s[31]|0,o=w>>>3,f=o<<1,l=144+(f<<2)|0,n=s[26]|0,o=1<<o,n&o?(n=144+(f+2<<2)|0,f=s[n>>2]|0,f>>>0<(s[30]|0)>>>0?b1():(k=n,b=f)):(s[26]=n|o,k=144+(f+2<<2)|0,b=l),s[k>>2]=t,s[b+12>>2]=t,s[t+8>>2]=b,s[t+12>>2]=l),s[28]=r,s[31]=u;break}if(i=s[27]|0,i){for(n=(i&0-i)+-1|0,m1=n>>>12&16,n=n>>>m1,w1=n>>>5&8,n=n>>>w1,$=n>>>2&4,n=n>>>$,f=n>>>1&2,n=n>>>f,u=n>>>1&1,u=s[408+((w1|m1|$|f|u)+(n>>>u)<<2)>>2]|0,n=(s[u+4>>2]&-8)-R|0,f=u;;){if(l=s[f+16>>2]|0,!l&&(l=s[f+20>>2]|0,!l)){r=n;break}f=(s[l+4>>2]&-8)-R|0,$=f>>>0<n>>>0,n=$?f:n,f=l,u=$?l:u}if(i=s[30]|0,u>>>0>=i>>>0&&(N=u+R|0,u>>>0<N>>>0)){o=s[u+24>>2]|0,l=s[u+12>>2]|0;do if((l|0)==(u|0)){if(f=u+20|0,l=s[f>>2]|0,!l&&(f=u+16|0,l=s[f>>2]|0,!l)){S=0;break}for(;;){if(a=l+20|0,n=s[a>>2]|0,n){l=n,f=a;continue}if(a=l+16|0,n=s[a>>2]|0,n)l=n,f=a;else break}if(f>>>0<i>>>0)b1();else{s[f>>2]=0,S=l;break}}else{if(f=s[u+8>>2]|0,f>>>0>=i>>>0&&(t=f+12|0,(s[t>>2]|0)==(u|0))&&(h=l+8|0,(s[h>>2]|0)==(u|0))){s[t>>2]=l,s[h>>2]=f,S=l;break}b1()}while(!1);do if(o){if(f=s[u+28>>2]|0,a=408+(f<<2)|0,(u|0)==(s[a>>2]|0)){if(s[a>>2]=S,!S){s[27]=s[27]&~(1<<f);break}}else if(o>>>0<(s[30]|0)>>>0&&b1(),f=o+16|0,(s[f>>2]|0)==(u|0)?s[f>>2]=S:s[o+20>>2]=S,!S)break;a=s[30]|0,S>>>0<a>>>0&&b1(),s[S+24>>2]=o,f=s[u+16>>2]|0;do if(f)if(f>>>0<a>>>0)b1();else{s[S+16>>2]=f,s[f+24>>2]=S;break}while(!1);if(f=s[u+20>>2]|0,f)if(f>>>0<(s[30]|0)>>>0)b1();else{s[S+20>>2]=f,s[f+24>>2]=S;break}}while(!1);r>>>0<16?($=r+R|0,s[u+4>>2]=$|3,$=u+($+4)|0,s[$>>2]=s[$>>2]|1):(s[u+4>>2]=R|3,s[u+(R|4)>>2]=r|1,s[u+(r+R)>>2]=r,o=s[28]|0,o&&(t=s[31]|0,n=o>>>3,f=n<<1,l=144+(f<<2)|0,a=s[26]|0,n=1<<n,a&n?(f=144+(f+2<<2)|0,a=s[f>>2]|0,a>>>0<(s[30]|0)>>>0?b1():(I=f,A=a)):(s[26]=a|n,I=144+(f+2<<2)|0,A=l),s[I>>2]=t,s[A+12>>2]=t,s[t+8>>2]=A,s[t+12>>2]=l),s[28]=r,s[31]=N),a=u+8|0;break}b1()}else H=154}else H=154}else if(i>>>0<=4294967231)if(i=i+11|0,A=i&-8,m=s[27]|0,m){u=0-A|0,i=i>>>8,i?A>>>0>16777215?d=31:(N=(i+1048320|0)>>>16&8,H=i<<N,I=(H+520192|0)>>>16&4,H=H<<I,d=(H+245760|0)>>>16&2,d=14-(I|N|d)+(H<<d>>>15)|0,d=A>>>(d+7|0)&1|d<<1):d=0,i=s[408+(d<<2)>>2]|0;e:do if(!i)l=0,i=0,H=86;else for(t=u,l=0,r=A<<((d|0)==31?0:25-(d>>>1)|0),c=i,i=0;;){if(o=s[c+4>>2]&-8,u=o-A|0,u>>>0<t>>>0)if((o|0)==(A|0)){o=c,i=c,H=90;break e}else i=c;else u=t;if(H=s[c+20>>2]|0,c=s[c+16+(r>>>31<<2)>>2]|0,l=(H|0)==0|(H|0)==(c|0)?l:H,c)t=u,r=r<<1;else{H=86;break}}while(!1);if((H|0)==86){if((l|0)==0&(i|0)==0){if(i=2<<d,i=m&(i|0-i),!i){R=A,H=154;break}i=(i&0-i)+-1|0,S=i>>>12&16,i=i>>>S,b=i>>>5&8,i=i>>>b,I=i>>>2&4,i=i>>>I,N=i>>>1&2,i=i>>>N,l=i>>>1&1,l=s[408+((b|S|I|N|l)+(i>>>l)<<2)>>2]|0,i=0}l?(o=l,H=90):(b=u,k=i)}if((H|0)==90)for(;;){if(H=0,N=(s[o+4>>2]&-8)-A|0,l=N>>>0<u>>>0,u=l?N:u,i=l?o:i,l=s[o+16>>2]|0,l){o=l,H=90;continue}if(o=s[o+20>>2]|0,o)H=90;else{b=u,k=i;break}}if(k|0&&b>>>0<((s[28]|0)-A|0)>>>0){if(i=s[30]|0,k>>>0>=i>>>0&&(U=k+A|0,k>>>0<U>>>0)){u=s[k+24>>2]|0,l=s[k+12>>2]|0;do if((l|0)==(k|0)){if(f=k+20|0,l=s[f>>2]|0,!l&&(f=k+16|0,l=s[f>>2]|0,!l)){T=0;break}for(;;){if(a=l+20|0,n=s[a>>2]|0,n){l=n,f=a;continue}if(a=l+16|0,n=s[a>>2]|0,n)l=n,f=a;else break}if(f>>>0<i>>>0)b1();else{s[f>>2]=0,T=l;break}}else{if(f=s[k+8>>2]|0,f>>>0>=i>>>0&&(E=f+12|0,(s[E>>2]|0)==(k|0))&&(R=l+8|0,(s[R>>2]|0)==(k|0))){s[E>>2]=l,s[R>>2]=f,T=l;break}b1()}while(!1);do if(u){if(l=s[k+28>>2]|0,f=408+(l<<2)|0,(k|0)==(s[f>>2]|0)){if(s[f>>2]=T,!T){s[27]=s[27]&~(1<<l);break}}else if(u>>>0<(s[30]|0)>>>0&&b1(),f=u+16|0,(s[f>>2]|0)==(k|0)?s[f>>2]=T:s[u+20>>2]=T,!T)break;l=s[30]|0,T>>>0<l>>>0&&b1(),s[T+24>>2]=u,f=s[k+16>>2]|0;do if(f)if(f>>>0<l>>>0)b1();else{s[T+16>>2]=f,s[f+24>>2]=T;break}while(!1);if(f=s[k+20>>2]|0,f)if(f>>>0<(s[30]|0)>>>0)b1();else{s[T+20>>2]=f,s[f+24>>2]=T;break}}while(!1);e:do if(b>>>0>=16){if(s[k+4>>2]=A|3,s[k+(A|4)>>2]=b|1,s[k+(b+A)>>2]=b,l=b>>>3,b>>>0<256){a=l<<1,o=144+(a<<2)|0,n=s[26]|0,f=1<<l,n&f?(f=144+(a+2<<2)|0,a=s[f>>2]|0,a>>>0<(s[30]|0)>>>0?b1():(x=f,M=a)):(s[26]=n|f,x=144+(a+2<<2)|0,M=o),s[x>>2]=U,s[M+12>>2]=U,s[k+(A+8)>>2]=M,s[k+(A+12)>>2]=o;break}if(t=b>>>8,t?b>>>0>16777215?l=31:(m1=(t+1048320|0)>>>16&8,$=t<<m1,w1=($+520192|0)>>>16&4,$=$<<w1,l=($+245760|0)>>>16&2,l=14-(w1|m1|l)+($<<l>>>15)|0,l=b>>>(l+7|0)&1|l<<1):l=0,f=408+(l<<2)|0,s[k+(A+28)>>2]=l,s[k+(A+20)>>2]=0,s[k+(A+16)>>2]=0,a=s[27]|0,n=1<<l,!(a&n)){s[27]=a|n,s[f>>2]=U,s[k+(A+24)>>2]=f,s[k+(A+12)>>2]=U,s[k+(A+8)>>2]=U;break}t=s[f>>2]|0;i:do if((s[t+4>>2]&-8|0)!=(b|0)){for(l=b<<((l|0)==31?0:25-(l>>>1)|0);r=t+16+(l>>>31<<2)|0,f=s[r>>2]|0,!!f;)if((s[f+4>>2]&-8|0)==(b|0)){z=f;break i}else l=l<<1,t=f;if(r>>>0<(s[30]|0)>>>0)b1();else{s[r>>2]=U,s[k+(A+24)>>2]=t,s[k+(A+12)>>2]=U,s[k+(A+8)>>2]=U;break e}}else z=t;while(!1);if(t=z+8|0,r=s[t>>2]|0,$=s[30]|0,r>>>0>=$>>>0&z>>>0>=$>>>0){s[r+12>>2]=U,s[t>>2]=U,s[k+(A+8)>>2]=r,s[k+(A+12)>>2]=z,s[k+(A+24)>>2]=0;break}else b1()}else $=b+A|0,s[k+4>>2]=$|3,$=k+($+4)|0,s[$>>2]=s[$>>2]|1;while(!1);a=k+8|0;break}b1()}else R=A,H=154}else R=A,H=154;else R=-1,H=154;while(!1);e:do if((H|0)==154){if(i=s[28]|0,i>>>0>=R>>>0){r=i-R|0,t=s[31]|0,r>>>0>15?(s[31]=t+R,s[28]=r,s[t+(R+4)>>2]=r|1,s[t+i>>2]=r,s[t+4>>2]=R|3):(s[28]=0,s[31]=0,s[t+4>>2]=i|3,H=t+(i+4)|0,s[H>>2]=s[H>>2]|1),a=t+8|0;break}if(i=s[29]|0,i>>>0>R>>>0){H=i-R|0,s[29]=H,a=s[32]|0,s[32]=a+R,s[a+(R+4)>>2]=H|1,s[a+4>>2]=R|3,a=a+8|0;break}if(s[144]|0||Yi(),m=R+48|0,t=s[146]|0,d=R+47|0,o=t+d|0,t=0-t|0,c=o&t,c>>>0>R>>>0){if(i=s[136]|0,i|0&&(z=s[134]|0,U=z+c|0,U>>>0<=z>>>0|U>>>0>i>>>0)){a=0;break}i:do if(s[137]&4)i=0,H=189;else{i=s[32]|0;t:do if(i){for(l=552;;){if(u=s[l>>2]|0,u>>>0<=i>>>0&&(F=l+4|0,(u+(s[F>>2]|0)|0)>>>0>i>>>0)){a=l,i=F;break}if(l=s[l+8>>2]|0,!l){H=172;break t}}if(u=o-(s[29]|0)&t,u>>>0<2147483647)if(l=b2(u|0)|0,U=(l|0)==((s[a>>2]|0)+(s[i>>2]|0)|0),i=U?u:0,U){if((l|0)!=-1){M=l,S=i,H=192;break i}}else H=182;else i=0}else H=172;while(!1);do if((H|0)==172)if(a=b2(0)|0,(a|0)!=-1)if(i=a,u=s[145]|0,l=u+-1|0,l&i?u=c-i+(l+i&0-u)|0:u=c,i=s[134]|0,l=i+u|0,u>>>0>R>>>0&u>>>0<2147483647){if(U=s[136]|0,U|0&&l>>>0<=i>>>0|l>>>0>U>>>0){i=0;break}if(l=b2(u|0)|0,H=(l|0)==(a|0),i=H?u:0,H){M=a,S=i,H=192;break i}else H=182}else i=0;else i=0;while(!1);t:do if((H|0)==182){a=0-u|0;do if(m>>>0>u>>>0&(u>>>0<2147483647&(l|0)!=-1)&&(C=s[146]|0,C=d-u+C&0-C,C>>>0<2147483647))if((b2(C|0)|0)==-1){b2(a|0)|0;break t}else{u=C+u|0;break}while(!1);if((l|0)!=-1){M=l,S=u,H=192;break i}}while(!1);s[137]=s[137]|4,H=189}while(!1);if((H|0)==189&&c>>>0<2147483647&&(j=b2(c|0)|0,G=b2(0)|0,j>>>0<G>>>0&((j|0)!=-1&(G|0)!=-1))&&(K=G-j|0,V=K>>>0>(R+40|0)>>>0,V)&&(M=j,S=V?K:i,H=192),(H|0)==192){u=(s[134]|0)+S|0,s[134]=u,u>>>0>(s[135]|0)>>>0&&(s[135]=u),b=s[32]|0;i:do if(b){a=552;do{if(i=s[a>>2]|0,u=a+4|0,l=s[u>>2]|0,(M|0)==(i+l|0)){q=i,i1=u,W=l,r1=a,H=202;break}a=s[a+8>>2]|0}while(a|0);if((H|0)==202&&!(s[r1+12>>2]&8|0)&&b>>>0<M>>>0&b>>>0>=q>>>0){s[i1>>2]=W+S,H=(s[29]|0)+S|0,$=b+8|0,$=$&7|0?0-$&7:0,m1=H-$|0,s[32]=b+$,s[29]=m1,s[b+($+4)>>2]=m1|1,s[b+(H+4)>>2]=40,s[33]=s[148];break}for(u=s[30]|0,M>>>0<u>>>0&&(s[30]=M,u=M),l=M+S|0,i=552;;){if((s[i>>2]|0)==(l|0)){a=i,l=i,H=210;break}if(i=s[i+8>>2]|0,!i){l=552;break}}if((H|0)==210)if(s[l+12>>2]&8)l=552;else{s[a>>2]=M,h=l+4|0,s[h>>2]=(s[h>>2]|0)+S,h=M+8|0,h=h&7|0?0-h&7:0,d=M+(S+8)|0,d=d&7|0?0-d&7:0,l=M+(d+S)|0,k=h+R|0,w=M+k|0,i=l-(M+h)-R|0,s[M+(h+4)>>2]=R|3;t:do if((l|0)!=(b|0)){if((l|0)==(s[31]|0)){H=(s[28]|0)+i|0,s[28]=H,s[31]=w,s[M+(k+4)>>2]=H|1,s[M+(H+k)>>2]=H;break}if(r=S+4|0,f=s[M+(r+d)>>2]|0,(f&3|0)==1){c=f&-8,o=f>>>3;r:do if(f>>>0>=256){t=s[M+((d|24)+S)>>2]|0,a=s[M+(S+12+d)>>2]|0;o:do if((a|0)==(l|0)){if(n=d|16,a=M+(r+n)|0,f=s[a>>2]|0,!f&&(a=M+(n+S)|0,f=s[a>>2]|0,!f)){l1=0;break}for(;;){if(n=f+20|0,o=s[n>>2]|0,o){f=o,a=n;continue}if(n=f+16|0,o=s[n>>2]|0,o)f=o,a=n;else break}if(a>>>0<u>>>0)b1();else{s[a>>2]=0,l1=f;break}}else{n=s[M+((d|8)+S)>>2]|0;do if(n>>>0>=u>>>0){if(u=n+12|0,(s[u>>2]|0)!=(l|0)||(f=a+8|0,(s[f>>2]|0)!=(l|0)))break;s[u>>2]=a,s[f>>2]=n,l1=a;break o}while(!1);b1()}while(!1);if(!t)break;u=s[M+(S+28+d)>>2]|0,f=408+(u<<2)|0;do if((l|0)!=(s[f>>2]|0)){if(t>>>0<(s[30]|0)>>>0&&b1(),f=t+16|0,(s[f>>2]|0)==(l|0)?s[f>>2]=l1:s[t+20>>2]=l1,!l1)break r}else{if(s[f>>2]=l1,l1)break;s[27]=s[27]&~(1<<u);break r}while(!1);u=s[30]|0,l1>>>0<u>>>0&&b1(),s[l1+24>>2]=t,l=d|16,f=s[M+(l+S)>>2]|0;do if(f)if(f>>>0<u>>>0)b1();else{s[l1+16>>2]=f,s[f+24>>2]=l1;break}while(!1);if(l=s[M+(r+l)>>2]|0,!l)break;if(l>>>0<(s[30]|0)>>>0)b1();else{s[l1+20>>2]=l,s[l+24>>2]=l1;break}}else{f=s[M+((d|8)+S)>>2]|0,a=s[M+(S+12+d)>>2]|0,n=144+(o<<1<<2)|0;do if((f|0)!=(n|0)){if(f>>>0>=u>>>0&&(s[f+12>>2]|0)==(l|0))break;b1()}while(!1);if((a|0)==(f|0)){s[26]=s[26]&~(1<<o);break}do if((a|0)==(n|0))s1=a+8|0;else{if(a>>>0>=u>>>0&&(c1=a+8|0,(s[c1>>2]|0)==(l|0))){s1=c1;break}b1()}while(!1);s[f+12>>2]=a,s[s1>>2]=f}while(!1);l=M+((c|d)+S)|0,i=c+i|0}if(l=l+4|0,s[l>>2]=s[l>>2]&-2,s[M+(k+4)>>2]=i|1,s[M+(i+k)>>2]=i,l=i>>>3,i>>>0<256){a=l<<1,o=144+(a<<2)|0,n=s[26]|0,f=1<<l;do if(!(n&f))s[26]=n|f,w1=144+(a+2<<2)|0,m1=o;else{if(f=144+(a+2<<2)|0,a=s[f>>2]|0,a>>>0>=(s[30]|0)>>>0){w1=f,m1=a;break}b1()}while(!1);s[w1>>2]=w,s[m1+12>>2]=w,s[M+(k+8)>>2]=m1,s[M+(k+12)>>2]=o;break}t=i>>>8;do if(!t)l=0;else{if(i>>>0>16777215){l=31;break}m1=(t+1048320|0)>>>16&8,H=t<<m1,w1=(H+520192|0)>>>16&4,H=H<<w1,l=(H+245760|0)>>>16&2,l=14-(w1|m1|l)+(H<<l>>>15)|0,l=i>>>(l+7|0)&1|l<<1}while(!1);if(f=408+(l<<2)|0,s[M+(k+28)>>2]=l,s[M+(k+20)>>2]=0,s[M+(k+16)>>2]=0,a=s[27]|0,n=1<<l,!(a&n)){s[27]=a|n,s[f>>2]=w,s[M+(k+24)>>2]=f,s[M+(k+12)>>2]=w,s[M+(k+8)>>2]=w;break}t=s[f>>2]|0;r:do if((s[t+4>>2]&-8|0)!=(i|0)){for(l=i<<((l|0)==31?0:25-(l>>>1)|0);r=t+16+(l>>>31<<2)|0,f=s[r>>2]|0,!!f;)if((s[f+4>>2]&-8|0)==(i|0)){$=f;break r}else l=l<<1,t=f;if(r>>>0<(s[30]|0)>>>0)b1();else{s[r>>2]=w,s[M+(k+24)>>2]=t,s[M+(k+12)>>2]=w,s[M+(k+8)>>2]=w;break t}}else $=t;while(!1);if(t=$+8|0,r=s[t>>2]|0,H=s[30]|0,r>>>0>=H>>>0&$>>>0>=H>>>0){s[r+12>>2]=w,s[t>>2]=w,s[M+(k+8)>>2]=r,s[M+(k+12)>>2]=$,s[M+(k+24)>>2]=0;break}else b1()}else H=(s[29]|0)+i|0,s[29]=H,s[32]=w,s[M+(k+4)>>2]=H|1;while(!1);a=M+(h|8)|0;break e}for(;a=s[l>>2]|0,!(a>>>0<=b>>>0&&(f=s[l+4>>2]|0,n=a+f|0,n>>>0>b>>>0));)l=s[l+8>>2]|0;if(l=a+(f+-39)|0,l=a+(f+-47+(l&7|0?0-l&7:0))|0,u=b+16|0,l=l>>>0<u>>>0?b:l,f=l+8|0,a=M+8|0,a=a&7|0?0-a&7:0,H=S+-40-a|0,s[32]=M+a,s[29]=H,s[M+(a+4)>>2]=H|1,s[M+(S+-36)>>2]=40,s[33]=s[148],a=l+4|0,s[a>>2]=27,s[f>>2]=s[138],s[f+4>>2]=s[139],s[f+8>>2]=s[140],s[f+12>>2]=s[141],s[138]=M,s[139]=S,s[141]=0,s[140]=f,f=l+28|0,s[f>>2]=7,(l+32|0)>>>0<n>>>0)do H=f,f=f+4|0,s[f>>2]=7;while((H+8|0)>>>0<n>>>0);if((l|0)!=(b|0)){if(i=l-b|0,s[a>>2]=s[a>>2]&-2,s[b+4>>2]=i|1,s[l>>2]=i,n=i>>>3,i>>>0<256){f=n<<1,l=144+(f<<2)|0,a=s[26]|0,o=1<<n,a&o?(t=144+(f+2<<2)|0,r=s[t>>2]|0,r>>>0<(s[30]|0)>>>0?b1():(h1=t,d1=r)):(s[26]=a|o,h1=144+(f+2<<2)|0,d1=l),s[h1>>2]=b,s[d1+12>>2]=b,s[b+8>>2]=d1,s[b+12>>2]=l;break}if(t=i>>>8,t?i>>>0>16777215?f=31:($=(t+1048320|0)>>>16&8,H=t<<$,m1=(H+520192|0)>>>16&4,H=H<<m1,f=(H+245760|0)>>>16&2,f=14-(m1|$|f)+(H<<f>>>15)|0,f=i>>>(f+7|0)&1|f<<1):f=0,o=408+(f<<2)|0,s[b+28>>2]=f,s[b+20>>2]=0,s[u>>2]=0,t=s[27]|0,r=1<<f,!(t&r)){s[27]=t|r,s[o>>2]=b,s[b+24>>2]=o,s[b+12>>2]=b,s[b+8>>2]=b;break}t=s[o>>2]|0;t:do if((s[t+4>>2]&-8|0)!=(i|0)){for(f=i<<((f|0)==31?0:25-(f>>>1)|0);r=t+16+(f>>>31<<2)|0,o=s[r>>2]|0,!!o;)if((s[o+4>>2]&-8|0)==(i|0)){t1=o;break t}else f=f<<1,t=o;if(r>>>0<(s[30]|0)>>>0)b1();else{s[r>>2]=b,s[b+24>>2]=t,s[b+12>>2]=b,s[b+8>>2]=b;break i}}else t1=t;while(!1);if(t=t1+8|0,r=s[t>>2]|0,H=s[30]|0,r>>>0>=H>>>0&t1>>>0>=H>>>0){s[r+12>>2]=b,s[t>>2]=b,s[b+8>>2]=r,s[b+12>>2]=t1,s[b+24>>2]=0;break}else b1()}}else{H=s[30]|0,(H|0)==0|M>>>0<H>>>0&&(s[30]=M),s[138]=M,s[139]=S,s[141]=0,s[35]=s[144],s[34]=-1,t=0;do H=t<<1,$=144+(H<<2)|0,s[144+(H+3<<2)>>2]=$,s[144+(H+2<<2)>>2]=$,t=t+1|0;while((t|0)!=32);H=M+8|0,H=H&7|0?0-H&7:0,$=S+-40-H|0,s[32]=M+H,s[29]=$,s[M+(H+4)>>2]=$|1,s[M+(S+-36)>>2]=40,s[33]=s[148]}while(!1);if(r=s[29]|0,r>>>0>R>>>0){H=r-R|0,s[29]=H,a=s[32]|0,s[32]=a+R,s[a+(R+4)>>2]=H|1,s[a+4>>2]=R|3,a=a+8|0;break}}s[(R4()|0)>>2]=12,a=0}else a=0}while(!1);return a|0}function K1(i){i=i|0;var r=0,t=0,o=0,n=0,a=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,I=0,N=0,A=0,F=0,T=0,x=0,M=0,C=0;e:do if(i){n=i+-8|0,c=s[30]|0;i:do if(n>>>0>=c>>>0&&(o=s[i+-4>>2]|0,t=o&3,(t|0)!=1)){N=o&-8,A=i+(N+-8)|0;do if(o&1)C=n,a=N;else{if(n=s[n>>2]|0,!t)break e;if(d=-8-n|0,w=i+d|0,h=n+N|0,w>>>0<c>>>0)break i;if((w|0)==(s[31]|0)){if(a=i+(N+-4)|0,n=s[a>>2]|0,(n&3|0)!=3){C=w,a=h;break}s[28]=h,s[a>>2]=n&-2,s[i+(d+4)>>2]=h|1,s[A>>2]=h;break e}if(t=n>>>3,n>>>0<256){o=s[i+(d+8)>>2]|0,a=s[i+(d+12)>>2]|0,n=144+(t<<1<<2)|0;do if((o|0)!=(n|0)){if(o>>>0>=c>>>0&&(s[o+12>>2]|0)==(w|0))break;b1()}while(!1);if((a|0)==(o|0)){s[26]=s[26]&~(1<<t),C=w,a=h;break}do if((a|0)==(n|0))r=a+8|0;else{if(a>>>0>=c>>>0&&(f=a+8|0,(s[f>>2]|0)==(w|0))){r=f;break}b1()}while(!1);s[o+12>>2]=a,s[r>>2]=o,C=w,a=h;break}f=s[i+(d+24)>>2]|0,n=s[i+(d+12)>>2]|0;do if((n|0)==(w|0)){if(o=i+(d+20)|0,n=s[o>>2]|0,!n&&(o=i+(d+16)|0,n=s[o>>2]|0,!n)){m=0;break}for(;;){if(t=n+20|0,r=s[t>>2]|0,r){n=r,o=t;continue}if(t=n+16|0,r=s[t>>2]|0,r)n=r,o=t;else break}if(o>>>0<c>>>0)b1();else{s[o>>2]=0,m=n;break}}else{if(o=s[i+(d+8)>>2]|0,o>>>0>=c>>>0&&(l=o+12|0,(s[l>>2]|0)==(w|0))&&(u=n+8|0,(s[u>>2]|0)==(w|0))){s[l>>2]=n,s[u>>2]=o,m=n;break}b1()}while(!1);if(f){if(n=s[i+(d+28)>>2]|0,o=408+(n<<2)|0,(w|0)==(s[o>>2]|0)){if(s[o>>2]=m,!m){s[27]=s[27]&~(1<<n),C=w,a=h;break}}else if(f>>>0<(s[30]|0)>>>0&&b1(),n=f+16|0,(s[n>>2]|0)==(w|0)?s[n>>2]=m:s[f+20>>2]=m,!m){C=w,a=h;break}o=s[30]|0,m>>>0<o>>>0&&b1(),s[m+24>>2]=f,n=s[i+(d+16)>>2]|0;do if(n)if(n>>>0<o>>>0)b1();else{s[m+16>>2]=n,s[n+24>>2]=m;break}while(!1);if(n=s[i+(d+20)>>2]|0,n)if(n>>>0<(s[30]|0)>>>0)b1();else{s[m+20>>2]=n,s[n+24>>2]=m,C=w,a=h;break}else C=w,a=h}else C=w,a=h}while(!1);if(C>>>0<A>>>0&&(k=i+(N+-4)|0,b=s[k>>2]|0,(b&1|0)!=0)){if(b&2)s[k>>2]=b&-2,s[C+4>>2]=a|1,s[C+a>>2]=a;else{if((A|0)==(s[32]|0)){if(M=(s[29]|0)+a|0,s[29]=M,s[32]=C,s[C+4>>2]=M|1,(C|0)!=(s[31]|0))break e;s[31]=0,s[28]=0;break e}if((A|0)==(s[31]|0)){M=(s[28]|0)+a|0,s[28]=M,s[31]=C,s[C+4>>2]=M|1,s[C+M>>2]=M;break e}u=(b&-8)+a|0,t=b>>>3;do if(b>>>0>=256){r=s[i+(N+16)>>2]|0,a=s[i+(N|4)>>2]|0;do if((a|0)==(A|0)){if(n=i+(N+12)|0,a=s[n>>2]|0,!a&&(n=i+(N+8)|0,a=s[n>>2]|0,!a)){F=0;break}for(;;){if(o=a+20|0,t=s[o>>2]|0,t){a=t,n=o;continue}if(o=a+16|0,t=s[o>>2]|0,t)a=t,n=o;else break}if(n>>>0<(s[30]|0)>>>0)b1();else{s[n>>2]=0,F=a;break}}else{if(n=s[i+N>>2]|0,n>>>0>=(s[30]|0)>>>0&&(S=n+12|0,(s[S>>2]|0)==(A|0))&&(I=a+8|0,(s[I>>2]|0)==(A|0))){s[S>>2]=a,s[I>>2]=n,F=a;break}b1()}while(!1);if(r){if(a=s[i+(N+20)>>2]|0,n=408+(a<<2)|0,(A|0)==(s[n>>2]|0)){if(s[n>>2]=F,!F){s[27]=s[27]&~(1<<a);break}}else if(r>>>0<(s[30]|0)>>>0&&b1(),a=r+16|0,(s[a>>2]|0)==(A|0)?s[a>>2]=F:s[r+20>>2]=F,!F)break;a=s[30]|0,F>>>0<a>>>0&&b1(),s[F+24>>2]=r,n=s[i+(N+8)>>2]|0;do if(n)if(n>>>0<a>>>0)b1();else{s[F+16>>2]=n,s[n+24>>2]=F;break}while(!1);if(t=s[i+(N+12)>>2]|0,t)if(t>>>0<(s[30]|0)>>>0)b1();else{s[F+20>>2]=t,s[t+24>>2]=F;break}}}else{o=s[i+N>>2]|0,a=s[i+(N|4)>>2]|0,n=144+(t<<1<<2)|0;do if((o|0)!=(n|0)){if(o>>>0>=(s[30]|0)>>>0&&(s[o+12>>2]|0)==(A|0))break;b1()}while(!1);if((a|0)==(o|0)){s[26]=s[26]&~(1<<t);break}do if((a|0)==(n|0))E=a+8|0;else{if(a>>>0>=(s[30]|0)>>>0&&(R=a+8|0,(s[R>>2]|0)==(A|0))){E=R;break}b1()}while(!1);s[o+12>>2]=a,s[E>>2]=o}while(!1);if(s[C+4>>2]=u|1,s[C+u>>2]=u,(C|0)==(s[31]|0)){s[28]=u;break e}else a=u}if(n=a>>>3,a>>>0<256){o=n<<1,a=144+(o<<2)|0,r=s[26]|0,t=1<<n,r&t?(t=144+(o+2<<2)|0,r=s[t>>2]|0,r>>>0<(s[30]|0)>>>0?b1():(T=t,x=r)):(s[26]=r|t,T=144+(o+2<<2)|0,x=a),s[T>>2]=C,s[x+12>>2]=C,s[C+8>>2]=x,s[C+12>>2]=a;break e}r=a>>>8,r?a>>>0>16777215?n=31:(T=(r+1048320|0)>>>16&8,x=r<<T,i=(x+520192|0)>>>16&4,x=x<<i,n=(x+245760|0)>>>16&2,n=14-(i|T|n)+(x<<n>>>15)|0,n=a>>>(n+7|0)&1|n<<1):n=0,t=408+(n<<2)|0,s[C+28>>2]=n,s[C+20>>2]=0,s[C+16>>2]=0,r=s[27]|0,o=1<<n;t:do if(r&o){t=s[t>>2]|0;r:do if((s[t+4>>2]&-8|0)!=(a|0)){for(n=a<<((n|0)==31?0:25-(n>>>1)|0);r=t+16+(n>>>31<<2)|0,o=s[r>>2]|0,!!o;)if((s[o+4>>2]&-8|0)==(a|0)){M=o;break r}else n=n<<1,t=o;if(r>>>0<(s[30]|0)>>>0)b1();else{s[r>>2]=C,s[C+24>>2]=t,s[C+12>>2]=C,s[C+8>>2]=C;break t}}else M=t;while(!1);if(r=M+8|0,t=s[r>>2]|0,x=s[30]|0,t>>>0>=x>>>0&M>>>0>=x>>>0){s[t+12>>2]=C,s[r>>2]=C,s[C+8>>2]=t,s[C+12>>2]=M,s[C+24>>2]=0;break}else b1()}else s[27]=r|o,s[t>>2]=C,s[C+24>>2]=t,s[C+12>>2]=C,s[C+8>>2]=C;while(!1);if(C=(s[34]|0)+-1|0,s[34]=C,!C)r=560;else break e;for(;r=s[r>>2]|0,r;)r=r+8|0;s[34]=-1;break e}}while(!1);b1()}while(!1)}function R4(){var i=0;return i=600,i|0}function Yi(){var i=0;do if(!(s[144]|0))if(i=g3(30)|0,i+-1&i)b1();else{s[146]=i,s[145]=i,s[147]=-1,s[148]=-1,s[149]=0,s[137]=0,s[144]=(y3(0)|0)&-16^1431655768;break}while(!1)}function Gi(){}function _0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;if((t|0)>=4096)return _3(i|0,r|0,t|0)|0;if(o=i|0,(i&3)==(r&3)){for(;i&3;){if(!t)return o|0;_[i>>0]=_[r>>0]|0,i=i+1|0,r=r+1|0,t=t-1|0}for(;(t|0)>=4;)s[i>>2]=s[r>>2],i=i+4|0,r=r+4|0,t=t-4|0}for(;(t|0)>0;)_[i>>0]=_[r>>0]|0,i=i+1|0,r=r+1|0,t=t-1|0;return o|0}function r2(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;if((r|0)<(i|0)&(i|0)<(r+t|0)){for(o=i,r=r+t|0,i=i+t|0;(t|0)>0;)i=i-1|0,r=r-1|0,t=t-1|0,_[i>>0]=_[r>>0]|0;i=o}else _0(i,r,t)|0;return i|0}function o2(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,a=0,f=0;if(o=i+t|0,(t|0)>=20){if(r=r&255,a=i&3,f=r|r<<8|r<<16|r<<24,n=o&-4,a)for(a=i+4-a|0;(i|0)<(a|0);)_[i>>0]=r,i=i+1|0;for(;(i|0)<(n|0);)s[i>>2]=f,i=i+4|0}for(;(i|0)<(o|0);)_[i>>0]=r,i=i+1|0;return i-t|0}return{_free:K1,___errno_location:R4,_memmove:r2,_Decoder_Interface_Decode:L3,_Decoder_Interface_exit:O3,_Encoder_Interface_init:C3,_memset:o2,_malloc:s0,_memcpy:_0,_Encoder_Interface_exit:F3,_Decoder_Interface_init:I3,_Encoder_Interface_Encode:T3,runPostSets:Gi,stackAlloc:R3,stackSave:S3,stackRestore:D3,establishStackSpace:A3,setThrew:M3,setTempRet0:P3,getTempRet0:N3}}(L.asmGlobalArg,L.asmLibraryArg,z0);L._Encoder_Interface_Encode=n0._Encoder_Interface_Encode;var N5=L._free=n0._free;L.runPostSets=n0.runPostSets;var b3=L._memmove=n0._memmove;L._Decoder_Interface_exit=n0._Decoder_Interface_exit,L._Encoder_Interface_init=n0._Encoder_Interface_init;var I5,v3=L._memset=n0._memset,H2=L._malloc=n0._malloc,E3=L._memcpy=n0._memcpy;function P2(p){this.name="ExitStatus",this.message="Program terminated with exit("+p+")",this.status=p}function O5(p){function v(){L.calledRun||(L.calledRun=!0,g1||(g6(),i3(),L.onRuntimeInitialized&&L.onRuntimeInitialized(),L._main&&N6&&L.callMain(p),r3()))}p=p||L.arguments,n2>0||(e3(),n2>0||L.calledRun||(L.setStatus?(L.setStatus("Running..."),setTimeout(function(){setTimeout(function(){L.setStatus("")},1),v()},1)):v()))}function M6(p,v){if(!v||!L.noExitRuntime)throw L.noExitRuntime||(g1=!0,$0=I5,t3(),L.onExit&&L.onExit(p)),P0&&typeof quit=="function"&&quit(p),new P2(p)}L._Decoder_Interface_Decode=n0._Decoder_Interface_Decode,L._Decoder_Interface_init=n0._Decoder_Interface_init,L._Encoder_Interface_exit=n0._Encoder_Interface_exit,L.___errno_location=n0.___errno_location,u1.stackAlloc=n0.stackAlloc,u1.stackSave=n0.stackSave,u1.stackRestore=n0.stackRestore,u1.establishStackSpace=n0.establishStackSpace,u1.setTempRet0=n0.setTempRet0,u1.getTempRet0=n0.getTempRet0,P2.prototype=new Error,P2.prototype.constructor=P2,q2=function p(){L.calledRun||O5(),L.calledRun||(q2=p)},L.callMain=L.callMain=function(p){y1(n2==0,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),y1(S5.length==0,"cannot call main when preRun functions remain to be called"),p=p||[],g6();var v=p.length+1;function g(){for(var s=0;s<3;s++)_.push(0)}var _=[C0(m2(L.thisProgram),"i8",q0)];g();for(var e=0;e<v-1;e+=1)_.push(C0(m2(p[e]),"i8",q0)),g();_.push(0),_=C0(_,"i32",q0),I5=u1.stackSave();try{M6(L._main(v,_,0),!0)}catch(s){if(s instanceof P2)return;if(s=="SimulateInfiniteLoop")return L.noExitRuntime=!0,void u1.stackRestore(I5);throw s&&typeof s=="object"&&s.stack&&L.printErr("exception thrown: "+[s,s.stack]),s}},L.run=L.run=O5,L.exit=L.exit=M6;var P6=[];function k2(p){p!==void 0?(L.print(p),L.printErr(p),p=JSON.stringify(p)):p="",g1=!0;var v=`
If this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.`,g="abort("+p+") at "+y5()+v;throw P6&&P6.forEach(function(_){g=_(g,p)}),g}if(L.abort=L.abort=k2,L.preInit)for(typeof L.preInit=="function"&&(L.preInit=[L.preInit]);L.preInit.length>0;)L.preInit.pop()();var N6=!0;return L.noInitialRun&&(N6=!1),L.noExitRuntime=!0,O5(),k1}();function o1(L,k1){k1=k1||8e3,self.postMessage({command:"encode",amr:V1.encode(L,k1,7)})}function f1(L){self.postMessage({command:"decode",amr:V1.decode(L)})}self.onmessage=function(L){switch(L.data.command){case"encode":o1(L.data.samples,L.data.sampleRate);break;case"decode":f1(L.data.buffer)}}},m6=w6.toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),i5=(window.URL||window.webkitURL).createObjectURL(new Blob([m6],{type:"text/javascript"})),E5=function(){function V1(){var o1=this;R2(this,V1),this._isInit=!1,this._isInitRecorder=!1,this._recorderControl=new h0,this._samples=new Float32Array(0),this._rawData=new Uint8Array(0),this._blob=null,this._onEnded=null,this._onAutoEnded=null,this._onPlay=null,this._onPause=null,this._onResume=null,this._onStop=null,this._onStartRecord=null,this._onCancelRecord=null,this._onFinishRecord=null,this._isPlaying=!1,this._isPaused=!1,this._startCtxTime=0,this._pauseTime=0,this._playEmpty=function(){o1._recorderControl.playPcm(new Float32Array(10),24e3)},this._onEndCallback=function(){o1._isPlaying&&(o1._isPlaying=!1,o1._onStop&&o1._onStop(),o1._onAutoEnded&&o1._onAutoEnded()),o1._isPaused||o1._onEnded&&o1._onEnded()},this._runAMRWorker=function(f1,L){var k1=new Worker(i5);k1.postMessage(f1),k1.onmessage=function(A1){L(A1.data.amr),k1.terminate()}}}return U0(V1,[{key:"isInit",value:function(){return this._isInit}},{key:"initWithArrayBuffer",value:function(o1){var f1=this;return(this._isInit||this._isInitRecorder)&&V1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(L,k1){var A1=new Uint8Array(o1);f1.decodeAMRAsync(A1).then(function(D1){f1._samples=D1,f1._isInit=!0,f1._samples?(f1._rawData=A1,L()):h0.decodeAudioArrayBufferByContext(o1).then(function(Y1){return f1._isInit=!0,f1.encodeAMRAsync(Y1,h0.getCtxSampleRate())}).then(function(Y1){return f1._rawData=Y1,f1._blob=V1.rawAMRData2Blob(Y1),f1.decodeAMRAsync(Y1)}).then(function(Y1){f1._samples=Y1,L()}).catch(function(){k1(new Error("Failed to decode."))})})})}},{key:"initWithBlob",value:function(o1){var f1=this;return(this._isInit||this._isInitRecorder)&&V1.throwAlreadyInitialized(),this._playEmpty(),this._blob=o1,new Promise(function(L){var k1=new FileReader;k1.onload=function(A1){L(A1.target.result)},k1.readAsArrayBuffer(o1)}).then(function(L){return f1.initWithArrayBuffer(L)})}},{key:"initWithUrl",value:function(o1){var f1=this;return(this._isInit||this._isInitRecorder)&&V1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(L,k1){var A1=new XMLHttpRequest;A1.open("GET",o1,!0),A1.responseType="arraybuffer",A1.onload=function(){L(this.response)},A1.onerror=function(){k1(new Error("Failed to fetch "+o1))},A1.send()}).then(function(L){return f1.initWithArrayBuffer(L)})}},{key:"initWithRecord",value:function(){var o1=this;return(this._isInit||this._isInitRecorder)&&V1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(f1,L){o1._recorderControl.initRecorder().then(function(){o1._isInitRecorder=!0,f1()}).catch(function(k1){L(k1)})})}},{key:"on",value:function(o1,f1){if(typeof f1=="function"||f1===null)switch(o1){case"play":this._onPlay=f1;break;case"stop":this._onStop=f1;break;case"pause":this._onPause=f1;break;case"resume":this._onResume=f1;break;case"ended":this._onEnded=f1;break;case"autoEnded":this._onAutoEnded=f1;break;case"startRecord":this._onStartRecord=f1;break;case"cancelRecord":this._onCancelRecord=f1;break;case"finishRecord":this._onFinishRecord=f1;break;case"*":case"all":this._onEnded=f1,this._onAutoEnded=f1,this._onPlay=f1,this._onPause=f1,this._onResume=f1,this._onStop=f1,this._onStartRecord=f1,this._onCancelRecord=f1,this._onFinishRecord=f1}}},{key:"off",value:function(o1){this.on(o1,null)}},{key:"onPlay",value:function(o1){this.on("play",o1)}},{key:"onStop",value:function(o1){this.on("stop",o1)}},{key:"onPause",value:function(o1){this.on("pause",o1)}},{key:"onResume",value:function(o1){this.on("resume",o1)}},{key:"onEnded",value:function(o1){this.on("ended",o1)}},{key:"onAutoEnded",value:function(o1){this.on("autoEnded",o1)}},{key:"onStartRecord",value:function(o1){this.on("startRecord",o1)}},{key:"onFinishRecord",value:function(o1){this.on("finishRecord",o1)}},{key:"onCancelRecord",value:function(o1){this.on("cancelRecord",o1)}},{key:"play",value:function(o1){var f1=o1&&o1<this.getDuration()?parseFloat(o1):0;if(!this._isInit)throw new Error("Please init AMR first.");this._onPlay&&this._onPlay(),this._isPlaying=!0,this._isPaused=!1,this._startCtxTime=h0.getCtxTime()-f1,this._recorderControl.playPcm(this._samples,this._isInitRecorder?h0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),f1)}},{key:"stop",value:function(){this._recorderControl.stopPcm(),this._isPlaying=!1,this._isPaused=!1,this._onStop&&this._onStop()}},{key:"pause",value:function(){this._isPlaying&&(this._isPlaying=!1,this._isPaused=!0,this._pauseTime=h0.getCtxTime()-this._startCtxTime,this._recorderControl.stopPcm(),this._onPause&&this._onPause())}},{key:"resume",value:function(){this._isPaused&&(this._isPlaying=!0,this._isPaused=!1,this._startCtxTime=h0.getCtxTime()-this._pauseTime,this._recorderControl.playPcm(this._samples,this._isInitRecorder?h0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),this._pauseTime),this._onResume&&this._onResume())}},{key:"playOrResume",value:function(){this._isPaused?this.resume():this.play()}},{key:"pauseOrResume",value:function(){this._isPaused?this.resume():this.pause()}},{key:"playOrPauseOrResume",value:function(){this._isPaused?this.resume():this._isPlaying?this.pause():this.play()}},{key:"setPosition",value:function(o1){var f1=parseFloat(o1);f1>this.getDuration()?this.stop():this._isPaused?this._pauseTime=f1:this._isPlaying?(this._recorderControl.stopPcmSilently(),this._startCtxTime=h0.getCtxTime()-f1,this._recorderControl.playPcm(this._samples,this._isInitRecorder?h0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),f1)):this.play(f1)}},{key:"getCurrentPosition",value:function(){return this._isPaused?this._pauseTime:this._isPlaying?h0.getCtxTime()-this._startCtxTime:0}},{key:"isPlaying",value:function(){return this._isPlaying}},{key:"isPaused",value:function(){return this._isPaused}},{key:"startRecord",value:function(){this._recorderControl.startRecord(),this._onStartRecord&&this._onStartRecord()}},{key:"finishRecord",value:function(){var o1=this;return new Promise(function(f1){o1._recorderControl.stopRecord(),o1._recorderControl.generateRecordSamples().then(function(L){return o1._samples=L,o1.encodeAMRAsync(L,h0.getCtxSampleRate())}).then(function(L){o1._rawData=L,o1._blob=V1.rawAMRData2Blob(o1._rawData),o1._isInit=!0,o1._onFinishRecord&&o1._onFinishRecord(),o1._recorderControl.releaseRecord(),f1()})})}},{key:"cancelRecord",value:function(){this._recorderControl.stopRecord(),this._recorderControl.releaseRecord(),this._onCancelRecord&&this._onCancelRecord()}},{key:"isRecording",value:function(){return this._recorderControl.isRecording()}},{key:"getDuration",value:function(){var o1=this._isInitRecorder?h0.getCtxSampleRate():8e3;return this._samples.length/o1}},{key:"getBlob",value:function(){return this._blob}},{key:"destroy",value:function(){this._recorderControl.stopPcmSilently(),this._recorderControl.stopRecord(),this._recorderControl.releaseRecord(),this.off("*"),this._recorderControl=null,this._samples=null,this._rawData=null,this._blob=null}},{key:"encodeAMRAsync",value:function(o1,f1){var L=this;return new Promise(function(k1){L._runAMRWorker({command:"encode",samples:o1,sampleRate:f1},k1)})}},{key:"decodeAMRAsync",value:function(o1){var f1=this;return new Promise(function(L){f1._runAMRWorker({command:"decode",buffer:o1},L)})}}],[{key:"rawAMRData2Blob",value:function(o1){return new Blob([o1.buffer],{type:"audio/amr"})}},{key:"throwAlreadyInitialized",value:function(){throw new Error("AMR has been initialized. For a new AMR, please generate a new BenzAMRRecorder().")}},{key:"isPlaySupported",value:function(){return h0.isPlaySupported()}},{key:"isRecordSupported",value:function(){return h0.isRecordSupported()}}]),V1}();return E5}();let c6,d6,h6;c6=Zi(u6.exports),d6={key:2,class:"amr-duration"},h6={key:0},C4=i7(Qi({name:"WxVoicePlayer",__name:"main",props:{url:{type:String,required:!0},content:{type:String,required:!1}},setup(R2){const J2=R2,U0=s6(),u2=s6(!1),x2=s6(),e5=()=>{U0.value!==void 0?U0.value.isPlaying()?h0():B2():r0()},r0=()=>{U0.value=new c6,U0.value.initWithUrl(J2.url).then(function(){B2(),x2.value=U0.value.getDuration()}),U0.value.onEnded(function(){u2.value=!1})},B2=()=>{u2.value=!0,U0.value.play()},h0=()=>{u2.value=!1,U0.value.stop()};return(w6,m6)=>{const i5=$i,E5=Ji,V1=e7;return $2(),f6("div",{class:"wx-voice-div",onClick:e5},[M4(E5,null,{default:P4(()=>[l6(u2)!==!0?($2(),N4(i5,{key:0,icon:"ep:video-play",size:32})):($2(),N4(i5,{key:1,icon:"ep:video-pause",size:32})),l6(x2)?($2(),f6("span",d6,I4(l6(x2))+" \u79D2",1)):O4("",!0)]),_:1}),R2.content?($2(),f6("div",h6,[M4(V1,{type:"success",size:"small"},{default:P4(()=>[L4("\u8BED\u97F3\u8BC6\u522B")]),_:1}),L4(" "+I4(R2.content),1)])):O4("",!0)])}}}),[["__scopeId","data-v-ec641f05"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-voice-play/main.vue"]])});export{r7 as __tla,C4 as default};
