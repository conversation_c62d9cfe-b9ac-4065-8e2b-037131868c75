import{_,__tla as e}from"./ContentWrap-DZg14iby.js";import{_ as s,__tla as l}from"./IFrame-DOdFY0xB.js";import{d as n,o,l as i,w as u,i as m,_ as c,__tla as p}from"./index-Daqg4PFz.js";import{__tla as d}from"./el-card-Dvjjuipo.js";let t,f=Promise.all([(()=>{try{return e}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return d}catch{}})()]).then(async()=>{t=c(n({name:"GoView",__name:"index",setup:y=>(h,v)=>{const a=s,r=_;return o(),i(r,null,{default:u(()=>[m(a,{src:"http://127.0.0.1:3000"})]),_:1})}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/report/goview/index.vue"]])});export{f as __tla,t as default};
