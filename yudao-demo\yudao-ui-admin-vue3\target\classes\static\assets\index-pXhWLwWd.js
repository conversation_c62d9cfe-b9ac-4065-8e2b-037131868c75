import{_ as i,__tla as n}from"./ContentWrap-DZg14iby.js";import{d as u,o as f,c as p,i as t,w as r,a as l,F as y,A as h,B as d,_ as T,__tla as C}from"./index-Daqg4PFz.js";import{_ as I,__tla as L}from"./index-CmwFi8Xl.js";import o,{__tla as v}from"./CustomerLimitConfigList-Cc4-YlYc.js";import{L as e,__tla as M}from"./CustomerLimitConfigForm-BH6EVeh3.js";import{__tla as U}from"./el-card-Dvjjuipo.js";import{__tla as x}from"./index-BBLwwrga.js";import{__tla as O}from"./index-CS70nJJ8.js";import{__tla as b}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as g}from"./formatTime-BCfRGyrF.js";import{__tla as w}from"./Dialog-BjBBVYCI.js";import{__tla as A}from"./el-tree-select-BKcJcOKx.js";import{__tla as E}from"./index-D-Abj-9W.js";import"./tree-BMqZf9_I.js";let c,R=Promise.all([(()=>{try{return n}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return v}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return E}catch{}})()]).then(async()=>{c=T(u({name:"CrmCustomerLimitConfig",__name:"index",setup:S=>(j,B)=>{const a=I,_=h,m=d,s=i;return f(),p(y,null,[t(a,{title:"\u3010\u5BA2\u6237\u3011\u5BA2\u6237\u7BA1\u7406\u3001\u516C\u6D77\u5BA2\u6237",url:"https://doc.iocoder.cn/crm/customer/"}),t(a,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),t(s,null,{default:r(()=>[t(m,null,{default:r(()=>[t(_,{label:"\u62E5\u6709\u5BA2\u6237\u6570\u9650\u5236"},{default:r(()=>[t(o,{confType:l(e).CUSTOMER_QUANTITY_LIMIT},null,8,["confType"])]),_:1}),t(_,{label:"\u9501\u5B9A\u5BA2\u6237\u6570\u9650\u5236"},{default:r(()=>[t(o,{confType:l(e).CUSTOMER_LOCK_LIMIT},null,8,["confType"])]),_:1})]),_:1})]),_:1})],64)}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/customer/limitConfig/index.vue"]])});export{R as __tla,c as default};
