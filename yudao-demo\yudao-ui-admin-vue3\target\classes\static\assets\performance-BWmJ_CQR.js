import{bE as r,__tla as a}from"./index-Daqg4PFz.js";let t,c=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{t={getContractPricePerformance:e=>r.get({url:"/crm/statistics-performance/get-contract-price-performance",params:e}),getReceivablePricePerformance:e=>r.get({url:"/crm/statistics-performance/get-receivable-price-performance",params:e}),getContractCountPerformance:e=>r.get({url:"/crm/statistics-performance/get-contract-count-performance",params:e})}});export{t as S,c as __tla};
