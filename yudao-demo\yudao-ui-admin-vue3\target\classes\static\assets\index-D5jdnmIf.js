import{d as i,p as V,b as c,o as f,l as v,w as y,i as x,a as t,z as s,cn as I,Z as U,_ as g,__tla as h}from"./index-Daqg4PFz.js";import{P as C}from"./color-BN7ZL7BD.js";let u,P=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{u=g(i({name:"ColorInput",__name:"index",props:{modelValue:V.string.def("")},emits:["update:modelValue"],setup(n,{emit:d}){const r=n,m=d,e=c({get:()=>r.modelValue,set:o=>{m("update:modelValue",o)}});return(o,a)=>{const p=I,_=U;return f(),v(_,{modelValue:t(e),"onUpdate:modelValue":a[1]||(a[1]=l=>s(e)?e.value=l:null)},{prepend:y(()=>[x(p,{modelValue:t(e),"onUpdate:modelValue":a[0]||(a[0]=l=>s(e)?e.value=l:null),predefine:t(C)},null,8,["modelValue","predefine"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-f1d07281"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/ColorInput/index.vue"]])});export{u as _,P as __tla};
