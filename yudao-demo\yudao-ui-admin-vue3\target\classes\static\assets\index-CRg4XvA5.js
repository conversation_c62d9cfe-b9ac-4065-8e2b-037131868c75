import{d as y,o as r,c as o,i as a,w as l,F as m,k as f,l as h,g as v,t as g,av as b,q as k,x as C,_ as E,__tla as j}from"./index-Daqg4PFz.js";import{E as q,a as w,__tla as B}from"./el-carousel-item-g4NI8jnR.js";import{E as N,__tla as U}from"./el-image-Bn34T02c.js";let c,D=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let e,s;e={class:"h-24px truncate leading-24px"},s=y({name:"NoticeBar",__name:"index",props:{property:{type:Object,required:!0}},setup:F=>(t,J)=>{const i=N,p=k,n=q,_=w,u=C;return r(),o("div",{class:"flex items-center p-y-4px text-12px",style:b({backgroundColor:t.property.backgroundColor,color:t.property.textColor})},[a(i,{src:t.property.iconUrl,class:"h-18px"},null,8,["src"]),a(p,{direction:"vertical"}),a(_,{height:"24px",direction:"vertical",autoplay:!0,class:"flex-1 p-r-8px"},{default:l(()=>[(r(!0),o(m,null,f(t.property.contents,(d,x)=>(r(),h(n,{key:x},{default:l(()=>[v("div",e,g(d.text),1)]),_:2},1024))),128))]),_:1}),a(u,{icon:"ep:arrow-right"})],4)}}),c=E(s,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/NoticeBar/index.vue"]])});export{D as __tla,c as default};
