import{d as na,n as sa,I as ia,u as ca,r as g,f as ua,C as _a,T as da,o as s,c as I,i as a,w as e,a as l,U as B,F as E,k as pa,V as ma,G as K,l as i,j as c,H as p,t as fa,a9 as R,Z as ya,L as ha,J as ba,K as ga,x as wa,N as ka,O as va,P as ja,Q as xa,R as Ca,_ as Na,__tla as Sa}from"./index-Daqg4PFz.js";import{_ as Oa,__tla as Ra}from"./index-BBLwwrga.js";import{E as Pa,a as Ta,b as Va,__tla as Ua}from"./el-dropdown-item-C6dpORMi.js";import{_ as Ja,__tla as Aa}from"./DictTag-BDZzHcIz.js";import{_ as qa,__tla as La}from"./ContentWrap-DZg14iby.js";import{_ as Fa,__tla as za}from"./index-CmwFi8Xl.js";import{c as P,__tla as Ia}from"./permission-CGrgdXzF.js";import Ba,{__tla as Ea}from"./JobForm-CQUlFoX_.js";import Ka,{__tla as Ma}from"./JobDetail-C8-s2FLO.js";import{d as Da}from"./download--D_IyRio.js";import{b as Ha,e as Qa,d as Za,f as Ga,r as Ya,__tla as Wa}from"./index-B3pUKYVa.js";import{c as m}from"./constants-WoCEnNvc.js";import{__tla as Xa}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as $a}from"./el-card-Dvjjuipo.js";import{__tla as ae}from"./Dialog-BjBBVYCI.js";import{__tla as ee}from"./el-descriptions-item-Bucl-KSp.js";import{__tla as te}from"./el-timeline-item-DLMaR2h1.js";import{__tla as le}from"./formatTime-BCfRGyrF.js";let M,re=Promise.all([(()=>{try{return Sa}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Xa}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return le}catch{}})()]).then(async()=>{M=Na(na({name:"InfraJob",__name:"index",setup(oe){const{t:x}=sa(),f=ia(),{push:D}=ca(),C=g(!0),T=g(0),V=g([]),n=ua({pageNo:1,pageSize:10,name:void 0,status:void 0,handlerName:void 0}),U=g(),N=g(!1),y=async()=>{C.value=!0;try{const r=await Ha(n);V.value=r.list,T.value=r.total}finally{C.value=!1}},k=()=>{n.pageNo=1,y()},H=()=>{U.value.resetFields(),k()},Q=async()=>{try{await f.exportConfirm(),N.value=!0;const r=await Qa(n);Da.excel(r,"\u5B9A\u65F6\u4EFB\u52A1.xls")}catch{}finally{N.value=!1}},J=g(),A=(r,o)=>{J.value.open(r,o)},Z=async r=>{try{await f.confirm("\u786E\u8BA4\u8981\u7ACB\u5373\u6267\u884C\u4E00\u6B21"+r.name+"?",x("common.reminder")),await Ya(r.id),f.success("\u6267\u884C\u6210\u529F"),await y()}catch{}},q=g(),G=r=>{q.value.open(r)},L=r=>{D(r&&r>0?"/job/job-log?id="+r:"/job/job-log")};return _a(()=>{y()}),(r,o)=>{const S=Fa,F=ya,v=ha,Y=ba,W=ga,w=wa,_=ka,X=va,z=qa,h=ja,$=Ja,O=Pa,aa=Ta,ea=Va,ta=xa,la=Oa,b=da("hasPermi"),ra=Ca;return s(),I(E,null,[a(S,{title:"\u5B9A\u65F6\u4EFB\u52A1",url:"https://doc.iocoder.cn/job/"}),a(S,{title:"\u5F02\u6B65\u4EFB\u52A1",url:"https://doc.iocoder.cn/async-task/"}),a(S,{title:"\u6D88\u606F\u961F\u5217",url:"https://doc.iocoder.cn/message-queue/"}),a(z,null,{default:e(()=>[a(X,{class:"-mb-15px",model:l(n),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"100px"},{default:e(()=>[a(v,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:e(()=>[a(F,{modelValue:l(n).name,"onUpdate:modelValue":o[0]||(o[0]=t=>l(n).name=t),placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",clearable:"",onKeyup:B(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,{label:"\u4EFB\u52A1\u72B6\u6001",prop:"status"},{default:e(()=>[a(W,{modelValue:l(n).status,"onUpdate:modelValue":o[1]||(o[1]=t=>l(n).status=t),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u72B6\u6001",clearable:"",class:"!w-240px"},{default:e(()=>[(s(!0),I(E,null,pa(l(ma)(l(K).INFRA_JOB_STATUS),t=>(s(),i(Y,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",prop:"handlerName"},{default:e(()=>[a(F,{modelValue:l(n).handlerName,"onUpdate:modelValue":o[2]||(o[2]=t=>l(n).handlerName=t),placeholder:"\u8BF7\u8F93\u5165\u5904\u7406\u5668\u7684\u540D\u5B57",clearable:"",onKeyup:B(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,null,{default:e(()=>[a(_,{onClick:k},{default:e(()=>[a(w,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),a(_,{onClick:H},{default:e(()=>[a(w,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1}),p((s(),i(_,{type:"primary",plain:"",onClick:o[3]||(o[3]=t=>A("create"))},{default:e(()=>[a(w,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[b,["infra:job:create"]]]),p((s(),i(_,{type:"success",plain:"",onClick:Q,loading:l(N)},{default:e(()=>[a(w,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[b,["infra:job:export"]]]),p((s(),i(_,{type:"info",plain:"",onClick:o[4]||(o[4]=t=>L())},{default:e(()=>[a(w,{icon:"ep:zoom-in",class:"mr-5px"}),c(" \u6267\u884C\u65E5\u5FD7 ")]),_:1})),[[b,["infra:job:query"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(z,null,{default:e(()=>[p((s(),i(ta,{data:l(V)},{default:e(()=>[a(h,{label:"\u4EFB\u52A1\u7F16\u53F7",align:"center",prop:"id"}),a(h,{label:"\u4EFB\u52A1\u540D\u79F0",align:"center",prop:"name"}),a(h,{label:"\u4EFB\u52A1\u72B6\u6001",align:"center",prop:"status"},{default:e(t=>[a($,{type:l(K).INFRA_JOB_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),a(h,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",align:"center",prop:"handlerName"}),a(h,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570",align:"center",prop:"handlerParam"}),a(h,{label:"CRON \u8868\u8FBE\u5F0F",align:"center",prop:"cronExpression"}),a(h,{label:"\u64CD\u4F5C",align:"center",width:"200"},{default:e(t=>[p((s(),i(_,{type:"primary",link:"",onClick:j=>A("update",t.row.id)},{default:e(()=>[c(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[b,["infra:job:update"]]]),p((s(),i(_,{type:"primary",link:"",onClick:j=>(async u=>{try{const d=u.status===m.STOP?"\u5F00\u542F":"\u5173\u95ED";await f.confirm("\u786E\u8BA4\u8981"+d+'\u5B9A\u65F6\u4EFB\u52A1\u7F16\u53F7\u4E3A"'+u.id+'"\u7684\u6570\u636E\u9879?',x("common.reminder"));const oa=u.status===m.STOP?m.NORMAL:m.STOP;await Za(u.id,oa),f.success(d+"\u6210\u529F"),await y()}catch{u.status=u.status===m.NORMAL?m.STOP:m.NORMAL}})(t.row)},{default:e(()=>[c(fa(t.row.status===l(m).STOP?"\u5F00\u542F":"\u6682\u505C"),1)]),_:2},1032,["onClick"])),[[b,["infra:job:update"]]]),p((s(),i(_,{type:"danger",link:"",onClick:j=>(async u=>{try{await f.delConfirm(),await Ga(u),f.success(x("common.delSuccess")),await y()}catch{}})(t.row.id)},{default:e(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[b,["infra:job:delete"]]]),p((s(),i(ea,{onCommand:j=>((u,d)=>{switch(u){case"handleRun":Z(d);break;case"openDetail":G(d.id);break;case"handleJobLog":L(d==null?void 0:d.id)}})(j,t.row)},{dropdown:e(()=>[a(aa,null,{default:e(()=>[l(P)(["infra:job:trigger"])?(s(),i(O,{key:0,command:"handleRun"},{default:e(()=>[c(" \u6267\u884C\u4E00\u6B21 ")]),_:1})):R("",!0),l(P)(["infra:job:query"])?(s(),i(O,{key:1,command:"openDetail"},{default:e(()=>[c(" \u4EFB\u52A1\u8BE6\u7EC6 ")]),_:1})):R("",!0),l(P)(["infra:job:query"])?(s(),i(O,{key:2,command:"handleJobLog"},{default:e(()=>[c(" \u8C03\u5EA6\u65E5\u5FD7 ")]),_:1})):R("",!0)]),_:1})]),default:e(()=>[a(_,{type:"primary",link:""},{default:e(()=>[a(w,{icon:"ep:d-arrow-right"}),c(" \u66F4\u591A")]),_:1})]),_:2},1032,["onCommand"])),[[b,["infra:job:trigger","infra:job:query"]]])]),_:1})]),_:1},8,["data"])),[[ra,l(C)]]),a(la,{total:l(T),page:l(n).pageNo,"onUpdate:page":o[5]||(o[5]=t=>l(n).pageNo=t),limit:l(n).pageSize,"onUpdate:limit":o[6]||(o[6]=t=>l(n).pageSize=t),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(Ba,{ref_key:"formRef",ref:J,onSuccess:y},null,512),a(Ka,{ref_key:"detailRef",ref:q},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/job/index.vue"]])});export{re as __tla,M as default};
