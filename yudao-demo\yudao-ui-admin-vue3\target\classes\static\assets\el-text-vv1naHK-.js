import{be as c,bn as m,d as s,c1 as y,bg as o,b,bS as d,o as g,l as _,w as f,aW as v,a0 as x,a as S,av as h,b1 as w,bh as C,bi as E,__tla as k}from"./index-Daqg4PFz.js";let l,z=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{const n=c({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:m,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),r=s({name:"ElText"});l=E(C(s({...r,props:n,setup(i){const e=i,p=y(),a=o("text"),u=b(()=>[a.b(),a.m(e.type),a.m(p.value),a.is("truncated",e.truncated),a.is("line-clamp",!d(e.lineClamp))]);return(t,B)=>(g(),_(w(t.tag),{class:x(S(u)),style:h({"-webkit-line-clamp":t.lineClamp})},{default:f(()=>[v(t.$slots,"default")]),_:3},8,["class","style"]))}}),[["__file","text.vue"]]))});export{l as E,z as __tla};
