import{_,__tla as s}from"./ContentWrap-DZg14iby.js";import{_ as l,__tla as o}from"./IFrame-DOdFY0xB.js";import{d as n,r as u,dY as i,o as m,l as c,w as p,i as d,a as f,_ as h,__tla as y}from"./index-Daqg4PFz.js";import{__tla as v}from"./el-card-Dvjjuipo.js";let t,x=Promise.all([(()=>{try{return s}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return v}catch{}})()]).then(async()=>{t=h(n({name:"JimuReport",__name:"index",setup(j){const a=u("http://localhost:48080/jmreport/list?token="+i());return(w,J)=>{const r=l,e=_;return m(),c(e,null,{default:p(()=>[d(r,{src:f(a)},null,8,["src"])]),_:1})}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/report/jmreport/index.vue"]])});export{x as __tla,t as default};
