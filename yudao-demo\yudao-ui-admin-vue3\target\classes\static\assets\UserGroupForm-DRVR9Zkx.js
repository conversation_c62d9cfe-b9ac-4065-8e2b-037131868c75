import{d as R,n as T,I as j,r as o,f as z,o as m,l as p,w as u,i as t,a,j as v,H,c as h,F as w,k,V as K,G as P,t as Z,z as D,ay as Q,Z as W,L as X,J as Y,K as $,am as ee,an as ae,O as le,N as se,R as ue,_ as te,__tla as re}from"./index-Daqg4PFz.js";import{_ as oe,__tla as de}from"./Dialog-BjBBVYCI.js";import{C as I}from"./constants-WoCEnNvc.js";import{a as ie,c as me,u as ne,__tla as ce}from"./index-BjA_Ugbr.js";let E,pe=Promise.all([(()=>{try{return re}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{E=te(R({name:"UserGroupForm",__name:"UserGroupForm",emits:["success"],setup(_e,{expose:F,emit:G}){const{t:_}=T(),f=j(),d=o(!1),y=o(""),i=o(!1),b=o(""),s=o({id:void 0,name:void 0,description:void 0,userIds:void 0,status:I.ENABLE}),S=z({name:[{required:!0,message:"\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userIds:[{required:!0,message:"\u6210\u5458\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=o(),V=o([]);F({open:async(r,l)=>{if(d.value=!0,y.value=_("action."+r),b.value=r,A(),l){i.value=!0;try{s.value=await ie(l)}finally{i.value=!1}}V.value=await Q()}});const q=G,x=async()=>{if(n&&await n.value.validate()){i.value=!0;try{const r=s.value;b.value==="create"?(await me(r),f.success(_("common.createSuccess"))):(await ne(r),f.success(_("common.updateSuccess"))),d.value=!1,q("success")}finally{i.value=!1}}},A=()=>{var r;s.value={id:void 0,name:void 0,description:void 0,userIds:void 0,status:I.ENABLE},(r=n.value)==null||r.resetFields()};return(r,l)=>{const g=W,c=X,C=Y,N=$,L=ee,O=ae,B=le,U=se,J=oe,M=ue;return m(),p(J,{modelValue:a(d),"onUpdate:modelValue":l[5]||(l[5]=e=>D(d)?d.value=e:null),title:a(y)},{footer:u(()=>[t(U,{disabled:a(i),type:"primary",onClick:x},{default:u(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),t(U,{onClick:l[4]||(l[4]=e=>d.value=!1)},{default:u(()=>[v("\u53D6 \u6D88")]),_:1})]),default:u(()=>[H((m(),p(B,{ref_key:"formRef",ref:n,model:a(s),rules:a(S),"label-width":"100px"},{default:u(()=>[t(c,{label:"\u7EC4\u540D",prop:"name"},{default:u(()=>[t(g,{modelValue:a(s).name,"onUpdate:modelValue":l[0]||(l[0]=e=>a(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),t(c,{label:"\u63CF\u8FF0"},{default:u(()=>[t(g,{modelValue:a(s).description,"onUpdate:modelValue":l[1]||(l[1]=e=>a(s).description=e),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1}),t(c,{label:"\u6210\u5458",prop:"userIds"},{default:u(()=>[t(N,{modelValue:a(s).userIds,"onUpdate:modelValue":l[2]||(l[2]=e=>a(s).userIds=e),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6210\u5458"},{default:u(()=>[(m(!0),h(w,null,k(a(V),e=>(m(),p(C,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(c,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[t(O,{modelValue:a(s).status,"onUpdate:modelValue":l[3]||(l[3]=e=>a(s).status=e)},{default:u(()=>[(m(!0),h(w,null,k(a(K)(a(P).COMMON_STATUS),e=>(m(),p(L,{key:e.value,label:e.value},{default:u(()=>[v(Z(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[M,a(i)]])]),_:1},8,["modelValue","title"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/group/UserGroupForm.vue"]])});export{pe as __tla,E as default};
