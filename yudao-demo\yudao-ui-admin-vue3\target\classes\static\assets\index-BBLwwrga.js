import{d as y,$ as b,b as p,r as f,bt as z,H as h,a8 as x,o as C,l as N,a as P,_ as S,__tla as U}from"./index-Daqg4PFz.js";import{E as j,__tla as k}from"./index-CS70nJJ8.js";let m,q=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{m=S(y({name:"Pagination",__name:"index",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pagerCount:{type:Number,default:document.body.clientWidth<992?5:7}},emits:["update:page","update:limit","pagination"],setup(t,{emit:g}){const c=b(),s=p(()=>c.currentSize),o=f(s.value==="small");z(()=>{o.value=s.value==="small"});const u=t,l=g,e=p({get:()=>u.page,set(a){l("update:page",a)}}),r=p({get:()=>u.limit,set(a){l("update:limit",a)}}),d=a=>{e.value*a>u.total&&(e.value=1),l("pagination",{page:e.value,limit:a})},_=a=>{l("pagination",{page:a,limit:r.value})};return(a,n)=>{const v=j;return h((C(),N(v,{"current-page":e.value,"onUpdate:currentPage":n[0]||(n[0]=i=>e.value=i),"page-size":r.value,"onUpdate:pageSize":n[1]||(n[1]=i=>r.value=i),background:!0,"page-sizes":[10,20,30,50,100],"pager-count":t.pagerCount,total:t.total,small:P(o),class:"float-right mb-15px mt-15px",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:_},null,8,["current-page","page-size","pager-count","total","small"])),[[x,t.total>0]])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Pagination/index.vue"]])});export{m as _,q as __tla};
