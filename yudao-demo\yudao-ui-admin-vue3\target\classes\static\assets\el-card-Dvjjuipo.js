import{be as b,bf as v,d,bg as u,o as r,c as o,a0 as s,a as t,aW as l,j as y,t as f,a9 as i,g as p,av as _,bh as g,bi as S,__tla as $}from"./index-Daqg4PFz.js";let c,w=Promise.all([(()=>{try{return $}catch{}})()]).then(async()=>{const h=b({header:{type:String,default:""},footer:{type:String,default:""},bodyStyle:{type:v([String,Object,Array]),default:""},bodyClass:String,shadow:{type:String,values:["always","hover","never"],default:"always"}}),n=d({name:"ElCard"});c=S(g(d({...n,props:h,setup(m){const e=u("card");return(a,C)=>(r(),o("div",{class:s([t(e).b(),t(e).is(`${a.shadow}-shadow`)])},[a.$slots.header||a.header?(r(),o("div",{key:0,class:s(t(e).e("header"))},[l(a.$slots,"header",{},()=>[y(f(a.header),1)])],2)):i("v-if",!0),p("div",{class:s([t(e).e("body"),a.bodyClass]),style:_(a.bodyStyle)},[l(a.$slots,"default")],6),a.$slots.footer||a.footer?(r(),o("div",{key:1,class:s(t(e).e("footer"))},[l(a.$slots,"footer",{},()=>[y(f(a.footer),1)])],2)):i("v-if",!0)],2))}}),[["__file","card.vue"]]))});export{c as E,w as __tla};
