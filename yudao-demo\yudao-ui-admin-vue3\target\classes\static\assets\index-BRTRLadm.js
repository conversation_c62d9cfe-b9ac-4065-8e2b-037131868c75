import{bE as h,d as H,I,n as L,r as d,f as N,C as O,T,o as i,c as b,i as a,w as e,H as g,l as w,a as l,g as G,j as o,a9 as W,F as q,N as A,am as B,an as K,L as M,ch as Q,O as X,R as Y,_ as Z,__tla as $}from"./index-Daqg4PFz.js";import{_ as aa,__tla as ta}from"./ContentWrap-DZg14iby.js";import{E as ea,__tla as la}from"./el-card-Dvjjuipo.js";import{_ as sa,__tla as na}from"./index-CmwFi8Xl.js";import{C as ra,__tla as oa}from"./CardTitle-BD5ZuvK3.js";let E,ia=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})()]).then(async()=>{let _,f;_={class:"flex items-center justify-between"},f={key:0},E=Z(H({name:"CrmContractConfig",__name:"index",setup(ca){const V=I(),{t:C}=L(),s=d(!1),t=d({notifyEnabled:!1,notifyDays:void 0}),x=N({}),c=d(),m=async()=>{try{s.value=!0;const r=await(async()=>await h.get({url:"/crm/contract-config/get"}))();if(r===null)return;t.value=r}finally{s.value=!1}},D=async()=>{if(c&&await c.value.validate()){s.value=!0;try{const r=t.value;await(async n=>await h.put({url:"/crm/contract-config/save",data:n}))(r),V.success(C("common.updateSuccess")),await m(),s.value=!1}finally{s.value=!1}}},k=()=>{t.value.notifyEnabled||(t.value.notifyDays=void 0)};return O(()=>{m()}),(r,n)=>{const y=sa,S=A,v=B,U=K,p=M,j=Q,z=ea,J=X,P=aa,R=T("hasPermi"),F=Y;return i(),b(q,null,[a(y,{title:"\u3010\u5408\u540C\u3011\u5408\u540C\u7BA1\u7406\u3001\u5408\u540C\u63D0\u9192",url:"https://doc.iocoder.cn/crm/contract/"}),a(y,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),a(P,null,{default:e(()=>[g((i(),w(J,{ref_key:"formRef",ref:c,model:l(t),rules:l(x),"label-width":"160px"},{default:e(()=>[a(z,{shadow:"never"},{header:e(()=>[G("div",_,[a(l(ra),{title:"\u5408\u540C\u914D\u7F6E\u8BBE\u7F6E"}),g((i(),w(S,{type:"primary",onClick:D},{default:e(()=>[o(" \u4FDD\u5B58 ")]),_:1})),[[R,["crm:contract-config:update"]]])])]),default:e(()=>[a(p,{label:"\u63D0\u524D\u63D0\u9192\u8BBE\u7F6E",prop:"notifyEnabled"},{default:e(()=>[a(U,{modelValue:l(t).notifyEnabled,"onUpdate:modelValue":n[0]||(n[0]=u=>l(t).notifyEnabled=u),onChange:k,class:"ml-4"},{default:e(()=>[a(v,{label:!1,size:"large"},{default:e(()=>[o("\u4E0D\u63D0\u9192")]),_:1}),a(v,{label:!0,size:"large"},{default:e(()=>[o("\u63D0\u9192")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(t).notifyEnabled?(i(),b("div",f,[a(p,null,{default:e(()=>[o(" \u63D0\u524D "),a(j,{class:"mx-2",modelValue:l(t).notifyDays,"onUpdate:modelValue":n[1]||(n[1]=u=>l(t).notifyDays=u)},null,8,["modelValue"]),o(" \u5929\u63D0\u9192 ")]),_:1})])):W("",!0)]),_:1})]),_:1},8,["model","rules"])),[[F,l(s)]])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/contract/config/index.vue"]])});export{ia as __tla,E as default};
