import{d as $,u as ee,r as v,f as ae,C as le,cK as x,G as i,o as s,c,i as a,w as t,a as l,U as S,F as m,k as w,l as f,j as h,H as te,t as y,g,aG as re,aE as ue,aK as oe,Z as se,L as ne,J as pe,K as de,M as _e,x as ie,N as ce,O as me,A as fe,B as ye,P as be,ax as ve,Q as we,R as he,_ as Te,__tla as Ae}from"./index-Daqg4PFz.js";import{_ as Ve,__tla as xe}from"./index-BBLwwrga.js";import{_ as ge,__tla as Ee}from"./DictTag-BDZzHcIz.js";import{E as Ne,__tla as ke}from"./el-image-Bn34T02c.js";import{_ as Se,__tla as Ue}from"./ContentWrap-DZg14iby.js";import{_ as <PERSON>,__tla as De}from"./index-CmwFi8Xl.js";import{e as Ce,__tla as Fe}from"./index-C_5N0uJE.js";import{f as Le,__tla as Ye}from"./formatTime-BCfRGyrF.js";import{__tla as Pe}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Ke}from"./el-card-Dvjjuipo.js";let z,He=Promise.all([(()=>{try{return Ae}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Ke}catch{}})()]).then(async()=>{let U,R;U={class:"flex items-center"},R={class:"mr-10px"},z=Te($({name:"TradeAfterSale",__name:"index",setup(ze){const{push:D}=ee(),E=v(!0),C=v(0),F=v([]),L=v([{label:"\u5168\u90E8",value:"0"}]),Y=v(),r=ae({pageNo:1,pageSize:10,no:null,status:"0",orderNo:null,spuName:null,createTime:[],way:null,type:null}),T=async()=>{E.value=!0;try{const o=ue(r);o.status==="0"&&delete o.status;const u=await Ce(o);F.value=u.list,C.value=u.total}finally{E.value=!1}},b=async()=>{r.pageNo=1,await T()},I=()=>{var o;(o=Y.value)==null||o.resetFields(),b()},M=async o=>{r.status=o.paneName,await T()};return le(async()=>{await T();for(const o of x(i.TRADE_AFTER_SALE_STATUS))L.value.push({label:o.label,value:o.value})}),(o,u)=>{const O=Re,N=se,p=ne,A=pe,k=de,G=_e,P=ie,V=ce,J=me,K=Se,W=fe,j=ye,n=be,q=Ne,B=ve,H=ge,Q=we,Z=Ve,X=he;return s(),c(m,null,[a(O,{title:"\u3010\u4EA4\u6613\u3011\u552E\u540E\u9000\u6B3E",url:"https://doc.iocoder.cn/mall/trade-aftersale/"}),a(K,null,{default:t(()=>[a(J,{ref_key:"queryFormRef",ref:Y,inline:!0,model:l(r),"label-width":"68px"},{default:t(()=>[a(p,{label:"\u5546\u54C1\u540D\u79F0",prop:"spuName"},{default:t(()=>[a(N,{modelValue:l(r).spuName,"onUpdate:modelValue":u[0]||(u[0]=e=>l(r).spuName=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1 SPU \u540D\u79F0",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),a(p,{label:"\u9000\u6B3E\u7F16\u53F7",prop:"no"},{default:t(()=>[a(N,{modelValue:l(r).no,"onUpdate:modelValue":u[1]||(u[1]=e=>l(r).no=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u9000\u6B3E\u7F16\u53F7",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),a(p,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"orderNo"},{default:t(()=>[a(N,{modelValue:l(r).orderNo,"onUpdate:modelValue":u[2]||(u[2]=e=>l(r).orderNo=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),a(p,{label:"\u552E\u540E\u72B6\u6001",prop:"status"},{default:t(()=>[a(k,{modelValue:l(r).status,"onUpdate:modelValue":u[3]||(u[3]=e=>l(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u72B6\u6001"},{default:t(()=>[a(A,{label:"\u5168\u90E8",value:"0"}),(s(!0),c(m,null,w(l(x)(l(i).TRADE_AFTER_SALE_STATUS),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u552E\u540E\u65B9\u5F0F",prop:"way"},{default:t(()=>[a(k,{modelValue:l(r).way,"onUpdate:modelValue":u[4]||(u[4]=e=>l(r).way=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u65B9\u5F0F"},{default:t(()=>[(s(!0),c(m,null,w(l(x)(l(i).TRADE_AFTER_SALE_WAY),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u552E\u540E\u7C7B\u578B",prop:"type"},{default:t(()=>[a(k,{modelValue:l(r).type,"onUpdate:modelValue":u[5]||(u[5]=e=>l(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u7C7B\u578B"},{default:t(()=>[(s(!0),c(m,null,w(l(x)(l(i).TRADE_AFTER_SALE_TYPE),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(G,{modelValue:l(r).createTime,"onUpdate:modelValue":u[6]||(u[6]=e=>l(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-260px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(p,null,{default:t(()=>[a(V,{onClick:b},{default:t(()=>[a(P,{class:"mr-5px",icon:"ep:search"}),h(" \u641C\u7D22 ")]),_:1}),a(V,{onClick:I},{default:t(()=>[a(P,{class:"mr-5px",icon:"ep:refresh"}),h(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(K,null,{default:t(()=>[a(j,{modelValue:l(r).status,"onUpdate:modelValue":u[7]||(u[7]=e=>l(r).status=e),onTabClick:M},{default:t(()=>[(s(!0),c(m,null,w(l(L),e=>(s(),f(W,{key:e.label,label:e.label,name:e.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),te((s(),f(Q,{data:l(F)},{default:t(()=>[a(n,{align:"center",label:"\u9000\u6B3E\u7F16\u53F7","min-width":"200",prop:"no"}),a(n,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"200",prop:"orderNo"},{default:t(({row:e})=>[a(V,{link:"",type:"primary",onClick:_=>{return d=e.orderId,void D({name:"TradeOrderDetail",params:{orderId:d}});var d}},{default:t(()=>[h(y(e.orderNo),1)]),_:2},1032,["onClick"])]),_:1}),a(n,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"600",prop:"spuName"},{default:t(({row:e})=>[g("div",U,[a(q,{src:e.picUrl,class:"mr-10px h-30px w-30px",onClick:_=>{return d=e.picUrl,void oe({urlList:[d]});var d}},null,8,["src","onClick"]),g("span",R,y(e.spuName),1),(s(!0),c(m,null,w(e.properties,_=>(s(),f(B,{key:_.propertyId,class:"mr-10px"},{default:t(()=>[h(y(_.propertyName)+": "+y(_.valueName),1)]),_:2},1024))),128))])]),_:1}),a(n,{align:"center",label:"\u8BA2\u5355\u91D1\u989D",prop:"refundPrice"},{default:t(e=>[g("span",null,y(l(re)(e.row.refundPrice))+" \u5143",1)]),_:1}),a(n,{align:"center",label:"\u4E70\u5BB6",prop:"user.nickname"}),a(n,{align:"center",label:"\u7533\u8BF7\u65F6\u95F4",prop:"createTime",width:"180"},{default:t(e=>[g("span",null,y(l(Le)(e.row.createTime)),1)]),_:1}),a(n,{align:"center",label:"\u552E\u540E\u72B6\u6001",width:"100"},{default:t(e=>[a(H,{type:l(i).TRADE_AFTER_SALE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{align:"center",label:"\u552E\u540E\u65B9\u5F0F"},{default:t(e=>[a(H,{type:l(i).TRADE_AFTER_SALE_WAY,value:e.row.way},null,8,["type","value"])]),_:1}),a(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"160"},{default:t(({row:e})=>[a(V,{link:"",type:"primary",onClick:_=>{return d=e.id,void D({name:"TradeAfterSaleDetail",params:{id:d}});var d}},{default:t(()=>[h("\u5904\u7406\u9000\u6B3E")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,l(E)]]),a(Z,{limit:l(r).pageSize,"onUpdate:limit":u[8]||(u[8]=e=>l(r).pageSize=e),page:l(r).pageNo,"onUpdate:page":u[9]||(u[9]=e=>l(r).pageNo=e),total:l(C),onPagination:T},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/trade/afterSale/index.vue"]])});export{He as __tla,z as default};
