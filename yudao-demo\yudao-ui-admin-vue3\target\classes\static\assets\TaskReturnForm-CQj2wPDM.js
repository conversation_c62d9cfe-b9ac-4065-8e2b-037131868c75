import{d as C,I as J,r as n,o as _,l as c,w as s,i as u,a,j as p,H as q,c as I,F as j,k as z,z as H,J as L,K as N,L as O,Z as P,O as Z,N as A,R as B,_ as E,__tla as G}from"./index-Daqg4PFz.js";import{_ as M,__tla as Q}from"./Dialog-BjBBVYCI.js";import{b as S,c as W,__tla as X}from"./index-CYOuQA7P.js";let g,Y=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{g=E(C({__name:"TaskReturnForm",emits:["success"],setup($,{expose:k,emit:b}){const f=J(),r=n(!1),o=n(!1),l=n({id:"",targetTaskDefinitionKey:void 0,reason:""}),h=n({targetTaskDefinitionKey:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u56DE\u9000\u8282\u70B9",trigger:"change"}],reason:[{required:!0,message:"\u56DE\u9000\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=n(),m=n([]);k({open:async i=>{if(m.value=await S(i),m.value.length===0)return f.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u56DE\u9000\u7684\u8282\u70B9"),!1;r.value=!0,V(),l.value.id=i}});const w=b,K=async()=>{if(d&&await d.value.validate()){o.value=!0;try{await W(l.value),f.success("\u56DE\u9000\u6210\u529F"),r.value=!1,w("success")}finally{o.value=!1}}},V=()=>{var i;l.value={id:"",targetTaskDefinitionKey:void 0,reason:""},(i=d.value)==null||i.resetFields()};return(i,t)=>{const T=L,D=N,y=O,F=P,R=Z,v=A,U=M,x=B;return _(),c(U,{modelValue:a(r),"onUpdate:modelValue":t[3]||(t[3]=e=>H(r)?r.value=e:null),title:"\u56DE\u9000\u4EFB\u52A1",width:"500"},{footer:s(()=>[u(v,{disabled:a(o),type:"primary",onClick:K},{default:s(()=>[p("\u786E \u5B9A")]),_:1},8,["disabled"]),u(v,{onClick:t[2]||(t[2]=e=>r.value=!1)},{default:s(()=>[p("\u53D6 \u6D88")]),_:1})]),default:s(()=>[q((_(),c(R,{ref_key:"formRef",ref:d,model:a(l),rules:a(h),"label-width":"110px"},{default:s(()=>[u(y,{label:"\u9000\u56DE\u8282\u70B9",prop:"targetTaskDefinitionKey"},{default:s(()=>[u(D,{modelValue:a(l).targetTaskDefinitionKey,"onUpdate:modelValue":t[0]||(t[0]=e=>a(l).targetTaskDefinitionKey=e),clearable:"",style:{width:"100%"}},{default:s(()=>[(_(!0),I(j,null,z(a(m),e=>(_(),c(T,{key:e.taskDefinitionKey,label:e.name,value:e.taskDefinitionKey},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(y,{label:"\u56DE\u9000\u7406\u7531",prop:"reason"},{default:s(()=>[u(F,{modelValue:a(l).reason,"onUpdate:modelValue":t[1]||(t[1]=e=>a(l).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u56DE\u9000\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[x,a(o)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/dialog/TaskReturnForm.vue"]])});export{Y as __tla,g as default};
