import{d as N,I as U,r as x,f as X,C as Z,T as q,o as O,c as B,i,g as A,w as n,a as e,ef as p,aG as d,H as K,l as V,j as Y,F as Q,cx as W,E as $,s as ee,x as te,N as ae,_ as re,__tla as le}from"./index-Daqg4PFz.js";import{E as oe,__tla as ce}from"./el-card-Dvjjuipo.js";import{E as ie,__tla as se}from"./el-skeleton-item-BmS2F7Yy.js";import{_ as ne,__tla as ue}from"./Echart-C33-KcLZ.js";import{_ as de,__tla as me}from"./index-CfQWqgvA.js";import{_ as _e,__tla as pe}from"./index-CmwFi8Xl.js";import{c as fe,d as ye,e as ve,f as Pe,__tla as xe}from"./trade-P1ZamxKR.js";import h,{__tla as ge}from"./TradeStatisticValue-g1zH3D0i.js";import{S as y,__tla as he}from"./index-CvS2v6KM.js";import{d as be}from"./download--D_IyRio.js";import{C as we,__tla as Ce}from"./CardTitle-BD5ZuvK3.js";import{m as ke,f as Se,__tla as Oe}from"./formatTime-BCfRGyrF.js";import{__tla as Ae}from"./CountTo-Dat_y5oU.js";let I,Ie=Promise.all([(()=>{try{return le}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ae}catch{}})()]).then(async()=>{let C,k;C={class:"flex flex-col"},k={class:"flex flex-row items-center justify-between"},I=re(N({name:"TradeStatistics",__name:"index",setup(Ee){const E=U(),b=x(!0),w=x(!1),u=x(),s=x(),v=x(),g=X({dataset:{dimensions:["date","turnoverPrice","orderPayPrice","rechargePrice","expensePrice"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u8425\u4E1A\u989D",type:"line",smooth:!0},{name:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D",type:"line",smooth:!0},{name:"\u5145\u503C\u91D1\u989D",type:"line",smooth:!0},{name:"\u652F\u51FA\u91D1\u989D",type:"line",smooth:!0}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u4EA4\u6613\u72B6\u51B5"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}}),R=async()=>{b.value=!0;const m=v.value.times;ke(m[0],m[1])&&(m[0]=Se(W(m[0]).subtract(1,"d"))),await Promise.all([T(),j()]),b.value=!1},T=async()=>{const m=v.value.times;s.value=await ye({times:m})},j=async()=>{const m=v.value.times,P=await ve({times:m});for(let f of P)f.turnoverPrice=d(f.turnoverPrice),f.orderPayPrice=d(f.orderPayPrice),f.rechargePrice=d(f.rechargePrice),f.expensePrice=d(f.expensePrice);g.dataset&&g.dataset.source&&(g.dataset.source=P)},G=async()=>{try{await E.exportConfirm(),w.value=!0;const m=v.value.times,P=await Pe({times:m});be.excel(P,"\u4EA4\u6613\u72B6\u51B5.xls")}catch{}finally{w.value=!1}};return Z(async()=>{await(async()=>{u.value=await fe()})()}),(m,P)=>{const f=_e,_=$,S=ee,z=te,D=ae,F=de,H=ne,J=ie,L=oe,M=q("hasPermi");return O(),B(Q,null,[i(f,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),A("div",C,[i(S,{gutter:16,class:"summary"},{default:n(()=>[i(_,{sm:6,xs:12},{default:n(()=>{var t,a,r,l,o,c;return[i(h,{tooltip:"\u6628\u65E5\u8BA2\u5355\u6570\u91CF",title:"\u6628\u65E5\u8BA2\u5355\u6570\u91CF",value:((a=(t=e(u))==null?void 0:t.value)==null?void 0:a.yesterdayOrderCount)||0,percent:e(p)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.yesterdayOrderCount,(c=(o=e(u))==null?void 0:o.reference)==null?void 0:c.yesterdayOrderCount)},null,8,["value","percent"])]}),_:1}),i(_,{sm:6,xs:12},{default:n(()=>{var t,a,r,l,o,c;return[i(h,{tooltip:"\u672C\u6708\u8BA2\u5355\u6570\u91CF",title:"\u672C\u6708\u8BA2\u5355\u6570\u91CF",value:((a=(t=e(u))==null?void 0:t.value)==null?void 0:a.monthOrderCount)||0,percent:e(p)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.monthOrderCount,(c=(o=e(u))==null?void 0:o.reference)==null?void 0:c.monthOrderCount)},null,8,["value","percent"])]}),_:1}),i(_,{sm:6,xs:12},{default:n(()=>{var t,a,r,l,o,c;return[i(h,{tooltip:"\u6628\u65E5\u652F\u4ED8\u91D1\u989D",title:"\u6628\u65E5\u652F\u4ED8\u91D1\u989D",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(u))==null?void 0:t.value)==null?void 0:a.yesterdayPayPrice)||0),percent:e(p)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.yesterdayPayPrice,(c=(o=e(u))==null?void 0:o.reference)==null?void 0:c.yesterdayPayPrice)},null,8,["value","percent"])]}),_:1}),i(_,{sm:6,xs:12},{default:n(()=>{var t,a,r,l,o,c;return[i(h,{tooltip:"\u672C\u6708\u652F\u4ED8\u91D1\u989D",title:"\u672C\u6708\u652F\u4ED8\u91D1\u989D",prefix:"\uFFE5",":decimals":2,value:e(d)(((a=(t=e(u))==null?void 0:t.value)==null?void 0:a.monthPayPrice)||0),percent:e(p)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.monthPayPrice,(c=(o=e(u))==null?void 0:o.reference)==null?void 0:c.monthPayPrice)},null,8,["value","percent"])]}),_:1})]),_:1}),i(L,{shadow:"never"},{header:n(()=>[A("div",k,[i(e(we),{title:"\u4EA4\u6613\u72B6\u51B5"}),i(F,{ref_key:"shortcutDateRangePicker",ref:v,onChange:R},{default:n(()=>[K((O(),V(D,{class:"ml-4",onClick:G,loading:e(w)},{default:n(()=>[i(z,{icon:"ep:download",class:"mr-1"}),Y("\u5BFC\u51FA ")]),_:1},8,["loading"])),[[M,["statistics:trade:export"]]])]),_:1},512)])]),default:n(()=>[i(S,{gutter:16},{default:n(()=>[i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u8425\u4E1A\u989D",tooltip:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D\u3001\u5145\u503C\u91D1\u989D",icon:"fa-solid:yen-sign","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.turnoverPrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.turnoverPrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.turnoverPrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D",tooltip:"\u7528\u6237\u8D2D\u4E70\u5546\u54C1\u7684\u5B9E\u9645\u652F\u4ED8\u91D1\u989D\uFF0C\u5305\u62EC\u5FAE\u4FE1\u652F\u4ED8\u3001\u4F59\u989D\u652F\u4ED8\u3001\u652F\u4ED8\u5B9D\u652F\u4ED8\u3001\u7EBF\u4E0B\u652F\u4ED8\u91D1\u989D\uFF08\u62FC\u56E2\u5546\u54C1\u5728\u6210\u56E2\u4E4B\u540E\u8BA1\u5165\uFF0C\u7EBF\u4E0B\u652F\u4ED8\u8BA2\u5355\u5728\u540E\u53F0\u786E\u8BA4\u652F\u4ED8\u540E\u8BA1\u5165\uFF09",icon:"fa-solid:shopping-cart","icon-color":"bg-purple-100","icon-bg-color":"text-purple-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.orderPayPrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.orderPayPrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.orderPayPrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u5145\u503C\u91D1\u989D",tooltip:"\u7528\u6237\u6210\u529F\u5145\u503C\u7684\u91D1\u989D",icon:"fa-solid:money-check-alt","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.rechargePrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.rechargePrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.rechargePrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u652F\u51FA\u91D1\u989D",tooltip:"\u4F59\u989D\u652F\u4ED8\u91D1\u989D\u3001\u652F\u4ED8\u4F63\u91D1\u91D1\u989D\u3001\u5546\u54C1\u9000\u6B3E\u91D1\u989D",icon:"ep:warning-filled","icon-color":"bg-green-100","icon-bg-color":"text-green-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.expensePrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.expensePrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.expensePrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u4F59\u989D\u652F\u4ED8\u91D1\u989D",tooltip:"\u7528\u6237\u4E0B\u5355\u65F6\u4F7F\u7528\u4F59\u989D\u5B9E\u9645\u652F\u4ED8\u7684\u91D1\u989D",icon:"fa-solid:wallet","icon-color":"bg-cyan-100","icon-bg-color":"text-cyan-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.walletPayPrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.walletPayPrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.walletPayPrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u652F\u4ED8\u4F63\u91D1\u91D1\u989D",tooltip:"\u540E\u53F0\u7ED9\u63A8\u5E7F\u5458\u652F\u4ED8\u7684\u63A8\u5E7F\u4F63\u91D1\uFF0C\u4EE5\u5B9E\u9645\u652F\u4ED8\u4E3A\u51C6",icon:"fa-solid:award","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.brokerageSettlementPrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.brokerageSettlementPrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.brokerageSettlementPrice)},null,8,["value","percent"])]}),_:1}),i(_,{md:6,sm:12,xs:24},{default:n(()=>{var t,a,r,l,o,c;return[i(y,{title:"\u5546\u54C1\u9000\u6B3E\u91D1\u989D",tooltip:"\u7528\u6237\u6210\u529F\u9000\u6B3E\u7684\u5546\u54C1\u91D1\u989D",icon:"fa-solid:times-circle","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",prefix:"\uFFE5",decimals:2,value:e(d)(((a=(t=e(s))==null?void 0:t.value)==null?void 0:a.afterSaleRefundPrice)||0),percent:e(p)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.afterSaleRefundPrice,(c=(o=e(s))==null?void 0:o.reference)==null?void 0:c.afterSaleRefundPrice)},null,8,["value","percent"])]}),_:1})]),_:1}),i(J,{loading:e(b),animated:""},{default:n(()=>[i(H,{height:500,options:e(g)},null,8,["options"])]),_:1},8,["loading"])]),_:1})])],64)}}}),[["__scopeId","data-v-fb5ef5e3"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mall/statistics/trade/index.vue"]])});export{Ie as __tla,I as default};
