import{be as De,bf as j,cZ as Pe,bh as G,d as L,r as F,b7 as I,b as E,cd as U,cU as Ke,bk as A,a as k,at as ae,c_ as Me,aW as D,c$ as T,au as h,o as x,l as z,w as g,i as C,aX as Ge,aY as Le,d0 as ie,bV as O,az as Oe,N as de,b3 as ze,d1 as Ae,aO as He,d2 as Ne,bs as se,b$ as Ue,d3 as We,as as ue,bg as X,bW as Ye,br as Je,d4 as Ve,bc as je,c1 as Xe,c as W,ce as Ze,ao as H,a0 as Z,a9 as q,d5 as ce,d6 as qe,d7 as Qe,g as en,b1 as nn,b2 as Q,F as on,d8 as pe,d9 as tn,da as ve,db as ln,dc as rn,dd as an,av as dn,de as sn,df as un,bi as cn,bl as fe,__tla as pn}from"./index-Daqg4PFz.js";let me,ge,be,vn=Promise.all([(()=>{try{return pn}catch{}})()]).then(async()=>{const we=De({style:{type:j([String,Array,Object])},currentTabId:{type:j(String)},defaultCurrentTabId:String,loop:Boolean,dir:{type:String,values:["ltr","rtl"],default:"ltr"},orientation:{type:j(String)},onBlur:Function,onFocus:Function,onMousedown:Function}),{ElCollection:he,ElCollectionItem:ye,COLLECTION_INJECTION_KEY:Y,COLLECTION_ITEM_INJECTION_KEY:Ie}=Pe("RovingFocusGroup"),J=Symbol("elRovingFocusGroup"),ee=Symbol("elRovingFocusGroupItem"),Ee={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"},V=e=>{const{activeElement:o}=document;for(const c of e)if(c===o||(c.focus(),o!==document.activeElement))return},ne="currentTabIdChange",oe="rovingFocusGroup.entryFocus",Fe={bubbles:!1,cancelable:!0},_e=L({name:"ElRovingFocusGroupImpl",inheritAttrs:!1,props:we,emits:[ne,"entryFocus"],setup(e,{emit:o}){var c;const d=F((c=e.currentTabId||e.defaultCurrentTabId)!=null?c:null),p=F(!1),s=F(!1),a=F(null),{getItems:r}=I(Y,void 0),v=E(()=>[{outline:"none"},e.style]),b=T(l=>{var i;(i=e.onMousedown)==null||i.call(e,l)},()=>{s.value=!0}),w=T(l=>{var i;(i=e.onFocus)==null||i.call(e,l)},l=>{const i=!k(s),{target:f,currentTarget:y}=l;if(f===y&&i&&!k(p)){const P=new Event(oe,Fe);if(y==null||y.dispatchEvent(P),!P.defaultPrevented){const R=r().filter(m=>m.focusable),S=[R.find(m=>m.active),R.find(m=>m.id===k(d)),...R].filter(Boolean).map(m=>m.ref);V(S)}}s.value=!1}),t=T(l=>{var i;(i=e.onBlur)==null||i.call(e,l)},()=>{p.value=!1});U(J,{currentTabbedId:Ke(d),loop:A(e,"loop"),tabIndex:E(()=>k(p)?-1:0),rovingFocusGroupRef:a,rovingFocusGroupRootStyle:v,orientation:A(e,"orientation"),dir:A(e,"dir"),onItemFocus:l=>{o(ne,l)},onItemShiftTab:()=>{p.value=!0},onBlur:t,onFocus:w,onMousedown:b}),ae(()=>e.currentTabId,l=>{d.value=l??null}),Me(a,oe,(...l)=>{o("entryFocus",...l)})}});var Ce=G(L({name:"ElRovingFocusGroup",components:{ElFocusGroupCollection:he,ElRovingFocusGroupImpl:G(_e,[["render",function(e,o,c,d,p,s){return D(e.$slots,"default")}],["__file","roving-focus-group-impl.vue"]])}}),[["render",function(e,o,c,d,p,s){const a=h("el-roving-focus-group-impl"),r=h("el-focus-group-collection");return x(),z(r,null,{default:g(()=>[C(a,Ge(Le(e.$attrs)),{default:g(()=>[D(e.$slots,"default")]),_:3},16)]),_:3})}],["__file","roving-focus-group.vue"]]),Re=G(L({components:{ElRovingFocusCollectionItem:ye},props:{focusable:{type:Boolean,default:!0},active:{type:Boolean,default:!1}},emits:["mousedown","focus","keydown"],setup(e,{emit:o}){const{currentTabbedId:c,loop:d,onItemFocus:p,onItemShiftTab:s}=I(J,void 0),{getItems:a}=I(Y,void 0),r=ie(),v=F(null),b=T(i=>{o("mousedown",i)},i=>{e.focusable?p(k(r)):i.preventDefault()}),w=T(i=>{o("focus",i)},()=>{p(k(r))}),t=T(i=>{o("keydown",i)},i=>{const{key:f,shiftKey:y,target:P,currentTarget:R}=i;if(f===O.tab&&y)return void s();if(P!==R)return;const S=((n,u,_)=>{const $=((K,M)=>K)(n.key);return Ee[$]})(i);if(S){i.preventDefault();let n=a().filter(u=>u.focusable).map(u=>u.ref);switch(S){case"last":n.reverse();break;case"prev":case"next":{S==="prev"&&n.reverse();const u=n.indexOf(R);n=d.value?(B=u+1,(m=n).map((_,$)=>m[($+B)%m.length])):n.slice(u+1);break}}Oe(()=>{V(n)})}var m,B}),l=E(()=>c.value===k(r));return U(ee,{rovingFocusGroupItemRef:v,tabIndex:E(()=>k(l)?0:-1),handleMousedown:b,handleFocus:w,handleKeydown:t}),{id:r,handleKeydown:t,handleFocus:w,handleMousedown:b}}}),[["render",function(e,o,c,d,p,s){const a=h("el-roving-focus-collection-item");return x(),z(a,{id:e.id,focusable:e.focusable,active:e.active},{default:g(()=>[D(e.$slots,"default")]),_:3},8,["id","focusable","active"])}],["__file","roving-focus-item.vue"]]);const N=Symbol("elDropdown"),{ButtonGroup:ke}=de;var Te=G(L({name:"ElDropdown",components:{ElButton:de,ElButtonGroup:ke,ElScrollbar:ze,ElDropdownCollection:Ae,ElTooltip:He,ElRovingFocusGroup:Ce,ElOnlyChild:Ne,ElIcon:se,ArrowDown:Ue},props:We,emits:["visible-change","click","command"],setup(e,{emit:o}){const c=ue(),d=X("dropdown"),{t:p}=Ye(),s=F(),a=F(),r=F(null),v=F(null),b=F(null),w=F(null),t=F(!1),l=[O.enter,O.space,O.down],i=E(()=>({maxHeight:Je(e.maxHeight)})),f=E(()=>[d.m(m.value)]),y=E(()=>Ve(e.trigger)),P=ie().value,R=E(()=>e.id||P);function S(){var n;(n=r.value)==null||n.onClose()}ae([s,y],([n,u],[_])=>{var $,K,M;($=_==null?void 0:_.$el)!=null&&$.removeEventListener&&_.$el.removeEventListener("pointerenter",B),(K=n==null?void 0:n.$el)!=null&&K.removeEventListener&&n.$el.removeEventListener("pointerenter",B),(M=n==null?void 0:n.$el)!=null&&M.addEventListener&&u.includes("hover")&&n.$el.addEventListener("pointerenter",B)},{immediate:!0}),je(()=>{var n,u;(u=(n=s.value)==null?void 0:n.$el)!=null&&u.removeEventListener&&s.value.$el.removeEventListener("pointerenter",B)});const m=Xe();function B(){var n,u;(u=(n=s.value)==null?void 0:n.$el)==null||u.focus()}return U(N,{contentRef:v,role:E(()=>e.role),triggerId:R,isUsingKeyboard:t,onItemEnter:function(){},onItemLeave:function(){const n=k(v);y.value.includes("hover")&&(n==null||n.focus()),w.value=null}}),U("elDropdown",{instance:c,dropdownSize:m,handleClick:function(){S()},commandHandler:function(...n){o("command",...n)},trigger:A(e,"trigger"),hideOnClick:A(e,"hideOnClick")}),{t:p,ns:d,scrollbar:b,wrapStyle:i,dropdownTriggerKls:f,dropdownSize:m,triggerId:R,triggerKeys:l,currentTabId:w,handleCurrentTabIdChange:function(n){w.value=n},handlerMainButtonClick:n=>{o("click",n)},handleEntryFocus:function(n){t.value||(n.preventDefault(),n.stopImmediatePropagation())},handleClose:S,handleOpen:function(){var n;(n=r.value)==null||n.onOpen()},handleBeforeShowTooltip:function(){o("visible-change",!0)},handleShowTooltip:function(n){(n==null?void 0:n.type)==="keydown"&&v.value.focus()},handleBeforeHideTooltip:function(){o("visible-change",!1)},onFocusAfterTrapped:n=>{var u,_;n.preventDefault(),(_=(u=v.value)==null?void 0:u.focus)==null||_.call(u,{preventScroll:!0})},popperRef:r,contentRef:v,triggeringElementRef:s,referenceElementRef:a}}}),[["render",function(e,o,c,d,p,s){var a;const r=h("el-dropdown-collection"),v=h("el-roving-focus-group"),b=h("el-scrollbar"),w=h("el-only-child"),t=h("el-tooltip"),l=h("el-button"),i=h("arrow-down"),f=h("el-icon"),y=h("el-button-group");return x(),W("div",{class:Z([e.ns.b(),e.ns.is("disabled",e.disabled)])},[C(t,{ref:"popperRef",role:e.role,effect:e.effect,"fallback-placements":["bottom","top"],"popper-options":e.popperOptions,"gpu-acceleration":!1,"hide-after":e.trigger==="hover"?e.hideTimeout:0,"manual-mode":!0,placement:e.placement,"popper-class":[e.ns.e("popper"),e.popperClass],"reference-element":(a=e.referenceElementRef)==null?void 0:a.$el,trigger:e.trigger,"trigger-keys":e.triggerKeys,"trigger-target-el":e.contentRef,"show-after":e.trigger==="hover"?e.showTimeout:0,"stop-popper-mouse-event":!1,"virtual-ref":e.triggeringElementRef,"virtual-triggering":e.splitButton,disabled:e.disabled,transition:`${e.ns.namespace.value}-zoom-in-top`,teleported:e.teleported,pure:"",persistent:"",onBeforeShow:e.handleBeforeShowTooltip,onShow:e.handleShowTooltip,onBeforeHide:e.handleBeforeHideTooltip},Ze({content:g(()=>[C(b,{ref:"scrollbar","wrap-style":e.wrapStyle,tag:"div","view-class":e.ns.e("list")},{default:g(()=>[C(v,{loop:e.loop,"current-tab-id":e.currentTabId,orientation:"horizontal",onCurrentTabIdChange:e.handleCurrentTabIdChange,onEntryFocus:e.handleEntryFocus},{default:g(()=>[C(r,null,{default:g(()=>[D(e.$slots,"dropdown")]),_:3})]),_:3},8,["loop","current-tab-id","onCurrentTabIdChange","onEntryFocus"])]),_:3},8,["wrap-style","view-class"])]),_:2},[e.splitButton?void 0:{name:"default",fn:g(()=>[C(w,{id:e.triggerId,ref:"triggeringElementRef",role:"button",tabindex:e.tabindex},{default:g(()=>[D(e.$slots,"default")]),_:3},8,["id","tabindex"])])}]),1032,["role","effect","popper-options","hide-after","placement","popper-class","reference-element","trigger","trigger-keys","trigger-target-el","show-after","virtual-ref","virtual-triggering","disabled","transition","teleported","onBeforeShow","onShow","onBeforeHide"]),e.splitButton?(x(),z(y,{key:0},{default:g(()=>[C(l,H({ref:"referenceElementRef"},e.buttonProps,{size:e.dropdownSize,type:e.type,disabled:e.disabled,tabindex:e.tabindex,onClick:e.handlerMainButtonClick}),{default:g(()=>[D(e.$slots,"default")]),_:3},16,["size","type","disabled","tabindex","onClick"]),C(l,H({id:e.triggerId,ref:"triggeringElementRef"},e.buttonProps,{role:"button",size:e.dropdownSize,type:e.type,class:e.ns.e("caret-button"),disabled:e.disabled,tabindex:e.tabindex,"aria-label":e.t("el.dropdown.toggleDropdown")}),{default:g(()=>[C(f,{class:Z(e.ns.e("icon"))},{default:g(()=>[C(i)]),_:1},8,["class"])]),_:1},16,["id","size","type","class","disabled","tabindex","aria-label"])]),_:3})):q("v-if",!0)],2)}],["__file","dropdown.vue"]]);const Se=L({name:"DropdownItemImpl",components:{ElIcon:se},props:ce,emits:["pointermove","pointerleave","click","clickimpl"],setup(e,{emit:o}){const c=X("dropdown"),{role:d}=I(N,void 0),{collectionItemRef:p}=I(qe,void 0),{collectionItemRef:s}=I(Ie,void 0),{rovingFocusGroupItemRef:a,tabIndex:r,handleFocus:v,handleKeydown:b,handleMousedown:w}=I(ee,void 0),t=pe(p,s,a),l=E(()=>d.value==="menu"?"menuitem":d.value==="navigation"?"link":"button"),i=T(f=>{const{code:y}=f;if(y===O.enter||y===O.space)return f.preventDefault(),f.stopImmediatePropagation(),o("clickimpl",f),!0},b);return{ns:c,itemRef:t,dataset:{[Qe]:""},role:l,tabIndex:r,handleFocus:v,handleKeydown:i,handleMousedown:w}}}),xe=["aria-disabled","tabindex","role"],te=()=>{const e=I("elDropdown",{}),o=E(()=>e==null?void 0:e.dropdownSize);return{elDropdown:e,_elDropdownSize:o}};var le=G(L({name:"ElDropdownItem",components:{ElDropdownCollectionItem:tn,ElRovingFocusItem:Re,ElDropdownItemImpl:G(Se,[["render",function(e,o,c,d,p,s){const a=h("el-icon");return x(),W(on,null,[e.divided?(x(),W("li",H({key:0,role:"separator",class:e.ns.bem("menu","item","divided")},e.$attrs),null,16)):q("v-if",!0),en("li",H({ref:e.itemRef},{...e.dataset,...e.$attrs},{"aria-disabled":e.disabled,class:[e.ns.be("menu","item"),e.ns.is("disabled",e.disabled)],tabindex:e.tabIndex,role:e.role,onClick:o[0]||(o[0]=r=>e.$emit("clickimpl",r)),onFocus:o[1]||(o[1]=(...r)=>e.handleFocus&&e.handleFocus(...r)),onKeydown:o[2]||(o[2]=Q((...r)=>e.handleKeydown&&e.handleKeydown(...r),["self"])),onMousedown:o[3]||(o[3]=(...r)=>e.handleMousedown&&e.handleMousedown(...r)),onPointermove:o[4]||(o[4]=r=>e.$emit("pointermove",r)),onPointerleave:o[5]||(o[5]=r=>e.$emit("pointerleave",r))}),[e.icon?(x(),z(a,{key:0},{default:g(()=>[(x(),z(nn(e.icon)))]),_:1})):q("v-if",!0),D(e.$slots,"default")],16,xe)],64)}],["__file","dropdown-item-impl.vue"]])},inheritAttrs:!1,props:ce,emits:["pointermove","pointerleave","click"],setup(e,{emit:o,attrs:c}){const{elDropdown:d}=te(),p=ue(),s=F(null),a=E(()=>{var t,l;return(l=(t=k(s))==null?void 0:t.textContent)!=null?l:""}),{onItemEnter:r,onItemLeave:v}=I(N,void 0),b=T(t=>(o("pointermove",t),t.defaultPrevented),ve(t=>{if(e.disabled)return void v(t);const l=t.currentTarget;l===document.activeElement||l.contains(document.activeElement)||(r(t),t.defaultPrevented||l==null||l.focus())})),w=T(t=>(o("pointerleave",t),t.defaultPrevented),ve(t=>{v(t)}));return{handleClick:T(t=>{if(!e.disabled)return o("click",t),t.type!=="keydown"&&t.defaultPrevented},t=>{var l,i,f;e.disabled?t.stopImmediatePropagation():((l=d==null?void 0:d.hideOnClick)!=null&&l.value&&((i=d.handleClick)==null||i.call(d)),(f=d.commandHandler)==null||f.call(d,e.command,p,t))}),handlePointerMove:b,handlePointerLeave:w,textContent:a,propsAndAttrs:E(()=>({...e,...c}))}}}),[["render",function(e,o,c,d,p,s){var a;const r=h("el-dropdown-item-impl"),v=h("el-roving-focus-item"),b=h("el-dropdown-collection-item");return x(),z(b,{disabled:e.disabled,"text-value":(a=e.textValue)!=null?a:e.textContent},{default:g(()=>[C(v,{focusable:!e.disabled},{default:g(()=>[C(r,H(e.propsAndAttrs,{onPointerleave:e.handlePointerLeave,onPointermove:e.handlePointerMove,onClickimpl:e.handleClick}),{default:g(()=>[D(e.$slots,"default")]),_:3},16,["onPointerleave","onPointermove","onClickimpl"])]),_:3},8,["focusable"])]),_:3},8,["disabled","text-value"])}],["__file","dropdown-item.vue"]]);const Be=L({name:"ElDropdownMenu",props:ln,setup(e){const o=X("dropdown"),{_elDropdownSize:c}=te(),d=c.value,{focusTrapRef:p,onKeydown:s}=I(rn,void 0),{contentRef:a,role:r,triggerId:v}=I(N,void 0),{collectionRef:b,getItems:w}=I(an,void 0),{rovingFocusGroupRef:t,rovingFocusGroupRootStyle:l,tabIndex:i,onBlur:f,onFocus:y,onMousedown:P}=I(J,void 0),{collectionRef:R}=I(Y,void 0),S=E(()=>[o.b("menu"),o.bm("menu",d==null?void 0:d.value)]),m=pe(a,b,p,t,R),B=T(n=>{var u;(u=e.onKeydown)==null||u.call(e,n)},n=>{const{currentTarget:u,code:_,target:$}=n;if(u.contains($),O.tab===_&&n.stopImmediatePropagation(),n.preventDefault(),$!==k(a)||!sn.includes(_))return;const K=w().filter(M=>!M.disabled).map(M=>M.ref);un.includes(_)&&K.reverse(),V(K)});return{size:d,rovingFocusGroupRootStyle:l,tabIndex:i,dropdownKls:S,role:r,triggerId:v,dropdownListWrapperRef:m,handleKeydown:n=>{B(n),s(n)},onBlur:f,onFocus:y,onMousedown:P}}}),$e=["role","aria-labelledby"];var re=G(Be,[["render",function(e,o,c,d,p,s){return x(),W("ul",{ref:e.dropdownListWrapperRef,class:Z(e.dropdownKls),style:dn(e.rovingFocusGroupRootStyle),tabindex:-1,role:e.role,"aria-labelledby":e.triggerId,onBlur:o[0]||(o[0]=(...a)=>e.onBlur&&e.onBlur(...a)),onFocus:o[1]||(o[1]=(...a)=>e.onFocus&&e.onFocus(...a)),onKeydown:o[2]||(o[2]=Q((...a)=>e.handleKeydown&&e.handleKeydown(...a),["self"])),onMousedown:o[3]||(o[3]=Q((...a)=>e.onMousedown&&e.onMousedown(...a),["self"]))},[D(e.$slots,"default")],46,$e)}],["__file","dropdown-menu.vue"]]);be=cn(Te,{DropdownItem:le,DropdownMenu:re}),me=fe(le),ge=fe(re)});export{me as E,vn as __tla,ge as a,be as b};
