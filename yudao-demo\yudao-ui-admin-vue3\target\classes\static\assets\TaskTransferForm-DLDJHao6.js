import{d as q,r as u,o as m,l as _,w as r,i as d,a,j as p,H as J,c as N,F as R,k as j,z,ay as H,J as K,K as L,L as O,Z as P,O as Z,N as A,R as B,_ as D,__tla as E}from"./index-Daqg4PFz.js";import{_ as G,__tla as M}from"./Dialog-BjBBVYCI.js";import{t as Q,__tla as S}from"./index-CYOuQA7P.js";let y,W=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{y=D(q({name:"TaskTransferForm",__name:"TaskTransferForm",emits:["success"],setup(X,{expose:g,emit:b}){const t=u(!1),i=u(!1),l=u({id:"",assigneeUserId:void 0,reason:""}),k=u({assigneeUserId:[{required:!0,message:"\u65B0\u5BA1\u6279\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u8F6C\u6D3E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=u(),c=u([]);g({open:async o=>{t.value=!0,w(),l.value.id=o,c.value=await H()}});const U=b,h=async()=>{if(n&&await n.value.validate()){i.value=!0;try{await Q(l.value),t.value=!1,U("success")}finally{i.value=!1}}},w=()=>{var o;l.value={id:"",assigneeUserId:void 0,reason:""},(o=n.value)==null||o.resetFields()};return(o,s)=>{const V=K,I=L,f=O,T=P,F=Z,v=A,x=G,C=B;return m(),_(x,{modelValue:a(t),"onUpdate:modelValue":s[3]||(s[3]=e=>z(t)?t.value=e:null),title:"\u8F6C\u6D3E\u4EFB\u52A1",width:"500"},{footer:r(()=>[d(v,{disabled:a(i),type:"primary",onClick:h},{default:r(()=>[p("\u786E \u5B9A")]),_:1},8,["disabled"]),d(v,{onClick:s[2]||(s[2]=e=>t.value=!1)},{default:r(()=>[p("\u53D6 \u6D88")]),_:1})]),default:r(()=>[J((m(),_(F,{ref_key:"formRef",ref:n,model:a(l),rules:a(k),"label-width":"110px"},{default:r(()=>[d(f,{label:"\u65B0\u5BA1\u6279\u4EBA",prop:"assigneeUserId"},{default:r(()=>[d(I,{modelValue:a(l).assigneeUserId,"onUpdate:modelValue":s[0]||(s[0]=e=>a(l).assigneeUserId=e),clearable:"",style:{width:"100%"}},{default:r(()=>[(m(!0),N(R,null,j(a(c),e=>(m(),_(V,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(f,{label:"\u8F6C\u6D3E\u7406\u7531",prop:"reason"},{default:r(()=>[d(T,{modelValue:a(l).reason,"onUpdate:modelValue":s[1]||(s[1]=e=>a(l).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8F6C\u6D3E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,a(i)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/dialog/TaskTransferForm.vue"]])});export{W as __tla,y as default};
