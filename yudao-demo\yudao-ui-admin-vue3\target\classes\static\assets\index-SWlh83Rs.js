import{d as x,b0 as v,aC as b,i as t,r as c,at as I,C as A,o as u,l as F,w as J,g as l,av as i,a as r,c as d,F as O,k as U,t as E,b3 as V,_ as W,__tla as B}from"./index-Daqg4PFz.js";import{e as G,__tla as H}from"./couponTemplate-B4pNZCk_.js";import{e as M,f as K}from"./constants-WoCEnNvc.js";import{f as R,__tla as L}from"./formatTime-BCfRGyrF.js";let S,N=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let y,_,g,C,w,h,k,$,D,P,T;y=x({name:"CouponDiscount",props:{coupon:v()},setup(s){const e=s.coupon;let p=e.discountPercent+"",n=" \u6298";return e.discountType===M.PRICE.type&&(p=b(e.discountPrice),n=" \u5143"),()=>t("div",null,[t("span",{class:"text-20px font-bold"},[p]),t("span",null,[n])])}}),_=x({name:"CouponDiscountDesc",props:{coupon:v()},setup(s){const e=s.coupon,p=e.usePrice>0?`\u6EE1${b(e.usePrice)}\u5143\uFF0C`:"",n=e.discountType===M.PRICE.type?`\u51CF${b(e.discountPrice)}\u5143`:`\u6253${e.discountPercent}\u6298`;return()=>t("div",null,[t("span",null,[p]),t("span",null,[n])])}}),g=x({name:"CouponValidTerm",props:{coupon:v()},setup(s){const e=s.coupon,p=e.validityType===K.DATE.type?`\u6709\u6548\u671F\uFF1A${R(e.validStartTime,"YYYY-MM-DD")} \u81F3 ${R(e.validEndTime,"YYYY-MM-DD")}`:`\u9886\u53D6\u540E\u7B2C ${e.fixedStartTerm} - ${e.fixedEndTerm} \u5929\u5185\u53EF\u7528`;return()=>t("div",null,[p])}}),C={key:0,class:"m-l-16px flex flex-row justify-between p-8px"},w={class:"flex flex-col justify-evenly gap-4px"},h={class:"flex flex-col justify-evenly"},k={key:1,class:"m-l-16px flex flex-row justify-between p-8px"},$={class:"flex flex-col justify-evenly gap-4px"},D={class:"flex flex-col"},P={key:2,class:"flex flex-col items-center justify-around gap-4px p-4px"},T=x({name:"CouponCard",__name:"index",props:{property:{type:Object,required:!0}},setup(s){const e=s,p=c([]);I(()=>e.property.couponIds,async()=>{var o;((o=e.property.couponIds)==null?void 0:o.length)>0&&(p.value=await G(e.property.couponIds))},{immediate:!0,deep:!0});const n=c(375),j=c(),Y=c("100%"),f=c(375);return I(()=>[e.property,n,p.value.length],()=>{f.value=(.95*n.value-e.property.space*(e.property.columns-1))/e.property.columns,Y.value=f.value*p.value.length+e.property.space*(p.value.length-1)+"px"},{immediate:!0,deep:!0}),A(()=>{var o,m;n.value=((m=(o=j.value)==null?void 0:o.wrapRef)==null?void 0:m.offsetWidth)||375}),(o,m)=>{const q=V;return u(),F(q,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:j},{default:J(()=>[l("div",{class:"flex flex-row text-12px",style:i({gap:`${o.property.space}px`,width:r(Y)})},[(u(!0),d(O,null,U(r(p),(a,z)=>(u(),d("div",{class:"box-content",style:i({background:o.property.bgImg?`url(${o.property.bgImg}) 100% center / 100% 100% no-repeat`:"#fff",width:`${r(f)}px`,color:o.property.textColor}),key:z},[o.property.columns===1?(u(),d("div",C,[l("div",w,[t(r(y),{coupon:a},null,8,["coupon"]),t(r(_),{coupon:a},null,8,["coupon"]),t(r(g),{coupon:a},null,8,["coupon"])]),l("div",h,[l("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):o.property.columns===2?(u(),d("div",k,[l("div",$,[t(r(y),{coupon:a},null,8,["coupon"]),l("div",null,E(a.name),1)]),l("div",D,[l("div",{class:"h-full w-20px rounded-20px p-x-2px p-y-8px text-center",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):(u(),d("div",P,[t(r(y),{coupon:a},null,8,["coupon"]),l("div",null,E(a.name),1),l("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)]))],4))),128))],4)]),_:1},512)}}}),S=W(T,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/CouponCard/index.vue"]])});export{N as __tla,S as default};
