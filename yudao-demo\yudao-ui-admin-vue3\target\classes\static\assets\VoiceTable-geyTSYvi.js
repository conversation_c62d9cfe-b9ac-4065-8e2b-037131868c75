import{d as k,T as h,H as p,o as r,l as i,w as t,i as a,a as m,a9 as T,g as v,t as x,j as _,P as C,x as q,N as P,Q as j,R as V,_ as A,__tla as B}from"./index-Daqg4PFz.js";import H,{__tla as I}from"./main-D2WNvJUY.js";import{d as J,__tla as N}from"./formatTime-BCfRGyrF.js";let c,Q=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{c=A(k({__name:"VoiceTable",props:{list:{type:Array,required:!0},loading:{type:Boolean,required:!0}},emits:["delete"],setup(u,{emit:y}){const s=u,n=y;return(R,U)=>{const l=C,o=q,d=P,f=j,g=h("hasPermi"),w=V;return p((r(),i(f,{data:s.list,stripe:"",border:"",style:{"margin-top":"10px"}},{default:t(()=>[a(l,{label:"\u7F16\u53F7",align:"center",prop:"mediaId"}),a(l,{label:"\u6587\u4EF6\u540D",align:"center",prop:"name"}),a(l,{label:"\u8BED\u97F3",align:"center"},{default:t(e=>[e.row.url?(r(),i(m(H),{key:0,url:e.row.url},null,8,["url"])):T("",!0)]),_:1}),a(l,{label:"\u4E0A\u4F20\u65F6\u95F4",align:"center",prop:"createTime",formatter:m(J),width:"180"},{default:t(e=>[v("span",null,x(e.row.createTime),1)]),_:1},8,["formatter"]),a(l,{label:"\u64CD\u4F5C",align:"center","class-name":"small-padding fixed-width"},{default:t(e=>[a(d,{type:"primary",link:"",onClick:b=>n("delete",e.row.id)},{default:t(()=>[a(o,{icon:"ep:download"}),_("\u4E0B\u8F7D ")]),_:2},1032,["onClick"]),p((r(),i(d,{type:"primary",link:"",onClick:b=>n("delete",e.row.id)},{default:t(()=>[a(o,{icon:"ep:delete"}),_("\u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["mp:material:delete"]]])]),_:1})]),_:1},8,["data"])),[[w,s.loading]])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/material/components/VoiceTable.vue"]])});export{Q as __tla,c as default};
