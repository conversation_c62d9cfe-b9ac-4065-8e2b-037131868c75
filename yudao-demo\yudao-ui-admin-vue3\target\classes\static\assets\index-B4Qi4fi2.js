import{bE as t,__tla as r}from"./index-Daqg4PFz.js";let e,g=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{e={getCategoryPage:async a=>await t.get({url:"/bpm/category/page",params:a}),getCategorySimpleList:async()=>await t.get({url:"/bpm/category/simple-list"}),getCategory:async a=>await t.get({url:"/bpm/category/get?id="+a}),createCategory:async a=>await t.post({url:"/bpm/category/create",data:a}),updateCategory:async a=>await t.put({url:"/bpm/category/update",data:a}),deleteCategory:async a=>await t.delete({url:"/bpm/category/delete?id="+a})}});export{e as C,g as __tla};
