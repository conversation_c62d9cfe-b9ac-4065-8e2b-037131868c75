import{d as $,u as W,I as X,n as ee,r as f,f as ae,bd as le,C as te,T as re,o as n,c as w,i as a,w as t,a as l,U as E,F as b,k as C,l as i,V as se,G as z,j as u,H as g,t as A,g as ne,dz as oe,Z as ie,L as pe,J as ue,K as ce,M as de,x as me,N as _e,O as fe,P as ye,Q as he,R as we,_ as be,__tla as ge}from"./index-Daqg4PFz.js";import{_ as ve,__tla as ke}from"./index-BBLwwrga.js";import{_ as xe,__tla as Ce}from"./DictTag-BDZzHcIz.js";import{_ as Se,__tla as Ie}from"./ContentWrap-DZg14iby.js";import{_ as Ve,__tla as Te}from"./index-CmwFi8Xl.js";import{d as H,a as Pe,__tla as Ue}from"./formatTime-BCfRGyrF.js";import{b as Me,c as Ne,__tla as De}from"./index-Wcjc3rZh.js";import{C as Be,__tla as qe}from"./index-B4Qi4fi2.js";import{__tla as Ee}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as ze}from"./el-card-Dvjjuipo.js";let O,Ae=Promise.all([(()=>{try{return ge}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return ze}catch{}})()]).then(async()=>{O=be($({name:"BpmProcessInstanceMy",__name:"index",setup(He){const S=W(),R=X(),{t:I}=ee(),v=f(!0),V=f(0),T=f([]),r=ae({pageNo:1,pageSize:10,name:"",processDefinitionId:void 0,category:void 0,status:void 0,createTime:[]}),P=f(),U=f([]),d=async()=>{v.value=!0;try{const m=await Me(r);T.value=m.list,V.value=m.total}finally{v.value=!1}},y=()=>{r.pageNo=1,d()},Y=()=>{P.value.resetFields(),y()},M=m=>{S.push({name:"BpmProcessInstanceCreate",query:{processInstanceId:m}})};return le(()=>{d()}),te(async()=>{await d(),U.value=await Be.getCategorySimpleList()}),(m,s)=>{const F=Ve,N=ie,c=pe,D=ue,B=ce,J=de,k=me,p=_e,K=fe,q=Se,o=ye,L=xe,j=he,G=ve,x=re("hasPermi"),Q=we;return n(),w(b,null,[a(F,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(q,null,{default:t(()=>[a(K,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:P,inline:!0,"label-width":"68px"},{default:t(()=>[a(c,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(N,{modelValue:l(r).name,"onUpdate:modelValue":s[0]||(s[0]=e=>l(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:E(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u6240\u5C5E\u6D41\u7A0B",prop:"processDefinitionId"},{default:t(()=>[a(N,{modelValue:l(r).processDefinitionId,"onUpdate:modelValue":s[1]||(s[1]=e=>l(r).processDefinitionId=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u5B9A\u4E49\u7684\u7F16\u53F7",clearable:"",onKeyup:E(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[a(B,{modelValue:l(r).category,"onUpdate:modelValue":s[2]||(s[2]=e=>l(r).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),w(b,null,C(l(U),e=>(n(),i(D,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status"},{default:t(()=>[a(B,{modelValue:l(r).status,"onUpdate:modelValue":s[3]||(s[3]=e=>l(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),w(b,null,C(l(se)(l(z).BPM_PROCESS_INSTANCE_STATUS),e=>(n(),i(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(J,{modelValue:l(r).createTime,"onUpdate:modelValue":s[4]||(s[4]=e=>l(r).createTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(c,null,{default:t(()=>[a(p,{onClick:y},{default:t(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),u(" \u641C\u7D22")]),_:1}),a(p,{onClick:Y},{default:t(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),u(" \u91CD\u7F6E")]),_:1}),g((n(),i(p,{type:"primary",plain:"",onClick:s[5]||(s[5]=e=>M())},{default:t(()=>[a(k,{icon:"ep:plus",class:"mr-5px"}),u(" \u53D1\u8D77\u6D41\u7A0B ")]),_:1})),[[x,["bpm:process-instance:query"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(q,null,{default:t(()=>[g((n(),i(j,{data:l(T)},{default:t(()=>[a(o,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name","min-width":"200px",fixed:"left"}),a(o,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName","min-width":"100",fixed:"left"}),a(o,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status",width:"120"},{default:t(e=>[a(L,{type:l(z).BPM_PROCESS_INSTANCE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(o,{label:"\u53D1\u8D77\u65F6\u95F4",align:"center",prop:"startTime",width:"180",formatter:l(H)},null,8,["formatter"]),a(o,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:l(H)},null,8,["formatter"]),a(o,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:t(e=>[u(A(e.row.durationInMillis>0?l(Pe)(e.row.durationInMillis):"-"),1)]),_:1}),a(o,{label:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",prop:"tasks","min-width":"120px"},{default:t(e=>[(n(!0),w(b,null,C(e.row.tasks,_=>(n(),i(p,{type:"primary",key:_.id,link:""},{default:t(()=>[ne("span",null,A(_.name),1)]),_:2},1024))),128))]),_:1}),a(o,{label:"\u6D41\u7A0B\u7F16\u53F7",align:"center",prop:"id","min-width":"320px"}),a(o,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"180"},{default:t(e=>[g((n(),i(p,{link:"",type:"primary",onClick:_=>{return h=e.row,void S.push({name:"BpmProcessInstanceDetail",query:{id:h.id}});var h}},{default:t(()=>[u(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[x,["bpm:process-instance:cancel"]]]),e.row.status===1?g((n(),i(p,{key:0,link:"",type:"primary",onClick:_=>(async h=>{const{value:Z}=await oe.prompt("\u8BF7\u8F93\u5165\u53D6\u6D88\u539F\u56E0","\u53D6\u6D88\u6D41\u7A0B",{confirmButtonText:I("common.ok"),cancelButtonText:I("common.cancel"),inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});await Ne(h.id,Z),R.success("\u53D6\u6D88\u6210\u529F"),await d()})(e.row)},{default:t(()=>[u(" \u53D6\u6D88 ")]),_:2},1032,["onClick"])),[[x,["bpm:process-instance:query"]]]):(n(),i(p,{key:1,link:"",type:"primary",onClick:_=>M(e.row.id)},{default:t(()=>[u(" \u91CD\u65B0\u53D1\u8D77 ")]),_:2},1032,["onClick"]))]),_:1})]),_:1},8,["data"])),[[Q,l(v)]]),a(G,{total:l(V),page:l(r).pageNo,"onUpdate:page":s[6]||(s[6]=e=>l(r).pageNo=e),limit:l(r).pageSize,"onUpdate:limit":s[7]||(s[7]=e=>l(r).pageSize=e),onPagination:d},null,8,["total","page","limit"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/index.vue"]])});export{Ae as __tla,O as default};
