import{bE as a,__tla as r}from"./index-Daqg4PFz.js";let e,u=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{e={getProductUnitPage:async t=>await a.get({url:"/erp/product-unit/page",params:t}),getProductUnitSimpleList:async()=>await a.get({url:"/erp/product-unit/simple-list"}),getProductUnit:async t=>await a.get({url:"/erp/product-unit/get?id="+t}),createProductUnit:async t=>await a.post({url:"/erp/product-unit/create",data:t}),updateProductUnit:async t=>await a.put({url:"/erp/product-unit/update",data:t}),deleteProductUnit:async t=>await a.delete({url:"/erp/product-unit/delete?id="+t}),exportProductUnit:async t=>await a.download({url:"/erp/product-unit/export-excel",params:t})}});export{e as P,u as __tla};
