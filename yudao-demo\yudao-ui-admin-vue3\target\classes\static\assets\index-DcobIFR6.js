import{d as de,I as ne,n as se,r as c,f as ie,C as ce,ay as pe,T as _e,o as u,c as b,i as a,w as r,a as l,U as R,F as h,k,l as d,V as me,G,j as p,H as f,e5 as fe,dV as O,t as ye,dX as be,Z as we,L as he,J as ve,K as ge,M as ke,x as Ve,N as xe,O as Se,P as Ce,ax as Ie,Q as Ue,R as Pe,_ as Ne,__tla as Te}from"./index-Daqg4PFz.js";import{_ as Re,__tla as Ae}from"./index-BBLwwrga.js";import{_ as De,__tla as Fe}from"./DictTag-BDZzHcIz.js";import{_ as Le,__tla as ze}from"./ContentWrap-DZg14iby.js";import{_ as He,__tla as Ke}from"./index-CmwFi8Xl.js";import{b as Ye,__tla as Ee}from"./formatTime-BCfRGyrF.js";import{d as Je}from"./download--D_IyRio.js";import{S as I,__tla as Me}from"./index-Bz4zEODo.js";import Ze,{__tla as qe}from"./SaleReturnForm-BvFlcQta.js";import{P as Ge,__tla as Oe}from"./index-BdaXniMm.js";import{C as Qe,__tla as We}from"./index-CzfV9k_X.js";import{W as je,__tla as Xe}from"./index-b9NHryvG.js";import{A as $e,__tla as Be}from"./index-AFe43Qgi.js";import{__tla as ea}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as aa}from"./el-card-Dvjjuipo.js";import{__tla as la}from"./Dialog-BjBBVYCI.js";import{__tla as ta}from"./SaleReturnItemForm-DQechds7.js";import{__tla as ra}from"./index-BUJ03bwx.js";import{__tla as oa}from"./SaleOrderReturnEnableList-CUxjJtaz.js";import{__tla as ua}from"./index-CJBNxTRD.js";let Q,da=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return Xe}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})()]).then(async()=>{let A;A={key:0},Q=Ne(de({name:"ErpSaleReturn",__name:"index",setup(na){const V=ne(),{t:W}=se(),U=c(!0),D=c([]),F=c(0),o=ie({pageNo:1,pageSize:10,no:void 0,customerId:void 0,productId:void 0,warehouseId:void 0,returnTime:[],orderNo:void 0,accountId:void 0,status:void 0,remark:void 0,creator:void 0,refundStatus:void 0}),L=c(),P=c(!1),z=c([]),H=c([]),K=c([]),Y=c([]),E=c([]),v=async()=>{U.value=!0;try{const n=await I.getSaleReturnPage(o);D.value=n.list,F.value=n.total}finally{U.value=!1}},x=()=>{o.pageNo=1,v()},j=()=>{L.value.resetFields(),x()},J=c(),N=(n,t)=>{J.value.open(n,t)},M=async n=>{try{await V.delConfirm(),await I.deleteSaleReturn(n),V.success(W("common.delSuccess")),await v(),S.value=S.value.filter(t=>!n.includes(t.id))}catch{}},Z=async(n,t)=>{try{await V.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u9000\u8D27\u5417\uFF1F`),await I.updateSaleReturnStatus(n,t),V.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},X=async()=>{try{await V.exportConfirm(),P.value=!0;const n=await I.exportSaleReturn(o);Je.excel(n,"\u9500\u552E\u9000\u8D27.xls")}catch{}finally{P.value=!1}},S=c([]),$=n=>{S.value=n};return ce(async()=>{await v(),z.value=await Ge.getProductSimpleList(),H.value=await Qe.getCustomerSimpleList(),K.value=await pe(),Y.value=await je.getWarehouseSimpleList(),E.value=await $e.getAccountSimpleList()}),(n,t)=>{const B=He,T=we,s=he,m=ve,w=ge,ee=ke,C=Ve,_=xe,ae=Se,q=Le,i=Ce,le=Ie,te=De,re=Ue,oe=Re,y=_e("hasPermi"),ue=Pe;return u(),b(h,null,[a(B,{title:"\u3010\u9500\u552E\u3011\u9500\u552E\u8BA2\u5355\u3001\u51FA\u5E93\u3001\u9000\u8D27",url:"https://doc.iocoder.cn/erp/sale/"}),a(q,null,{default:r(()=>[a(ae,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:L,inline:!0,"label-width":"68px"},{default:r(()=>[a(s,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:r(()=>[a(T,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u9000\u8D27\u5355\u53F7",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(w,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(z),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u9000\u8D27\u65F6\u95F4",prop:"outTime"},{default:r(()=>[a(ee,{modelValue:l(o).outTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).outTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),a(s,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[a(w,{modelValue:l(o).customerId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).customerId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5BA2\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(H),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(w,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(Y),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(w,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(K),e=>(u(),d(m,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[a(T,{modelValue:l(o).orderNo,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).orderNo=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u8054\u8BA2\u5355",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[a(w,{modelValue:l(o).accountId,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).accountId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(E),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u9000\u6B3E\u72B6\u6001",prop:"refundStatus"},{default:r(()=>[a(w,{modelValue:l(o).refundStatus,"onUpdate:modelValue":t[8]||(t[8]=e=>l(o).refundStatus=e),placeholder:"\u8BF7\u9009\u62E9\u9000\u6B3E\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[a(m,{label:"\u672A\u9000\u6B3E",value:"0"}),a(m,{label:"\u90E8\u5206\u9000\u6B3E",value:"1"}),a(m,{label:"\u5168\u90E8\u9000\u6B3E",value:"2"})]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5BA1\u6838\u72B6\u6001",prop:"status"},{default:r(()=>[a(w,{modelValue:l(o).status,"onUpdate:modelValue":t[9]||(t[9]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6838\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),b(h,null,k(l(me)(l(G).ERP_AUDIT_STATUS),e=>(u(),d(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(T,{modelValue:l(o).remark,"onUpdate:modelValue":t[10]||(t[10]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,null,{default:r(()=>[a(_,{onClick:x},{default:r(()=>[a(C,{icon:"ep:search",class:"mr-5px"}),p(" \u641C\u7D22")]),_:1}),a(_,{onClick:j},{default:r(()=>[a(C,{icon:"ep:refresh",class:"mr-5px"}),p(" \u91CD\u7F6E")]),_:1}),f((u(),d(_,{type:"primary",plain:"",onClick:t[11]||(t[11]=e=>N("create"))},{default:r(()=>[a(C,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u589E ")]),_:1})),[[y,["erp:sale-return:create"]]]),f((u(),d(_,{type:"success",plain:"",onClick:X,loading:l(P)},{default:r(()=>[a(C,{icon:"ep:download",class:"mr-5px"}),p(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:sale-return:export"]]]),f((u(),d(_,{type:"danger",plain:"",onClick:t[12]||(t[12]=e=>M(l(S).map(g=>g.id))),disabled:l(S).length===0},{default:r(()=>[a(C,{icon:"ep:delete",class:"mr-5px"}),p(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[y,["erp:sale-return:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(q,null,{default:r(()=>[f((u(),d(re,{data:l(D),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:$},{default:r(()=>[a(i,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(i,{"min-width":"180",label:"\u9000\u8D27\u5355\u53F7",align:"center",prop:"no"}),a(i,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(i,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),a(i,{label:"\u9000\u8D27\u65F6\u95F4",align:"center",prop:"returnTime",formatter:l(Ye),width:"120px"},null,8,["formatter"]),a(i,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(i,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(fe)},null,8,["formatter"]),a(i,{label:"\u5E94\u9000\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(O)},null,8,["formatter"]),a(i,{label:"\u5DF2\u9000\u91D1\u989D",align:"center",prop:"refundPrice",formatter:l(O)},null,8,["formatter"]),a(i,{label:"\u672A\u9000\u91D1\u989D",align:"center"},{default:r(e=>[e.row.refundPrice===e.row.totalPrice?(u(),b("span",A,"0")):(u(),d(le,{key:1,type:"danger"},{default:r(()=>[p(ye(l(be)(e.row.totalPrice-e.row.refundPrice)),1)]),_:2},1024))]),_:1}),a(i,{label:"\u5BA1\u6838\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(te,{type:l(G).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(i,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[f((u(),d(_,{link:"",onClick:g=>N("detail",e.row.id)},{default:r(()=>[p(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:query"]]]),f((u(),d(_,{link:"",type:"primary",onClick:g=>N("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[y,["erp:sale-return:update"]]]),e.row.status===10?f((u(),d(_,{key:0,link:"",type:"primary",onClick:g=>Z(e.row.id,20)},{default:r(()=>[p(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:update-status"]]]):f((u(),d(_,{key:1,link:"",type:"danger",onClick:g=>Z(e.row.id,10)},{default:r(()=>[p(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:update-status"]]]),f((u(),d(_,{link:"",type:"danger",onClick:g=>M([e.row.id])},{default:r(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:delete"]]])]),_:1})]),_:1},8,["data"])),[[ue,l(U)]]),a(oe,{total:l(F),page:l(o).pageNo,"onUpdate:page":t[13]||(t[13]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[14]||(t[14]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(Ze,{ref_key:"formRef",ref:J,onSuccess:v},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/sale/return/index.vue"]])});export{da as __tla,Q as default};
