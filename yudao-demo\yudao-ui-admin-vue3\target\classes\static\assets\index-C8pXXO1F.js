import{d as F,f as A,e as H,r as o,at as T,C as j,o as B,c as J,i as a,w as e,a as t,j as S,z as L,F as N,M as O,L as G,x as K,N as Q,O as W,A as X,B as Z,E as $,_ as aa,__tla as ea}from"./index-Daqg4PFz.js";import{_ as ta,__tla as ra}from"./ContentWrap-DZg14iby.js";import{E as la,__tla as oa}from"./el-tree-select-BKcJcOKx.js";import sa,{__tla as ua}from"./ContractPriceRank-C7sGfvyM.js";import na,{__tla as _a}from"./ReceivablePriceRank-BQTefdjo.js";import ca,{__tla as ma}from"./ContractCountRank-Bu2-YyJ7.js";import fa,{__tla as da}from"./ProductSalesRank-B4WjHsMc.js";import pa,{__tla as ia}from"./CustomerCountRank-lI5OJ71m.js";import ya,{__tla as ka}from"./ContactCountRank-HjpwdvgM.js";import Ra,{__tla as ha}from"./FollowCountRank-BFGS6cFA.js";import ba,{__tla as Ca}from"./FollowCustomerCountRank-DIddS6C0.js";import{h as va,d as wa}from"./tree-BMqZf9_I.js";import{g as qa,__tla as Da}from"./index-D-Abj-9W.js";import{f as Y,c as xa,e as za,g as Va,__tla as Pa}from"./formatTime-BCfRGyrF.js";import{__tla as ga}from"./el-card-Dvjjuipo.js";import{__tla as Ua}from"./el-skeleton-item-BmS2F7Yy.js";import{__tla as Ia}from"./Echart-C33-KcLZ.js";import{__tla as Sa}from"./rank-TDBVYYEQ.js";let M,Ya=Promise.all([(()=>{try{return ea}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Sa}catch{}})()]).then(async()=>{M=aa(F({name:"CrmStatisticsRank",__name:"index",setup(Ma){const r=A({deptId:H().getUser.deptId,times:[Y(xa(new Date(new Date().getTime()-6048e5))),Y(za(new Date(new Date().getTime()-864e5)))]}),C=o(),v=o([]),p=o("contractPriceRank"),w=o(),q=o(),D=o(),x=o(),z=o(),V=o(),P=o(),g=o(),b=()=>{var i,u,_,c,m,y,n,k,f,l,d,R,s,U,h,I;switch(p.value){case"contractPriceRank":(u=(i=w.value)==null?void 0:i.loadData)==null||u.call(i);break;case"receivablePriceRank":(c=(_=q.value)==null?void 0:_.loadData)==null||c.call(_);break;case"contractCountRank":(y=(m=D.value)==null?void 0:m.loadData)==null||y.call(m);break;case"productSalesRank":(k=(n=x.value)==null?void 0:n.loadData)==null||k.call(n);break;case"customerCountRank":(l=(f=z.value)==null?void 0:f.loadData)==null||l.call(f);break;case"contactCountRank":(R=(d=V.value)==null?void 0:d.loadData)==null||R.call(d);break;case"followCountRank":(U=(s=P.value)==null?void 0:s.loadData)==null||U.call(s);break;case"followCustomerCountRank":(I=(h=g.value)==null?void 0:h.loadData)==null||I.call(h)}};T(p,()=>{b()});const E=()=>{C.value.resetFields(),b()};return j(async()=>{v.value=va(await qa())}),(i,u)=>{const _=O,c=G,m=la,y=K,n=Q,k=W,f=ta,l=X,d=Z,R=$;return B(),J(N,null,[a(f,null,{default:e(()=>[a(k,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:C,inline:!0,"label-width":"68px"},{default:e(()=>[a(c,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:e(()=>[a(_,{modelValue:t(r).times,"onUpdate:modelValue":u[0]||(u[0]=s=>t(r).times=s),shortcuts:t(Va),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")]},null,8,["modelValue","shortcuts","default-time"])]),_:1}),a(c,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:e(()=>[a(m,{modelValue:t(r).deptId,"onUpdate:modelValue":u[1]||(u[1]=s=>t(r).deptId=s),data:t(v),props:t(wa),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",class:"!w-240px"},null,8,["modelValue","data","props"])]),_:1}),a(c,null,{default:e(()=>[a(n,{onClick:b},{default:e(()=>[a(y,{icon:"ep:search",class:"mr-5px"}),S(" \u641C\u7D22")]),_:1}),a(n,{onClick:E},{default:e(()=>[a(y,{icon:"ep:refresh",class:"mr-5px"}),S(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(R,null,{default:e(()=>[a(d,{modelValue:t(p),"onUpdate:modelValue":u[2]||(u[2]=s=>L(p)?p.value=s:null)},{default:e(()=>[a(l,{label:"\u5408\u540C\u91D1\u989D\u6392\u884C",name:"contractPriceRank",lazy:""},{default:e(()=>[a(sa,{"query-params":t(r),ref_key:"contractPriceRankRef",ref:w},null,8,["query-params"])]),_:1}),a(l,{label:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C",name:"receivablePriceRank",lazy:""},{default:e(()=>[a(na,{"query-params":t(r),ref_key:"receivablePriceRankRef",ref:q},null,8,["query-params"])]),_:1}),a(l,{label:"\u7B7E\u7EA6\u5408\u540C\u6392\u884C",name:"contractCountRank",lazy:""},{default:e(()=>[a(ca,{"query-params":t(r),ref_key:"contractCountRankRef",ref:D},null,8,["query-params"])]),_:1}),a(l,{label:"\u4EA7\u54C1\u9500\u91CF\u6392\u884C",name:"productSalesRank",lazy:""},{default:e(()=>[a(fa,{"query-params":t(r),ref_key:"productSalesRankRef",ref:x},null,8,["query-params"])]),_:1}),a(l,{label:"\u65B0\u589E\u5BA2\u6237\u6570\u6392\u884C",name:"customerCountRank",lazy:""},{default:e(()=>[a(pa,{"query-params":t(r),ref_key:"customerCountRankRef",ref:z},null,8,["query-params"])]),_:1}),a(l,{label:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\u6392\u884C",name:"contactCountRank",lazy:""},{default:e(()=>[a(ya,{"query-params":t(r),ref_key:"contactCountRankRef",ref:V},null,8,["query-params"])]),_:1}),a(l,{label:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C",name:"followCountRank",lazy:""},{default:e(()=>[a(Ra,{"query-params":t(r),ref_key:"followCountRankRef",ref:P},null,8,["query-params"])]),_:1}),a(l,{label:"\u8DDF\u8FDB\u5BA2\u6237\u6570\u6392\u884C",name:"followCustomerCountRank",lazy:""},{default:e(()=>[a(ba,{"query-params":t(r),ref_key:"followCustomerCountRankRef",ref:g},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/statistics/rank/index.vue"]])});export{Ya as __tla,M as default};
