import{bE as R,__tla as l}from"./index-Daqg4PFz.js";let s,e,t,E,r,_,i,M=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{s=(a=>(a[a.CRM_CLUE=1]="CRM_CLUE",a[a.CRM_CUSTOMER=2]="CRM_CUSTOMER",a[a.CRM_CONTACT=3]="CRM_CONTACT",a[a.CRM_BUSINESS=4]="CRM_BUSINESS",a[a.CRM_CONTRACT=5]="CRM_CONTRACT",a[a.CRM_PRODUCT=6]="CRM_PRODUCT",a[a.CRM_RECEIVABLE=7]="CRM_RECEIVABLE",a[a.CRM_RECEIVABLE_PLAN=8]="CRM_RECEIVABLE_PLAN",a))(s||{}),e=(a=>(a[a.OWNER=1]="OWNER",a[a.READ=2]="READ",a[a.WRITE=3]="WRITE",a))(e||{}),_=async a=>await R.get({url:"/crm/permission/list",params:a}),E=async a=>await R.post({url:"/crm/permission/create",data:a}),i=async a=>await R.put({url:"/crm/permission/update",data:a}),r=async a=>await R.delete({url:"/crm/permission/delete?ids="+a.join(",")}),t=async a=>await R.delete({url:"/crm/permission/delete-self?id="+a})});export{s as B,e as P,M as __tla,t as a,E as c,r as d,_ as g,i as u};
