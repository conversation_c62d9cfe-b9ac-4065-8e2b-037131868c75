{"version": 3, "sources": ["../../web-storage-cache/dist/web-storage-cache.min.js"], "sourcesContent": ["/*!\n    web-storage-cache -- Added `expires` attribute and serialize data with `JSON.parse` for the localStorage and sessionStorage.\n    Version 1.1.1\n    https://github.com/WQTeam/web-storage-cache\n    (c) 2013-2016 WQTeam, MIT license\n*/\n!function(a,b){\"function\"==typeof define&&define.amd?define(b):\"object\"==typeof exports?module.exports=b():a.WebStorageCache=b()}(this,function(){\"use strict\";function a(a,b){for(var c in b)a[c]=b[c];return a}function b(a){var b=!1;if(a&&a.setItem){b=!0;var c=\"__\"+Math.round(1e7*Math.random());try{a.setItem(c,c),a.removeItem(c)}catch(d){b=!1}}return b}function c(a){var b=typeof a;return\"string\"===b&&window[a]instanceof Storage?window[a]:a}function d(a){return\"[object Date]\"===Object.prototype.toString.call(a)&&!isNaN(a.getTime())}function e(a,b){if(b=b||new Date,\"number\"==typeof a?a=a===1/0?l:new Date(b.getTime()+1e3*a):\"string\"==typeof a&&(a=new Date(a)),a&&!d(a))throw new Error(\"`expires` parameter cannot be converted to a valid Date instance\");return a}function f(a){var b=!1;if(a)if(a.code)switch(a.code){case 22:b=!0;break;case 1014:\"NS_ERROR_DOM_QUOTA_REACHED\"===a.name&&(b=!0)}else-**********===a.number&&(b=!0);return b}function g(a,b){this.c=(new Date).getTime(),b=b||m;var c=e(b);this.e=c.getTime(),this.v=a}function h(a){return\"object\"!=typeof a?!1:a&&\"c\"in a&&\"e\"in a&&\"v\"in a?!0:!1}function i(a){var b=(new Date).getTime();return b<a.e}function j(a){return\"string\"!=typeof a&&(console.warn(a+\" used as a key, but it is not a string.\"),a=String(a)),a}function k(e){var f={storage:\"localStorage\",exp:1/0},g=a(f,e),h=g.exp;if(h&&\"number\"!=typeof h&&!d(h))throw new Error(\"Constructor `exp` parameter cannot be converted to a valid Date instance\");m=h;var i=c(g.storage),j=b(i);this.isSupported=function(){return j},j?(this.storage=i,this.quotaExceedHandler=function(a,b,c){if(console.warn(\"Quota exceeded!\"),c&&c.force===!0){var d=this.deleteAllExpires();console.warn(\"delete all expires CacheItem : [\"+d+\"] and try execute `set` method again!\");try{c.force=!1,this.set(a,b,c)}catch(e){console.warn(e)}}}):a(this,o)}var l=new Date(\"Fri, 31 Dec 9999 23:59:59 UTC\"),m=l,n={serialize:function(a){return JSON.stringify(a)},deserialize:function(a){return a&&JSON.parse(a)}},o={set:function(){},get:function(){},\"delete\":function(){},deleteAllExpires:function(){},clear:function(){},add:function(){},replace:function(){},touch:function(){}},p={set:function(b,c,d){if(b=j(b),\"number\"==typeof d&&(d={exp:d}),d=a({force:!0},d),void 0===c)return this[\"delete\"](b);var e=n.serialize(c),h=new g(e,d.exp);try{this.storage.setItem(b,n.serialize(h))}catch(i){f(i)?this.quotaExceedHandler(b,e,d,i):console.error(i)}return c},get:function(a){a=j(a);var b=null;try{b=n.deserialize(this.storage.getItem(a))}catch(c){return null}if(h(b)){if(i(b)){var d=b.v;return n.deserialize(d)}this[\"delete\"](a)}return null},\"delete\":function(a){return a=j(a),this.storage.removeItem(a),a},deleteAllExpires:function(){for(var a=this.storage.length,b=[],c=this,d=0;a>d;d++){var e=this.storage.key(d),f=null;try{f=n.deserialize(this.storage.getItem(e))}catch(g){}if(null!==f&&void 0!==f.e){var h=(new Date).getTime();h>=f.e&&b.push(e)}}return b.forEach(function(a){c[\"delete\"](a)}),b},clear:function(){this.storage.clear()},add:function(b,c,d){b=j(b),\"number\"==typeof d&&(d={exp:d}),d=a({force:!0},d);try{var e=n.deserialize(this.storage.getItem(b));if(!h(e)||!i(e))return this.set(b,c,d),!0}catch(f){return this.set(b,c,d),!0}return!1},replace:function(a,b,c){a=j(a);var d=null;try{d=n.deserialize(this.storage.getItem(a))}catch(e){return!1}if(h(d)){if(i(d))return this.set(a,b,c),!0;this[\"delete\"](a)}return!1},touch:function(a,b){a=j(a);var c=null;try{c=n.deserialize(this.storage.getItem(a))}catch(d){return!1}if(h(c)){if(i(c))return this.set(a,this.get(a),{exp:b}),!0;this[\"delete\"](a)}return!1}};return k.prototype=p,k});"], "mappings": ";;;;;AAAA;AAAA;AAMA,KAAC,SAAS,GAAE,GAAE;AAAC,oBAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,IAAE,YAAU,OAAO,UAAQ,OAAO,UAAQ,EAAE,IAAE,EAAE,kBAAgB,EAAE;AAAA,IAAC,EAAE,SAAK,WAAU;AAAC;AAAa,eAAS,EAAEA,IAAEC,IAAE;AAAC,iBAAQC,MAAKD;AAAE,UAAAD,GAAEE,EAAC,IAAED,GAAEC,EAAC;AAAE,eAAOF;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,YAAIC,KAAE;AAAG,YAAGD,MAAGA,GAAE,SAAQ;AAAC,UAAAC,KAAE;AAAG,cAAIC,KAAE,OAAK,KAAK,MAAM,MAAI,KAAK,OAAO,CAAC;AAAE,cAAG;AAAC,YAAAF,GAAE,QAAQE,IAAEA,EAAC,GAAEF,GAAE,WAAWE,EAAC;AAAA,UAAC,SAAOC,IAAE;AAAC,YAAAF,KAAE;AAAA,UAAE;AAAA,QAAC;AAAC,eAAOA;AAAA,MAAC;AAAC,eAAS,EAAED,IAAE;AAAC,YAAIC,KAAE,OAAOD;AAAE,eAAM,aAAWC,MAAG,OAAOD,EAAC,aAAY,UAAQ,OAAOA,EAAC,IAAEA;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAM,oBAAkB,OAAO,UAAU,SAAS,KAAKA,EAAC,KAAG,CAAC,MAAMA,GAAE,QAAQ,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEC,IAAE;AAAC,YAAGA,KAAEA,MAAG,oBAAI,QAAK,YAAU,OAAOD,KAAEA,KAAEA,OAAI,IAAE,IAAE,IAAE,IAAI,KAAKC,GAAE,QAAQ,IAAE,MAAID,EAAC,IAAE,YAAU,OAAOA,OAAIA,KAAE,IAAI,KAAKA,EAAC,IAAGA,MAAG,CAAC,EAAEA,EAAC;AAAE,gBAAM,IAAI,MAAM,kEAAkE;AAAE,eAAOA;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,YAAIC,KAAE;AAAG,YAAGD;AAAE,cAAGA,GAAE;AAAK,oBAAOA,GAAE,MAAK;AAAA,cAAC,KAAK;AAAG,gBAAAC,KAAE;AAAG;AAAA,cAAM,KAAK;AAAK,iDAA+BD,GAAE,SAAOC,KAAE;AAAA,YAAG;AAAA;AAAK,4BAAcD,GAAE,WAASC,KAAE;AAAI,eAAOA;AAAA,MAAC;AAAC,eAAS,EAAED,IAAEC,IAAE;AAAC,aAAK,KAAG,oBAAI,QAAM,QAAQ,GAAEA,KAAEA,MAAG;AAAE,YAAIC,KAAE,EAAED,EAAC;AAAE,aAAK,IAAEC,GAAE,QAAQ,GAAE,KAAK,IAAEF;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAM,YAAU,OAAOA,KAAE,QAAGA,MAAG,OAAMA,MAAG,OAAMA,MAAG,OAAMA,KAAE,OAAG;AAAA,MAAE;AAAC,eAAS,EAAEA,IAAE;AAAC,YAAIC,MAAG,oBAAI,QAAM,QAAQ;AAAE,eAAOA,KAAED,GAAE;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAM,YAAU,OAAOA,OAAI,QAAQ,KAAKA,KAAE,yCAAyC,GAAEA,KAAE,OAAOA,EAAC,IAAGA;AAAA,MAAC;AAAC,eAAS,EAAEI,IAAE;AAAC,YAAIC,KAAE,EAAC,SAAQ,gBAAe,KAAI,IAAE,EAAC,GAAEC,KAAE,EAAED,IAAED,EAAC,GAAEG,KAAED,GAAE;AAAI,YAAGC,MAAG,YAAU,OAAOA,MAAG,CAAC,EAAEA,EAAC;AAAE,gBAAM,IAAI,MAAM,0EAA0E;AAAE,YAAEA;AAAE,YAAIC,KAAE,EAAEF,GAAE,OAAO,GAAEG,KAAE,EAAED,EAAC;AAAE,aAAK,cAAY,WAAU;AAAC,iBAAOC;AAAA,QAAC,GAAEA,MAAG,KAAK,UAAQD,IAAE,KAAK,qBAAmB,SAASR,IAAEC,IAAEC,IAAE;AAAC,cAAG,QAAQ,KAAK,iBAAiB,GAAEA,MAAGA,GAAE,UAAQ,MAAG;AAAC,gBAAIC,KAAE,KAAK,iBAAiB;AAAE,oBAAQ,KAAK,qCAAmCA,KAAE,uCAAuC;AAAE,gBAAG;AAAC,cAAAD,GAAE,QAAM,OAAG,KAAK,IAAIF,IAAEC,IAAEC,EAAC;AAAA,YAAC,SAAOE,IAAE;AAAC,sBAAQ,KAAKA,EAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC,KAAG,EAAE,MAAK,CAAC;AAAA,MAAC;AAAC,UAAI,IAAE,oBAAI,KAAK,+BAA+B,GAAE,IAAE,GAAE,IAAE,EAAC,WAAU,SAASJ,IAAE;AAAC,eAAO,KAAK,UAAUA,EAAC;AAAA,MAAC,GAAE,aAAY,SAASA,IAAE;AAAC,eAAOA,MAAG,KAAK,MAAMA,EAAC;AAAA,MAAC,EAAC,GAAE,IAAE,EAAC,KAAI,WAAU;AAAA,MAAC,GAAE,KAAI,WAAU;AAAA,MAAC,GAAE,UAAS,WAAU;AAAA,MAAC,GAAE,kBAAiB,WAAU;AAAA,MAAC,GAAE,OAAM,WAAU;AAAA,MAAC,GAAE,KAAI,WAAU;AAAA,MAAC,GAAE,SAAQ,WAAU;AAAA,MAAC,GAAE,OAAM,WAAU;AAAA,MAAC,EAAC,GAAE,IAAE,EAAC,KAAI,SAASC,IAAEC,IAAEC,IAAE;AAAC,YAAGF,KAAE,EAAEA,EAAC,GAAE,YAAU,OAAOE,OAAIA,KAAE,EAAC,KAAIA,GAAC,IAAGA,KAAE,EAAE,EAAC,OAAM,KAAE,GAAEA,EAAC,GAAE,WAASD;AAAE,iBAAO,KAAK,QAAQ,EAAED,EAAC;AAAE,YAAIG,KAAE,EAAE,UAAUF,EAAC,GAAEK,KAAE,IAAI,EAAEH,IAAED,GAAE,GAAG;AAAE,YAAG;AAAC,eAAK,QAAQ,QAAQF,IAAE,EAAE,UAAUM,EAAC,CAAC;AAAA,QAAC,SAAOC,IAAE;AAAC,YAAEA,EAAC,IAAE,KAAK,mBAAmBP,IAAEG,IAAED,IAAEK,EAAC,IAAE,QAAQ,MAAMA,EAAC;AAAA,QAAC;AAAC,eAAON;AAAA,MAAC,GAAE,KAAI,SAASF,IAAE;AAAC,QAAAA,KAAE,EAAEA,EAAC;AAAE,YAAIC,KAAE;AAAK,YAAG;AAAC,UAAAA,KAAE,EAAE,YAAY,KAAK,QAAQ,QAAQD,EAAC,CAAC;AAAA,QAAC,SAAOE,IAAE;AAAC,iBAAO;AAAA,QAAI;AAAC,YAAG,EAAED,EAAC,GAAE;AAAC,cAAG,EAAEA,EAAC,GAAE;AAAC,gBAAIE,KAAEF,GAAE;AAAE,mBAAO,EAAE,YAAYE,EAAC;AAAA,UAAC;AAAC,eAAK,QAAQ,EAAEH,EAAC;AAAA,QAAC;AAAC,eAAO;AAAA,MAAI,GAAE,UAAS,SAASA,IAAE;AAAC,eAAOA,KAAE,EAAEA,EAAC,GAAE,KAAK,QAAQ,WAAWA,EAAC,GAAEA;AAAA,MAAC,GAAE,kBAAiB,WAAU;AAAC,iBAAQA,KAAE,KAAK,QAAQ,QAAOC,KAAE,CAAC,GAAEC,KAAE,MAAKC,KAAE,GAAEH,KAAEG,IAAEA,MAAI;AAAC,cAAIC,KAAE,KAAK,QAAQ,IAAID,EAAC,GAAEE,KAAE;AAAK,cAAG;AAAC,YAAAA,KAAE,EAAE,YAAY,KAAK,QAAQ,QAAQD,EAAC,CAAC;AAAA,UAAC,SAAOE,IAAE;AAAA,UAAC;AAAC,cAAG,SAAOD,MAAG,WAASA,GAAE,GAAE;AAAC,gBAAIE,MAAG,oBAAI,QAAM,QAAQ;AAAE,YAAAA,MAAGF,GAAE,KAAGJ,GAAE,KAAKG,EAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAOH,GAAE,QAAQ,SAASD,IAAE;AAAC,UAAAE,GAAE,QAAQ,EAAEF,EAAC;AAAA,QAAC,CAAC,GAAEC;AAAA,MAAC,GAAE,OAAM,WAAU;AAAC,aAAK,QAAQ,MAAM;AAAA,MAAC,GAAE,KAAI,SAASA,IAAEC,IAAEC,IAAE;AAAC,QAAAF,KAAE,EAAEA,EAAC,GAAE,YAAU,OAAOE,OAAIA,KAAE,EAAC,KAAIA,GAAC,IAAGA,KAAE,EAAE,EAAC,OAAM,KAAE,GAAEA,EAAC;AAAE,YAAG;AAAC,cAAIC,KAAE,EAAE,YAAY,KAAK,QAAQ,QAAQH,EAAC,CAAC;AAAE,cAAG,CAAC,EAAEG,EAAC,KAAG,CAAC,EAAEA,EAAC;AAAE,mBAAO,KAAK,IAAIH,IAAEC,IAAEC,EAAC,GAAE;AAAA,QAAE,SAAOE,IAAE;AAAC,iBAAO,KAAK,IAAIJ,IAAEC,IAAEC,EAAC,GAAE;AAAA,QAAE;AAAC,eAAM;AAAA,MAAE,GAAE,SAAQ,SAASH,IAAEC,IAAEC,IAAE;AAAC,QAAAF,KAAE,EAAEA,EAAC;AAAE,YAAIG,KAAE;AAAK,YAAG;AAAC,UAAAA,KAAE,EAAE,YAAY,KAAK,QAAQ,QAAQH,EAAC,CAAC;AAAA,QAAC,SAAOI,IAAE;AAAC,iBAAM;AAAA,QAAE;AAAC,YAAG,EAAED,EAAC,GAAE;AAAC,cAAG,EAAEA,EAAC;AAAE,mBAAO,KAAK,IAAIH,IAAEC,IAAEC,EAAC,GAAE;AAAG,eAAK,QAAQ,EAAEF,EAAC;AAAA,QAAC;AAAC,eAAM;AAAA,MAAE,GAAE,OAAM,SAASA,IAAEC,IAAE;AAAC,QAAAD,KAAE,EAAEA,EAAC;AAAE,YAAIE,KAAE;AAAK,YAAG;AAAC,UAAAA,KAAE,EAAE,YAAY,KAAK,QAAQ,QAAQF,EAAC,CAAC;AAAA,QAAC,SAAOG,IAAE;AAAC,iBAAM;AAAA,QAAE;AAAC,YAAG,EAAED,EAAC,GAAE;AAAC,cAAG,EAAEA,EAAC;AAAE,mBAAO,KAAK,IAAIF,IAAE,KAAK,IAAIA,EAAC,GAAE,EAAC,KAAIC,GAAC,CAAC,GAAE;AAAG,eAAK,QAAQ,EAAED,EAAC;AAAA,QAAC;AAAC,eAAM;AAAA,MAAE,EAAC;AAAE,aAAO,EAAE,YAAU,GAAE;AAAA,IAAC,CAAC;AAAA;AAAA;", "names": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j"]}