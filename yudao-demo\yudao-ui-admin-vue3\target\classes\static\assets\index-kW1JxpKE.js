import{d as i,o as t,c as o,i as l,l as n,x as p,_,__tla as m}from"./index-Daqg4PFz.js";import{E as y,__tla as u}from"./el-image-Bn34T02c.js";let r,d=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return u}catch{}})()]).then(async()=>{let e;e={key:0,class:"h-50px flex items-center justify-center bg-gray-3"},r=_(i({name:"ImageBar",__name:"index",props:{property:{type:Object,required:!0}},setup:x=>(a,g)=>{const s=p,c=y;return a.property.imgUrl?(t(),n(c,{key:1,class:"min-h-30px",src:a.property.imgUrl},null,8,["src"])):(t(),o("div",e,[l(s,{icon:"ep:picture",class:"text-gray-8 text-30px!"})]))}}),[["__scopeId","data-v-a0145e41"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/ImageBar/index.vue"]])});export{d as __tla,r as default};
