import{d as N,S as b,r as i,u as P,C as S,o as u,c as T,H as D,a,l as w,w as _,j as F,a9 as H,i as r,F as U,I as W,N as q,A as J,B as Q,E as G,R as K,_ as O,__tla as X}from"./index-Daqg4PFz.js";import{g as Y,_ as Z,__tla as $}from"./index-YDxIZBTH.js";import{u as aa,__tla as ta}from"./tagsView-CrrEoR03.js";import{a as ra,__tla as _a}from"./index-CbcZjzqw.js";import ea,{__tla as la}from"./ReceivableDetailsHeader-DrUCvXRZ.js";import ia,{__tla as sa}from"./ReceivableDetailsInfo-Bp_-2SPL.js";import oa,{__tla as ca}from"./PermissionList-BFo34k9h.js";import{B as R,__tla as ua}from"./index-BWsMQsUV.js";import na,{__tla as ma}from"./ReceivableForm-BTabXLiZ.js";import{__tla as fa}from"./el-timeline-item-DLMaR2h1.js";import{__tla as ya}from"./formatTime-BCfRGyrF.js";import{__tla as pa}from"./ContentWrap-DZg14iby.js";import{__tla as da}from"./el-card-Dvjjuipo.js";import{__tla as ha}from"./el-descriptions-item-Bucl-KSp.js";import{__tla as va}from"./el-collapse-item-CUcELNOM.js";import{__tla as ba}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as wa}from"./PermissionForm-CVzor7tm.js";import{__tla as Ra}from"./Dialog-BjBBVYCI.js";import{__tla as Ca}from"./index-BC06Brp1.js";import{__tla as Ea}from"./index-CCPyMtv-.js";import{__tla as za}from"./index-BYuPmJ1X.js";let C,Ba=Promise.all([(()=>{try{return X}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return za}catch{}})()]).then(async()=>{C=O(N({name:"CrmReceivablePlanDetail",__name:"index",props:{id:{type:Number,required:!1}},setup(E){const z=E,B=b(),g=W(),n=i(0),s=i(!0),e=i({}),m=i(),f=async t=>{s.value=!0;try{e.value=await ra(t),await k(t)}finally{s.value=!1}},y=i(),p=i([]),k=async t=>{if(!t)return;const l=await Y({bizType:R.CRM_RECEIVABLE,bizId:t});p.value=l.list},{delView:x}=aa(),{currentRoute:I}=P(),d=()=>{x(a(I))};return b(),S(async()=>{const t=z.id||B.params.id;if(!t)return g.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u56DE\u6B3E\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void d();n.value=t,await f(n.value)}),(t,l)=>{const A=q,o=J,L=Z,V=Q,j=G,M=K;return u(),T(U,null,[D((u(),w(ea,{receivable:a(e)},{default:_(()=>{var c;return[(c=a(m))!=null&&c.validateWrite?(u(),w(A,{key:0,onClick:l[0]||(l[0]=ga=>{return h="update",v=a(e).id,void y.value.open(h,v);var h,v})},{default:_(()=>[F(" \u7F16\u8F91 ")]),_:1})):H("",!0)]}),_:1},8,["receivable"])),[[M,a(s)]]),r(j,null,{default:_(()=>[r(V,null,{default:_(()=>[r(o,{label:"\u8BE6\u7EC6\u8D44\u6599"},{default:_(()=>[r(ia,{receivable:a(e)},null,8,["receivable"])]),_:1}),r(o,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:_(()=>[r(L,{"log-list":a(p)},null,8,["log-list"])]),_:1}),r(o,{label:"\u56E2\u961F\u6210\u5458"},{default:_(()=>[r(oa,{ref_key:"permissionListRef",ref:m,"biz-id":a(e).id,"biz-type":a(R).CRM_RECEIVABLE,"show-action":!0,onQuitTeam:d},null,8,["biz-id","biz-type"])]),_:1})]),_:1})]),_:1}),r(na,{ref_key:"formRef",ref:y,onSuccess:l[1]||(l[1]=c=>f(a(e).id))},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/receivable/detail/index.vue"]])});export{Ba as __tla,C as default};
