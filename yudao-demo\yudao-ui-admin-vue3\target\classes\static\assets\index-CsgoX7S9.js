import{d as O,f as j,e as G,r as u,b as Q,at as W,C as X,ay as Z,o as k,c as q,i as a,w as r,a as e,F as V,k as Y,V as $,G as aa,l as F,j as M,z as ea,M as la,L as ra,J as ta,K as sa,x as ua,N as oa,O as ma,A as _a,B as da,E as ca,_ as na,__tla as ya}from"./index-Daqg4PFz.js";import{_ as pa,__tla as fa}from"./ContentWrap-DZg14iby.js";import{E as ia,__tla as ha}from"./el-tree-select-BKcJcOKx.js";import{g as va,__tla as ba}from"./index-D-Abj-9W.js";import{f as H,c as ka,e as wa,g as Ca,__tla as Da}from"./formatTime-BCfRGyrF.js";import{h as J,d as qa}from"./tree-BMqZf9_I.js";import Va,{__tla as Ua}from"./CustomerConversionStat-CZwr_PM0.js";import Ia,{__tla as Sa}from"./CustomerDealCycleByUser-BjeMDyXk.js";import xa,{__tla as Ra}from"./CustomerDealCycleByArea-mW0vKmbq.js";import ga,{__tla as za}from"./CustomerDealCycleByProduct-DYlPAlha.js";import Ba,{__tla as Ta}from"./CustomerFollowUpSummary-DAnp_EPB.js";import Aa,{__tla as Ea}from"./CustomerFollowUpType-CBLMGqla.js";import Pa,{__tla as Ya}from"./CustomerSummary-CUQOJbAH.js";import Fa,{__tla as Ma}from"./CustomerPoolSummary-ChtSQ75A.js";import{__tla as Ha}from"./el-card-Dvjjuipo.js";import{__tla as Ja}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as Ka}from"./el-skeleton-item-BmS2F7Yy.js";import{__tla as La}from"./Echart-C33-KcLZ.js";import{__tla as Na}from"./customer-B7xLyCV3.js";let K,Oa=Promise.all([(()=>{try{return ya}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return Na}catch{}})()]).then(async()=>{let U;U=O({name:"CrmStatisticsCustomer",__name:"index",setup(ja){const t=j({interval:2,deptId:G().getUser.deptId,userId:void 0,times:[H(ka(new Date(new Date().getTime()-6048e5))),H(wa(new Date(new Date().getTime()-864e5)))]}),I=u(),S=u([]),x=u([]),L=Q(()=>t.deptId?x.value.filter(d=>d.deptId===t.deptId):[]),h=u("customerSummary"),R=u(),g=u(),z=u(),B=u(),T=u(),A=u(),E=u(),P=u(),_=()=>{var d,s,y,m,c,v,p,b,n,w,f,o,i,C,l,D;switch(h.value){case"customerSummary":(s=(d=R.value)==null?void 0:d.loadData)==null||s.call(d);break;case"followUpSummary":(m=(y=g.value)==null?void 0:y.loadData)==null||m.call(y);break;case"followUpType":(v=(c=z.value)==null?void 0:c.loadData)==null||v.call(c);break;case"conversionStat":(b=(p=B.value)==null?void 0:p.loadData)==null||b.call(p);break;case"poolSummary":(w=(n=T.value)==null?void 0:n.loadData)==null||w.call(n);break;case"dealCycleByUser":(o=(f=A.value)==null?void 0:f.loadData)==null||o.call(f);break;case"dealCycleByArea":(C=(i=E.value)==null?void 0:i.loadData)==null||C.call(i);break;case"dealCycleByProduct":(D=(l=P.value)==null?void 0:l.loadData)==null||D.call(l)}};W(h,()=>{_()});const N=()=>{I.value.resetFields(),_()};return X(async()=>{S.value=J(await va()),x.value=J(await Z())}),(d,s)=>{const y=la,m=ra,c=ta,v=sa,p=ia,b=ua,n=oa,w=ma,f=pa,o=_a,i=da,C=ca;return k(),q(V,null,[a(f,null,{default:r(()=>[a(w,{ref_key:"queryFormRef",ref:I,inline:!0,model:e(t),class:"-mb-15px","label-width":"68px"},{default:r(()=>[a(m,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:r(()=>[a(y,{modelValue:e(t).times,"onUpdate:modelValue":s[0]||(s[0]=l=>e(t).times=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:e(Ca),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss",onChange:_},null,8,["modelValue","default-time","shortcuts"])]),_:1}),a(m,{label:"\u65F6\u95F4\u95F4\u9694",prop:"interval"},{default:r(()=>[a(v,{modelValue:e(t).interval,"onUpdate:modelValue":s[1]||(s[1]=l=>e(t).interval=l),class:"!w-240px",placeholder:"\u95F4\u9694\u7C7B\u578B",onChange:_},{default:r(()=>[(k(!0),q(V,null,Y(e($)(e(aa).DATE_INTERVAL),l=>(k(),F(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:r(()=>[a(p,{modelValue:e(t).deptId,"onUpdate:modelValue":s[2]||(s[2]=l=>e(t).deptId=l),data:e(S),props:e(qa),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:s[3]||(s[3]=l=>(e(t).userId=void 0,_()))},null,8,["modelValue","data","props"])]),_:1}),a(m,{label:"\u5458\u5DE5",prop:"userId"},{default:r(()=>[a(v,{modelValue:e(t).userId,"onUpdate:modelValue":s[4]||(s[4]=l=>e(t).userId=l),class:"!w-240px",clearable:"",placeholder:"\u5458\u5DE5",onChange:_},{default:r(()=>[(k(!0),q(V,null,Y(e(L),(l,D)=>(k(),F(c,{key:D,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,null,{default:r(()=>[a(n,{onClick:_},{default:r(()=>[a(b,{class:"mr-5px",icon:"ep:search"}),M(" \u67E5\u8BE2 ")]),_:1}),a(n,{onClick:N},{default:r(()=>[a(b,{class:"mr-5px",icon:"ep:refresh"}),M(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:r(()=>[a(i,{modelValue:e(h),"onUpdate:modelValue":s[5]||(s[5]=l=>ea(h)?h.value=l:null)},{default:r(()=>[a(o,{label:"\u5BA2\u6237\u603B\u91CF\u5206\u6790",lazy:"",name:"customerSummary"},{default:r(()=>[a(Pa,{ref_key:"customerSummaryRef",ref:R,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u5BA2\u6237\u8DDF\u8FDB\u6B21\u6570\u5206\u6790",lazy:"",name:"followUpSummary"},{default:r(()=>[a(Ba,{ref_key:"followUpSummaryRef",ref:g,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u5BA2\u6237\u8DDF\u8FDB\u65B9\u5F0F\u5206\u6790",lazy:"",name:"followUpType"},{default:r(()=>[a(Aa,{ref_key:"followUpTypeRef",ref:z,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u5BA2\u6237\u8F6C\u5316\u7387\u5206\u6790",lazy:"",name:"conversionStat"},{default:r(()=>[a(Va,{ref_key:"conversionStatRef",ref:B,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u516C\u6D77\u5BA2\u6237\u5206\u6790",lazy:"",name:"poolSummary"},{default:r(()=>[a(Fa,{ref_key:"customerPoolSummaryRef",ref:T,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u5458\u5DE5\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByUser"},{default:r(()=>[a(Ia,{ref_key:"dealCycleByUserRef",ref:A,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u5730\u533A\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByArea"},{default:r(()=>[a(xa,{ref_key:"dealCycleByAreaRef",ref:E,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(o,{label:"\u4EA7\u54C1\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByProduct"},{default:r(()=>[a(ga,{ref_key:"dealCycleByProductRef",ref:P,"query-params":e(t)},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}}),K=na(U,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/statistics/customer/index.vue"]])});export{Oa as __tla,K as default};
