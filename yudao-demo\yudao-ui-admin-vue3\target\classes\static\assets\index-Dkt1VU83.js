import{d as A,I as B,n as D,r as s,f as G,C as M,T as X,o as i,c as g,i as a,w as t,a as l,F as v,k as F,l as u,j as d,H as w,e5 as Y,J as Z,K as $,L as aa,x as ea,N as la,O as ta,P as ra,Q as oa,R as sa,_ as ia,__tla as ca}from"./index-Daqg4PFz.js";import{_ as na,__tla as ua}from"./index-BBLwwrga.js";import{_ as pa,__tla as _a}from"./ContentWrap-DZg14iby.js";import{_ as da,__tla as ma}from"./index-CmwFi8Xl.js";import{d as fa}from"./download--D_IyRio.js";import{S as L,__tla as ya}from"./index-BUJ03bwx.js";import{P as ha,__tla as ga}from"./index-BdaXniMm.js";import{W as va,__tla as wa}from"./index-b9NHryvG.js";import{__tla as ba}from"./index-CS70nJJ8.js";import{__tla as xa}from"./el-card-Dvjjuipo.js";let W,ka=Promise.all([(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return xa}catch{}})()]).then(async()=>{W=ia(A({name:"ErpStock",__name:"index",setup(Na){const J=B();D();const m=s(!0),b=s([]),x=s(0),r=G({pageNo:1,pageSize:10,productId:void 0,warehouseId:void 0}),k=s(),f=s(!1),N=s([]),I=s([]),y=async()=>{m.value=!0;try{const c=await L.getStockPage(r);b.value=c.list,x.value=c.total}finally{m.value=!1}},S=()=>{r.pageNo=1,y()},R=()=>{k.value.resetFields(),S()},j=s(),q=async()=>{try{await J.exportConfirm(),f.value=!0;const c=await L.exportStock(r);fa.excel(c,"\u4EA7\u54C1\u5E93\u5B58.xls")}catch{}finally{f.value=!1}};return M(async()=>{await y(),N.value=await ha.getProductSimpleList(),I.value=await va.getWarehouseSimpleList()}),(c,o)=>{const E=da,P=Z,C=$,h=aa,p=ea,_=la,H=ta,U=pa,n=ra,K=oa,O=na,V=X("hasPermi"),Q=sa;return i(),g(v,null,[a(E,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),a(U,null,{default:t(()=>[a(H,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:t(()=>[a(h,{label:"\u4EA7\u54C1",prop:"productId"},{default:t(()=>[a(C,{modelValue:l(r).productId,"onUpdate:modelValue":o[0]||(o[0]=e=>l(r).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:t(()=>[(i(!0),g(v,null,F(l(N),e=>(i(),u(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(h,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:t(()=>[a(C,{modelValue:l(r).warehouseId,"onUpdate:modelValue":o[1]||(o[1]=e=>l(r).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:t(()=>[(i(!0),g(v,null,F(l(I),e=>(i(),u(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(h,null,{default:t(()=>[a(_,{onClick:S},{default:t(()=>[a(p,{icon:"ep:search",class:"mr-5px"}),d(" \u641C\u7D22")]),_:1}),a(_,{onClick:R},{default:t(()=>[a(p,{icon:"ep:refresh",class:"mr-5px"}),d(" \u91CD\u7F6E")]),_:1}),w((i(),u(_,{type:"primary",plain:"",onClick:o[2]||(o[2]=e=>{return z="create",void j.value.open(z,T);var z,T})},{default:t(()=>[a(p,{icon:"ep:plus",class:"mr-5px"}),d(" \u65B0\u589E ")]),_:1})),[[V,["erp:stock:create"]]]),w((i(),u(_,{type:"success",plain:"",onClick:q,loading:l(f)},{default:t(()=>[a(p,{icon:"ep:download",class:"mr-5px"}),d(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[V,["erp:stock:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(U,null,{default:t(()=>[w((i(),u(K,{data:l(b),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(n,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName"}),a(n,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unitName"}),a(n,{label:"\u4EA7\u54C1\u5206\u7C7B",align:"center",prop:"categoryName"}),a(n,{label:"\u5E93\u5B58\u91CF",align:"center",prop:"count",formatter:l(Y)},null,8,["formatter"]),a(n,{label:"\u4ED3\u5E93",align:"center",prop:"warehouseName"})]),_:1},8,["data"])),[[Q,l(m)]]),a(O,{total:l(x),page:l(r).pageNo,"onUpdate:page":o[3]||(o[3]=e=>l(r).pageNo=e),limit:l(r).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>l(r).pageSize=e),onPagination:y},null,8,["total","page","limit"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/erp/stock/stock/index.vue"]])});export{ka as __tla,W as default};
