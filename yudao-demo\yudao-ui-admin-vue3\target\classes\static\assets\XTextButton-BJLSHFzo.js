import{d as u,p as n,b as f,o as a,l as s,w as _,ao as m,a as x,bz as y,N as I,a9 as c,j as b,t as k,x as g,_ as C,__tla as v}from"./index-Daqg4PFz.js";let r,B=Promise.all([(()=>{try{return v}catch{}})()]).then(async()=>{r=C(u({name:"XTextButton",__name:"XTextButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def("primary"),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(e){const i=e,d=f(()=>{const l=["title","preIcon","postIcon","onClick"],o={...y(),...i};for(const t in o)l.indexOf(t)!==-1&&delete o[t];return o});return(l,o)=>{const t=g,p=I;return a(),s(p,m({link:""},x(d),{onClick:e.onClick}),{default:_(()=>[e.preIcon?(a(),s(t,{key:0,icon:e.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),b(" "+k(e.title?e.title:"")+" ",1),e.postIcon?(a(),s(t,{key:1,icon:e.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-bd4a9e34"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/XButton/src/XTextButton.vue"]])});export{r as _,B as __tla};
