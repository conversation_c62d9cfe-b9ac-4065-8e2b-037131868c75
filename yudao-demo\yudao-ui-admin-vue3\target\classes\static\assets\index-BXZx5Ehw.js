import{d as T,r as w,at as R,o as r,c as o,F as B,k as I,av as p,i as g,a9 as s,g as i,a0 as y,t as a,l as P,a as S,_ as $,__tla as U}from"./index-Daqg4PFz.js";import{E,__tla as L}from"./el-image-Bn34T02c.js";import{b as j,__tla as q}from"./spu-zkQh6zUd.js";let v,z=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{let d,u,x,f;d={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},u={class:"text-12px"},x={class:"absolute bottom-8px right-8px"},f=T({name:"ProductCard",__name:"index",props:{property:{type:Object,required:!0}},setup(_){const l=_,m=w([]);R(()=>l.property.spuIds,async()=>{m.value=await j(l.property.spuIds)},{immediate:!0,deep:!0});const k=t=>{const c=l.property.layoutType==="twoCol"?2:1;return{marginLeft:t%c==0?"0":l.property.space+"px",marginTop:t<c?"0":l.property.space+"px"}},b=w(),C=()=>{let t="100%";return l.property.layoutType==="twoCol"&&(t=(b.value.offsetWidth-l.property.space)/2+"px"),{width:t}};return(t,c)=>{const n=E;return r(),o("div",{class:y("box-content min-h-30px w-full flex flex-row flex-wrap"),ref_key:"containerRef",ref:b},[(r(!0),o(B,null,I(S(m),(e,h)=>(r(),o("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:p({...k(h),...C(),borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`}),key:h},[t.property.badge.show?(r(),o("div",d,[g(n,{fit:"cover",src:t.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):s("",!0),i("div",{class:y(["h-140px",{"w-full":t.property.layoutType!=="oneColSmallImg","w-140px":t.property.layoutType==="oneColSmallImg"}])},[g(n,{fit:"cover",class:"h-full w-full",src:e.picUrl},null,8,["src"])],2),i("div",{class:y([" flex flex-col gap-8px p-8px box-border",{"w-full":t.property.layoutType!=="oneColSmallImg","w-[calc(100%-140px-16px)]":t.property.layoutType==="oneColSmallImg"}])},[t.property.fields.name.show?(r(),o("div",{key:0,class:y(["text-14px ",{truncate:t.property.layoutType!=="oneColSmallImg","overflow-ellipsis line-clamp-2":t.property.layoutType==="oneColSmallImg"}]),style:p({color:t.property.fields.name.color})},a(e.name),7)):s("",!0),t.property.fields.introduction.show?(r(),o("div",{key:1,class:"truncate text-12px",style:p({color:t.property.fields.introduction.color})},a(e.introduction),5)):s("",!0),i("div",null,[t.property.fields.price.show?(r(),o("span",{key:0,class:"text-16px",style:p({color:t.property.fields.price.color})}," \uFFE5"+a(e.price),5)):s("",!0),t.property.fields.marketPrice.show&&e.marketPrice?(r(),o("span",{key:1,class:"ml-4px text-10px line-through",style:p({color:t.property.fields.marketPrice.color})},"\uFFE5"+a(e.marketPrice),5)):s("",!0)]),i("div",u,[t.property.fields.salesCount.show?(r(),o("span",{key:0,style:p({color:t.property.fields.salesCount.color})}," \u5DF2\u552E"+a((e.salesCount||0)+(e.virtualSalesCount||0))+"\u4EF6 ",5)):s("",!0),t.property.fields.stock.show?(r(),o("span",{key:1,style:p({color:t.property.fields.stock.color})}," \u5E93\u5B58"+a(e.stock||0),5)):s("",!0)])],2),i("div",x,[t.property.btnBuy.type==="text"?(r(),o("span",{key:0,class:"rounded-full p-x-12px p-y-4px text-12px text-white",style:p({background:`linear-gradient(to right, ${t.property.btnBuy.bgBeginColor}, ${t.property.btnBuy.bgEndColor}`})},a(t.property.btnBuy.text),5)):(r(),P(n,{key:1,class:"h-28px w-28px rounded-full",fit:"cover",src:t.property.btnBuy.imgUrl},null,8,["src"]))])],4))),128))],512)}}}),v=$(f,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/ProductCard/index.vue"]])});export{z as __tla,v as default};
