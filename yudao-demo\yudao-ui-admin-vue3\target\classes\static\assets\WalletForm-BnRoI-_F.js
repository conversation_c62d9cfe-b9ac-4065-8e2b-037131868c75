import{_ as p,__tla as f}from"./Dialog-BjBBVYCI.js";import{d as y,r as l,o as h,l as v,w as r,i as c,j as w,a as e,z as V,N as j,_ as x,__tla as F}from"./index-Daqg4PFz.js";import U,{__tla as W}from"./WalletTransactionList--ylxVhvZ.js";import{__tla as b}from"./ContentWrap-DZg14iby.js";import{__tla as k}from"./el-card-Dvjjuipo.js";import{__tla as z}from"./index-BBLwwrga.js";import{__tla as C}from"./index-CS70nJJ8.js";import{__tla as J}from"./formatTime-BCfRGyrF.js";let n,N=Promise.all([(()=>{try{return f}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{n=x(y({__name:"WalletForm",setup(P,{expose:m}){const a=l(!1),_=l("");l(!1);const o=l(0);return m({open:async s=>{a.value=!0,_.value="\u94B1\u5305\u4F59\u989D\u660E\u7EC6",o.value=s}}),(s,t)=>{const i=j,d=p;return h(),v(d,{title:e(_),modelValue:e(a),"onUpdate:modelValue":t[1]||(t[1]=u=>V(a)?a.value=u:null),width:"800"},{footer:r(()=>[c(i,{onClick:t[0]||(t[0]=u=>a.value=!1)},{default:r(()=>[w("\u53D6 \u6D88")]),_:1})]),default:r(()=>[c(U,{"wallet-id":e(o)},null,8,["wallet-id"])]),_:1},8,["title","modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/pay/wallet/balance/WalletForm.vue"]])});export{N as __tla,n as default};
