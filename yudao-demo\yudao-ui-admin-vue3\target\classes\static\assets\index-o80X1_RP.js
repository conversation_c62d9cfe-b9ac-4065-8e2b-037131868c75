import{u as R,_ as w,a as j,__tla as A}from"./useTable-1UdEztBx.js";import{_ as F,__tla as T}from"./ContentWrap-DZg14iby.js";import{d as U,r as C,C as H,T as J,o,c as N,i as e,w as _,a as t,H as m,l as i,j as u,F as O,x as q,N as B,_ as D,__tla as E}from"./index-Daqg4PFz.js";import{_ as G,__tla as I}from"./index-CmwFi8Xl.js";import{a as P,M as K,__tla as Q}from"./MailTemplateForm-Dl7cL6O2.js";import{a as V,d as W,__tla as X}from"./index-DPI42eGd.js";import Y,{__tla as Z}from"./MailTemplateSendForm-BLpnniGC.js";import{__tla as $}from"./Form-R69XsyLN.js";import{__tla as tt}from"./el-virtual-list-ByJAteiO.js";import{__tla as at}from"./el-tree-select-BKcJcOKx.js";import{__tla as rt}from"./el-time-select-BnExG5dm.js";import{__tla as et}from"./InputPassword-Dxw5CWOJ.js";import{__tla as _t}from"./index-CS70nJJ8.js";import{__tla as lt}from"./useForm-CSyrGYVb.js";import"./download--D_IyRio.js";import{__tla as st}from"./el-card-Dvjjuipo.js";import{__tla as ot}from"./Dialog-BjBBVYCI.js";import{__tla as nt}from"./formatTime-BCfRGyrF.js";import{__tla as ct}from"./index-DmR7jHBv.js";import{__tla as mt}from"./formRules-BBK7HL0H.js";import{__tla as it}from"./useCrudSchemas-C1aGM0Lr.js";import"./tree-BMqZf9_I.js";import{__tla as ut}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";let x,pt=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return ut}catch{}})()]).then(async()=>{x=D(U({name:"SystemMailTemplate",__name:"index",setup(yt){const{tableObject:l,tableMethods:p}=R({getListApi:V,delListApi:W}),{getList:y,setSearchParams:d}=p,f=C(),h=(S,a)=>{f.value.open(S,a)},g=C();return H(()=>{y()}),(S,a)=>{const b=G,z=q,n=B,L=w,k=F,M=j,c=J("hasPermi");return o(),N(O,null,[e(b,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),e(k,null,{default:_(()=>[e(L,{schema:t(P).searchSchema,onSearch:t(d),onReset:t(d)},{actionMore:_(()=>[m((o(),i(n,{type:"primary",plain:"",onClick:a[0]||(a[0]=r=>h("create"))},{default:_(()=>[e(z,{icon:"ep:plus",class:"mr-5px"}),u(" \u65B0\u589E ")]),_:1})),[[c,["system:mail-template:create"]]])]),_:1},8,["schema","onSearch","onReset"])]),_:1}),e(k,null,{default:_(()=>[e(M,{columns:t(P).tableColumns,data:t(l).tableList,loading:t(l).loading,pagination:{total:t(l).total},pageSize:t(l).pageSize,"onUpdate:pageSize":a[1]||(a[1]=r=>t(l).pageSize=r),currentPage:t(l).currentPage,"onUpdate:currentPage":a[2]||(a[2]=r=>t(l).currentPage=r)},{action:_(({row:r})=>[m((o(),i(n,{link:"",type:"primary",onClick:v=>{return s=r.id,void g.value.open(s);var s}},{default:_(()=>[u(" \u6D4B\u8BD5 ")]),_:2},1032,["onClick"])),[[c,["system:mail-template:send-mail"]]]),m((o(),i(n,{link:"",type:"primary",onClick:v=>h("update",r.id)},{default:_(()=>[u(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[c,["system:mail-template:update"]]]),m((o(),i(n,{link:"",type:"danger",onClick:v=>{return s=r.id,void p.delList(s,!1);var s}},{default:_(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[c,["system:mail-template:delete"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),e(K,{ref_key:"formRef",ref:f,onSuccess:t(y)},null,8,["onSuccess"]),e(Y,{ref_key:"sendFormRef",ref:g},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/system/mail/template/index.vue"]])});export{pt as __tla,x as default};
