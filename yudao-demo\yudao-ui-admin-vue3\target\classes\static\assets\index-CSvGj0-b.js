import{bE as t,__tla as i}from"./index-Daqg4PFz.js";let e,r,s,m,l,p,c=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{s=async a=>await t.post({url:"/bpm/form/create",data:a}),p=async a=>await t.put({url:"/bpm/form/update",data:a}),m=async a=>await t.delete({url:"/bpm/form/delete?id="+a}),l=async a=>await t.get({url:"/bpm/form/get?id="+a}),r=async a=>await t.get({url:"/bpm/form/page",params:a}),e=async()=>await t.get({url:"/bpm/form/simple-list"})});export{c as __tla,e as a,r as b,s as c,m as d,l as g,p as u};
