const e={common:{inputText:"Please input",selectText:"Please select",startTimeText:"Start time",endTimeText:"End time",login:"Login",required:"This is required",loginOut:"Login out",document:"Document",profile:"User Center",reminder:"Reminder",loginOutMessage:"Exit the system?",back:"Back",ok:"OK",save:"Save",cancel:"Cancel",close:"Close",reload:"Reload current",success:"Success",closeTab:"Close current",closeTheLeftTab:"Close left",closeTheRightTab:"Close right",closeOther:"Close other",closeAll:"Close all",prevLabel:"Prev",nextLabel:"Next",skipLabel:"Jump",doneLabel:"End",menu:"Menu",menuDes:"Menu bar rendered in routed structure",collapse:"Collapse",collapseDes:"Expand and zoom the menu bar",tagsView:"Tags view",tagsViewDes:"Used to record routing history",tool:"Tool",toolDes:"Used to set up custom systems",query:"Query",reset:"Reset",shrink:"Put away",expand:"Expand",confirmTitle:"System Hint",exportMessage:"Whether to confirm export data item?",importMessage:"Whether to confirm import data item?",createSuccess:"Create Success",updateSuccess:"Update Success",delMessage:"Delete the selected data?",delDataMessage:"Delete the data?",delNoData:"Please select the data to delete",delSuccess:"Deleted successfully",index:"Index",status:"Status",createTime:"Create Time",updateTime:"Update Time",copy:"Copy",copySuccess:"Copy Success",copyError:"Copy Error"},lock:{lockScreen:"Lock screen",lock:"Lock",lockPassword:"Lock screen password",unlock:"Click to unlock",backToLogin:"Back to login",entrySystem:"Entry the system",placeholder:"Please enter the lock screen password",message:"Lock screen password error"},error:{noPermission:"Sorry, you don't have permission to access this page.",pageError:"Sorry, the page you visited does not exist.",networkError:"Sorry, the server reported an error.",returnToHome:"Return to home"},permission:{hasPermission:"Please set the operation permission label value",hasRole:"Please set the role permission tag value"},setting:{projectSetting:"Project setting",theme:"Theme",layout:"Layout",systemTheme:"System theme",menuTheme:"Menu theme",interfaceDisplay:"Interface display",breadcrumb:"Breadcrumb",breadcrumbIcon:"Breadcrumb icon",collapseMenu:"Collapse menu",hamburgerIcon:"Hamburger icon",screenfullIcon:"Screenfull icon",sizeIcon:"Size icon",localeIcon:"Locale icon",messageIcon:"Message icon",tagsView:"Tags view",logo:"Logo",greyMode:"Grey mode",fixedHeader:"Fixed header",headerTheme:"Header theme",cutMenu:"Cut Menu",copy:"Copy",clearAndReset:"Clear cache and reset",copySuccess:"Copy success",copyFailed:"Copy failed",footer:"Footer",uniqueOpened:"Unique opened",tagsViewIcon:"Tags view icon",reExperienced:"Please exit the login experience again",fixedMenu:"Fixed menu"},size:{default:"Default",large:"Large",small:"Small"},login:{welcome:"Welcome to the system",message:"Backstage management system",tenantname:"TenantName",username:"Username",password:"Password",code:"verification code",login:"Sign in",relogin:"Sign in again",otherLogin:"Sign in with",register:"Register",checkPassword:"Confirm password",remember:"Remember me",hasUser:"Existing account? Go to login",forgetPassword:"Forget password?",tenantNamePlaceholder:"Please Enter Tenant Name",usernamePlaceholder:"Please Enter Username",passwordPlaceholder:"Please Enter Password",codePlaceholder:"Please Enter Verification Code",mobileTitle:"Mobile sign in",mobileNumber:"Mobile Number",mobileNumberPlaceholder:"Plaease Enter Mobile Number",backLogin:"back",getSmsCode:"Get SMS Code",btnMobile:"Mobile sign in",btnQRCode:"QR code sign in",qrcode:"Scan the QR code to log in",btnRegister:"Sign up",SmsSendMsg:"code has been sent"},captcha:{verification:"Please complete security verification",slide:"Swipe right to complete verification",point:"Please click",success:"Verification succeeded",fail:"verification failed"},router:{login:"Login",home:"Home",analysis:"Analysis",workplace:"Workplace"},analysis:{newUser:"New user",unreadInformation:"Unread information",transactionAmount:"Transaction amount",totalShopping:"Total Shopping",monthlySales:"Monthly sales",userAccessSource:"User access source",january:"January",february:"February",march:"March",april:"April",may:"May",june:"June",july:"July",august:"August",september:"September",october:"October",november:"November",december:"December",estimate:"Estimate",actual:"Actual",directAccess:"Airect access",mailMarketing:"Mail marketing",allianceAdvertising:"Alliance advertising",videoAdvertising:"Video advertising",searchEngines:"Search engines",weeklyUserActivity:"Weekly user activity",activeQuantity:"Active quantity",monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"},workplace:{welcome:"Hello",happyDay:"Wish you happy every day!",toady:"It's sunny today",notice:"Announcement",project:"Project",access:"Project access",toDo:"To do",introduction:"A serious introduction",shortcutOperation:"Quick entry",operation:"Operation",index:"Index",personal:"Personal",team:"Team",quote:"Quote",contribution:"Contribution",hot:"Hot",yield:"Yield",dynamic:"Dynamic",push:"push",follow:"Follow"},form:{input:"Input",inputNumber:"InputNumber",default:"Default",icon:"Icon",mixed:"Mixed",textarea:"Textarea",slot:"Slot",position:"Position",autocomplete:"Autocomplete",select:"Select",selectGroup:"Select Group",selectV2:"SelectV2",cascader:"Cascader",switch:"Switch",rate:"Rate",colorPicker:"Color Picker",transfer:"Transfer",render:"Render",radio:"Radio",button:"Button",checkbox:"Checkbox",slider:"Slider",datePicker:"Date Picker",shortcuts:"Shortcuts",today:"Today",yesterday:"Yesterday",aWeekAgo:"A week ago",week:"Week",year:"Year",month:"Month",dates:"Dates",daterange:"Date Range",monthrange:"Month Range",dateTimePicker:"DateTimePicker",dateTimerange:"Datetime Range",timePicker:"Time Picker",timeSelect:"Time Select",inputPassword:"input Password",passwordStrength:"Password Strength",operate:"operate",change:"Change",restore:"Restore",disabled:"Disabled",disablement:"Disablement",delete:"Delete",add:"Add",setValue:"Set value",resetValue:"Reset value",set:"Set",subitem:"Subitem",formValidation:"Form validation",verifyReset:"Verify reset",remark:"Remark"},watermark:{watermark:"Watermark"},table:{table:"Table",index:"Index",title:"Title",author:"Author",createTime:"Create time",action:"Action",pagination:"pagination",reserveIndex:"Reserve index",restoreIndex:"Restore index",showSelections:"Show selections",hiddenSelections:"Restore selections",showExpandedRows:"Show expanded rows",hiddenExpandedRows:"Hidden expanded rows",header:"Header"},action:{create:"Create",add:"Add",del:"Delete",delete:"Delete",edit:"Edit",update:"Update",preview:"Preview",more:"More",sync:"Sync",save:"Save",detail:"Detail",export:"Export",import:"Import",generate:"Generate",logout:"Login Out",test:"Test",typeCreate:"Dict Type Create",typeUpdate:"Dict Type Eidt",dataCreate:"Dict Data Create",dataUpdate:"Dict Data Eidt",fileUpload:"File Upload"},dialog:{dialog:"Dialog",open:"Open",close:"Close"},sys:{api:{operationFailed:"Operation failed",errorTip:"Error Tip",errorMessage:"The operation failed, the system is abnormal!",timeoutMessage:"Login timed out, please log in again!",apiTimeoutMessage:"The interface request timed out, please refresh the page and try again!",apiRequestFailed:"The interface request failed, please try again later!",networkException:"network anomaly",networkExceptionMsg:"Please check if your network connection is normal! The network is abnormal",errMsg401:"The user does not have permission (token, user name, password error)!",errMsg403:"The user is authorized, but access is forbidden!",errMsg404:"Network request error, the resource was not found!",errMsg405:"Network request error, request method not allowed!",errMsg408:"Network request timed out!",errMsg500:"Server error, please contact the administrator!",errMsg501:"The network is not implemented!",errMsg502:"Network Error!",errMsg503:"The service is unavailable, the server is temporarily overloaded or maintained!",errMsg504:"Network timeout!",errMsg505:"The http version does not support the request!",errMsg901:"Demo mode, no write operations are possible!"},app:{logoutTip:"Reminder",logoutMessage:"Confirm to exit the system?",menuLoading:"Menu loading..."},exception:{backLogin:"Back Login",backHome:"Back Home",subTitle403:"Sorry, you don't have access to this page.",subTitle404:"Sorry, the page you visited does not exist.",subTitle500:"Sorry, the server is reporting an error.",noDataTitle:"No data on the current page.",networkErrorTitle:"Network Error",networkErrorSubTitle:"Sorry, Your network connection has been disconnected, please check your network!"},lock:{unlock:"Click to unlock",alert:"Lock screen password error",backToLogin:"Back to login",entry:"Enter the system",placeholder:"Please enter the lock screen password or user password"},login:{backSignIn:"Back sign in",mobileSignInFormTitle:"Mobile sign in",qrSignInFormTitle:"Qr code sign in",signInFormTitle:"Sign in",signUpFormTitle:"Sign up",forgetFormTitle:"Reset password",signInTitle:"Backstage management system",signInDesc:"Enter your personal details and get started!",policy:"I agree to the xxx Privacy Policy",scanSign:"scanning the code to complete the login",loginButton:"Sign in",registerButton:"Sign up",rememberMe:"Remember me",forgetPassword:"Forget Password?",otherSignIn:"Sign in with",loginSuccessTitle:"Login successful",loginSuccessDesc:"Welcome back",accountPlaceholder:"Please input username",passwordPlaceholder:"Please input password",smsPlaceholder:"Please input sms code",mobilePlaceholder:"Please input mobile",policyPlaceholder:"Register after checking",diffPwd:"The two passwords are inconsistent",userName:"Username",password:"Password",confirmPassword:"Confirm Password",email:"Email",smsCode:"SMS code",mobile:"Mobile"}},profile:{user:{title:"Personal Information",username:"User Name",nickname:"Nick Name",mobile:"Phone Number",email:"User Mail",dept:"Department",posts:"Position",roles:"Own Role",sex:"Sex",man:"Man",woman:"Woman",createTime:"Created Date"},info:{title:"Basic Information",basicInfo:"Basic Information",resetPwd:"Reset Password",userSocial:"Social Information"},rules:{nickname:"Please Enter User Nickname",mail:"Please Input The Email Address",truemail:"Please Input The Correct Email Address",phone:"Please Enter The Phone Number",truephone:"Please Enter The Correct Phone Number"},password:{oldPassword:"Old PassWord",newPassword:"New Password",confirmPassword:"Confirm Password",oldPwdMsg:"Please Enter Old Password",newPwdMsg:"Please Enter New Password",cfPwdMsg:"Please Enter Confirm Password",diffPwd:"The Passwords Entered Twice No Match"}},cropper:{selectImage:"Select Image",uploadSuccess:"Uploaded success!",modalTitle:"Avatar upload",okText:"Confirm and upload",btn_reset:"Reset",btn_rotate_left:"Counterclockwise rotation",btn_rotate_right:"Clockwise rotation",btn_scale_x:"Flip horizontal",btn_scale_y:"Flip vertical",btn_zoom_in:"Zoom in",btn_zoom_out:"Zoom out",preview:"Preivew"}};export{e as default};
