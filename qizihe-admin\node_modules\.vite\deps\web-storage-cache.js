import {
  __commonJS
} from "./chunk-CWGEVKOD.js";

// node_modules/web-storage-cache/dist/web-storage-cache.min.js
var require_web_storage_cache_min = __commonJS({
  "node_modules/web-storage-cache/dist/web-storage-cache.min.js"(exports, module) {
    !function(a, b) {
      "function" == typeof define && define.amd ? define(b) : "object" == typeof exports ? module.exports = b() : a.WebStorageCache = b();
    }(exports, function() {
      "use strict";
      function a(a2, b2) {
        for (var c2 in b2)
          a2[c2] = b2[c2];
        return a2;
      }
      function b(a2) {
        var b2 = false;
        if (a2 && a2.setItem) {
          b2 = true;
          var c2 = "__" + Math.round(1e7 * Math.random());
          try {
            a2.setItem(c2, c2), a2.removeItem(c2);
          } catch (d2) {
            b2 = false;
          }
        }
        return b2;
      }
      function c(a2) {
        var b2 = typeof a2;
        return "string" === b2 && window[a2] instanceof Storage ? window[a2] : a2;
      }
      function d(a2) {
        return "[object Date]" === Object.prototype.toString.call(a2) && !isNaN(a2.getTime());
      }
      function e(a2, b2) {
        if (b2 = b2 || /* @__PURE__ */ new Date(), "number" == typeof a2 ? a2 = a2 === 1 / 0 ? l : new Date(b2.getTime() + 1e3 * a2) : "string" == typeof a2 && (a2 = new Date(a2)), a2 && !d(a2))
          throw new Error("`expires` parameter cannot be converted to a valid Date instance");
        return a2;
      }
      function f(a2) {
        var b2 = false;
        if (a2)
          if (a2.code)
            switch (a2.code) {
              case 22:
                b2 = true;
                break;
              case 1014:
                "NS_ERROR_DOM_QUOTA_REACHED" === a2.name && (b2 = true);
            }
          else
            -2147024882 === a2.number && (b2 = true);
        return b2;
      }
      function g(a2, b2) {
        this.c = (/* @__PURE__ */ new Date()).getTime(), b2 = b2 || m;
        var c2 = e(b2);
        this.e = c2.getTime(), this.v = a2;
      }
      function h(a2) {
        return "object" != typeof a2 ? false : a2 && "c" in a2 && "e" in a2 && "v" in a2 ? true : false;
      }
      function i(a2) {
        var b2 = (/* @__PURE__ */ new Date()).getTime();
        return b2 < a2.e;
      }
      function j(a2) {
        return "string" != typeof a2 && (console.warn(a2 + " used as a key, but it is not a string."), a2 = String(a2)), a2;
      }
      function k(e2) {
        var f2 = { storage: "localStorage", exp: 1 / 0 }, g2 = a(f2, e2), h2 = g2.exp;
        if (h2 && "number" != typeof h2 && !d(h2))
          throw new Error("Constructor `exp` parameter cannot be converted to a valid Date instance");
        m = h2;
        var i2 = c(g2.storage), j2 = b(i2);
        this.isSupported = function() {
          return j2;
        }, j2 ? (this.storage = i2, this.quotaExceedHandler = function(a2, b2, c2) {
          if (console.warn("Quota exceeded!"), c2 && c2.force === true) {
            var d2 = this.deleteAllExpires();
            console.warn("delete all expires CacheItem : [" + d2 + "] and try execute `set` method again!");
            try {
              c2.force = false, this.set(a2, b2, c2);
            } catch (e3) {
              console.warn(e3);
            }
          }
        }) : a(this, o);
      }
      var l = /* @__PURE__ */ new Date("Fri, 31 Dec 9999 23:59:59 UTC"), m = l, n = { serialize: function(a2) {
        return JSON.stringify(a2);
      }, deserialize: function(a2) {
        return a2 && JSON.parse(a2);
      } }, o = { set: function() {
      }, get: function() {
      }, "delete": function() {
      }, deleteAllExpires: function() {
      }, clear: function() {
      }, add: function() {
      }, replace: function() {
      }, touch: function() {
      } }, p = { set: function(b2, c2, d2) {
        if (b2 = j(b2), "number" == typeof d2 && (d2 = { exp: d2 }), d2 = a({ force: true }, d2), void 0 === c2)
          return this["delete"](b2);
        var e2 = n.serialize(c2), h2 = new g(e2, d2.exp);
        try {
          this.storage.setItem(b2, n.serialize(h2));
        } catch (i2) {
          f(i2) ? this.quotaExceedHandler(b2, e2, d2, i2) : console.error(i2);
        }
        return c2;
      }, get: function(a2) {
        a2 = j(a2);
        var b2 = null;
        try {
          b2 = n.deserialize(this.storage.getItem(a2));
        } catch (c2) {
          return null;
        }
        if (h(b2)) {
          if (i(b2)) {
            var d2 = b2.v;
            return n.deserialize(d2);
          }
          this["delete"](a2);
        }
        return null;
      }, "delete": function(a2) {
        return a2 = j(a2), this.storage.removeItem(a2), a2;
      }, deleteAllExpires: function() {
        for (var a2 = this.storage.length, b2 = [], c2 = this, d2 = 0; a2 > d2; d2++) {
          var e2 = this.storage.key(d2), f2 = null;
          try {
            f2 = n.deserialize(this.storage.getItem(e2));
          } catch (g2) {
          }
          if (null !== f2 && void 0 !== f2.e) {
            var h2 = (/* @__PURE__ */ new Date()).getTime();
            h2 >= f2.e && b2.push(e2);
          }
        }
        return b2.forEach(function(a3) {
          c2["delete"](a3);
        }), b2;
      }, clear: function() {
        this.storage.clear();
      }, add: function(b2, c2, d2) {
        b2 = j(b2), "number" == typeof d2 && (d2 = { exp: d2 }), d2 = a({ force: true }, d2);
        try {
          var e2 = n.deserialize(this.storage.getItem(b2));
          if (!h(e2) || !i(e2))
            return this.set(b2, c2, d2), true;
        } catch (f2) {
          return this.set(b2, c2, d2), true;
        }
        return false;
      }, replace: function(a2, b2, c2) {
        a2 = j(a2);
        var d2 = null;
        try {
          d2 = n.deserialize(this.storage.getItem(a2));
        } catch (e2) {
          return false;
        }
        if (h(d2)) {
          if (i(d2))
            return this.set(a2, b2, c2), true;
          this["delete"](a2);
        }
        return false;
      }, touch: function(a2, b2) {
        a2 = j(a2);
        var c2 = null;
        try {
          c2 = n.deserialize(this.storage.getItem(a2));
        } catch (d2) {
          return false;
        }
        if (h(c2)) {
          if (i(c2))
            return this.set(a2, this.get(a2), { exp: b2 }), true;
          this["delete"](a2);
        }
        return false;
      } };
      return k.prototype = p, k;
    });
  }
});
export default require_web_storage_cache_min();
/*! Bundled license information:

web-storage-cache/dist/web-storage-cache.min.js:
  (*!
      web-storage-cache -- Added `expires` attribute and serialize data with `JSON.parse` for the localStorage and sessionStorage.
      Version 1.1.1
      https://github.com/WQTeam/web-storage-cache
      (c) 2013-2016 WQTeam, MIT license
  *)
*/
//# sourceMappingURL=web-storage-cache.js.map
