import{be as j,bf as q,cA as le,c7 as O,ca as P,bo as te,bq as ie,bu as oe,r as k,d4 as R,at as de,cd as ne,bg as $,b as i,d as g,o as T,c as U,aW as w,a0 as p,a as e,bh as z,b7 as ce,dA as re,g as E,j as ue,t as ve,i as H,w as D,cR as be,bs as pe,U as me,b2 as fe,H as he,a8 as Ce,dB as ye,bi as Ke,bl as _e,__tla as ge}from"./index-Daqg4PFz.js";let G,J,Ae=Promise.all([(()=>{try{return ge}catch{}})()]).then(async()=>{const B=r=>te(r)||ie(r)||oe(r),L=j({accordion:Boolean,modelValue:{type:q([Array,String,Number]),default:()=>le([])}}),M={[O]:B,[P]:B},F=Symbol("collapseContextKey"),Q=g({name:"ElCollapse"});var X=z(g({...Q,props:L,emits:M,setup(r,{expose:A,emit:f}){const h=r,{activeNames:x,setActiveNames:u}=((l,v)=>{const o=k(R(l.modelValue)),m=c=>{o.value=c;const n=l.accordion?o.value[0]:o.value;v(O,n),v(P,n)};return de(()=>l.modelValue,()=>o.value=R(l.modelValue),{deep:!0}),ne(F,{activeNames:o,handleItemClick:c=>{if(l.accordion)m([o.value[0]===c?"":c]);else{const n=[...o.value],y=n.indexOf(c);y>-1?n.splice(y,1):n.push(c),m(n)}}}),{activeNames:o,setActiveNames:m}})(h,f),{rootKls:C}=(()=>{const l=$("collapse");return{rootKls:i(()=>l.b())}})();return A({activeNames:x,setActiveNames:u}),(l,v)=>(T(),U("div",{class:p(e(C))},[w(l.$slots,"default")],2))}}),[["__file","collapse.vue"]]);const Y=j({title:{type:String,default:""},name:{type:q([String,Number]),default:void 0},disabled:Boolean}),Z=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],ee=["id","aria-hidden","aria-labelledby"],ae=g({name:"ElCollapseItem"});var S=z(g({...ae,props:Y,setup(r,{expose:A}){const f=r,{focusing:h,id:x,isActive:u,handleFocus:C,handleHeaderClick:l,handleEnterClick:v}=(d=>{const a=ce(F),{namespace:t}=$("collapse"),b=k(!1),s=k(!1),K=re(),_=i(()=>K.current++),I=i(()=>{var W;return(W=d.name)!=null?W:`${t.value}-id-${K.prefix}-${e(_)}`}),se=i(()=>a==null?void 0:a.activeNames.value.includes(e(I)));return{focusing:b,id:_,isActive:se,handleFocus:()=>{setTimeout(()=>{s.value?s.value=!1:b.value=!0},50)},handleHeaderClick:()=>{d.disabled||(a==null||a.handleItemClick(e(I)),b.value=!1,s.value=!0)},handleEnterClick:()=>{a==null||a.handleItemClick(e(I))}}})(f),{arrowKls:o,headKls:m,rootKls:c,itemWrapperKls:n,itemContentKls:y,scopedContentId:N,scopedHeadId:V}=((d,{focusing:a,isActive:t,id:b})=>{const s=$("collapse"),K=i(()=>[s.b("item"),s.is("active",e(t)),s.is("disabled",d.disabled)]),_=i(()=>[s.be("item","header"),s.is("active",e(t)),{focusing:e(a)&&!d.disabled}]);return{arrowKls:i(()=>[s.be("item","arrow"),s.is("active",e(t))]),headKls:_,rootKls:K,itemWrapperKls:i(()=>s.be("item","wrap")),itemContentKls:i(()=>s.be("item","content")),scopedContentId:i(()=>s.b(`content-${e(b)}`)),scopedHeadId:i(()=>s.b(`head-${e(b)}`))}})(f,{focusing:h,isActive:u,id:x});return A({isActive:u}),(d,a)=>(T(),U("div",{class:p(e(c))},[E("button",{id:e(V),class:p(e(m)),"aria-expanded":e(u),"aria-controls":e(N),"aria-describedby":e(N),tabindex:d.disabled?-1:0,type:"button",onClick:a[0]||(a[0]=(...t)=>e(l)&&e(l)(...t)),onKeydown:a[1]||(a[1]=me(fe((...t)=>e(v)&&e(v)(...t),["stop","prevent"]),["space","enter"])),onFocus:a[2]||(a[2]=(...t)=>e(C)&&e(C)(...t)),onBlur:a[3]||(a[3]=t=>h.value=!1)},[w(d.$slots,"title",{},()=>[ue(ve(d.title),1)]),H(e(pe),{class:p(e(o))},{default:D(()=>[H(e(be))]),_:1},8,["class"])],42,Z),H(e(ye),null,{default:D(()=>[he(E("div",{id:e(N),role:"region",class:p(e(n)),"aria-hidden":!e(u),"aria-labelledby":e(V)},[E("div",{class:p(e(y))},[w(d.$slots,"default")],2)],10,ee),[[Ce,e(u)]])]),_:3})],2))}}),[["__file","collapse-item.vue"]]);J=Ke(X,{CollapseItem:S}),G=_e(S)});export{G as E,Ae as __tla,J as a};
