import{be as hi,d as mt,bg as ci,b as rt,h as ia,aW as li,r as X,bt as di,bu as kt,bo as zt,bf as St,bv as pi,bq as aa,bn as na,i as B,bw as Z,j as te,bx as ui,by as ra,bi as oa,p as I,bz as sa,C as ha,bA as ca,a as v,o as q,c as gt,H as la,a8 as da,g as ot,av as mi,a0 as F,a4 as ee,bB as pa,_ as Wt,bC as gi,w as H,z as ua,t as fi,l as Lt,a9 as ft,aO as ma,bD as ga,N as vi,at as fa,I as va,e as ba,__tla as ya}from"./index-Daqg4PFz.js";import{b as wa,__tla as xa}from"./profile-D5EjPWcr.js";import{E as bi,__tla as Ca}from"./el-avatar-DpVhY4zL.js";import{_ as Ma,__tla as Da}from"./Dialog-BjBBVYCI.js";import{_ as ka,__tla as Ba}from"./XButton-CfHP8l0l.js";import{a as _a}from"./avatar-BG6NdH5s.js";let yi,Oa=Promise.all([(()=>{try{return ya}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Ba}catch{}})()]).then(async()=>{const ie=mt({name:"ElSpaceItem",props:hi({prefixCls:{type:String}}),setup(t,{slots:i}){const e=ci("space"),n=rt(()=>`${t.prefixCls||e.b()}__item`);return()=>ia("div",{class:n.value},li(i,"default"))}}),ae={small:8,default:12,large:16},wi=mt({name:"ElSpace",props:hi({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:St([String,Object,Array]),default:""},style:{type:St([String,Array,Object]),default:""},alignment:{type:St(String),default:"center"},prefixCls:{type:String},spacer:{type:St([Object,String,Number,Array]),default:null,validator:t=>pi(t)||zt(t)||aa(t)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:na,validator:t=>zt(t)||kt(t)&&t.length===2&&t.every(zt)}}),setup(t,{slots:i}){const{classes:e,containerStyle:n,itemStyle:a}=function(h){const s=ci("space"),c=rt(()=>[s.b(),s.m(h.direction),h.class]),o=X(0),d=X(0),p=rt(()=>[h.wrap||h.fill?{flexWrap:"wrap"}:{},{alignItems:h.alignment},{rowGap:`${d.value}px`,columnGap:`${o.value}px`},h.style]),l=rt(()=>h.fill?{flexGrow:1,minWidth:`${h.fillRatio}%`}:{});return di(()=>{const{size:m="small",wrap:f,direction:g,fill:b}=h;if(kt(m)){const[y=0,w=0]=m;o.value=y,d.value=w}else{let y;y=zt(m)?m:ae[m||"small"]||ae.small,(f||b)&&g==="horizontal"?o.value=d.value=y:g==="horizontal"?(o.value=y,d.value=0):(d.value=y,o.value=0)}}),{classes:c,containerStyle:p,itemStyle:l}}(t);function r(h,s="",c=[]){const{prefixCls:o}=t;return h.forEach((d,p)=>{ui(d)?kt(d.children)&&d.children.forEach((l,m)=>{ui(l)&&kt(l.children)?r(l.children,`${s+m}-`,c):c.push(B(ie,{style:a.value,prefixCls:o,key:`nested-${s+m}`},{default:()=>[l]},Z.PROPS|Z.STYLE,["style","prefixCls"]))}):ra(d)&&c.push(B(ie,{style:a.value,prefixCls:o,key:`LoopKey${s+p}`},{default:()=>[d]},Z.PROPS|Z.STYLE,["style","prefixCls"]))}),c}return()=>{var h;const{spacer:s,direction:c}=t,o=li(i,"default",{key:0},()=>[]);if(((h=o.children)!=null?h:[]).length===0)return null;if(kt(o.children)){let d=r(o.children);if(s){const p=d.length-1;d=d.reduce((l,m,f)=>{const g=[...l,m];return f!==p&&g.push(B("span",{style:[a.value,c==="vertical"?"width: 100%":null],key:f},[pi(s)?s:te(s,Z.TEXT)],Z.STYLE)),g},[])}return B("div",{class:e.value,style:n.value},d,Z.STYLE|Z.CLASS)}return o.children}}}),xi=oa(wi);function ne(t,i){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),e.push.apply(e,n)}return e}function re(t){for(var i=1;i<arguments.length;i++){var e=arguments[i]!=null?arguments[i]:{};i%2?ne(Object(e),!0).forEach(function(n){Ci(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ne(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function oe(t){var i=function(e,n){if(typeof e!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}(t,"string");return typeof i=="symbol"?i:i+""}function Nt(t){return Nt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Nt(t)}function se(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,oe(n.key),n)}}function Ci(t,i,e){return(i=oe(i))in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}function he(t){return function(i){if(Array.isArray(i))return Rt(i)}(t)||function(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}(t)||function(i,e){if(i){if(typeof i=="string")return Rt(i,e);var n=Object.prototype.toString.call(i).slice(8,-1);if(n==="Object"&&i.constructor&&(n=i.constructor.name),n==="Map"||n==="Set")return Array.from(i);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rt(i,e)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Rt(t,i){(i==null||i>t.length)&&(i=t.length);for(var e=0,n=new Array(i);e<i;e++)n[e]=t[e];return n}var Bt=typeof window<"u"&&window.document!==void 0,U=Bt?window:{},Ht=!(!Bt||!U.document.documentElement)&&"ontouchstart"in U.document.documentElement,At=!!Bt&&"PointerEvent"in U,_="cropper",Yt="all",ce="crop",le="move",de="zoom",tt="e",et="w",st="s",G="n",vt="ne",bt="nw",yt="se",wt="sw",Xt="".concat(_,"-crop"),pe="".concat(_,"-disabled"),N="".concat(_,"-hidden"),ue="".concat(_,"-hide"),Mi="".concat(_,"-invisible"),_t="".concat(_,"-modal"),It="".concat(_,"-move"),xt="".concat(_,"Action"),Ot="".concat(_,"Preview"),jt="crop",me="move",ge="none",Pt="crop",Ut="cropend",$t="cropmove",qt="cropstart",fe="dblclick",ve=At?"pointerdown":Ht?"touchstart":"mousedown",be=At?"pointermove":Ht?"touchmove":"mousemove",ye=At?"pointerup pointercancel":Ht?"touchend touchcancel":"mouseup",we="ready",xe="resize",Ce="wheel",Vt="zoom",Me="image/jpeg",Di=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,ki=/^data:/,Bi=/^data:image\/jpeg;base64,/,_i=/^img|canvas$/i,De={viewMode:0,dragMode:jt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},Oi=Number.isNaN||U.isNaN;function C(t){return typeof t=="number"&&!Oi(t)}var ke=function(t){return t>0&&t<1/0};function Ft(t){return t===void 0}function it(t){return Nt(t)==="object"&&t!==null}var Ti=Object.prototype.hasOwnProperty;function ht(t){if(!it(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&Ti.call(e,"isPrototypeOf")}catch{return!1}}function R(t){return typeof t=="function"}var Ei=Array.prototype.slice;function Be(t){return Array.from?Array.from(t):Ei.call(t)}function E(t,i){return t&&R(i)&&(Array.isArray(t)||C(t.length)?Be(t).forEach(function(e,n){i.call(t,e,n,t)}):it(t)&&Object.keys(t).forEach(function(e){i.call(t,t[e],e,t)})),t}var O=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return it(t)&&e.length>0&&e.forEach(function(a){it(a)&&Object.keys(a).forEach(function(r){t[r]=a[r]})}),t},zi=/\.\d*(?:0|9){12}\d*$/;function ct(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return zi.test(t)?Math.round(t*i)/i:t}var Si=/^width|height|left|top|marginLeft|marginTop$/;function J(t,i){var e=t.style;E(i,function(n,a){Si.test(a)&&C(n)&&(n="".concat(n,"px")),e[a]=n})}function z(t,i){if(i)if(C(t.length))E(t,function(n){z(n,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i}}function $(t,i){i&&(C(t.length)?E(t,function(e){$(e,i)}):t.classList?t.classList.remove(i):t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,"")))}function lt(t,i,e){i&&(C(t.length)?E(t,function(n){lt(n,i,e)}):e?z(t,i):$(t,i))}var Wi=/([a-z\d])([A-Z])/g;function Gt(t){return t.replace(Wi,"$1-$2").toLowerCase()}function Jt(t,i){return it(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Gt(i)))}function Ct(t,i,e){it(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(Gt(i)),e)}var _e=/\s\s*/,Oe=function(){var t=!1;if(Bt){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(a){i=a}});U.addEventListener("test",e,n),U.removeEventListener("test",e,n)}return t}();function j(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(_e).forEach(function(r){if(!Oe){var h=t.listeners;h&&h[r]&&h[r][e]&&(a=h[r][e],delete h[r][e],Object.keys(h[r]).length===0&&delete h[r],Object.keys(h).length===0&&delete t.listeners)}t.removeEventListener(r,a,n)})}function Y(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(_e).forEach(function(r){if(n.once&&!Oe){var h=t.listeners,s=h===void 0?{}:h;a=function(){delete s[r][e],t.removeEventListener(r,a,n);for(var c=arguments.length,o=new Array(c),d=0;d<c;d++)o[d]=arguments[d];e.apply(t,o)},s[r]||(s[r]={}),s[r][e]&&t.removeEventListener(r,s[r][e],n),s[r][e]=a,t.listeners=s}t.addEventListener(r,a,n)})}function dt(t,i,e){var n;return R(Event)&&R(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function Te(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var Kt=U.location,Li=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Ee(t){var i=t.match(Li);return i!==null&&(i[1]!==Kt.protocol||i[2]!==Kt.hostname||i[3]!==Kt.port)}function ze(t){var i="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+i}function Mt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,a=t.translateX,r=t.translateY,h=[];C(a)&&a!==0&&h.push("translateX(".concat(a,"px)")),C(r)&&r!==0&&h.push("translateY(".concat(r,"px)")),C(i)&&i!==0&&h.push("rotate(".concat(i,"deg)")),C(e)&&e!==1&&h.push("scaleX(".concat(e,")")),C(n)&&n!==1&&h.push("scaleY(".concat(n,")"));var s=h.length?h.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Tt(t,i){var e=t.pageX,n=t.pageY,a={endX:e,endY:n};return i?a:re({startX:e,startY:n},a)}function K(t){var i=t.aspectRatio,e=t.height,n=t.width,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=ke(n),h=ke(e);if(r&&h){var s=e*i;a==="contain"&&s>n||a==="cover"&&s<n?e=n/i:n=e*i}else r?e=n/i:h&&(n=e*i);return{width:n,height:e}}var Se=String.fromCharCode,Ni=/^data:.*,/;function Ri(t){var i,e=new DataView(t);try{var n,a,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var h=e.byteLength,s=2;s+1<h;){if(e.getUint8(s)===255&&e.getUint8(s+1)===225){a=s;break}s+=1}if(a){var c=a+10;if(function(f,g,b){var y="";b+=g;for(var w=g;w<b;w+=1)y+=Se(f.getUint8(w));return y}(e,a+4,4)==="Exif"){var o=e.getUint16(c);if(((n=o===18761)||o===19789)&&e.getUint16(c+2,n)===42){var d=e.getUint32(c+4,n);d>=8&&(r=c+d)}}}if(r){var p,l,m=e.getUint16(r,n);for(l=0;l<m;l+=1)if(p=r+12*l+2,e.getUint16(p,n)===274){p+=8,i=e.getUint16(p,n),e.setUint16(p,1,n);break}}}catch{i=1}return i}var Hi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,a=Number(i.minContainerWidth),r=Number(i.minContainerHeight);z(n,N),$(t,N);var h={width:Math.max(e.offsetWidth,a>=0?a:200),height:Math.max(e.offsetHeight,r>=0?r:100)};this.containerData=h,J(n,{width:h.width,height:h.height}),z(t,N),$(n,N)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180==90,a=n?i.naturalHeight:i.naturalWidth,r=n?i.naturalWidth:i.naturalHeight,h=a/r,s=t.width,c=t.height;t.height*h>t.width?e===3?s=t.height*h:c=t.width/h:e===3?c=t.width/h:s=t.height*h;var o={aspectRatio:h,naturalWidth:a,naturalHeight:r,width:s,height:c};this.canvasData=o,this.limited=e===1||e===2,this.limitCanvas(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.left=(t.width-o.width)/2,o.top=(t.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCanvasData=O({},o)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=e.viewMode,s=a.aspectRatio,c=this.cropped&&r;if(t){var o=Number(e.minCanvasWidth)||0,d=Number(e.minCanvasHeight)||0;h>1?(o=Math.max(o,n.width),d=Math.max(d,n.height),h===3&&(d*s>o?o=d*s:d=o/s)):h>0&&(o?o=Math.max(o,c?r.width:0):d?d=Math.max(d,c?r.height:0):c&&(o=r.width,(d=r.height)*s>o?o=d*s:d=o/s));var p=K({aspectRatio:s,width:o,height:d});o=p.width,d=p.height,a.minWidth=o,a.minHeight=d,a.maxWidth=1/0,a.maxHeight=1/0}if(i)if(h>(c?0:1)){var l=n.width-a.width,m=n.height-a.height;a.minLeft=Math.min(0,l),a.minTop=Math.min(0,m),a.maxLeft=Math.max(0,l),a.maxTop=Math.max(0,m),c&&this.limited&&(a.minLeft=Math.min(r.left,r.left+(r.width-a.width)),a.minTop=Math.min(r.top,r.top+(r.height-a.height)),a.maxLeft=r.left,a.maxTop=r.top,h===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,l),a.maxLeft=Math.max(0,l)),a.height>=n.height&&(a.minTop=Math.min(0,m),a.maxTop=Math.max(0,m))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var a=function(o){var d=o.width,p=o.height,l=o.degree;if((l=Math.abs(l)%180)==90)return{width:p,height:d};var m=l%90*Math.PI/180,f=Math.sin(m),g=Math.cos(m),b=d*g+p*f,y=d*f+p*g;return l>90?{width:y,height:b}:{width:b,height:y}}({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),r=a.width,h=a.height,s=e.width*(r/e.naturalWidth),c=e.height*(h/e.naturalHeight);e.left-=(s-e.width)/2,e.top-=(c-e.height)/2,e.width=s,e.height=c,e.aspectRatio=r/h,e.naturalWidth=r,e.naturalHeight=h,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,J(this.canvas,O({width:e.width,height:e.height},Mt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),a=e.naturalHeight*(i.height/i.naturalHeight);O(e,{width:n,height:a,left:(i.width-n)/2,top:(i.height-a)/2}),J(this.image,O({width:e.width,height:e.height},Mt(O({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,a={width:i.width,height:i.height};e&&(i.height*e>i.width?a.height=a.width/e:a.width=a.height*e),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=i.left+(i.width-a.width)/2,a.top=i.top+(i.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=O({},a)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=this.limited,s=e.aspectRatio;if(t){var c=Number(e.minCropBoxWidth)||0,o=Number(e.minCropBoxHeight)||0,d=h?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,p=h?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;c=Math.min(c,n.width),o=Math.min(o,n.height),s&&(c&&o?o*s>c?o=c/s:c=o*s:c?o=c/s:o&&(c=o*s),p*s>d?p=d/s:d=p*s),r.minWidth=Math.min(c,d),r.minHeight=Math.min(o,p),r.maxWidth=d,r.maxHeight=p}i&&(h?(r.minLeft=Math.max(0,a.left),r.minTop=Math.max(0,a.top),r.maxLeft=Math.min(n.width,a.left+a.width)-r.width,r.maxTop=Math.min(n.height,a.top+a.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=n.width-r.width,r.maxTop=n.height-r.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&Ct(this.face,xt,e.width>=i.width&&e.height>=i.height?le:Yt),J(this.cropBox,O({width:e.width,height:e.height},Mt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),dt(this.element,Pt,this.getData())}},Ai={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,a=t.alt||"The image to preview",r=document.createElement("img");if(i&&(r.crossOrigin=i),r.src=n,r.alt=a,this.viewBox.appendChild(r),this.viewBoxImage=r,e){var h=e;typeof e=="string"?h=t.ownerDocument.querySelectorAll(e):e.querySelector&&(h=[e]),this.previews=h,E(h,function(s){var c=document.createElement("img");Ct(s,Ot,{width:s.offsetWidth,height:s.offsetHeight,html:s.innerHTML}),i&&(c.crossOrigin=i),c.src=n,c.alt=a,c.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',s.innerHTML="",s.appendChild(c)})}},resetPreview:function(){E(this.previews,function(t){var i=Jt(t,Ot);J(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(e,n){if(it(e[n]))try{delete e[n]}catch{e[n]=void 0}else if(e.dataset)try{delete e.dataset[n]}catch{e.dataset[n]=void 0}else e.removeAttribute("data-".concat(Gt(n)))}(t,Ot)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,a=e.height,r=t.width,h=t.height,s=e.left-i.left-t.left,c=e.top-i.top-t.top;this.cropped&&!this.disabled&&(J(this.viewBoxImage,O({width:r,height:h},Mt(O({translateX:-s,translateY:-c},t)))),E(this.previews,function(o){var d=Jt(o,Ot),p=d.width,l=d.height,m=p,f=l,g=1;n&&(f=a*(g=p/n)),a&&f>l&&(m=n*(g=l/a),f=l),J(o,{width:m,height:f}),J(o.getElementsByTagName("img")[0],O({width:r*g,height:h*g},Mt(O({translateX:-s*g,translateY:-c*g},t))))}))}},Yi={bind:function(){var t=this.element,i=this.options,e=this.cropper;R(i.cropstart)&&Y(t,qt,i.cropstart),R(i.cropmove)&&Y(t,$t,i.cropmove),R(i.cropend)&&Y(t,Ut,i.cropend),R(i.crop)&&Y(t,Pt,i.crop),R(i.zoom)&&Y(t,Vt,i.zoom),Y(e,ve,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&Y(e,Ce,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&Y(e,fe,this.onDblclick=this.dblclick.bind(this)),Y(t.ownerDocument,be,this.onCropMove=this.cropMove.bind(this)),Y(t.ownerDocument,ye,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&Y(window,xe,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;R(i.cropstart)&&j(t,qt,i.cropstart),R(i.cropmove)&&j(t,$t,i.cropmove),R(i.cropend)&&j(t,Ut,i.cropend),R(i.crop)&&j(t,Pt,i.crop),R(i.zoom)&&j(t,Vt,i.zoom),j(e,ve,this.onCropStart),i.zoomable&&i.zoomOnWheel&&j(e,Ce,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&j(e,fe,this.onDblclick),j(t.ownerDocument,be,this.onCropMove),j(t.ownerDocument,ye,this.onCropEnd),i.responsive&&j(window,xe,this.onResize)}},Xi={resize:function(){if(!this.disabled){var t,i,e=this.options,n=this.container,a=this.containerData,r=n.offsetWidth/a.width,h=n.offsetHeight/a.height,s=Math.abs(r-1)>Math.abs(h-1)?r:h;s!==1&&(e.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(E(t,function(c,o){t[o]=c*s})),this.setCropBoxData(E(i,function(c,o){i[o]=c*s}))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===ge||this.setDragMode((t=this.dragBox,i=Xt,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?me:jt))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(C(i)&&i!==1||C(e)&&e!==0||t.ctrlKey))){var n,a=this.options,r=this.pointers;t.changedTouches?E(t.changedTouches,function(h){r[h.identifier]=Tt(h)}):r[t.pointerId||0]=Tt(t),n=Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?de:Jt(t.target,xt),Di.test(n)&&dt(this.element,qt,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===ce&&(this.cropping=!0,z(this.dragBox,_t)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),dt(this.element,$t,{originalEvent:t,action:i})!==!1&&(t.changedTouches?E(t.changedTouches,function(n){O(e[n.identifier]||{},Tt(n,!0))}):O(e[t.pointerId||0]||{},Tt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?E(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,lt(this.dragBox,_t,this.cropped&&this.options.modal)),dt(this.element,Ut,{originalEvent:t,action:i}))}}},Ii={change:function(t){var i,e=this.options,n=this.canvasData,a=this.containerData,r=this.cropBoxData,h=this.pointers,s=this.action,c=e.aspectRatio,o=r.left,d=r.top,p=r.width,l=r.height,m=o+p,f=d+l,g=0,b=0,y=a.width,w=a.height,D=!0;!c&&t.shiftKey&&(c=p&&l?p/l:1),this.limited&&(g=r.minLeft,b=r.minTop,y=g+Math.min(a.width,n.width,n.left+n.width),w=b+Math.min(a.height,n.height,n.top+n.height));var M=h[Object.keys(h)[0]],u={x:M.endX-M.startX,y:M.endY-M.startY},x=function(k){switch(k){case tt:m+u.x>y&&(u.x=y-m);break;case et:o+u.x<g&&(u.x=g-o);break;case G:d+u.y<b&&(u.y=b-d);break;case st:f+u.y>w&&(u.y=w-f)}};switch(s){case Yt:o+=u.x,d+=u.y;break;case tt:if(u.x>=0&&(m>=y||c&&(d<=b||f>=w))){D=!1;break}x(tt),(p+=u.x)<0&&(s=et,o-=p=-p),c&&(l=p/c,d+=(r.height-l)/2);break;case G:if(u.y<=0&&(d<=b||c&&(o<=g||m>=y))){D=!1;break}x(G),l-=u.y,d+=u.y,l<0&&(s=st,d-=l=-l),c&&(p=l*c,o+=(r.width-p)/2);break;case et:if(u.x<=0&&(o<=g||c&&(d<=b||f>=w))){D=!1;break}x(et),p-=u.x,o+=u.x,p<0&&(s=tt,o-=p=-p),c&&(l=p/c,d+=(r.height-l)/2);break;case st:if(u.y>=0&&(f>=w||c&&(o<=g||m>=y))){D=!1;break}x(st),(l+=u.y)<0&&(s=G,d-=l=-l),c&&(p=l*c,o+=(r.width-p)/2);break;case vt:if(c){if(u.y<=0&&(d<=b||m>=y)){D=!1;break}x(G),l-=u.y,d+=u.y,p=l*c}else x(G),x(tt),u.x>=0?m<y?p+=u.x:u.y<=0&&d<=b&&(D=!1):p+=u.x,u.y<=0?d>b&&(l-=u.y,d+=u.y):(l-=u.y,d+=u.y);p<0&&l<0?(s=wt,d-=l=-l,o-=p=-p):p<0?(s=bt,o-=p=-p):l<0&&(s=yt,d-=l=-l);break;case bt:if(c){if(u.y<=0&&(d<=b||o<=g)){D=!1;break}x(G),l-=u.y,d+=u.y,p=l*c,o+=r.width-p}else x(G),x(et),u.x<=0?o>g?(p-=u.x,o+=u.x):u.y<=0&&d<=b&&(D=!1):(p-=u.x,o+=u.x),u.y<=0?d>b&&(l-=u.y,d+=u.y):(l-=u.y,d+=u.y);p<0&&l<0?(s=yt,d-=l=-l,o-=p=-p):p<0?(s=vt,o-=p=-p):l<0&&(s=wt,d-=l=-l);break;case wt:if(c){if(u.x<=0&&(o<=g||f>=w)){D=!1;break}x(et),p-=u.x,o+=u.x,l=p/c}else x(st),x(et),u.x<=0?o>g?(p-=u.x,o+=u.x):u.y>=0&&f>=w&&(D=!1):(p-=u.x,o+=u.x),u.y>=0?f<w&&(l+=u.y):l+=u.y;p<0&&l<0?(s=vt,d-=l=-l,o-=p=-p):p<0?(s=yt,o-=p=-p):l<0&&(s=bt,d-=l=-l);break;case yt:if(c){if(u.x>=0&&(m>=y||f>=w)){D=!1;break}x(tt),l=(p+=u.x)/c}else x(st),x(tt),u.x>=0?m<y?p+=u.x:u.y>=0&&f>=w&&(D=!1):p+=u.x,u.y>=0?f<w&&(l+=u.y):l+=u.y;p<0&&l<0?(s=bt,d-=l=-l,o-=p=-p):p<0?(s=wt,o-=p=-p):l<0&&(s=vt,d-=l=-l);break;case le:this.move(u.x,u.y),D=!1;break;case de:this.zoom(function(k){var A=re({},k),L=0;return E(k,function(S,Q){delete A[Q],E(A,function(W){var T=Math.abs(S.startX-W.startX),Dt=Math.abs(S.startY-W.startY),at=Math.abs(S.endX-W.endX),pt=Math.abs(S.endY-W.endY),V=Math.sqrt(T*T+Dt*Dt),ut=(Math.sqrt(at*at+pt*pt)-V)/V;Math.abs(ut)>Math.abs(L)&&(L=ut)})}),L}(h),t),D=!1;break;case ce:if(!u.x||!u.y){D=!1;break}i=Te(this.cropper),o=M.startX-i.left,d=M.startY-i.top,p=r.minWidth,l=r.minHeight,u.x>0?s=u.y>0?yt:vt:u.x<0&&(o-=p,s=u.y>0?wt:bt),u.y<0&&(d-=l),this.cropped||($(this.cropBox,N),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}D&&(r.width=p,r.height=l,r.left=o,r.top=d,this.action=s,this.renderCropBox()),E(h,function(k){k.startX=k.endX,k.startY=k.endY})}},ji={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&z(this.dragBox,_t),$(this.cropBox,N),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=O({},this.initialImageData),this.canvasData=O({},this.initialCanvasData),this.cropBoxData=O({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(O(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),$(this.dragBox,_t),z(this.cropBox,N)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,E(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,$(this.cropper,pe)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,z(this.cropper,pe)),this},destroy:function(){var t=this.element;return t[_]?(t[_]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,a=e.top;return this.moveTo(Ft(t)?t:n+Number(t),Ft(i)?i:a+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(C(t)&&(e.left=t,n=!0),C(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,a=this.canvasData,r=a.width,h=a.height,s=a.naturalWidth,c=a.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&n.zoomable){var o=s*t,d=c*t;if(dt(this.element,Vt,{ratio:t,oldRatio:r/s,originalEvent:e})===!1)return this;if(e){var p=this.pointers,l=Te(this.cropper),m=p&&Object.keys(p).length?function(f){var g=0,b=0,y=0;return E(f,function(w){var D=w.startX,M=w.startY;g+=D,b+=M,y+=1}),{pageX:g/=y,pageY:b/=y}}(p):{pageX:e.pageX,pageY:e.pageY};a.left-=(o-r)*((m.pageX-l.left-a.left)/r),a.top-=(d-h)*((m.pageY-l.top-a.top)/h)}else ht(i)&&C(i.x)&&C(i.y)?(a.left-=(o-r)*((i.x-a.left)/r),a.top-=(d-h)*((i.y-a.top)/h)):(a.left-=(o-r)/2,a.top-=(d-h)/2);a.width=o,a.height=d,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return C(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,C(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(C(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(C(t)&&(e.scaleX=t,n=!0),C(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.options,n=this.imageData,a=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var h=n.width/n.naturalWidth;if(E(t,function(o,d){t[d]=o/h}),i){var s=Math.round(t.y+t.height),c=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=c-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return e.rotatable&&(t.rotate=n.rotate||0),e.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&ht(t)){var r=!1;i.rotatable&&C(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,r=!0),i.scalable&&(C(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,r=!0),C(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var h=e.width/e.naturalWidth;C(t.x)&&(a.left=t.x*h+n.left),C(t.y)&&(a.top=t.y*h+n.top),C(t.width)&&(a.width=t.width*h),C(t.height)&&(a.height=t.height*h),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?O({},this.containerData):{}},getImageData:function(){return this.sized?O({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&E(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&ht(t)&&(C(t.left)&&(i.left=t.left),C(t.top)&&(i.top=t.top),C(t.width)?(i.width=t.width,i.height=t.width/e):C(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,e,n=this.cropBoxData,a=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&ht(t)&&(C(t.left)&&(n.left=t.left),C(t.top)&&(n.top=t.top),C(t.width)&&t.width!==n.width&&(i=!0,n.width=t.width),C(t.height)&&t.height!==n.height&&(e=!0,n.height=t.height),a&&(i?n.height=n.width/a:e&&(n.width=n.height*a)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=function(pt,V,ut,nt){var Xe=V.aspectRatio,Ui=V.naturalWidth,$i=V.naturalHeight,Ie=V.rotate,qi=Ie===void 0?0:Ie,je=V.scaleX,Vi=je===void 0?1:je,Pe=V.scaleY,Fi=Pe===void 0?1:Pe,Ue=ut.aspectRatio,Gi=ut.naturalWidth,Ji=ut.naturalHeight,$e=nt.fillColor,Ki=$e===void 0?"transparent":$e,qe=nt.imageSmoothingEnabled,Qi=qe===void 0||qe,Ve=nt.imageSmoothingQuality,Zi=Ve===void 0?"low":Ve,Fe=nt.maxWidth,Ge=Fe===void 0?1/0:Fe,Je=nt.maxHeight,Ke=Je===void 0?1/0:Je,Qe=nt.minWidth,Ze=Qe===void 0?0:Qe,ti=nt.minHeight,ei=ti===void 0?0:ti,Et=document.createElement("canvas"),P=Et.getContext("2d"),ii=K({aspectRatio:Ue,width:Ge,height:Ke}),ai=K({aspectRatio:Ue,width:Ze,height:ei},"cover"),Qt=Math.min(ii.width,Math.max(ai.width,Gi)),Zt=Math.min(ii.height,Math.max(ai.height,Ji)),ni=K({aspectRatio:Xe,width:Ge,height:Ke}),ri=K({aspectRatio:Xe,width:Ze,height:ei},"cover"),oi=Math.min(ni.width,Math.max(ri.width,Ui)),si=Math.min(ni.height,Math.max(ri.height,$i)),ta=[-oi/2,-si/2,oi,si];return Et.width=ct(Qt),Et.height=ct(Zt),P.fillStyle=Ki,P.fillRect(0,0,Qt,Zt),P.save(),P.translate(Qt/2,Zt/2),P.rotate(qi*Math.PI/180),P.scale(Vi,Fi),P.imageSmoothingEnabled=Qi,P.imageSmoothingQuality=Zi,P.drawImage.apply(P,[pt].concat(he(ta.map(function(ea){return Math.floor(ct(ea))})))),P.restore(),Et}(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),a=n.x,r=n.y,h=n.width,s=n.height,c=e.width/Math.floor(i.naturalWidth);c!==1&&(a*=c,r*=c,h*=c,s*=c);var o=h/s,d=K({aspectRatio:o,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=K({aspectRatio:o,width:t.minWidth||0,height:t.minHeight||0},"cover"),l=K({aspectRatio:o,width:t.width||(c!==1?e.width:h),height:t.height||(c!==1?e.height:s)}),m=l.width,f=l.height;m=Math.min(d.width,Math.max(p.width,m)),f=Math.min(d.height,Math.max(p.height,f));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=ct(m),g.height=ct(f),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,f);var y=t.imageSmoothingEnabled,w=y===void 0||y,D=t.imageSmoothingQuality;b.imageSmoothingEnabled=w,D&&(b.imageSmoothingQuality=D);var M,u,x,k,A,L,S=e.width,Q=e.height,W=a,T=r;W<=-h||W>S?(W=0,M=0,x=0,A=0):W<=0?(x=-W,W=0,A=M=Math.min(S,h+W)):W<=S&&(x=0,A=M=Math.min(h,S-W)),M<=0||T<=-s||T>Q?(T=0,u=0,k=0,L=0):T<=0?(k=-T,T=0,L=u=Math.min(Q,s+T)):T<=Q&&(k=0,L=u=Math.min(s,Q-T));var Dt=[W,T,M,u];if(A>0&&L>0){var at=m/h;Dt.push(x*at,k*at,A*at,L*at)}return b.drawImage.apply(b,[e].concat(he(Dt.map(function(pt){return Math.floor(ct(pt))})))),g},setAspectRatio:function(t){var i=this.options;return this.disabled||Ft(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=t===jt,r=i.movable&&t===me;t=a||r?t:ge,i.dragMode=t,Ct(e,xt,t),lt(e,Xt,a),lt(e,It,r),i.cropBoxMovable||(Ct(n,xt,t),lt(n,Xt,a),lt(n,It,r))}return this}},Pi=U.Cropper,We=function(){function t(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(function(h,s){if(!(h instanceof s))throw new TypeError("Cannot call a class as a function")}(this,t),!a||!_i.test(a.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=a,this.options=O({},De,ht(r)&&r),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return i=t,n=[{key:"noConflict",value:function(){return window.Cropper=Pi,t}},{key:"setDefaults",value:function(a){O(De,ht(a)&&a)}}],(e=[{key:"init",value:function(){var a,r=this.element,h=r.tagName.toLowerCase();if(!r[_]){if(r[_]=this,h==="img"){if(this.isImg=!0,a=r.getAttribute("src")||"",this.originalUrl=a,!a)return;a=r.src}else h==="canvas"&&window.HTMLCanvasElement&&(a=r.toDataURL());this.load(a)}}},{key:"load",value:function(a){var r=this;if(a){this.url=a,this.imageData={};var h=this.element,s=this.options;if(s.rotatable||s.scalable||(s.checkOrientation=!1),s.checkOrientation&&window.ArrayBuffer)if(ki.test(a))Bi.test(a)?this.read((c=a.replace(Ni,""),o=atob(c),d=new ArrayBuffer(o.length),E(p=new Uint8Array(d),function(f,g){p[g]=o.charCodeAt(g)}),d)):this.clone();else{var c,o,d,p,l=new XMLHttpRequest,m=this.clone.bind(this);this.reloading=!0,this.xhr=l,l.onabort=m,l.onerror=m,l.ontimeout=m,l.onprogress=function(){l.getResponseHeader("content-type")!==Me&&l.abort()},l.onload=function(){r.read(l.response)},l.onloadend=function(){r.reloading=!1,r.xhr=null},s.checkCrossOrigin&&Ee(a)&&h.crossOrigin&&(a=ze(a)),l.open("GET",a,!0),l.responseType="arraybuffer",l.withCredentials=h.crossOrigin==="use-credentials",l.send()}else this.clone()}}},{key:"read",value:function(a){var r=this.options,h=this.imageData,s=Ri(a),c=0,o=1,d=1;if(s>1){this.url=function(l,m){for(var f=[],g=new Uint8Array(l);g.length>0;)f.push(Se.apply(null,Be(g.subarray(0,8192)))),g=g.subarray(8192);return"data:".concat(m,";base64,").concat(btoa(f.join("")))}(a,Me);var p=function(l){var m=0,f=1,g=1;switch(l){case 2:f=-1;break;case 3:m=-180;break;case 4:g=-1;break;case 5:m=90,g=-1;break;case 6:m=90;break;case 7:m=90,f=-1;break;case 8:m=-90}return{rotate:m,scaleX:f,scaleY:g}}(s);c=p.rotate,o=p.scaleX,d=p.scaleY}r.rotatable&&(h.rotate=c),r.scalable&&(h.scaleX=o,h.scaleY=d),this.clone()}},{key:"clone",value:function(){var a=this.element,r=this.url,h=a.crossOrigin,s=r;this.options.checkCrossOrigin&&Ee(r)&&(h||(h="anonymous"),s=ze(r)),this.crossOrigin=h,this.crossOriginUrl=s;var c=document.createElement("img");h&&(c.crossOrigin=h),c.src=s||r,c.alt=a.alt||"The image to crop",this.image=c,c.onload=this.start.bind(this),c.onerror=this.stop.bind(this),z(c,ue),a.parentNode.insertBefore(c,a.nextSibling)}},{key:"start",value:function(){var a=this,r=this.image;r.onload=null,r.onerror=null,this.sizing=!0;var h=U.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(U.navigator.userAgent),s=function(d,p){O(a.imageData,{naturalWidth:d,naturalHeight:p,aspectRatio:d/p}),a.initialImageData=O({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(!r.naturalWidth||h){var c=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=c,c.onload=function(){s(c.width,c.height),h||o.removeChild(c)},c.src=r.src,h||(c.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(c))}else s(r.naturalWidth,r.naturalHeight)}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var a=this.element,r=this.options,h=this.image,s=a.parentNode,c=document.createElement("div");c.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=c.querySelector(".".concat(_,"-container")),d=o.querySelector(".".concat(_,"-canvas")),p=o.querySelector(".".concat(_,"-drag-box")),l=o.querySelector(".".concat(_,"-crop-box")),m=l.querySelector(".".concat(_,"-face"));this.container=s,this.cropper=o,this.canvas=d,this.dragBox=p,this.cropBox=l,this.viewBox=o.querySelector(".".concat(_,"-view-box")),this.face=m,d.appendChild(h),z(a,N),s.insertBefore(o,a.nextSibling),$(h,ue),this.initPreview(),this.bind(),r.initialAspectRatio=Math.max(0,r.initialAspectRatio)||NaN,r.aspectRatio=Math.max(0,r.aspectRatio)||NaN,r.viewMode=Math.max(0,Math.min(3,Math.round(r.viewMode)))||0,z(l,N),r.guides||z(l.getElementsByClassName("".concat(_,"-dashed")),N),r.center||z(l.getElementsByClassName("".concat(_,"-center")),N),r.background&&z(o,"".concat(_,"-bg")),r.highlight||z(m,Mi),r.cropBoxMovable&&(z(m,It),Ct(m,xt,Yt)),r.cropBoxResizable||(z(l.getElementsByClassName("".concat(_,"-line")),N),z(l.getElementsByClassName("".concat(_,"-point")),N)),this.render(),this.ready=!0,this.setDragMode(r.dragMode),r.autoCrop&&this.crop(),this.setData(r.data),R(r.ready)&&Y(a,we,r.ready,{once:!0}),dt(a,we)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),$(this.element,N)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&se(i.prototype,e),n&&se(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,e,n}();O(We.prototype,Hi,Ai,Yi,Xi,Ii,ji);let Le,Ne,Re,He,Ae,Ye;Le=["alt","crossorigin","src"],Ne=Wt(mt({name:"Cropper",__name:"Cropper",props:{src:I.string.def(""),alt:I.string.def(""),circled:I.bool.def(!1),realTimePreview:I.bool.def(!0),height:I.string.def("360px"),crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},emits:["cropend","ready","cropendError"],setup(t,{emit:i}){const e={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},n=t,a=i,r=sa(),h=X(),s=X(),c=X(!1),{getPrefixCls:o}=ee(),d=o("cropper-image"),p=pa(g,80),l=rt(()=>({height:n.height,maxWidth:"100%",...n.imageStyle})),m=rt(()=>[d,r.class,{[`${d}--circled`]:n.circled}]),f=rt(()=>({height:`${n.height}`.replace(/px/,"")+"px"}));function g(){n.realTimePreview&&function(){if(!s.value)return;let b=s.value.getData();(n.circled?function(){const y=s.value.getCroppedCanvas(),w=document.createElement("canvas"),D=w.getContext("2d"),M=y.width,u=y.height;return w.width=M,w.height=u,D.imageSmoothingEnabled=!0,D.drawImage(y,0,0,M,u),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(M/2,u/2,Math.min(M,u)/2,0,2*Math.PI,!0),D.fill(),w}():s.value.getCroppedCanvas()).toBlob(y=>{if(!y)return;let w=new FileReader;w.readAsDataURL(y),w.onloadend=D=>{var M;a("cropend",{imgBase64:((M=D.target)==null?void 0:M.result)??"",imgInfo:b})},w.onerror=()=>{a("cropendError")}},"image/png")}()}return ha(async function(){const b=v(h);b&&(s.value=new We(b,{...e,ready:()=>{c.value=!0,g(),a("ready",s.value)},crop(){p()},zoom(){p()},cropmove(){p()},...n.options}))}),ca(()=>{var b;(b=s.value)==null||b.destroy()}),(b,y)=>(q(),gt("div",{class:F(v(m)),style:mi(v(f))},[la(ot("img",{ref_key:"imgElRef",ref:h,alt:t.alt,crossorigin:t.crossorigin,src:t.src,style:mi(v(l))},null,12,Le),[[da,v(c)]])],6))}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Cropper/src/Cropper.vue"]]),Re=["alt","src"],He=Wt(mt({name:"CopperModal",__name:"CopperModal",props:{srcValue:I.string.def(""),circled:I.bool.def(!0)},emits:["uploadSuccess"],setup(t,{expose:i,emit:e}){const n=t,a=e,{t:r}=gi.useI18n(),{getPrefixCls:h}=ee(),s=h("cropper-am"),c=X(n.srcValue),o=X(""),d=X(),p=X(!1);let l="",m=1,f=1;function g(M){const u=new FileReader;return u.readAsDataURL(M),c.value="",o.value="",u.onload=function(x){var k;c.value=((k=x.target)==null?void 0:k.result)??"",l=M.name},!1}function b({imgBase64:M}){o.value=M}function y(M){d.value=M}function w(M,u){var x,k;M==="scaleX"&&(m=u=m===-1?1:-1),M==="scaleY"&&(f=u=f===-1?1:-1),(k=(x=d==null?void 0:d.value)==null?void 0:x[M])==null||k.call(x,u)}async function D(){const M=(u=>{const x=u.split(","),k=x[0].match(/:(.*?);/)[1],A=window.atob(x[1]);let L=A.length;const S=new Uint8Array(L);for(;L--;)S[L]=A.charCodeAt(L);return new Blob([S],{type:k})})(o.value);a("uploadSuccess",{source:o.value,data:M,filename:l})}return i({openModal:function(){p.value=!0},closeModal:function(){p.value=!1}}),(M,u)=>{const x=ka,k=ma,A=ga,L=xi,S=bi,Q=vi,W=Ma;return q(),gt("div",null,[B(W,{modelValue:v(p),"onUpdate:modelValue":u[7]||(u[7]=T=>ua(p)?p.value=T:null),canFullscreen:!1,title:v(r)("cropper.modalTitle"),maxHeight:"380px",width:"800px"},{footer:H(()=>[B(Q,{type:"primary",onClick:D},{default:H(()=>[te(fi(v(r)("cropper.okText")),1)]),_:1})]),default:H(()=>[ot("div",{class:F(v(s))},[ot("div",{class:F(`${v(s)}-left`)},[ot("div",{class:F(`${v(s)}-cropper`)},[v(c)?(q(),Lt(v(Ne),{key:0,circled:t.circled,src:v(c),height:"300px",onCropend:b,onReady:y},null,8,["circled","src"])):ft("",!0)],2),ot("div",{class:F(`${v(s)}-toolbar`)},[B(A,{beforeUpload:g,fileList:[],accept:"image/*"},{default:H(()=>[B(k,{content:v(r)("cropper.selectImage"),placement:"bottom"},{default:H(()=>[B(x,{preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["content"])]),_:1}),B(L,null,{default:H(()=>[B(k,{content:v(r)("cropper.btn_reset"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"ant-design:reload-outlined",size:"small",type:"primary",onClick:u[0]||(u[0]=T=>w("reset"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_left"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"ant-design:rotate-left-outlined",size:"small",type:"primary",onClick:u[1]||(u[1]=T=>w("rotate",-45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_right"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:u[2]||(u[2]=T=>w("rotate",45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_x"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"vaadin:arrows-long-h",size:"small",type:"primary",onClick:u[3]||(u[3]=T=>w("scaleX"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_y"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"vaadin:arrows-long-v",size:"small",type:"primary",onClick:u[4]||(u[4]=T=>w("scaleY"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_in"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"ant-design:zoom-in-outlined",size:"small",type:"primary",onClick:u[5]||(u[5]=T=>w("zoom",.1))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_out"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(c),preIcon:"ant-design:zoom-out-outlined",size:"small",type:"primary",onClick:u[6]||(u[6]=T=>w("zoom",-.1))},null,8,["disabled"])]),_:1},8,["content"])]),_:1})],2)],2),ot("div",{class:F(`${v(s)}-right`)},[ot("div",{class:F(`${v(s)}-preview`)},[v(o)?(q(),gt("img",{key:0,alt:v(r)("cropper.preview"),src:v(o)},null,8,Re)):ft("",!0)],2),v(o)?(q(),gt("div",{key:0,class:F(`${v(s)}-group`)},[B(S,{src:v(o),size:"large"},null,8,["src"]),B(S,{size:48,src:v(o)},null,8,["src"]),B(S,{size:64,src:v(o)},null,8,["src"]),B(S,{size:80,src:v(o)},null,8,["src"])],2)):ft("",!0)],2)],2)]),_:1},8,["modelValue","title"])])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Cropper/src/CopperModal.vue"]]),Ae=Wt(mt({name:"CropperAvatar",__name:"CropperAvatar",props:{width:I.string.def("200px"),value:I.string.def(""),showBtn:I.bool.def(!0),btnText:I.string.def("")},emits:["update:value","change"],setup(t,{expose:i,emit:e}){const n=t,a=e,r=X(n.value),{getPrefixCls:h}=ee(),s=h("cropper-avatar"),c=va(),{t:o}=gi.useI18n(),d=X();function p({source:m,data:f,filename:g}){r.value=m,a("change",{source:m,data:f,filename:g}),c.success(o("cropper.uploadSuccess"))}function l(){d.value.openModal()}return di(()=>{r.value=n.value}),fa(()=>r.value,m=>{a("update:value",m)}),i({open:l,close:function(){d.value.closeModal()}}),(m,f)=>{const g=bi,b=vi;return q(),gt("div",{class:"user-info-head",onClick:f[1]||(f[1]=y=>l())},[v(r)?(q(),Lt(g,{key:0,src:v(r),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])):ft("",!0),v(r)?ft("",!0):(q(),Lt(g,{key:1,src:v(_a),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])),t.showBtn?(q(),Lt(b,{key:2,class:F(`${v(s)}-upload-btn`),onClick:f[0]||(f[0]=y=>l())},{default:H(()=>[te(fi(t.btnText?t.btnText:v(o)("cropper.selectImage")),1)]),_:1},8,["class"])):ft("",!0),B(He,{ref_key:"cropperModelRef",ref:d,srcValue:v(r),onUploadSuccess:p},null,8,["srcValue"])])}}}),[["__scopeId","data-v-34729f3a"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Cropper/src/CropperAvatar.vue"]]),Ye={class:"change-avatar"},yi=Wt(mt({name:"UserAvatar",__name:"UserAvatar",props:{img:I.string.def("")},setup(t){const i=ba(),e=X(),n=async({data:a})=>{const r=await wa({avatarFile:a});e.value.close(),i.setUserAvatarAction(r.data)};return(a,r)=>(q(),gt("div",Ye,[B(v(Ae),{ref_key:"cropperRef",ref:e,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},showBtn:!1,value:t.img,width:"120px",onChange:n},null,8,["value"])]))}}),[["__scopeId","data-v-d3692dbc"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/Profile/components/UserAvatar.vue"]])});export{Oa as __tla,yi as default};
