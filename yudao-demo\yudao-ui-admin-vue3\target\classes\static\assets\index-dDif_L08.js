import{u as P,_ as v,a as b,__tla as x}from"./useTable-1UdEztBx.js";import{d as z,r as C,C as k,T as w,o as i,c as L,i as r,w as l,a as t,H as M,l as R,j as U,F as j,N as q,_ as A,__tla as F}from"./index-Daqg4PFz.js";import{_ as H,__tla as J}from"./ContentWrap-DZg14iby.js";import{_ as N,__tla as O}from"./index-CmwFi8Xl.js";import{a as m,M as T,g as B,__tla as D}from"./MailLogDetail-D8lY6UZ7.js";import{__tla as E}from"./Form-R69XsyLN.js";import{__tla as G}from"./el-virtual-list-ByJAteiO.js";import{__tla as I}from"./el-tree-select-BKcJcOKx.js";import{__tla as K}from"./el-time-select-BnExG5dm.js";import{__tla as Q}from"./InputPassword-Dxw5CWOJ.js";import{__tla as V}from"./index-CS70nJJ8.js";import{__tla as W}from"./useForm-CSyrGYVb.js";import"./download--D_IyRio.js";import{__tla as X}from"./el-card-Dvjjuipo.js";import{__tla as Y}from"./Dialog-BjBBVYCI.js";import{__tla as Z}from"./Descriptions-4c7ee9Qf.js";import{__tla as $}from"./Descriptions.vue_vue_type_style_index_0_scoped_aee191e8_lang-nMrQIkhC.js";import{__tla as tt}from"./el-descriptions-item-Bucl-KSp.js";import{__tla as at}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as rt}from"./formatTime-BCfRGyrF.js";import{__tla as _t}from"./index-DmR7jHBv.js";import{__tla as et}from"./useCrudSchemas-C1aGM0Lr.js";import"./tree-BMqZf9_I.js";let u,lt=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return et}catch{}})()]).then(async()=>{u=A(z({name:"SystemMailLog",__name:"index",setup(ot){const{tableObject:a,tableMethods:p}=P({getListApi:B}),{getList:h,setSearchParams:o}=p,s=C();return k(()=>{h()}),(st,e)=>{const y=N,f=v,c=H,d=q,g=b,S=w("hasPermi");return i(),L(j,null,[r(y,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),r(c,null,{default:l(()=>[r(f,{schema:t(m).searchSchema,onSearch:t(o),onReset:t(o)},null,8,["schema","onSearch","onReset"])]),_:1}),r(c,null,{default:l(()=>[r(g,{columns:t(m).tableColumns,data:t(a).tableList,loading:t(a).loading,pagination:{total:t(a).total},pageSize:t(a).pageSize,"onUpdate:pageSize":e[0]||(e[0]=_=>t(a).pageSize=_),currentPage:t(a).currentPage,"onUpdate:currentPage":e[1]||(e[1]=_=>t(a).currentPage=_)},{action:l(({row:_})=>[M((i(),R(d,{link:"",type:"primary",onClick:ct=>{return n=_.id,void s.value.open(n);var n}},{default:l(()=>[U(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[S,["system:mail-log:query"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),r(T,{ref_key:"detailRef",ref:s},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/system/mail/log/index.vue"]])});export{lt as __tla,u as default};
