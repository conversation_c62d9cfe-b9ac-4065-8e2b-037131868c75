import{d as W,r as i,f as D,C as Q,ay as X,o as n,l as m,w as l,i as s,j as o,a as e,H as Y,c as b,k as S,F as R,z as E,t as Z,a9 as T,V as $,G as ee,I as ae,J as le,K as re,L as te,am as se,an as ue,ai as _e,cf as ne,O as oe,N as ie,R as de,_ as me,__tla as ce}from"./index-Daqg4PFz.js";import{_ as fe,__tla as Ce}from"./Dialog-BjBBVYCI.js";import{t as pe,__tla as ye}from"./index-W7V8hhz8.js";import{a as ve,__tla as we}from"./index-C_SCPERO.js";import{t as be,__tla as Re}from"./index-78jf5nCk.js";import{t as Te,__tla as Oe}from"./index-CCPyMtv-.js";import{t as Me,__tla as Ue}from"./index-BYuPmJ1X.js";import{P as Ve,B as t,__tla as he}from"./index-BWsMQsUV.js";let N,Se=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{N=me(W({name:"CrmTransferForm",__name:"TransferForm",props:{bizType:{type:Number,required:!0}},emits:["success"],setup(k,{expose:I,emit:L}){const O=k,M=ae(),d=i(!1),y=i(""),c=i(!1),U=i([]),f=i(!1),_=i({}),z=D({newOwnerUserId:[{required:!0,message:"\u65B0\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],oldOwnerPermissionLevel:[{required:!0,message:"\u8001\u8D1F\u8D23\u4EBA\u52A0\u5165\u56E2\u961F\u540E\u7684\u6743\u9650\u7EA7\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),C=i();I({open:async r=>{d.value=!0,y.value=F(),q(),_.value.id=r}});const g=r=>{r||(_.value.oldOwnerPermissionLevel=void 0)},P=L,B=async()=>{if(C&&await C.value.validate()){c.value=!0;try{const r=_.value;await A(e(r)),M.success(y.value+"\u6210\u529F"),d.value=!1,P("success")}finally{c.value=!1}}},A=async r=>{switch(O.bizType){case t.CRM_CLUE:return await ve(r);case t.CRM_CUSTOMER:return await Te(r);case t.CRM_CONTACT:return await be(r);case t.CRM_BUSINESS:return await pe(r);case t.CRM_CONTRACT:return await Me(r);default:throw M.error("\u3010\u8F6C\u79FB\u5931\u8D25\u3011\u6CA1\u6709\u8F6C\u79FB\u63A5\u53E3"),new Error("\u3010\u8F6C\u79FB\u5931\u8D25\u3011\u6CA1\u6709\u8F6C\u79FB\u63A5\u53E3")}},F=()=>{switch(O.bizType){case t.CRM_CLUE:return"\u7EBF\u7D22\u8F6C\u79FB";case t.CRM_CUSTOMER:return"\u5BA2\u6237\u8F6C\u79FB";case t.CRM_CONTACT:return"\u8054\u7CFB\u4EBA\u8F6C\u79FB";case t.CRM_BUSINESS:return"\u5546\u673A\u8F6C\u79FB";case t.CRM_CONTRACT:return"\u5408\u540C\u8F6C\u79FB";default:return"\u8F6C\u79FB"}},q=()=>{var r;(r=C.value)==null||r.resetFields(),_.value={}};return Q(async()=>{U.value=await X()}),(r,u)=>{const x=le,j=re,p=te,v=se,V=ue,w=_e,J=ne,G=oe,h=ie,H=fe,K=de;return n(),m(H,{modelValue:e(d),"onUpdate:modelValue":u[5]||(u[5]=a=>E(d)?d.value=a:null),title:e(y),width:"30%"},{footer:l(()=>[s(h,{disabled:e(c),type:"primary",onClick:B},{default:l(()=>[o("\u786E \u5B9A")]),_:1},8,["disabled"]),s(h,{onClick:u[4]||(u[4]=a=>d.value=!1)},{default:l(()=>[o("\u53D6 \u6D88")]),_:1})]),default:l(()=>[Y((n(),m(G,{ref_key:"formRef",ref:C,model:e(_),rules:e(z),"label-width":"150px"},{default:l(()=>[s(p,{label:"\u9009\u62E9\u65B0\u8D1F\u8D23\u4EBA",prop:"newOwnerUserId"},{default:l(()=>[s(j,{modelValue:e(_).newOwnerUserId,"onUpdate:modelValue":u[0]||(u[0]=a=>e(_).newOwnerUserId=a)},{default:l(()=>[(n(!0),b(R,null,S(e(U),a=>(n(),m(x,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"\u8001\u8D1F\u8D23\u4EBA"},{default:l(()=>[s(V,{modelValue:e(f),"onUpdate:modelValue":u[1]||(u[1]=a=>E(f)?f.value=a:null),onChange:g},{default:l(()=>[s(v,{label:!1,size:"large"},{default:l(()=>[o("\u79FB\u9664")]),_:1}),s(v,{label:!0,size:"large"},{default:l(()=>[o("\u52A0\u5165\u56E2\u961F")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(f)?(n(),m(p,{key:0,label:"\u8001\u8D1F\u8D23\u4EBA\u6743\u9650\u7EA7\u522B",prop:"oldOwnerPermissionLevel"},{default:l(()=>[s(V,{modelValue:e(_).oldOwnerPermissionLevel,"onUpdate:modelValue":u[2]||(u[2]=a=>e(_).oldOwnerPermissionLevel=a)},{default:l(()=>[(n(!0),b(R,null,S(e($)(e(ee).CRM_PERMISSION_LEVEL),a=>(n(),b(R,{key:a.value},[a.value!=e(Ve).OWNER?(n(),m(v,{key:0,label:a.value},{default:l(()=>[o(Z(a.label),1)]),_:2},1032,["label"])):T("",!0)],64))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),r.bizType===e(t).CRM_CUSTOMER?(n(),m(p,{key:1,label:"\u540C\u65F6\u8F6C\u79FB"},{default:l(()=>[s(J,{modelValue:e(_).toBizTypes,"onUpdate:modelValue":u[3]||(u[3]=a=>e(_).toBizTypes=a)},{default:l(()=>[s(w,{label:e(t).CRM_CONTACT},{default:l(()=>[o("\u8054\u7CFB\u4EBA")]),_:1},8,["label"]),s(w,{label:e(t).CRM_BUSINESS},{default:l(()=>[o("\u5546\u673A")]),_:1},8,["label"]),s(w,{label:e(t).CRM_CONTRACT},{default:l(()=>[o("\u5408\u540C")]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})):T("",!0)]),_:1},8,["model","rules"])),[[K,e(c)]])]),_:1},8,["modelValue","title"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/permission/components/TransferForm.vue"]])});export{Se as __tla,N as default};
