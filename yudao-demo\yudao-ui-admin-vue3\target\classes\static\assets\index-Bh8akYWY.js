import{d as f,p as r,at as d,a as t,r as i,o as h,l as y,w as x,i as I,z as m,ao as U,cn as g,Z as q,_ as C,__tla as P}from"./index-Daqg4PFz.js";import{P as R}from"./color-BN7ZL7BD.js";let p,W=Promise.all([(()=>{try{return P}catch{}})()]).then(async()=>{p=C(f({name:"InputWithColor",__name:"index",props:{modelValue:r.string.def("").isRequired,color:r.string.def("").isRequired},emits:["update:modelValue","update:color"],setup(c,{emit:_}){const s=c;d(()=>s.modelValue,e=>{e!==t(a)&&(a.value=e)});const n=_,a=i(s.modelValue);d(()=>a.value,e=>{n("update:modelValue",e)});const l=i(s.color);return d(()=>l.value,e=>{n("update:color",e)}),(e,o)=>{const V=g,v=q;return h(),y(v,U({modelValue:t(a),"onUpdate:modelValue":o[1]||(o[1]=u=>m(a)?a.value=u:null)},e.$attrs),{append:x(()=>[I(V,{modelValue:t(l),"onUpdate:modelValue":o[0]||(o[0]=u=>m(l)?l.value=u:null),predefine:t(R)},null,8,["modelValue","predefine"])]),_:1},16,["modelValue"])}}}),[["__scopeId","data-v-556d261b"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/InputWithColor/index.vue"]])});export{p as _,W as __tla};
