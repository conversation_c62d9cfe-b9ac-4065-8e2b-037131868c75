import{d as _,o as r,c as o,l as y,a9 as e,g as c,av as p,t as i,H as u,a8 as g,x,_ as f,__tla as v}from"./index-Daqg4PFz.js";import{E as h,__tla as b}from"./el-image-Bn34T02c.js";let n,k=Promise.all([(()=>{try{return v}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{let s,l,a;s={class:"title-bar"},l={class:"absolute left-0 top-0 w-full"},a={key:0},n=f(_({name:"TitleBar",__name:"index",props:{property:{type:Object,required:!0}},setup:w=>(t,z)=>{const d=h,m=x;return r(),o("div",s,[t.property.bgImgUrl?(r(),y(d,{key:0,src:t.property.bgImgUrl,fit:"cover",class:"w-full"},null,8,["src"])):e("",!0),c("div",l,[t.property.title?(r(),o("div",{key:0,style:p({fontSize:`${t.property.titleSize}px`,fontWeight:t.property.titleWeight,color:t.property.titleColor,textAlign:t.property.textAlign})},i(t.property.title),5)):e("",!0),t.property.description?(r(),o("div",{key:1,style:p({fontSize:`${t.property.descriptionSize}px`,fontWeight:t.property.descriptionWeight,color:t.property.descriptionColor,textAlign:t.property.textAlign}),class:"m-t-8px"},i(t.property.description),5)):e("",!0)]),u(c("div",{class:"more",style:p({color:t.property.descriptionColor})},[t.property.more.type!=="icon"?(r(),o("span",a,i(t.property.more.text),1)):e("",!0),t.property.more.type!=="text"?(r(),y(m,{key:1,icon:"ep:arrow-right"})):e("",!0)],4),[[g,t.property.more.show]])])}}),[["__scopeId","data-v-6cc79237"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/TitleBar/index.vue"]])});export{k as __tla,n as default};
