import{d as _a,S as oa,u as ua,I as na,r as _,b as ia,C as ca,au as ma,o,c as h,i as s,a,H as pa,l as y,w as t,z as M,F as b,k as w,g as P,j as z,t as O,ay as fa,E as da,s as ya,A as va,B as ha,x as ba,N as ga,J as xa,K as ka,L as wa,O as Ca,R as Va,_ as Ua,__tla as Sa}from"./index-Daqg4PFz.js";import{_ as Ia,__tla as Fa}from"./ContentWrap-DZg14iby.js";import{E as Pa,__tla as za}from"./el-card-Dvjjuipo.js";import{E as Aa,__tla as Ba}from"./el-text-vv1naHK-.js";import{E as Ea,__tla as Ja}from"./el-image-Bn34T02c.js";import{_ as <PERSON>,__tla as <PERSON>}from"./index-CmwFi8Xl.js";import{b as ja,a as qa,__tla as Da}from"./index-DIoS19iR.js";import{g as Ma,a as Oa,__tla as Qa}from"./index-Wcjc3rZh.js";import{b as Ta,__tla as Xa}from"./formCreate-Cp7STxiP.js";import Ga,{__tla as Ha}from"./ProcessInstanceBpmnViewer-CfM7PJYm.js";import{C as Ka,__tla as Na}from"./index-B4Qi4fi2.js";import{u as Wa,__tla as Ya}from"./tagsView-CrrEoR03.js";import{__tla as $a}from"./bpmn-embedded-CyKj3vrC.js";import{__tla as Za}from"./Dialog-BjBBVYCI.js";import{__tla as at}from"./XTextButton-BJLSHFzo.js";import{__tla as tt}from"./XButton-CfHP8l0l.js";import{__tla as et}from"./el-collapse-item-CUcELNOM.js";import{__tla as rt}from"./index-CSvGj0-b.js";import{__tla as lt}from"./el-tree-select-BKcJcOKx.js";import"./tree-BMqZf9_I.js";import{__tla as st}from"./index-BCA1igdc.js";import{__tla as _t}from"./index-D-Abj-9W.js";import{__tla as ot}from"./index-Co1vaaHn.js";import{__tla as ut}from"./index-BjA_Ugbr.js";import{__tla as nt}from"./index-BBLwwrga.js";import{__tla as it}from"./index-CS70nJJ8.js";import"./constants-WoCEnNvc.js";import{__tla as ct}from"./index-Ch7bD5NQ.js";import{__tla as mt}from"./el-drawer-cP-FViL4.js";import{__tla as pt}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as ft}from"./index-SjM4AotX.js";import{__tla as dt}from"./formatTime-BCfRGyrF.js";let Q,yt=Promise.all([(()=>{try{return Sa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Qa}catch{}})(),(()=>{try{return Xa}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return dt}catch{}})()]).then(async()=>{let A,B,E;A={class:"flex"},B={class:"clearfix"},E={class:"el-icon-document"},Q=Ua(_a({name:"BpmProcessInstanceCreate",__name:"index",setup(vt){const T=oa(),{push:J,currentRoute:X}=ua(),C=na(),{delView:G}=Wa(),g=T.query.processInstanceId,V=_(!0),x=_([]),v=_(""),U=_([]),H=ia(()=>U.value.filter(e=>e.category==v.value)),i=_(),c=_({rule:[],option:{},value:{}}),m=_(),L=_(null),p=_([]),f=_({}),R=_(),S=_({}),j=_([]),q=async(e,r)=>{var d;if(m.value=e,p.value=[],f.value={},S.value={},e.formType==10){Ta(c,e.formConf,e.formFields,r);const n=await qa(e.id);if(n&&(L.value=n.bpmnXml,p.value=n.startUserSelectTasks,((d=p.value)==null?void 0:d.length)>0)){c.value.rule.push({type:"startUserSelect",props:{title:"\u6307\u5B9A\u5BA1\u6279\u4EBA"}});for(const k of p.value)f.value[k.id]=[],S.value[k.id]=[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA",trigger:"blur"}];j.value=await fa()}}else e.formCustomCreatePath&&await J({path:e.formCustomCreatePath})},K=async e=>{var r;if(i.value&&m.value){((r=p.value)==null?void 0:r.length)>0&&await R.value.validate(),i.value.btn.loading(!0);try{await Oa({processDefinitionId:m.value.id,variables:e,startUserSelectAssignees:f.value}),C.success("\u53D1\u8D77\u6D41\u7A0B\u6210\u529F"),G(a(X)),await J({name:"BpmProcessInstanceMy"})}finally{i.value.btn.loading(!1)}}};return ca(()=>{(async()=>{V.value=!0;try{if(x.value=await Ka.getCategorySimpleList(),x.value.length>0&&(v.value=x.value[0].code),U.value=await ja({suspensionState:1}),(g==null?void 0:g.length)>0){const e=await Ma(g);if(!e)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9E\u4F8B\u4E0D\u5B58\u5728");const r=U.value.find(d=>{var n;return d.key==((n=e.processDefinition)==null?void 0:n.key)});if(!r)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9A\u4E49\u4E0D\u5B58\u5728");await q(r,e.formVariables)}}finally{V.value=!1}})()}),(e,r)=>{const d=La,n=Ea,k=Aa,I=Pa,F=da,N=ya,W=va,Y=ha,D=Ia,$=ba,Z=ga,aa=xa,ta=ka,ea=wa,ra=Ca,la=ma("form-create"),sa=Va;return o(),h(b,null,[s(d,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(m)?(o(),y(D,{key:1},{default:t(()=>[s(I,{class:"box-card"},{default:t(()=>[P("div",B,[P("span",E,"\u7533\u8BF7\u4FE1\u606F\u3010"+O(a(m).name)+"\u3011",1),s(Z,{style:{float:"right"},type:"primary",onClick:r[1]||(r[1]=l=>m.value=void 0)},{default:t(()=>[s($,{icon:"ep:delete"}),z(" \u9009\u62E9\u5176\u5B83\u6D41\u7A0B ")]),_:1})]),s(F,{span:16,offset:6,style:{"margin-top":"20px"}},{default:t(()=>[s(la,{rule:a(c).rule,api:a(i),"onUpdate:api":r[2]||(r[2]=l=>M(i)?i.value=l:null),modelValue:a(c).value,"onUpdate:modelValue":r[3]||(r[3]=l=>a(c).value=l),option:a(c).option,onSubmit:K},{"type-startUserSelect":t(()=>[s(F,{span:24},{default:t(()=>[s(I,{class:"mb-10px"},{header:t(()=>[z("\u6307\u5B9A\u5BA1\u6279\u4EBA")]),default:t(()=>[s(ra,{model:a(f),rules:a(S),ref_key:"startUserSelectAssigneesFormRef",ref:R},{default:t(()=>[(o(!0),h(b,null,w(a(p),l=>(o(),y(ea,{key:l.id,label:`\u4EFB\u52A1\u3010${l.name}\u3011`,prop:l.id},{default:t(()=>[s(ta,{modelValue:a(f)[l.id],"onUpdate:modelValue":u=>a(f)[l.id]=u,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA"},{default:t(()=>[(o(!0),h(b,null,w(a(j),u=>(o(),y(aa,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["rule","api","modelValue","option"])]),_:1})]),_:1}),s(Ga,{"bpmn-xml":a(L)},null,8,["bpmn-xml"])]),_:1})):pa((o(),y(D,{key:0},{default:t(()=>[s(Y,{"tab-position":"left",modelValue:a(v),"onUpdate:modelValue":r[0]||(r[0]=l=>M(v)?v.value=l:null)},{default:t(()=>[(o(!0),h(b,null,w(a(x),l=>(o(),y(W,{label:l.name,name:l.code,key:l.code},{default:t(()=>[s(N,{gutter:20},{default:t(()=>[(o(!0),h(b,null,w(a(H),u=>(o(),y(F,{lg:6,sm:12,xs:24,key:u.id},{default:t(()=>[s(I,{shadow:"hover",class:"mb-20px cursor-pointer",onClick:ht=>q(u)},{default:t(()=>[P("div",A,[s(n,{src:u.icon,class:"w-32px h-32px"},null,8,["src"]),s(k,{class:"!ml-10px",size:"large"},{default:t(()=>[z(O(u.name),1)]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[sa,a(V)]])],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/create/index.vue"]])});export{yt as __tla,Q as default};
