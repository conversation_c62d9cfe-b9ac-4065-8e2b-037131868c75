import{as as Ne,b as c,cy as ze,cz as v,bf as w,be as I,cA as Ee,d as Me,bg as Z,r as ee,f as Le,cB as Be,a as R,at as Ce,bc as Ie,h as te,b2 as Re,bF as Te,bH as Fe,__tla as Oe}from"./index-Daqg4PFz.js";let ae,G,re,le,ne,j,h,se,T,oe,F,H,P,ie,ue,k,ce,de,me,ve,pe,fe,V,ge,he,_e=Promise.all([(()=>{try{return Oe}catch{}})()]).then(async()=>{var K=Number.isNaN||function(e){return typeof e=="number"&&e!=e};function ye(e,a){if(e.length!==a.length)return!1;for(var l=0;l<e.length;l++)if(n=e[l],r=a[l],!(n===r||K(n)&&K(r)))return!1;var n,r;return!0}let W,x,N,z,Y,E,y,O,_,M,$,q;ge=()=>{const e=Ne().proxy.$props;return c(()=>{const a=(l,n,r)=>({});return e.perfMode?ze(a):function(l,n){n===void 0&&(n=ye);var r=null;function p(){for(var u=[],d=0;d<arguments.length;d++)u[d]=arguments[d];if(r&&r.lastThis===this&&n(u,r.lastArgs))return r.lastResult;var f=l.apply(this,u);return r={lastResult:f,lastArgs:u,lastThis:this},f}return p.clear=function(){r=null},p}(a)})},le=50,se="itemRendered",oe="scroll",j="forward",G="backward",ae="auto",me="smart",ce="start",re="center",ne="end",h="horizontal",F="vertical",T="rtl",P="negative",H="positive-ascending",k="positive-descending",W={[h]:"left",[F]:"top"},x=v({type:w([Number,Function]),required:!0}),N=v({type:Number}),z=v({type:Number,default:2}),Y=v({type:String,values:["ltr","rtl"],default:"ltr"}),E=v({type:Number,default:0}),y=v({type:Number,required:!0}),O=v({type:String,values:["horizontal","vertical"],default:F}),_=I({className:{type:String,default:""},containerElement:{type:w([String,Object]),default:"div"},data:{type:w(Array),default:()=>Ee([])},direction:Y,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:w([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),he=I({cache:z,estimatedItemSize:N,layout:O,initScrollOffset:E,total:y,itemSize:x,..._}),M={type:Number,default:6},$={type:Number,default:0},q={type:Number,default:2},pe=I({columnCache:z,columnWidth:x,estimatedColumnWidth:N,estimatedRowHeight:N,initScrollLeft:E,initScrollTop:E,itemKey:{type:w(Function),default:({columnIndex:e,rowIndex:a})=>`${a}:${e}`},rowCache:z,rowHeight:x,totalColumn:y,totalRow:y,hScrollbarSize:M,vScrollbarSize:M,scrollbarStartGap:$,scrollbarEndGap:q,role:String,..._}),V=I({alwaysOn:Boolean,class:String,layout:O,total:y,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:M,startGap:$,endGap:q,visible:Boolean}),ue=(e,a)=>e<a?j:G,ve=e=>e==="ltr"||e===T||e===h,fe=e=>e===T;let b=null;de=function(e=!1){if(b===null||e){const a=document.createElement("div"),l=a.style;l.width="50px",l.height="50px",l.overflow="scroll",l.direction="rtl";const n=document.createElement("div"),r=n.style;return r.width="100px",r.height="100px",a.appendChild(n),document.body.appendChild(a),a.scrollLeft>0?b=k:(a.scrollLeft=1,b=a.scrollLeft===0?P:H),document.body.removeChild(a),b}return b},ie=Me({name:"ElVirtualScrollBar",props:V,emits:["scroll","start-move","stop-move"],setup(e,{emit:a}){const l=c(()=>e.startGap+e.endGap),n=Z("virtual-scrollbar"),r=Z("scrollbar"),p=ee(),u=ee();let d=null,f=null;const i=Le({isDragging:!1,traveled:0}),s=c(()=>Be[e.layout]),J=c(()=>e.clientSize-R(l)),be=c(()=>({position:"absolute",width:`${h===e.layout?J.value:e.scrollbarSize}px`,height:`${h===e.layout?e.scrollbarSize:J.value}px`,[W[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"})),A=c(()=>{const t=e.ratio,o=e.clientSize;if(t>=100)return Number.POSITIVE_INFINITY;if(t>=50)return t*o/100;const g=o/3;return Math.floor(Math.min(Math.max(t*o,20),g))}),Se=c(()=>{if(!Number.isFinite(A.value))return{display:"none"};const t=`${A.value}px`;return function({move:g,size:C,bar:X},xe){const m={},D=`translate${X.axis}(${g}px)`;return m[X.size]=C,m.transform=D,m.msTransform=D,m.webkitTransform=D,xe==="horizontal"?m.height="100%":m.width="100%",m}({bar:s.value,size:t,move:i.traveled},e.layout)}),S=c(()=>Math.floor(e.clientSize-A.value-R(l))),Q=()=>{window.removeEventListener("mousemove",B),window.removeEventListener("mouseup",L),document.onselectstart=f,f=null;const t=R(u);t&&(t.removeEventListener("touchmove",B),t.removeEventListener("touchend",L))},U=t=>{t.stopImmediatePropagation(),t.ctrlKey||[1,2].includes(t.button)||(i.isDragging=!0,i[s.value.axis]=t.currentTarget[s.value.offset]-(t[s.value.client]-t.currentTarget.getBoundingClientRect()[s.value.direction]),a("start-move"),(()=>{window.addEventListener("mousemove",B),window.addEventListener("mouseup",L);const o=R(u);o&&(f=document.onselectstart,document.onselectstart=()=>!1,o.addEventListener("touchmove",B),o.addEventListener("touchend",L))})())},L=()=>{i.isDragging=!1,i[s.value.axis]=0,a("stop-move"),Q()},B=t=>{const{isDragging:o}=i;if(!o||!u.value||!p.value)return;const g=i[s.value.axis];if(!g)return;Te(d);const C=-1*(p.value.getBoundingClientRect()[s.value.direction]-t[s.value.client])-(u.value[s.value.offset]-g);d=Fe(()=>{i.traveled=Math.max(e.startGap,Math.min(C,S.value)),a("scroll",C,S.value)})},we=t=>{const o=Math.abs(t.target.getBoundingClientRect()[s.value.direction]-t[s.value.client])-u.value[s.value.offset]/2;i.traveled=Math.max(0,Math.min(o,S.value)),a("scroll",o,S.value)};return Ce(()=>e.scrollFrom,t=>{i.isDragging||(i.traveled=Math.ceil(t*S.value))}),Ie(()=>{Q()}),()=>te("div",{role:"presentation",ref:p,class:[n.b(),e.class,(e.alwaysOn||i.isDragging)&&"always-on"],style:be.value,onMousedown:Re(we,["stop","prevent"]),onTouchstartPrevent:U},te("div",{ref:u,class:r.e("thumb"),style:Se.value,onMousedown:U},[]))}})});export{ae as A,G as B,re as C,le as D,ne as E,j as F,h as H,se as I,T as R,oe as S,F as V,_e as __tla,H as a,P as b,ie as c,ue as d,k as e,ce as f,de as g,me as h,ve as i,pe as j,fe as k,V as l,ge as u,he as v};
