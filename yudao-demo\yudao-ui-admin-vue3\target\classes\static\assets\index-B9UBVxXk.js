import{d as Y,u as z,r as o,f as F,C as H,o as N,c as M,i as a,w as t,a as e,j as u,H as q,l as B,F as R,Z as j,L as J,M as L,x as Q,N as O,O as Z,P as A,Q as E,R as G,_ as K,__tla as W}from"./index-Daqg4PFz.js";import{_ as X,__tla as $}from"./index-BBLwwrga.js";import{_ as aa,__tla as ea}from"./ContentWrap-DZg14iby.js";import{_ as ta,__tla as la}from"./index-CmwFi8Xl.js";import{d as v,__tla as ra}from"./formatTime-BCfRGyrF.js";import{f as sa,__tla as na}from"./index-Wcjc3rZh.js";import{__tla as oa}from"./index-CS70nJJ8.js";import{__tla as ia}from"./el-card-Dvjjuipo.js";let x,ca=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ia}catch{}})()]).then(async()=>{x=K(Y({name:"BpmProcessInstanceCopy",__name:"index",setup(pa){const{push:I}=z(),i=o(!1),d=o(0),f=o([]),l=F({pageNo:1,pageSize:10,processInstanceId:"",processInstanceName:"",createTime:[]}),h=o(),c=async()=>{i.value=!0;try{const p=await sa(l);f.value=p.list,d.value=p.total}finally{i.value=!1}},y=()=>{l.pageNo=1,c()},k=()=>{h.value.resetFields(),y()};return H(()=>{c()}),(p,s)=>{const C=ta,V=j,m=J,T=L,g=Q,_=O,U=Z,w=aa,n=A,D=E,P=X,S=G;return N(),M(R,null,[a(C,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),a(w,null,{default:t(()=>[a(U,{ref_key:"queryFormRef",ref:h,inline:!0,class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(V,{modelValue:e(l).processInstanceName,"onUpdate:modelValue":s[0]||(s[0]=r=>e(l).processInstanceName=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0"},null,8,["modelValue"])]),_:1}),a(m,{label:"\u6284\u9001\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(T,{modelValue:e(l).createTime,"onUpdate:modelValue":s[1]||(s[1]=r=>e(l).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:t(()=>[a(_,{onClick:y},{default:t(()=>[a(g,{class:"mr-5px",icon:"ep:search"}),u(" \u641C\u7D22 ")]),_:1}),a(_,{onClick:k},{default:t(()=>[a(g,{class:"mr-5px",icon:"ep:refresh"}),u(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},512)]),_:1}),a(w,null,{default:t(()=>[q((N(),B(D,{data:e(f)},{default:t(()=>[a(n,{align:"center",label:"\u6D41\u7A0B\u540D",prop:"processInstanceName","min-width":"180"}),a(n,{align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",prop:"startUserName","min-width":"100"}),a(n,{formatter:e(v),align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u65F6\u95F4",prop:"processInstanceStartTime",width:"180"},null,8,["formatter"]),a(n,{align:"center",label:"\u6284\u9001\u4EFB\u52A1",prop:"taskName","min-width":"180"}),a(n,{align:"center",label:"\u6284\u9001\u4EBA",prop:"creatorName","min-width":"100"}),a(n,{align:"center",label:"\u6284\u9001\u65F6\u95F4",prop:"createTime",width:"180",formatter:e(v)},null,8,["formatter"]),a(n,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:t(r=>[a(_,{link:"",type:"primary",onClick:ma=>{return b=r.row,void I({name:"BpmProcessInstanceDetail",query:{id:b.processInstanceId}});var b}},{default:t(()=>[u("\u8BE6\u60C5")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,e(i)]]),a(P,{limit:e(l).pageSize,"onUpdate:limit":s[2]||(s[2]=r=>e(l).pageSize=r),page:e(l).pageNo,"onUpdate:page":s[3]||(s[3]=r=>e(l).pageNo=r),total:e(d),onPagination:c},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/task/copy/index.vue"]])});export{ca as __tla,x as default};
