import{be as te,bf as x,cA as se,bo as ie,d as B,bW as oe,bg as ne,bz as re,dL as ce,r as c,b as u,bI as p,at as ue,C as de,o as n,c as d,aW as _,g as P,t as ve,a,a0 as g,F as T,ao as fe,a9 as y,l as pe,w as ye,dM as me,av as ge,bh as be,az as we,dN as he,bq as Se,dO as ze,c_ as $,dP as ke,dQ as xe,bi as _e,__tla as Ee}from"./index-Daqg4PFz.js";let M,Le=Promise.all([(()=>{try{return Ee}catch{}})()]).then(async()=>{const j=te({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:x([String,Object])},previewSrcList:{type:x(Array),default:()=>se([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:x(String)}}),R={load:i=>i instanceof Event,error:i=>i instanceof Event,switch:i=>ie(i),close:()=>!0,show:()=>!0},W=["src","loading","crossorigin"],q={key:0},F=B({name:"ElImage",inheritAttrs:!1});M=_e(be(B({...F,props:j,emits:R,setup(i,{emit:v}){const l=i;let E="";const{t:Y}=oe(),o=ne("image"),D=re(),H=ce(),b=c(),f=c(!1),r=c(!0),w=c(!1),m=c(),s=c(),K=p&&"loading"in HTMLImageElement.prototype;let h,S;const Q=u(()=>[o.e("inner"),z.value&&o.e("preview"),r.value&&o.is("loading")]),G=u(()=>D.style),J=u(()=>{const{fit:e}=l;return p&&e?{objectFit:e}:{}}),z=u(()=>{const{previewSrcList:e}=l;return Array.isArray(e)&&e.length>0}),U=u(()=>{const{previewSrcList:e,initialIndex:t}=l;let A=t;return t>e.length-1&&(A=0),A}),L=u(()=>l.loading!=="eager"&&(!K&&l.loading==="lazy"||l.lazy)),k=()=>{p&&(r.value=!0,f.value=!1,b.value=l.src)};function V(e){r.value=!1,f.value=!1,v("load",e)}function X(e){r.value=!1,f.value=!0,v("error",e)}function C(){ke(m.value,s.value)&&(k(),O())}const I=xe(C,200,!0);async function N(){var e;if(!p)return;await we();const{scrollContainer:t}=l;he(t)?s.value=t:Se(t)&&t!==""?s.value=(e=document.querySelector(t))!=null?e:void 0:m.value&&(s.value=ze(m.value)),s.value&&(h=$(s,"scroll",I),setTimeout(()=>C(),100))}function O(){p&&s.value&&I&&(h==null||h(),s.value=void 0)}function Z(e){if(e.ctrlKey)return e.deltaY<0||e.deltaY>0?(e.preventDefault(),!1):void 0}function ee(){z.value&&(S=$("wheel",Z,{passive:!1}),E=document.body.style.overflow,document.body.style.overflow="hidden",w.value=!0,v("show"))}function ae(){S==null||S(),document.body.style.overflow=E,w.value=!1,v("close")}function le(e){v("switch",e)}return ue(()=>l.src,()=>{L.value?(r.value=!0,f.value=!1,O(),N()):k()}),de(()=>{L.value?N():k()}),(e,t)=>(n(),d("div",{ref_key:"container",ref:m,class:g([a(o).b(),e.$attrs.class]),style:ge(a(G))},[f.value?_(e.$slots,"error",{key:0},()=>[P("div",{class:g(a(o).e("error"))},ve(a(Y)("el.image.error")),3)]):(n(),d(T,{key:1},[b.value!==void 0?(n(),d("img",fe({key:0},a(H),{src:b.value,loading:e.loading,style:a(J),class:a(Q),crossorigin:e.crossorigin,onClick:ee,onLoad:V,onError:X}),null,16,W)):y("v-if",!0),r.value?(n(),d("div",{key:1,class:g(a(o).e("wrapper"))},[_(e.$slots,"placeholder",{},()=>[P("div",{class:g(a(o).e("placeholder"))},null,2)])],2)):y("v-if",!0)],64)),a(z)?(n(),d(T,{key:2},[w.value?(n(),pe(a(me),{key:0,"z-index":e.zIndex,"initial-index":a(U),infinite:e.infinite,"zoom-rate":e.zoomRate,"min-scale":e.minScale,"max-scale":e.maxScale,"url-list":e.previewSrcList,"hide-on-click-modal":e.hideOnClickModal,teleported:e.previewTeleported,"close-on-press-escape":e.closeOnPressEscape,onClose:ae,onSwitch:le},{default:ye(()=>[e.$slots.viewer?(n(),d("div",q,[_(e.$slots,"viewer")])):y("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):y("v-if",!0)],64)):y("v-if",!0)],6))}}),[["__file","image.vue"]]))});export{M as E,Le as __tla};
