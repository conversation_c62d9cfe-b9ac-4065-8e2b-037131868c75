import{bE as a,__tla as d}from"./index-Daqg4PFz.js";let r,e,o,c,l,u=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{e=t=>a.post({url:"/product/category/create",data:t}),l=t=>a.put({url:"/product/category/update",data:t}),o=t=>a.delete({url:`/product/category/delete?id=${t}`}),r=t=>a.get({url:`/product/category/get?id=${t}`}),c=t=>a.get({url:"/product/category/list",params:t})});export{u as __tla,r as a,e as c,o as d,c as g,l as u};
