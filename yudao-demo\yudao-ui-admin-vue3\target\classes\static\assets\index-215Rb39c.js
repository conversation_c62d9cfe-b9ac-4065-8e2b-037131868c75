import{_ as o,__tla as d}from"./ContentWrap-DZg14iby.js";import{_ as m,__tla as y}from"./IFrame-DOdFY0xB.js";import{_ as f,__tla as h}from"./index-CmwFi8Xl.js";import{d as p,r as l,C as v,o as e,c as x,i as t,w,a as i,l as b,a9 as g,F as k,_ as B,__tla as C}from"./index-Daqg4PFz.js";import{b as D,__tla as F}from"./index-Cz8k7H0s.js";import{__tla as I}from"./el-card-Dvjjuipo.js";let n,J=Promise.all([(()=>{try{return d}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{n=B(p({name:"InfraDruid",__name:"index",setup(M){const r=l(!0),s=l("http://localhost:48080/druid/index.html");return v(async()=>{try{const a=await D("url.druid");a&&a.length>0&&(s.value=a)}finally{r.value=!1}}),(a,P)=>{const _=f,c=m,u=o;return e(),x(k,null,[t(_,{title:"\u6570\u636E\u5E93 MyBatis",url:"https://doc.iocoder.cn/mybatis/"}),t(_,{title:"\u591A\u6570\u636E\u6E90\uFF08\u8BFB\u5199\u5206\u79BB\uFF09",url:"https://doc.iocoder.cn/dynamic-datasource/"}),t(u,null,{default:w(()=>[i(r)?g("",!0):(e(),b(c,{key:0,src:i(s)},null,8,["src"]))]),_:1})],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/infra/druid/index.vue"]])});export{J as __tla,n as default};
