import{bE as g,d as H,I as N,r as A,f as E,C as P,o as D,c as k,i as a,w as e,a as h,z as J,F as O,k as W,l as Q,g as o,J as X,K as Z,L as $,M as aa,O as ea,E as ta,s as sa,_ as la,__tla as ra}from"./index-Daqg4PFz.js";import{E as na,__tla as ua}from"./el-card-Dvjjuipo.js";import{_ as ia,__tla as da}from"./Echart-C33-KcLZ.js";import{_ as oa,__tla as ca}from"./ContentWrap-DZg14iby.js";import{c as ma,e as pa,n as G,f as i,o as _a,__tla as ha}from"./formatTime-BCfRGyrF.js";import{f as va,__tla as xa}from"./index-CtIY6rl-.js";let j,fa=Promise.all([(()=>{try{return ra}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return xa}catch{}})()]).then(async()=>{let I,Y,U,M,V;I=o("div",null,[o("span",null,"\u7528\u6237\u589E\u51CF\u6570\u636E")],-1),Y=o("div",null,[o("span",null,"\u7D2F\u8BA1\u7528\u6237\u6570\u636E")],-1),U=o("div",null,[o("span",null,"\u6D88\u606F\u6982\u51B5\u6570\u636E")],-1),M=o("div",null,[o("span",null,"\u63A5\u53E3\u5206\u6790\u6570\u636E")],-1),V=H({name:"MpStatistics",__name:"index",setup(ya){const F=N(),t=A([ma(new Date(new Date().getTime()-6048e5)),pa(new Date(new Date().getTime()-864e5))]),d=A(-1),f=A([]),c=A([]),m=E({color:["#67C23A","#E5323E"],legend:{data:["\u65B0\u589E\u7528\u6237","\u53D6\u6D88\u5173\u6CE8\u7684\u7528\u6237"]},tooltip:{},xAxis:{data:[]},yAxis:{minInterval:1},series:[{name:"\u65B0\u589E\u7528\u6237",type:"bar",label:{show:!0},barGap:0,data:[]},{name:"\u53D6\u6D88\u5173\u6CE8\u7684\u7528\u6237",type:"bar",label:{show:!0},data:[]}]}),x=E({legend:{data:["\u7D2F\u8BA1\u7528\u6237\u91CF"]},xAxis:{type:"category",data:[]},yAxis:{minInterval:1},series:[{name:"\u7D2F\u8BA1\u7528\u6237\u91CF",data:[],type:"line",smooth:!0,label:{show:!0}}]}),p=E({color:["#67C23A","#E5323E"],legend:{data:["\u7528\u6237\u53D1\u9001\u4EBA\u6570","\u7528\u6237\u53D1\u9001\u6761\u6570"]},tooltip:{},xAxis:{data:[]},yAxis:{minInterval:1},series:[{name:"\u7528\u6237\u53D1\u9001\u4EBA\u6570",type:"line",smooth:!0,label:{show:!0},data:[]},{name:"\u7528\u6237\u53D1\u9001\u6761\u6570",type:"line",smooth:!0,label:{show:!0},data:[]}]}),u=E({color:["#67C23A","#E5323E","#E6A23C","#409EFF"],legend:{data:["\u88AB\u52A8\u56DE\u590D\u7528\u6237\u6D88\u606F\u7684\u6B21\u6570","\u5931\u8D25\u6B21\u6570","\u6700\u5927\u8017\u65F6","\u603B\u8017\u65F6"]},tooltip:{},xAxis:{data:[]},yAxis:{},series:[{name:"\u88AB\u52A8\u56DE\u590D\u7528\u6237\u6D88\u606F\u7684\u6B21\u6570",type:"bar",label:{show:!0},barGap:0,data:[]},{name:"\u5931\u8D25\u6B21\u6570",type:"bar",label:{show:!0},data:[]},{name:"\u6700\u5927\u8017\u65F6",type:"bar",label:{show:!0},data:[]},{name:"\u603B\u8017\u65F6",type:"bar",label:{show:!0},data:[]}]}),C=()=>{if(!d)return F.error("\u672A\u9009\u4E2D\u516C\u4F17\u53F7\uFF0C\u65E0\u6CD5\u7EDF\u8BA1\u6570\u636E"),!1;if(G(t.value[0],t.value[1])>=7)return F.error("\u65F6\u95F4\u95F4\u9694 7 \u5929\u4EE5\u5185\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9"),!1;c.value=[];const l=G(t.value[0],t.value[1]);for(let s=0;s<=l;s++)c.value.push(i(_a(t.value[0],864e5*s),"YYYY-MM-DD"));q(),z(),K(),L()},q=async()=>{m.xAxis.data=[],m.series[0].data=[],m.series[1].data=[];try{const s=await(l={accountId:d.value,date:[i(t.value[0]),i(t.value[1])]},g.get({url:"/mp/statistics/user-summary",params:l}));m.xAxis.data=c.value,c.value.forEach((n,r)=>{s.forEach(v=>{i(new Date(v.refDate),"YYYY-MM-DD").indexOf(n)!==-1&&(m.series[0].data[r]=v.newUser,m.series[1].data[r]=v.cancelUser)})})}catch{}var l},z=async()=>{x.xAxis.data=[],x.series[0].data=[];try{const s=await(l={accountId:d.value,date:[i(t.value[0]),i(t.value[1])]},g.get({url:"/mp/statistics/user-cumulate",params:l}));x.xAxis.data=c.value,s.forEach((n,r)=>{x.series[0].data[r]=n.cumulateUser})}catch{}var l},K=async()=>{p.xAxis.data=[],p.series[0].data=[],p.series[1].data=[];try{const s=await(l={accountId:d.value,date:[i(t.value[0]),i(t.value[1])]},g.get({url:"/mp/statistics/upstream-message",params:l}));p.xAxis.data=c.value,s.forEach((n,r)=>{p.series[0].data[r]=n.messageUser,p.series[1].data[r]=n.messageCount})}catch{}var l},L=async()=>{u.xAxis.data=[],u.series[0].data=[],u.series[1].data=[],u.series[2].data=[],u.series[3].data=[];try{const s=await(l={accountId:d.value,date:[i(t.value[0]),i(t.value[1])]},g.get({url:"/mp/statistics/interface-summary",params:l}));u.xAxis.data=c.value,s.forEach((n,r)=>{u.series[0].data[r]=n.callbackCount,u.series[1].data[r]=n.failCount,u.series[2].data[r]=n.maxTimeCost,u.series[3].data[r]=n.totalTimeCost})}catch{}var l};return P(async()=>{await(async()=>{f.value=await va(),f.value.length>0&&(d.value=f.value[0].id)})(),C()}),(l,s)=>{const n=X,r=Z,v=$,R=aa,S=ea,T=oa,y=ia,b=na,w=ta,B=sa;return D(),k(O,null,[a(T,null,{default:e(()=>[a(S,{class:"-mb-15px",ref:"queryForm",inline:!0,"label-width":"68px"},{default:e(()=>[a(v,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:e(()=>[a(r,{modelValue:h(d),"onUpdate:modelValue":s[0]||(s[0]=_=>J(d)?d.value=_:null),onChange:C,class:"!w-240px"},{default:e(()=>[(D(!0),k(O,null,W(h(f),_=>(D(),Q(n,{key:_.id,label:_.name,value:_.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"\u65F6\u95F4\u8303\u56F4",prop:"dateRange"},{default:e(()=>[a(R,{modelValue:h(t),"onUpdate:modelValue":s[1]||(s[1]=_=>J(t)?t.value=_:null),type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],onChange:C,class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1})]),_:1},512)]),_:1}),a(T,null,{default:e(()=>[a(B,null,{default:e(()=>[a(w,{span:12,class:"card-box"},{default:e(()=>[a(b,null,{header:e(()=>[I]),default:e(()=>[a(y,{options:h(m),height:420},null,8,["options"])]),_:1})]),_:1}),a(w,{span:12,class:"card-box"},{default:e(()=>[a(b,null,{header:e(()=>[Y]),default:e(()=>[a(y,{options:h(x),height:420},null,8,["options"])]),_:1})]),_:1}),a(w,{span:12,class:"card-box"},{default:e(()=>[a(b,null,{header:e(()=>[U]),default:e(()=>[a(y,{options:h(p),height:420},null,8,["options"])]),_:1})]),_:1}),a(w,{span:12,class:"card-box"},{default:e(()=>[a(b,null,{header:e(()=>[M]),default:e(()=>[a(y,{options:h(u),height:420},null,8,["options"])]),_:1})]),_:1})]),_:1})]),_:1})],64)}}}),j=la(V,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/statistics/index.vue"]])});export{fa as __tla,j as default};
