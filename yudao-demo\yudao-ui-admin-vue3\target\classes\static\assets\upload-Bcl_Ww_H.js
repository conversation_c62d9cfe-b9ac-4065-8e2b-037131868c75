import{dY as i,__tla as m}from"./index-Daqg4PFz.js";import{u as t,U as r,__tla as c}from"./useUpload-DvwaTvLo.js";let e,o,s,l,_,n=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{e={Authorization:"Bearer "+i()},o="http://localhost:48080/admin-api/mp/material/upload-permanent",l=a=>t(r.Image,2)(a),s=a=>t(r.Voice,2)(a),_=a=>t(r.Video,10)(a)});export{e as H,o as U,n as __tla,s as a,l as b,_ as c};
