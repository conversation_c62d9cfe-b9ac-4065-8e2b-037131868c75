import{d as u,o as m,c as y,i as a,w as l,g as d,j as _,t as q,s as f,x as b,E as v,v as h,_ as x,__tla as g}from"./index-Daqg4PFz.js";let i,k=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{let o;o=["src"],i=x(u({name:"WxLocation",__name:"main",props:{locationX:{required:!0,type:Number},locationY:{required:!0,type:Number},label:{required:!0,type:String},qqMapKey:{required:!1,type:String,default:"TVDBZ-TDILD-4ON4B-PFDZA-RNLKH-VVF6E"}},setup(e,{expose:r}){const t=e;return r({locationX:t.locationX,locationY:t.locationY,label:t.label,qqMapKey:t.qqMapKey}),(w,K)=>{const n=f,s=b,p=v,c=h;return m(),y("div",null,[a(c,{type:"primary",target:"_blank",href:"https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx="+e.locationY+"&pointy="+e.locationX+"&name="+e.label+"&ref=yudao"},{default:l(()=>[a(p,null,{default:l(()=>[a(n,null,{default:l(()=>[d("img",{src:"https://apis.map.qq.com/ws/staticmap/v2/?zoom=10&markers=color:blue|label:A|"+e.locationX+","+e.locationY+"&key="+e.qqMapKey+"&size=250*180"},null,8,o)]),_:1}),a(n,null,{default:l(()=>[a(s,{icon:"ep:location"}),_(" "+q(e.label),1)]),_:1})]),_:1})]),_:1},8,["href"])])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-location/main.vue"]])});export{k as __tla,i as default};
