import{d as ya,S as ha,as as va,e as ka,r as n,f as ba,at as Ua,C as ga,ay as Ca,au as Va,o as c,l as v,w as s,c as L,k as ta,H as ra,a,g as S,t as x,i as t,j as d,a9 as D,F as la,z as wa,I as xa,az as Ta,aA as Ia,aB as Fa,L as Da,ax as Ra,Z as Sa,J as Ea,K as za,O as Ba,x as Ja,N as La,E as Na,R as Pa,_ as ja,__tla as qa}from"./index-Daqg4PFz.js";import{_ as Ka,__tla as Oa}from"./ContentWrap-DZg14iby.js";import{E as $a,__tla as Aa}from"./el-card-Dvjjuipo.js";import{b as sa,__tla as Ha}from"./formCreate-Cp7STxiP.js";import{a as Xa,__tla as Za}from"./index-DIoS19iR.js";import{g as Ga,__tla as Ma}from"./index-Wcjc3rZh.js";import{a as Qa,r as Wa,g as Ya,__tla as ae}from"./index-CYOuQA7P.js";import ee,{__tla as te}from"./ProcessInstanceBpmnViewer-CfM7PJYm.js";import re,{__tla as le}from"./ProcessInstanceTaskList-BDNx2r8-.js";import se,{__tla as oe}from"./TaskReturnForm-CQj2wPDM.js";import ne,{__tla as _e}from"./TaskDelegateForm-GQTon-ll.js";import ie,{__tla as ue}from"./TaskTransferForm-DLDJHao6.js";import ce,{__tla as me}from"./TaskSignCreateForm-BW_jsyev.js";import{__tla as pe}from"./bpmn-embedded-CyKj3vrC.js";import{__tla as fe}from"./Dialog-BjBBVYCI.js";import{__tla as de}from"./XTextButton-BJLSHFzo.js";import{__tla as ye}from"./XButton-CfHP8l0l.js";import{__tla as he}from"./el-collapse-item-CUcELNOM.js";import{__tla as ve}from"./index-CSvGj0-b.js";import{__tla as ke}from"./el-tree-select-BKcJcOKx.js";import"./tree-BMqZf9_I.js";import{__tla as be}from"./index-BCA1igdc.js";import{__tla as Ue}from"./index-D-Abj-9W.js";import{__tla as ge}from"./index-Co1vaaHn.js";import{__tla as Ce}from"./index-BjA_Ugbr.js";import{__tla as Ve}from"./index-BBLwwrga.js";import{__tla as we}from"./index-CS70nJJ8.js";import"./constants-WoCEnNvc.js";import{__tla as xe}from"./index-Ch7bD5NQ.js";import{__tla as Te}from"./el-drawer-cP-FViL4.js";import{__tla as Ie}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as Fe}from"./index-SjM4AotX.js";import{__tla as De}from"./formatTime-BCfRGyrF.js";import{__tla as Re}from"./el-timeline-item-DLMaR2h1.js";import{__tla as Se}from"./TaskSignList-C8ia0WyR.js";import{__tla as Ee}from"./TaskSignDeleteForm-DWPmX3ze.js";let oa,ze=Promise.all([(()=>{try{return qa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Ee}catch{}})()]).then(async()=>{let N,P,j,q,K,O;N={class:"el-icon-picture-outline"},P={class:"el-icon-picture-outline"},j={style:{"margin-bottom":"20px","margin-left":"10%","font-size":"14px"}},q={class:"el-icon-document"},K={key:1},O=ya({name:"BpmProcessInstanceDetail",__name:"index",setup(Be){const{query:na}=ha(),E=xa(),{proxy:_a}=va(),ia=ka().getUser.id,z=na.id,T=n(!1),_=n({}),$=n(""),B=n(!0),k=n([]),b=n([]),f=n([]),ua=ba({reason:[{required:!0,message:"\u5BA1\u6279\u5EFA\u8BAE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=n([]),I=n([]),U=n(),F=n({rule:[],option:{},value:{}});Ua(()=>I.value,i=>{i==null||i.forEach(e=>{e.btn.show(!1),e.resetBtn.show(!1)})},{deep:!0});const A=async(i,e)=>{const l=b.value.indexOf(i),C=_a.$refs["form"+l][0],h=a(C);if(!h||!await h.validate())return;const m={id:i.id,reason:f.value[l].reason,copyUserIds:f.value[l].copyUserIds};if(e){const R=I.value[l];R&&(await R.validate(),m.variables=y.value[l].value),await Qa(m),E.success("\u5BA1\u6279\u901A\u8FC7\u6210\u529F")}else await Wa(m),E.success("\u5BA1\u6279\u4E0D\u901A\u8FC7\u6210\u529F");g()},H=n(),X=n(),Z=n(),G=n(),g=()=>{ca(),Q()},M=n(null),ca=async()=>{var i;try{T.value=!0;const e=await Ga(z);if(!e)return void E.error("\u67E5\u8BE2\u4E0D\u5230\u6D41\u7A0B\u4FE1\u606F\uFF01");_.value=e;const l=e.processDefinition;l.formType===10?(sa(F,l.formConf,l.formFields,e.formVariables),Ta().then(()=>{var C,h,m;(C=U.value)==null||C.btn.show(!1),(h=U.value)==null||h.resetBtn.show(!1),(m=U.value)==null||m.disabled(!0)})):M.value=Ia(e.processDefinition.formCustomViewPath),$.value=(i=await Xa(l.id))==null?void 0:i.bpmnXml}finally{T.value=!1}},Q=async()=>{b.value=[],f.value=[],y.value=[],I.value=[];try{B.value=!0;const i=await Ya(z);k.value=[],i.forEach(e=>{e.status!==4&&k.value.push(e)}),k.value.sort((e,l)=>e.endTime&&l.endTime?l.endTime-e.endTime:e.endTime?1:l.endTime?-1:l.createTime-e.createTime),W(k.value)}finally{B.value=!1}},W=i=>{i.forEach(e=>{if(Fa(e.children)||W(e.children),(e.status===1||e.status===6)&&e.assigneeUser&&e.assigneeUser.id===ia)if(b.value.push({...e}),f.value.push({reason:"",copyUserIds:[]}),e.formId&&e.formConf){const l={};sa(l,e.formConf,e.formFields,e.formVariable),y.value.push(l)}else y.value.push({})})},Y=n([]);return ga(async()=>{g(),Y.value=await Ca()}),(i,e)=>{const l=Da,C=Ra,h=Va("form-create"),m=$a,R=Sa,ma=Ea,pa=za,fa=Ba,V=Ja,w=La,aa=Na,da=Ka,ea=Pa;return c(),v(da,null,{default:s(()=>[(c(!0),L(la,null,ta(a(b),(p,o)=>ra((c(),v(m,{key:o,class:"box-card"},{header:s(()=>[S("span",N,"\u5BA1\u6279\u4EFB\u52A1\u3010"+x(p.name)+"\u3011",1)]),default:s(()=>[t(aa,{offset:6,span:16},{default:s(()=>[t(fa,{ref_for:!0,ref:"form"+o,model:a(f)[o],rules:a(ua),"label-width":"100px"},{default:s(()=>[a(_)&&a(_).name?(c(),v(l,{key:0,label:"\u6D41\u7A0B\u540D"},{default:s(()=>[d(x(a(_).name),1)]),_:1})):D("",!0),a(_)&&a(_).startUser?(c(),v(l,{key:1,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:s(()=>{var r;return[d(x((r=a(_))==null?void 0:r.startUser.nickname)+" ",1),t(C,{size:"small",type:"info"},{default:s(()=>{var u;return[d(x((u=a(_))==null?void 0:u.startUser.deptName),1)]}),_:1})]}),_:1})):D("",!0),a(b)[o].formId>0?(c(),v(m,{key:2,class:"mb-15px !-mt-10px"},{header:s(()=>{var r;return[S("span",P," \u586B\u5199\u8868\u5355\u3010"+x((r=a(b)[o])==null?void 0:r.formName)+"\u3011 ",1)]}),default:s(()=>[t(h,{modelValue:a(y)[o].value,"onUpdate:modelValue":r=>a(y)[o].value=r,api:a(I)[o],"onUpdate:api":r=>a(I)[o]=r,option:a(y)[o].option,rule:a(y)[o].rule},null,8,["modelValue","onUpdate:modelValue","api","onUpdate:api","option","rule"])]),_:2},1024)):D("",!0),t(l,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:s(()=>[t(R,{modelValue:a(f)[o].reason,"onUpdate:modelValue":r=>a(f)[o].reason=r,placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t(l,{label:"\u6284\u9001\u4EBA",prop:"copyUserIds"},{default:s(()=>[t(pa,{modelValue:a(f)[o].copyUserIds,"onUpdate:modelValue":r=>a(f)[o].copyUserIds=r,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6284\u9001\u4EBA"},{default:s(()=>[(c(!0),L(la,null,ta(a(Y),r=>(c(),v(ma,{key:r.id,label:r.nickname,value:r.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["model","rules"]),S("div",j,[t(w,{type:"success",onClick:r=>A(p,!0)},{default:s(()=>[t(V,{icon:"ep:select"}),d(" \u901A\u8FC7 ")]),_:2},1032,["onClick"]),t(w,{type:"danger",onClick:r=>A(p,!1)},{default:s(()=>[t(V,{icon:"ep:close"}),d(" \u4E0D\u901A\u8FC7 ")]),_:2},1032,["onClick"]),t(w,{type:"primary",onClick:r=>{return u=p.id,void H.value.open(u);var u}},{default:s(()=>[t(V,{icon:"ep:edit"}),d(" \u8F6C\u529E ")]),_:2},1032,["onClick"]),t(w,{type:"primary",onClick:r=>(async u=>{X.value.open(u.id)})(p)},{default:s(()=>[t(V,{icon:"ep:position"}),d(" \u59D4\u6D3E ")]),_:2},1032,["onClick"]),t(w,{type:"primary",onClick:r=>(async u=>{G.value.open(u.id)})(p)},{default:s(()=>[t(V,{icon:"ep:plus"}),d(" \u52A0\u7B7E ")]),_:2},1032,["onClick"]),t(w,{type:"warning",onClick:r=>(async u=>{Z.value.open(u.id)})(p)},{default:s(()=>[t(V,{icon:"ep:back"}),d(" \u56DE\u9000 ")]),_:2},1032,["onClick"])])]),_:2},1024)]),_:2},1024)),[[ea,a(T)]])),128)),ra((c(),v(m,{class:"box-card"},{header:s(()=>[S("span",q,"\u7533\u8BF7\u4FE1\u606F\u3010"+x(a(_).name)+"\u3011",1)]),default:s(()=>{var p,o,r,u;return[((o=(p=a(_))==null?void 0:p.processDefinition)==null?void 0:o.formType)===10?(c(),v(aa,{key:0,offset:6,span:16},{default:s(()=>[t(h,{modelValue:a(F).value,"onUpdate:modelValue":e[0]||(e[0]=J=>a(F).value=J),api:a(U),"onUpdate:api":e[1]||(e[1]=J=>wa(U)?U.value=J:null),option:a(F).option,rule:a(F).rule},null,8,["modelValue","api","option","rule"])]),_:1})):D("",!0),((u=(r=a(_))==null?void 0:r.processDefinition)==null?void 0:u.formType)===20?(c(),L("div",K,[t(a(M),{id:a(_).businessKey},null,8,["id"])])):D("",!0)]}),_:1})),[[ea,a(T)]]),t(re,{loading:a(B),"process-instance":a(_),tasks:a(k),onRefresh:Q},null,8,["loading","process-instance","tasks"]),t(ee,{id:`${a(z)}`,"bpmn-xml":a($),loading:a(T),"process-instance":a(_),tasks:a(k)},null,8,["id","bpmn-xml","loading","process-instance","tasks"]),t(ie,{ref_key:"taskTransferFormRef",ref:H,onSuccess:g},null,512),t(se,{ref_key:"taskReturnFormRef",ref:Z,onSuccess:g},null,512),t(ne,{ref_key:"taskDelegateForm",ref:X,onSuccess:g},null,512),t(ce,{ref_key:"taskSignCreateFormRef",ref:G,onSuccess:g},null,512)]),_:1})}}}),oa=ja(O,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/index.vue"]])});export{ze as __tla,oa as default};
