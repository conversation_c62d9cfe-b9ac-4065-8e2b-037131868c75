import{d as Q,b6 as T,p as M,r as k,at as G,o as d,c as v,g as H,F as V,k as $,a0 as W,av as K,i as L,a as A,a9 as X,j as Y,t as Z,x as ee,_ as te,__tla as le}from"./index-Daqg4PFz.js";let N,ae=Promise.all([(()=>{try{return le}catch{}})()]).then(async()=>{let w,E,j,q,F,I;w=(i,y)=>{const[t,l]=[i.x,y.x].sort(),[a,r]=[i.y,y.y].sort(),p=l+1,f=r+1;return{left:t,right:p,top:a,bottom:f,height:f-a,width:p-t}},E={class:"relative"},j={class:"cube-table"},q=["onClick","onMouseenter"],F=["onClick"],I=["onClick"],N=te(Q({name:"MagicCubeEditor",__name:"index",props:{modelValue:T().isRequired,rows:M.number.def(4),cols:M.number.def(4),cubeSize:M.number.def(75)},emits:["update:modelValue","hotAreaSelected"],setup(i,{emit:y}){const t=i,l=k([]);G(()=>[t.rows,t.cols],()=>{if(l.value=[],t.rows&&t.cols)for(let o=0;o<t.rows;o++){l.value[o]=[];for(let e=0;e<t.cols;e++)l.value[o].push({x:e,y:o,active:!1})}},{immediate:!0});const a=k([]);G(()=>t.modelValue,()=>a.value=t.modelValue||[],{immediate:!0});const r=k(),p=()=>!!r.value,f=y,J=()=>f("update:modelValue",a),P=k(0),R=(o,e)=>{P.value=e,f("hotAreaSelected",o,e)};function g(){U((o,e,s)=>{s.active&&(s.active=!1)}),r.value=void 0}const U=o=>{for(let e=0;e<l.value.length;e++)for(let s=0;s<l.value[e].length;s++)o(e,s,l.value[e][s])};return(o,e)=>{const s=ee;return d(),v("div",E,[H("table",j,[H("tbody",null,[(d(!0),v(V,null,$(A(l),(u,n)=>(d(),v("tr",{key:n},[(d(!0),v(V,null,$(u,(S,m)=>(d(),v("td",{key:m,class:W(["cube",{active:S.active}]),style:K({width:`${i.cubeSize}px`,height:`${i.cubeSize}px`}),onClick:O=>((C,z)=>{const b=l.value[C][z];if(!p())return r.value=b,void(r.value.active=!0);a.value.push(w(r.value,b)),g();let c=a.value.length-1;R(a.value[c],c),J()})(n,m),onMouseenter:O=>((C,z)=>{if(!p())return;const b=w(r.value,l.value[C][z]);for(const B of a.value)if(h=b,(c=B).left<h.left+h.width&&c.left+c.width>h.left&&c.top<h.top+h.height&&c.height+c.top>h.top)return void g();var c,h;U((B,oe,D)=>{var x,_;D.active=(x=b,(_=D).x>=x.left&&_.x<x.right&&_.y>=x.top&&_.y<x.bottom)})})(n,m)},[L(s,{icon:"ep-plus"})],46,q))),128))]))),128))]),(d(!0),v(V,null,$(A(a),(u,n)=>(d(),v("div",{key:n,class:"hot-area",style:K({top:i.cubeSize*u.top+"px",left:i.cubeSize*u.left+"px",height:i.cubeSize*u.height+"px",width:i.cubeSize*u.width+"px"}),onClick:S=>R(u,n),onMouseover:g},[A(P)===n?(d(),v("div",{key:0,class:"btn-delete",onClick:S=>(m=>{a.value.splice(m,1),g(),J()})(n)},[L(s,{icon:"ep:circle-close-filled"})],8,I)):X("",!0),Y(" "+Z(`${u.width}\xD7${u.height}`),1)],44,F))),128))])])}}}),[["__scopeId","data-v-d5b18453"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/MagicCubeEditor/index.vue"]])});export{N as _,ae as __tla};
