import{d as L,r as a,at as b,C as j,o as s,l as z,w as C,g as v,av as l,a as o,c as i,F as P,k as U,i as g,a9 as x,a0 as E,t as R,b3 as I,_ as q,__tla as D}from"./index-Daqg4PFz.js";import{E as F,__tla as G}from"./el-image-Bn34T02c.js";import{b as J,__tla as O}from"./spu-zkQh6zUd.js";let $,S=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return O}catch{}})()]).then(async()=>{let m,_;m={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},_=L({name:"ProductList",__name:"index",props:{property:{type:Object,required:!0}},setup(T){const t=T,p=a([]);b(()=>t.property.spuIds,async()=>{p.value=await J(t.property.spuIds)},{immediate:!0,deep:!0});const u=a(375),h=a(),r=a(2),d=a("100%"),c=a("0"),y=a("");return b(()=>[t.property,u,p.value.length],()=>{r.value=t.property.layoutType==="twoCol"?2:3;const e=(u.value-t.property.space*(r.value-1))/r.value;c.value=r.value===2?"64px":`${e}px`,t.property.layoutType==="horizSwiper"?(y.value=`repeat(auto-fill, ${e}px)`,d.value=e*p.value.length+t.property.space*(p.value.length-1)+"px"):(y.value=`repeat(${r.value}, auto)`,d.value="100%")},{immediate:!0,deep:!0}),j(()=>{var e,n;u.value=((n=(e=h.value)==null?void 0:e.wrapRef)==null?void 0:n.offsetWidth)||375}),(e,n)=>{const w=F,k=I;return s(),z(k,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:h},{default:C(()=>[v("div",{class:"grid overflow-x-auto",style:l({gridGap:`${e.property.space}px`,gridTemplateColumns:o(y),width:o(d)})},[(s(!0),i(P,null,U(o(p),(f,B)=>(s(),i("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:l({borderTopLeftRadius:`${e.property.borderRadiusTop}px`,borderTopRightRadius:`${e.property.borderRadiusTop}px`,borderBottomLeftRadius:`${e.property.borderRadiusBottom}px`,borderBottomRightRadius:`${e.property.borderRadiusBottom}px`}),key:B},[e.property.badge.show?(s(),i("div",m,[g(w,{fit:"cover",src:e.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):x("",!0),g(w,{fit:"cover",src:f.picUrl,style:l({width:o(c),height:o(c)})},null,8,["src","style"]),v("div",{class:E(["flex flex-col gap-8px p-8px box-border",{"w-[calc(100%-64px)]":o(r)===2,"w-full":o(r)===3}])},[e.property.fields.name.show?(s(),i("div",{key:0,class:"truncate text-12px",style:l({color:e.property.fields.name.color})},R(f.name),5)):x("",!0),v("div",null,[e.property.fields.price.show?(s(),i("span",{key:0,class:"text-12px",style:l({color:e.property.fields.price.color})}," \uFFE5"+R(f.price),5)):x("",!0)])],2)],4))),128))],4)]),_:1},512)}}}),$=q(_,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/ProductList/index.vue"]])});export{S as __tla,$ as default};
