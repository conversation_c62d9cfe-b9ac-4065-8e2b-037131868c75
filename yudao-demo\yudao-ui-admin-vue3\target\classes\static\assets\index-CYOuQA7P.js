import{bE as t,__tla as k}from"./index-Daqg4PFz.js";let s,e,r,p,n,l,i,c,u,m,b,d,y,g=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{m=async a=>await t.get({url:"/bpm/task/todo-page",params:a}),c=async a=>await t.get({url:"/bpm/task/done-page",params:a}),u=async a=>await t.get({url:"/bpm/task/manager-page",params:a}),s=async a=>await t.put({url:"/bpm/task/approve",data:a}),b=async a=>await t.put({url:"/bpm/task/reject",data:a}),i=async a=>await t.get({url:"/bpm/task/list-by-process-instance-id?processInstanceId="+a}),e=async a=>await t.get({url:"/bpm/task/list-by-return",params:{id:a}}),r=async a=>await t.put({url:"/bpm/task/return",data:a}),p=async a=>await t.put({url:"/bpm/task/delegate",data:a}),y=async a=>await t.put({url:"/bpm/task/transfer",data:a}),d=async a=>await t.put({url:"/bpm/task/create-sign",data:a}),l=async a=>await t.delete({url:"/bpm/task/delete-sign",data:a}),n=async a=>await t.get({url:"/bpm/task/list-by-parent-task-id?parentTaskId="+a})});export{g as __tla,s as a,e as b,r as c,p as d,n as e,l as f,i as g,c as h,u as i,m as j,b as r,d as s,y as t};
