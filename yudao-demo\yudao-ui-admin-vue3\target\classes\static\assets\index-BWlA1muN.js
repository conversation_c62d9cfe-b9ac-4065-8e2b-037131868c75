import{bE as a,__tla as c}from"./index-Daqg4PFz.js";let l,t,r,s,m,i,u=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{t=async e=>await a.get({url:"/member/level/list",params:e}),m=async e=>await a.get({url:"/member/level/get?id="+e}),l=async()=>await a.get({url:"/member/level/list-all-simple"}),r=async e=>await a.post({url:"/member/level/create",data:e}),i=async e=>await a.put({url:"/member/level/update",data:e}),s=async e=>await a.delete({url:"/member/level/delete?id="+e})});export{u as __tla,l as a,t as b,r as c,s as d,m as g,i as u};
