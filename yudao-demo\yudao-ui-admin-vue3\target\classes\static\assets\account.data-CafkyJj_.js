import{d as o,__tla as m}from"./formatTime-BCfRGyrF.js";import{n as _,f as t,G as r,__tla as n}from"./index-Daqg4PFz.js";import{r as a,__tla as c}from"./formRules-BBK7HL0H.js";import{u as d,__tla as f}from"./useCrudSchemas-C1aGM0Lr.js";let s,i,b=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{let e,l;({t:e}=_()),i=t({mail:[{required:!0,message:e("profile.rules.mail"),trigger:"blur"},{type:"email",message:e("profile.rules.truemail"),trigger:["blur","change"]}],username:[a],password:[a],host:[a],port:[a],sslEnable:[a],starttlsEnable:[a]}),l=t([{label:"\u90AE\u7BB1",field:"mail",isSearch:!0},{label:"\u7528\u6237\u540D",field:"username",isSearch:!0},{label:"\u5BC6\u7801",field:"password",isTable:!1},{label:"SMTP \u670D\u52A1\u5668\u57DF\u540D",field:"host"},{label:"SMTP \u670D\u52A1\u5668\u7AEF\u53E3",field:"port",form:{component:"InputNumber",value:465}},{label:"\u662F\u5426\u5F00\u542F SSL",field:"sslEnable",dictType:r.INFRA_BOOLEAN_STRING,dictClass:"boolean",form:{component:"Radio"}},{label:"\u662F\u5426\u5F00\u542F STARTTLS",field:"starttlsEnable",dictType:r.INFRA_BOOLEAN_STRING,dictClass:"boolean",form:{component:"Radio"}},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isForm:!1,formatter:o,detail:{dateFormat:"YYYY-MM-DD HH:mm:ss"}},{label:"\u64CD\u4F5C",field:"action",isForm:!1,isDetail:!1}]),{allSchemas:s}=d(l)});export{b as __tla,s as a,i as r};
