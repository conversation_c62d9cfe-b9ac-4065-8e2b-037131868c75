import{d as x,o as s,c as t,F as U,k as b,g as e,i as q,t as w,_ as A,__tla as E}from"./index-Daqg4PFz.js";import{E as F,__tla as I}from"./el-image-Bn34T02c.js";let h,J=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{let l,i,n,r,c,m,_,o,u,d,p;l={class:"news-home"},i=["href"],n={class:"news-main"},r={class:"news-content"},c={class:"news-content-title"},m=["href"],_={class:"news-main-item"},o={class:"news-content-item"},u={class:"news-content-item-title"},d={class:"news-content-item-img"},p=["src"],h=A(x({name:"WxNews",__name:"main",props:{articles:{type:[Array,null],required:!0,default:null}},setup:(y,{expose:f})=>(f({articles:y.articles}),(g,N)=>{const k=F;return s(),t("div",l,[(s(!0),t(U,null,b(g.articles,(a,v)=>(s(),t("div",{key:v,class:"news-div"},[v===0?(s(),t("a",{key:0,href:a.url,target:"_blank"},[e("div",n,[e("div",r,[q(k,{src:a.picUrl,class:"material-img",style:{width:"100%",height:"120px"}},null,8,["src"]),e("div",c,[e("span",null,w(a.title),1)])])])],8,i)):(s(),t("a",{key:1,href:a.url,target:"_blank"},[e("div",_,[e("div",o,[e("div",u,w(a.title),1),e("div",d,[e("img",{src:a.picUrl,class:"material-img",height:"100%"},null,8,p)])])])],8,m))]))),128))])})}),[["__scopeId","data-v-42232a23"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-news/main.vue"]])});export{J as __tla,h as default};
