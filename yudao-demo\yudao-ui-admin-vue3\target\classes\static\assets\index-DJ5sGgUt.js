import{d as g,r as b,o as a,c as l,g as y,a as r,F as d,k as C,i as o,w as m,av as w,t as z,a9 as c,a0 as f,x as A,N as F,_ as j,__tla as B}from"./index-Daqg4PFz.js";import{E,__tla as U}from"./el-image-Bn34T02c.js";let v,q=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let i,n,p;i=["onClick"],n={class:"h-full w-full flex items-center justify-center"},p=g({name:"FloatingActionButton",__name:"index",props:{property:{type:Object,required:!0}},setup(D){const t=b(!0),u=()=>{t.value=!t.value};return(e,I)=>{const x=A,h=E,k=F;return a(),l(d,null,[y("div",{class:f(["absolute bottom-32px right-[calc(50%-375px/2+32px)] flex z-12 gap-12px items-center",{"flex-row":e.property.direction==="horizontal","flex-col":e.property.direction==="vertical"}])},[r(t)?(a(!0),l(d,{key:0},C(e.property.list,(s,_)=>(a(),l("div",{key:_,class:"flex flex-col items-center",onClick:J=>e.handleActive(_)},[o(h,{src:s.imgUrl,fit:"contain",class:"h-27px w-27px"},{error:m(()=>[y("div",n,[o(x,{icon:"ep:picture",color:s.textColor},null,8,["color"])])]),_:2},1032,["src"]),e.property.showText?(a(),l("span",{key:0,class:"mt-4px text-12px",style:w({color:s.textColor})},z(s.text),5)):c("",!0)],8,i))),128)):c("",!0),o(k,{type:"primary",size:"large",circle:"",onClick:u},{default:m(()=>[o(x,{icon:"ep:plus",class:f(["fab-icon",{active:r(t)}])},null,8,["class"])]),_:1})],2),r(t)?(a(),l("div",{key:0,class:"modal-bg",onClick:u})):c("",!0)],64)}}}),v=j(p,[["__scopeId","data-v-5ef8fad6"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/FloatingActionButton/index.vue"]])});export{q as __tla,v as default};
