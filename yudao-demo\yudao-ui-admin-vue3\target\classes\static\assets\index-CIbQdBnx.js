import{d as B,r as e,bd as I,C as J,o as a,c as m,i as n,w as s,g as U,F as x,k as Z,a as _,a0 as $,j as q,t as z,l,a9 as o,dZ as D,E as L,s as M,_ as N,__tla as Q}from"./index-Daqg4PFz.js";import{_ as S,__tla as V}from"./index-CmwFi8Xl.js";import W,{__tla as X}from"./CustomerFollowList-D1-yKwXG.js";import Y,{__tla as G}from"./CustomerTodayContactList-CdptNDel.js";import H,{__tla as K}from"./CustomerPutPoolRemindList-CNkUNKod.js";import O,{__tla as tt}from"./ClueFollowList-Bo34-PaD.js";import at,{__tla as rt}from"./ContractAuditList-D8AzDS3J.js";import et,{__tla as _t}from"./ContractRemindList-wxZmK6_L.js";import lt,{__tla as ot}from"./ReceivablePlanRemindList-DGe78sPQ.js";import ct,{__tla as nt}from"./ReceivableAuditList-Bo4IdH3w.js";import{h as mt,i as st,j as ut,__tla as it}from"./index-CCPyMtv-.js";import{d as yt,__tla as dt}from"./index-C_SCPERO.js";import{f as ht,h as pt,__tla as ft}from"./index-BYuPmJ1X.js";import{e as vt,__tla as kt}from"./index-CbcZjzqw.js";import{f as wt,__tla as Ct}from"./index-BC06Brp1.js";import{__tla as Pt}from"./index-BBLwwrga.js";import{__tla as bt}from"./index-CS70nJJ8.js";import{__tla as xt}from"./DictTag-BDZzHcIz.js";import"./color-BN7ZL7BD.js";import{__tla as Rt}from"./ContentWrap-DZg14iby.js";import{__tla as gt}from"./el-card-Dvjjuipo.js";import{__tla as At}from"./formatTime-BCfRGyrF.js";import"./common-BQQO87UM.js";import{__tla as Ft}from"./el-text-vv1naHK-.js";import{__tla as Tt}from"./ReceivableForm-BTabXLiZ.js";import{__tla as jt}from"./Dialog-BjBBVYCI.js";let R,Et=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Pt}catch{}})(),(()=>{try{return bt}catch{}})(),(()=>{try{return xt}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return At}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return Tt}catch{}})(),(()=>{try{return jt}catch{}})()]).then(async()=>{let u,i,y;u={class:"side-item-list"},i=["onClick"],y=B({name:"CrmBacklog",__name:"index",setup(Bt){const r=e("customerTodayContact"),d=e(0),h=e(0),p=e(0),f=e(0),v=e(0),k=e(0),w=e(0),C=e(0),g=e([{name:"\u4ECA\u65E5\u9700\u8054\u7CFB\u5BA2\u6237",menu:"customerTodayContact",count:f},{name:"\u5206\u914D\u7ED9\u6211\u7684\u7EBF\u7D22",menu:"clueFollow",count:d},{name:"\u5206\u914D\u7ED9\u6211\u7684\u5BA2\u6237",menu:"customerFollow",count:h},{name:"\u5F85\u8FDB\u5165\u516C\u6D77\u7684\u5BA2\u6237",menu:"customerPutPoolRemind",count:p},{name:"\u5F85\u5BA1\u6838\u5408\u540C",menu:"contractAudit",count:v},{name:"\u5F85\u5BA1\u6838\u56DE\u6B3E",menu:"receivableAudit",count:w},{name:"\u5F85\u56DE\u6B3E\u63D0\u9192",menu:"receivablePlanRemind",count:C},{name:"\u5373\u5C06\u5230\u671F\u7684\u5408\u540C",menu:"contractRemind",count:k}]),P=()=>{mt().then(t=>f.value=t),st().then(t=>p.value=t),ut().then(t=>h.value=t),yt().then(t=>d.value=t),ht().then(t=>v.value=t),pt().then(t=>k.value=t),vt().then(t=>w.value=t),wt().then(t=>C.value=t)};return I(async()=>{P()}),J(async()=>{P()}),(t,It)=>{const A=S,F=D,b=L,T=M;return a(),m(x,null,[n(A,{title:"\u3010\u901A\u7528\u3011\u8DDF\u8FDB\u8BB0\u5F55\u3001\u5F85\u529E\u4E8B\u9879",url:"https://doc.iocoder.cn/crm/follow-up/"}),n(T,{gutter:20},{default:s(()=>[n(b,{span:4,class:"min-w-[200px]"},{default:s(()=>[U("div",u,[(a(!0),m(x,null,Z(_(g),(c,j)=>(a(),m("div",{key:j,class:$([_(r)==c.menu?"side-item-select":"side-item-default","side-item"]),onClick:Jt=>(E=>{r.value=E.menu})(c)},[q(z(c.name)+" ",1),c.count>0?(a(),l(F,{key:0,max:99,value:c.count},null,8,["value"])):o("",!0)],10,i))),128))])]),_:1}),n(b,{span:20,xs:24},{default:s(()=>[_(r)==="customerTodayContact"?(a(),l(Y,{key:0})):o("",!0),_(r)==="clueFollow"?(a(),l(O,{key:1})):o("",!0),_(r)==="contractAudit"?(a(),l(at,{key:2})):o("",!0),_(r)==="receivableAudit"?(a(),l(ct,{key:3})):o("",!0),_(r)==="contractRemind"?(a(),l(et,{key:4})):o("",!0),_(r)==="customerFollow"?(a(),l(W,{key:5})):o("",!0),_(r)==="customerPutPoolRemind"?(a(),l(H,{key:6})):o("",!0),_(r)==="receivablePlanRemind"?(a(),l(lt,{key:7})):o("",!0)]),_:1})]),_:1})],64)}}}),R=N(y,[["__scopeId","data-v-ed829911"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/backlog/index.vue"]])});export{Et as __tla,R as default};
