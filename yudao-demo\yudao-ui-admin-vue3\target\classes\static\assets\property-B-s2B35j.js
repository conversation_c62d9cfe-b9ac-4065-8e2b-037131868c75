import{_ as c,__tla as n}from"./ComponentContainerProperty-S0tn7S7r.js";import{d as p,o as i,l as y,a,_ as d,__tla as f}from"./index-Daqg4PFz.js";import{u as h,__tla as V}from"./util-BXiX1W-V.js";import{__tla as U}from"./el-card-Dvjjuipo.js";import{__tla as v}from"./index-D5jdnmIf.js";import"./color-BN7ZL7BD.js";import{__tla as b}from"./Dialog-BjBBVYCI.js";import{__tla as D}from"./Qrcode-CIHNtQVl.js";import{__tla as P}from"./el-text-vv1naHK-.js";import{__tla as W}from"./IFrame-DOdFY0xB.js";import{__tla as j}from"./vuedraggable.umd-BozBW0_1.js";import{__tla as q}from"./el-collapse-item-CUcELNOM.js";let _,x=Promise.all([(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return v}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{_=d(p({name:"UserWalletProperty",__name:"property",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(e,{emit:l}){const o=e,m=l,{formData:t}=h(o.modelValue,m);return(E,r)=>{const s=c;return i(),y(s,{modelValue:a(t).style,"onUpdate:modelValue":r[0]||(r[0]=u=>a(t).style=u)},null,8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/UserWallet/property.vue"]])});export{x as __tla,_ as default};
