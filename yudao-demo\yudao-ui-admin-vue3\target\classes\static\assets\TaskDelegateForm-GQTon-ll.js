import{d as J,r as d,o as m,l as _,w as t,i as u,a,j as f,H as T,c as C,F as R,k as j,z as N,ay as z,J as B,K as H,L as K,Z as L,O,N as P,R as Z,_ as A,__tla as E}from"./index-Daqg4PFz.js";import{_ as G,__tla as M}from"./Dialog-BjBBVYCI.js";import{d as Q,__tla as S}from"./index-CYOuQA7P.js";let g,W=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{g=A(J({name:"BpmTaskDelegateForm",__name:"TaskDelegateForm",emits:["success"],setup(X,{expose:y,emit:b}){const r=d(!1),i=d(!1),l=d({id:"",delegateUserId:void 0,reason:""}),k=d({delegateUserId:[{required:!0,message:"\u63A5\u6536\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u59D4\u6D3E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=d(),c=d([]);y({open:async o=>{r.value=!0,w(),l.value.id=o,c.value=await z()}});const U=b,h=async()=>{if(n&&await n.value.validate()){i.value=!0;try{await Q(l.value),r.value=!1,U("success")}finally{i.value=!1}}},w=()=>{var o;l.value={id:"",delegateUserId:void 0,reason:""},(o=n.value)==null||o.resetFields()};return(o,s)=>{const V=B,I=H,p=K,F=L,q=O,v=P,x=G,D=Z;return m(),_(x,{modelValue:a(r),"onUpdate:modelValue":s[3]||(s[3]=e=>N(r)?r.value=e:null),title:"\u59D4\u6D3E\u4EFB\u52A1",width:"500"},{footer:t(()=>[u(v,{disabled:a(i),type:"primary",onClick:h},{default:t(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),u(v,{onClick:s[2]||(s[2]=e=>r.value=!1)},{default:t(()=>[f("\u53D6 \u6D88")]),_:1})]),default:t(()=>[T((m(),_(q,{ref_key:"formRef",ref:n,model:a(l),rules:a(k),"label-width":"110px"},{default:t(()=>[u(p,{label:"\u63A5\u6536\u4EBA",prop:"delegateUserId"},{default:t(()=>[u(I,{modelValue:a(l).delegateUserId,"onUpdate:modelValue":s[0]||(s[0]=e=>a(l).delegateUserId=e),clearable:"",style:{width:"100%"}},{default:t(()=>[(m(!0),C(R,null,j(a(c),e=>(m(),_(V,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(p,{label:"\u59D4\u6D3E\u7406\u7531",prop:"reason"},{default:t(()=>[u(F,{modelValue:a(l).reason,"onUpdate:modelValue":s[1]||(s[1]=e=>a(l).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u59D4\u6D3E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[D,a(i)]])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/bpm/processInstance/detail/dialog/TaskDelegateForm.vue"]])});export{W as __tla,g as default};
