import{_ as mt,__tla as _t}from"./ComponentContainerProperty-S0tn7S7r.js";import{f1 as dt,f2 as ft,d$ as ht,d as K,b6 as yt,f3 as vt,r as b,o as H,c as E,i as o,w as y,j as z,g as Q,F as I,k as S,a as _,av as tt,t as gt,z as xt,x as wt,N as et,_ as at,cq as kt,L as Vt,O as bt,__tla as Ht}from"./index-Daqg4PFz.js";import{E as Et,__tla as Tt}from"./el-text-vv1naHK-.js";import{u as Dt,__tla as Ut}from"./util-BXiX1W-V.js";import{_ as zt,__tla as It}from"./AppLinkSelectDialog-D0bg80Di.js";import{_ as Ct,__tla as Lt}from"./Dialog-BjBBVYCI.js";import{E as Pt,__tla as Ot}from"./el-image-Bn34T02c.js";import{__tla as Xt}from"./el-card-Dvjjuipo.js";import{__tla as Zt}from"./index-D5jdnmIf.js";import"./color-BN7ZL7BD.js";import{__tla as $t}from"./Qrcode-CIHNtQVl.js";import{__tla as Ft}from"./IFrame-DOdFY0xB.js";import{__tla as Wt}from"./vuedraggable.umd-BozBW0_1.js";import{__tla as Mt}from"./el-collapse-item-CUcELNOM.js";import{__tla as jt}from"./ProductCategorySelect-DXgDK0XS.js";import{__tla as Gt}from"./el-tree-select-BKcJcOKx.js";import"./tree-BMqZf9_I.js";import{__tla as At}from"./category-D3voy_BE.js";let lt,qt=Promise.all([(()=>{try{return _t}catch{}})(),(()=>{try{return Ht}catch{}})(),(()=>{try{return Tt}catch{}})(),(()=>{try{return Ut}catch{}})(),(()=>{try{return It}catch{}})(),(()=>{try{return Lt}catch{}})(),(()=>{try{return Ot}catch{}})(),(()=>{try{return Xt}catch{}})(),(()=>{try{return Zt}catch{}})(),(()=>{try{return $t}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return Wt}catch{}})(),(()=>{try{return Mt}catch{}})(),(()=>{try{return jt}catch{}})(),(()=>{try{return Gt}catch{}})(),(()=>{try{return At}catch{}})()]).then(async()=>{var ot=Array.prototype.splice;function rt(e,d){var v=[];if(!e||!e.length)return v;var f=-1,l=[],s=e.length;for(d=ht(d);++f<s;){var n=e[f];d(n,f,e)&&(v.push(n),l.push(f))}return function(i,r){for(var p=i?r.length:0,g=p-1;p--;){var c=r[p];if(p==g||c!==T){var T=c;dt(c)?ot.call(i,c,1):ft(i,c)}}}(e,l),v}const U=100;var x=(e=>(e[e.LEFT=0]="LEFT",e[e.TOP=1]="TOP",e[e.WIDTH=2]="WIDTH",e[e.HEIGHT=3]="HEIGHT",e))(x||{});let W,C,M,j,G,A;W=[{position:"\u5DE6\u4E0A\u89D2",types:[0,1,2,3],style:{left:"-5px",top:"-5px",cursor:"nwse-resize"}},{position:"\u4E0A\u65B9\u4E2D\u95F4",types:[1,3],style:{left:"50%",top:"-5px",cursor:"n-resize",transform:"translateX(-50%)"}},{position:"\u53F3\u4E0A\u89D2",types:[1,2,3],style:{right:"-5px",top:"-5px",cursor:"nesw-resize"}},{position:"\u53F3\u4FA7\u4E2D\u95F4",types:[2],style:{right:"-5px",top:"50%",cursor:"e-resize",transform:"translateX(-50%)"}},{position:"\u53F3\u4E0B\u89D2",types:[2,3],style:{right:"-5px",bottom:"-5px",cursor:"nwse-resize"}},{position:"\u4E0B\u65B9\u4E2D\u95F4",types:[3],style:{left:"50%",bottom:"-5px",cursor:"s-resize",transform:"translateX(-50%)"}},{position:"\u5DE6\u4E0B\u89D2",types:[0,2,3],style:{left:"-5px",bottom:"-5px",cursor:"nesw-resize"}},{position:"\u5DE6\u4FA7\u4E2D\u95F4",types:[0,2],style:{left:"-5px",top:"50%",cursor:"w-resize",transform:"translateX(-50%)"}}],C=(e,d,v)=>{d.stopPropagation();const{clientX:f,clientY:l}=d,{left:s,top:n,width:i,height:r}=e;document.onmousemove=p=>{const g=p.clientX-f,c=p.clientY-l;v(s,n,i,r,g,c)},document.onmouseup=()=>{document.onmousemove=null,document.onmouseup=null}},M=["onMousedown","onDblclick"],j={class:"pointer-events-none select-none"},G=["onMousedown"],A=at(K({name:"HotZoneEditDialog",__name:"index",props:{modelValue:yt(),imgUrl:vt().def("")},emits:["update:modelValue"],setup(e,{expose:d,emit:v}){const f=e,l=v,s=b([]),n=b(!1);d({open:()=>{var a;s.value=(a=f.modelValue,(a==null?void 0:a.map(t=>({...t,left:t.left*=2,top:t.top*=2,width:t.width*=2,height:t.height*=2})))||[]),n.value=!0}});const i=b(),r=()=>{s.value.push({width:U,height:U,top:0,left:0})},p=(a,t)=>{t>=0&&t<=i.value.offsetWidth-a.width&&(a.left=t)},g=(a,t)=>{t>=0&&t<=i.value.offsetHeight-a.height&&(a.top=t)},c=(a,t)=>{t>=U&&a.left+t<=i.value.offsetWidth&&(a.width=t)},T=(a,t)=>{t>=U&&a.top+t<=i.value.offsetHeight&&(a.height=t)},L=()=>{n.value=!1},P=()=>{const a=(t=>(t==null?void 0:t.map(w=>({...w,left:w.left/=2,top:w.top/=2,width:w.width/=2,height:w.height/=2})))||[])(s.value);l("update:modelValue",a)},h=b(),q=b(),st=a=>{a&&h.value&&(h.value.name=a.name,h.value.url=a.path)};return(a,t)=>{const w=Pt,O=wt,J=et,nt=Ct,it=zt;return H(),E(I,null,[o(nt,{modelValue:_(n),"onUpdate:modelValue":t[0]||(t[0]=m=>xt(n)?n.value=m:null),title:"\u8BBE\u7F6E\u70ED\u533A",width:"780",onClose:P},{footer:y(()=>[o(J,{onClick:r,type:"primary",plain:""},{default:y(()=>[o(O,{icon:"ep:plus",class:"mr-5px"}),z(" \u6DFB\u52A0\u70ED\u533A ")]),_:1}),o(J,{onClick:L,type:"primary",plain:""},{default:y(()=>[o(O,{icon:"ep:check",class:"mr-5px"}),z(" \u786E\u5B9A ")]),_:1})]),default:y(()=>[Q("div",{ref_key:"container",ref:i,class:"relative h-full w-750px"},[o(w,{src:e.imgUrl,class:"pointer-events-none h-full w-750px select-none"},null,8,["src"]),(H(!0),E(I,null,S(_(s),(m,pt)=>(H(),E("div",{key:pt,class:"hot-zone",style:tt({width:`${m.width}px`,height:`${m.height}px`,top:`${m.top}px`,left:`${m.left}px`}),onMousedown:V=>((u,X)=>{C(u,X,(k,D,R,Y,Z,$)=>{p(u,k+Z),g(u,D+$)})})(m,V),onDblclick:V=>{return u=m,h.value=u,void q.value.open(u.url);var u}},[Q("span",j,gt(m.name||"\u53CC\u51FB\u9009\u62E9\u94FE\u63A5"),1),o(O,{icon:"ep:close",class:"delete",size:14,onClick:V=>{return u=m,void rt(s.value,u);var u}},null,8,["onClick"]),(H(!0),E(I,null,S(_(W),(V,u)=>(H(),E("span",{class:"ctrl-dot",key:u,style:tt(V.style),onMousedown:X=>((k,D,R)=>{C(k,R,(Y,Z,$,ut,N,B)=>{D.types.forEach(ct=>{switch(ct){case x.LEFT:p(k,Y+N);break;case x.TOP:g(k,Z+B);break;case x.WIDTH:{const F=D.types.includes(x.LEFT)?-1:1;c(k,$+N*F)}break;case x.HEIGHT:{const F=D.types.includes(x.TOP)?-1:1;T(k,ut+B*F)}}})})})(m,V,X)},null,44,G))),128))],44,M))),128))],512)]),_:1},8,["modelValue"]),o(it,{ref_key:"appLinkDialogRef",ref:q,onAppLinkChange:st},null,512)],64)}}}),[["__scopeId","data-v-e60b1a26"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/HotZone/components/HotZoneEditDialog/index.vue"]]),lt=at(K({name:"HotZoneProperty",__name:"property",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(e,{emit:d}){const v=e,f=d,{formData:l}=Dt(v.modelValue,f),s=b(),n=()=>{s.value.open()};return(i,r)=>{const p=Et,g=kt,c=Vt,T=bt,L=et,P=mt;return H(),E(I,null,[o(P,{modelValue:_(l).style,"onUpdate:modelValue":r[1]||(r[1]=h=>_(l).style=h)},{default:y(()=>[o(T,{"label-width":"80px",model:_(l),class:"m-t-8px"},{default:y(()=>[o(c,{label:"\u4E0A\u4F20\u56FE\u7247",prop:"imgUrl"},{default:y(()=>[o(g,{modelValue:_(l).imgUrl,"onUpdate:modelValue":r[0]||(r[0]=h=>_(l).imgUrl=h),height:"50px",width:"auto",class:"min-w-80px"},{tip:y(()=>[o(p,{type:"info",size:"small"},{default:y(()=>[z(" \u63A8\u8350\u5BBD\u5EA6 750")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),o(L,{type:"primary",plain:"",class:"w-full",onClick:n},{default:y(()=>[z(" \u8BBE\u7F6E\u70ED\u533A ")]),_:1})]),_:1},8,["modelValue"]),o(A,{ref_key:"editDialogRef",ref:s,modelValue:_(l).list,"onUpdate:modelValue":r[2]||(r[2]=h=>_(l).list=h),"img-url":_(l).imgUrl},null,8,["modelValue","img-url"])],64)}}}),[["__scopeId","data-v-1dfabffd"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/HotZone/property.vue"]])});export{qt as __tla,lt as default};
