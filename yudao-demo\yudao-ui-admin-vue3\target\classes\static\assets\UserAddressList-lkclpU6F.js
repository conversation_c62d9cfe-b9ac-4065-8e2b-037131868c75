import{bE as y,d as b,r as l,C as w,H as v,a as t,o as h,l as g,w as p,i as a,G as x,P as A,Q as I,R as S,_ as U,__tla as N}from"./index-Daqg4PFz.js";import{_ as T,__tla as C}from"./DictTag-BDZzHcIz.js";import{d as L,__tla as M}from"./formatTime-BCfRGyrF.js";import"./color-BN7ZL7BD.js";let d,O=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return M}catch{}})()]).then(async()=>{d=U(b({__name:"UserAddressList",props:{userId:{type:Number,required:!0}},setup(n){const{userId:u}=n,r=l(!0);l(0);const s=l([]),o=async()=>{r.value=!0;try{s.value=await(async i=>await y.get({url:"/member/address/list",params:i}))({userId:u})}finally{r.value=!1}};return w(()=>{o()}),(i,P)=>{const e=A,_=T,m=I,c=S;return v((h(),g(m,{data:t(s),stripe:!0,"show-overflow-tooltip":!0},{default:p(()=>[a(e,{label:"\u5730\u5740\u7F16\u53F7",align:"center",prop:"id",width:"150px"}),a(e,{label:"\u6536\u4EF6\u4EBA\u540D\u79F0",align:"center",prop:"name",width:"150px"}),a(e,{label:"\u624B\u673A\u53F7",align:"center",prop:"mobile",width:"150px"}),a(e,{label:"\u5730\u533A\u7F16\u7801",align:"center",prop:"areaId",width:"150px"}),a(e,{label:"\u6536\u4EF6\u8BE6\u7EC6\u5730\u5740",align:"center",prop:"detailAddress"}),a(e,{label:"\u662F\u5426\u9ED8\u8BA4",align:"center",prop:"defaultStatus",width:"150px"},{default:p(f=>[a(_,{type:t(x).COMMON_STATUS,value:Number(f.row.defaultStatus)},null,8,["type","value"])]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(L),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[c,t(r)]])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/member/user/detail/UserAddressList.vue"]])});export{O as __tla,d as default};
