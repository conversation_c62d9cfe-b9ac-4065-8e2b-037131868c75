import{_ as U,__tla as V}from"./ComponentContainerProperty-S0tn7S7r.js";import{d as b,o as g,l as w,w as o,i as r,a,j,ck as v,L as x,cs as P,cq as q,cj as z,O as D,_ as O,__tla as k}from"./index-Daqg4PFz.js";import{u as E,__tla as J}from"./util-BXiX1W-V.js";import{__tla as L}from"./el-card-Dvjjuipo.js";import{__tla as A}from"./index-D5jdnmIf.js";import"./color-BN7ZL7BD.js";import{__tla as B}from"./Dialog-BjBBVYCI.js";import{__tla as C}from"./Qrcode-CIHNtQVl.js";import{__tla as F}from"./el-text-vv1naHK-.js";import{__tla as G}from"./IFrame-DOdFY0xB.js";import{__tla as H}from"./vuedraggable.umd-BozBW0_1.js";import{__tla as I}from"./el-collapse-item-CUcELNOM.js";let m,K=Promise.all([(()=>{try{return V}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{m=O(b({name:"VideoPlayerProperty",__name:"property",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(s,{emit:u}){const p=s,i=u,{formData:t}=E(p.modelValue,i);return(M,e)=>{const d=v,_=x,n=P,c=q,y=z,f=D,h=U;return g(),w(h,{modelValue:a(t).style,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).style=l)},{style:o(()=>[r(_,{label:"\u9AD8\u5EA6",prop:"height"},{default:o(()=>[r(d,{modelValue:a(t).style.height,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).style.height=l),max:500,min:100,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),default:o(()=>[r(f,{"label-width":"80px",model:a(t)},{default:o(()=>[r(_,{label:"\u4E0A\u4F20\u89C6\u9891",prop:"videoUrl"},{default:o(()=>[r(n,{modelValue:a(t).videoUrl,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).videoUrl=l),"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue"])]),_:1}),r(_,{label:"\u4E0A\u4F20\u5C01\u9762",prop:"posterUrl"},{default:o(()=>[r(c,{modelValue:a(t).posterUrl,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).posterUrl=l),draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},{tip:o(()=>[j(" \u5EFA\u8BAE\u5BBD\u5EA6750 ")]),_:1},8,["modelValue"])]),_:1}),r(_,{label:"\u81EA\u52A8\u64AD\u653E",prop:"autoplay"},{default:o(()=>[r(y,{modelValue:a(t).autoplay,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).autoplay=l)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/VideoPlayer/property.vue"]])});export{K as __tla,m as default};
