import{bE as s,__tla as a}from"./index-Daqg4PFz.js";let r,p=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{r={getProcessExpressionPage:async e=>await s.get({url:"/bpm/process-expression/page",params:e}),getProcessExpression:async e=>await s.get({url:"/bpm/process-expression/get?id="+e}),createProcessExpression:async e=>await s.post({url:"/bpm/process-expression/create",data:e}),updateProcessExpression:async e=>await s.put({url:"/bpm/process-expression/update",data:e}),deleteProcessExpression:async e=>await s.delete({url:"/bpm/process-expression/delete?id="+e}),exportProcessExpression:async e=>await s.download({url:"/bpm/process-expression/export-excel",params:e})}});export{r as P,p as __tla};
