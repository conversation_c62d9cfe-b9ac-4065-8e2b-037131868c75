import{d as ra,I as oa,n as na,r as _,f as ia,u as ca,C as sa,T as ma,o as m,c as L,i as a,w as t,a as l,U as T,F as X,k as ua,l as p,j as c,H as f,z as pa,t as k,dV as S,dX as da,G as _a,a9 as fa,Z as ya,L as wa,J as ha,K as ba,x as ga,N as ka,O as Ca,A as va,B as xa,P as Na,v as Ua,Q as Va,R as Ia,_ as Ta,__tla as Sa}from"./index-Daqg4PFz.js";import{_ as Pa,__tla as Da}from"./index-BBLwwrga.js";import{_ as Ra,__tla as qa}from"./DictTag-BDZzHcIz.js";import{_ as za,__tla as Ka}from"./ContentWrap-DZg14iby.js";import{_ as Aa,__tla as Ba}from"./index-CmwFi8Xl.js";import{b as P,d as D,__tla as Fa}from"./formatTime-BCfRGyrF.js";import{d as ja}from"./download--D_IyRio.js";import{e as Ha,i as Ja,j as La,s as Xa,__tla as Za}from"./index-BYuPmJ1X.js";import $a,{__tla as Ga}from"./ContractForm-D-v6dYOX.js";import{a as Ma,__tla as Oa}from"./index-CCPyMtv-.js";import{__tla as Qa}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Wa}from"./el-card-Dvjjuipo.js";import{__tla as Ea}from"./Dialog-BjBBVYCI.js";import{__tla as Ya}from"./index-78jf5nCk.js";import{__tla as ae}from"./index-W7V8hhz8.js";import{__tla as ee}from"./ContractProductForm-Cu5yKHq2.js";import{__tla as te}from"./index-DlXW_sq5.js";let Z,le=Promise.all([(()=>{try{return Sa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Qa}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{Z=Ta(ra({name:"CrmContract",__name:"index",setup(re){const b=oa(),{t:$}=na(),N=_(!0),R=_(0),q=_([]),i=ia({pageNo:1,pageSize:10,sceneType:"1",name:null,customerId:null,orderDate:[],no:null}),z=_(),U=_(!1),V=_("1"),K=_([]),G=s=>{i.sceneType=s.paneName,h()},w=async()=>{N.value=!0;try{const s=await Ha(i);q.value=s.list,R.value=s.total}finally{N.value=!1}},h=()=>{i.pageNo=1,w()},M=()=>{z.value.resetFields(),h()},A=_(),B=(s,o)=>{A.value.open(s,o)},O=async()=>{try{await b.exportConfirm(),U.value=!0;const s=await La(i);ja.excel(s,"\u5408\u540C.xls")}catch{}finally{U.value=!1}},{push:g}=ca(),F=s=>{g({name:"CrmContractDetail",params:{id:s}})};return sa(async()=>{await w(),K.value=await Ma()}),(s,o)=>{const j=Aa,H=ya,C=wa,Q=ha,W=ba,v=ga,u=ka,E=Ca,J=za,I=va,Y=xa,r=Na,x=Ua,aa=Ra,ea=Va,ta=Pa,y=ma("hasPermi"),la=Ia;return m(),L(X,null,[a(j,{title:"\u3010\u5408\u540C\u3011\u5408\u540C\u7BA1\u7406\u3001\u5408\u540C\u63D0\u9192",url:"https://doc.iocoder.cn/crm/contract/"}),a(j,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),a(J,null,{default:t(()=>[a(E,{ref_key:"queryFormRef",ref:z,inline:!0,model:l(i),class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(C,{label:"\u5408\u540C\u7F16\u53F7",prop:"no"},{default:t(()=>[a(H,{modelValue:l(i).no,"onUpdate:modelValue":o[0]||(o[0]=e=>l(i).no=e),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u7F16\u53F7",onKeyup:T(h,["enter"])},null,8,["modelValue"])]),_:1}),a(C,{label:"\u5408\u540C\u540D\u79F0",prop:"name"},{default:t(()=>[a(H,{modelValue:l(i).name,"onUpdate:modelValue":o[1]||(o[1]=e=>l(i).name=e),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u540D\u79F0",onKeyup:T(h,["enter"])},null,8,["modelValue"]),a(C,{label:"\u5BA2\u6237",prop:"customerId"},{default:t(()=>[a(W,{modelValue:l(i).customerId,"onUpdate:modelValue":o[2]||(o[2]=e=>l(i).customerId=e),class:"!w-240px",clearable:"","lable-key":"name",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237","value-key":"id",onKeyup:T(h,["enter"])},{default:t(()=>[(m(!0),L(X,null,ua(l(K),e=>(m(),p(Q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(C,null,{default:t(()=>[a(u,{onClick:h},{default:t(()=>[a(v,{class:"mr-5px",icon:"ep:search"}),c(" \u641C\u7D22 ")]),_:1}),a(u,{onClick:M},{default:t(()=>[a(v,{class:"mr-5px",icon:"ep:refresh"}),c(" \u91CD\u7F6E ")]),_:1}),f((m(),p(u,{type:"primary",onClick:o[3]||(o[3]=e=>B("create"))},{default:t(()=>[a(v,{class:"mr-5px",icon:"ep:plus"}),c(" \u65B0\u589E ")]),_:1})),[[y,["crm:contract:create"]]]),f((m(),p(u,{loading:l(U),plain:"",type:"success",onClick:O},{default:t(()=>[a(v,{class:"mr-5px",icon:"ep:download"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["crm:contract:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(J,null,{default:t(()=>[a(Y,{modelValue:l(V),"onUpdate:modelValue":o[4]||(o[4]=e=>pa(V)?V.value=e:null),onTabClick:G},{default:t(()=>[a(I,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),a(I,{label:"\u6211\u53C2\u4E0E\u7684",name:"2"}),a(I,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),f((m(),p(ea,{data:l(q),"show-overflow-tooltip":!0,stripe:!0},{default:t(()=>[a(r,{align:"center",fixed:"left",label:"\u5408\u540C\u7F16\u53F7",prop:"no",width:"180"}),a(r,{align:"center",fixed:"left",label:"\u5408\u540C\u540D\u79F0",prop:"name",width:"160"},{default:t(e=>[a(x,{underline:!1,type:"primary",onClick:d=>F(e.row.id)},{default:t(()=>[c(k(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:t(e=>[a(x,{underline:!1,type:"primary",onClick:d=>{return n=e.row.customerId,void g({name:"CrmCustomerDetail",params:{id:n}});var n}},{default:t(()=>[c(k(e.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5546\u673A\u540D\u79F0",prop:"businessName",width:"130"},{default:t(e=>[a(x,{underline:!1,type:"primary",onClick:d=>{return n=e.row.businessId,void g({name:"CrmBusinessDetail",params:{id:n}});var n}},{default:t(()=>[c(k(e.row.businessName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalPrice",width:"140",formatter:l(S)},null,8,["formatter"]),a(r,{align:"center",label:"\u4E0B\u5355\u65F6\u95F4",prop:"orderDate",width:"120",formatter:l(P)},null,8,["formatter"]),a(r,{align:"center",label:"\u5408\u540C\u5F00\u59CB\u65F6\u95F4",prop:"startTime",width:"120",formatter:l(P)},null,8,["formatter"]),a(r,{align:"center",label:"\u5408\u540C\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"120",formatter:l(P)},null,8,["formatter"]),a(r,{align:"center",label:"\u5BA2\u6237\u7B7E\u7EA6\u4EBA",prop:"contactName",width:"130"},{default:t(e=>[a(x,{underline:!1,type:"primary",onClick:d=>{return n=e.row.signContactId,void g({name:"CrmContactDetail",params:{id:n}});var n}},{default:t(()=>[c(k(e.row.signContactName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u516C\u53F8\u7B7E\u7EA6\u4EBA",prop:"signUserName",width:"130"}),a(r,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(r,{align:"center",label:"\u5DF2\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:l(S)},null,8,["formatter"]),a(r,{align:"center",label:"\u672A\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:l(S)},{default:t(e=>[c(k(l(da)(e.row.totalPrice-e.row.totalReceivablePrice)),1)]),_:1},8,["formatter"]),a(r,{formatter:l(D),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(r,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),a(r,{formatter:l(D),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(r,{formatter:l(D),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),a(r,{align:"center",fixed:"right",label:"\u5408\u540C\u72B6\u6001",prop:"auditStatus",width:"120"},{default:t(e=>[a(aa,{type:l(_a).CRM_AUDIT_STATUS,value:e.row.auditStatus},null,8,["type","value"])]),_:1}),a(r,{fixed:"right",label:"\u64CD\u4F5C",width:"250"},{default:t(e=>[e.row.auditStatus===0?f((m(),p(u,{key:0,link:"",type:"primary",onClick:d=>B("update",e.row.id)},{default:t(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["crm:contract:update"]]]):fa("",!0),e.row.auditStatus===0?f((m(),p(u,{key:1,link:"",type:"primary",onClick:d=>(async n=>{await b.confirm(`\u60A8\u786E\u5B9A\u63D0\u4EA4\u3010${n.name}\u3011\u5BA1\u6838\u5417\uFF1F`),await Xa(n.id),b.success("\u63D0\u4EA4\u5BA1\u6838\u6210\u529F\uFF01"),await w()})(e.row)},{default:t(()=>[c(" \u63D0\u4EA4\u5BA1\u6838 ")]),_:2},1032,["onClick"])),[[y,["crm:contract:update"]]]):f((m(),p(u,{key:2,link:"",type:"primary",onClick:d=>{return n=e.row,void g({name:"BpmProcessInstanceDetail",query:{id:n.processInstanceId}});var n}},{default:t(()=>[c(" \u67E5\u770B\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["crm:contract:update"]]]),f((m(),p(u,{link:"",type:"primary",onClick:d=>F(e.row.id)},{default:t(()=>[c(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["crm:contract:query"]]]),f((m(),p(u,{link:"",type:"danger",onClick:d=>(async n=>{try{await b.delConfirm(),await Ja(n),b.success($("common.delSuccess")),await w()}catch{}})(e.row.id)},{default:t(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["crm:contract:delete"]]])]),_:1})]),_:1},8,["data"])),[[la,l(N)]]),a(ta,{limit:l(i).pageSize,"onUpdate:limit":o[5]||(o[5]=e=>l(i).pageSize=e),page:l(i).pageNo,"onUpdate:page":o[6]||(o[6]=e=>l(i).pageNo=e),total:l(R),onPagination:w},null,8,["limit","page","total"])]),_:1}),a($a,{ref_key:"formRef",ref:A,onSuccess:w},null,512)],64)}}}),[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/crm/contract/index.vue"]])});export{le as __tla,Z as default};
