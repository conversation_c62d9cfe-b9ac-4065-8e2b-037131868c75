import{d as p,o as e,c as a,i as c,F as _,k as u,av as d,t as m,_ as h,__tla as y}from"./index-Daqg4PFz.js";import{E as f,__tla as v}from"./el-image-Bn34T02c.js";let r,x=Promise.all([(()=>{try{return y}catch{}})(),(()=>{try{return v}catch{}})()]).then(async()=>{let s,l;s={class:"relative h-full min-h-30px w-full"},l=p({name:"HotZone",__name:"index",props:{property:{type:Object,required:!0}},setup:w=>(o,$)=>{const n=f;return e(),a("div",s,[c(n,{src:o.property.imgUrl,class:"pointer-events-none h-full w-full select-none"},null,8,["src"]),(e(!0),a(_,null,u(o.property.list,(t,i)=>(e(),a("div",{key:i,class:"hot-zone",style:d({width:`${t.width}px`,height:`${t.height}px`,top:`${t.top}px`,left:`${t.left}px`})},m(t.name),5))),128))])}}),r=h(l,[["__scopeId","data-v-74b676c6"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/HotZone/index.vue"]])});export{x as __tla,r as default};
