import{d as $,r as o,at as q,o as t,l as c,w as g,c as l,F as v,k as w,a as s,g as b,av as x,a0 as z,t as k,a9 as y,_ as D,__tla as F}from"./index-Daqg4PFz.js";import{E as H,a as J,__tla as O}from"./el-carousel-item-g4NI8jnR.js";import{E as P,__tla as A}from"./el-image-Bn34T02c.js";let C,B=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{let d,h;d={class:"flex flex-row flex-wrap"},h=$({name:"MenuSwiper",__name:"index",props:{property:{type:Object,required:!0}},setup(j){const a=j,p=o([]),_=o(0),i=o(0),m=o("");return q(()=>a.property,()=>{m.value=1/a.property.column*100+"%",i.value=32+(a.property.layout==="iconText"?62:42),_.value=a.property.row*i.value;const n=a.property.row*a.property.column;p.value=[];let r=[];for(const u of a.property.list)r.length===n&&(r=[]),r.length===0&&p.value.push(r),r.push(u)},{immediate:!0,deep:!0}),(n,r)=>{const u=P,E=H,U=J;return t(),c(U,{height:`${s(_)}px`,autoplay:!1,arrow:"hover","indicator-position":"outside"},{default:g(()=>[(t(!0),l(v,null,w(s(p),(M,S)=>(t(),c(E,{key:S},{default:g(()=>[b("div",d,[(t(!0),l(v,null,w(M,(e,T)=>{var f;return t(),l("div",{key:T,class:"relative flex flex-col items-center justify-center",style:x({width:s(m),height:`${s(i)}px`})},[b("div",{class:z(["relative","h-42px w-42px"])},[(f=e.badge)!=null&&f.show?(t(),l("span",{key:0,class:"absolute right--10px top--10px z-1 h-20px rounded-10px p-x-6px text-center text-12px leading-20px",style:x({color:e.badge.textColor,backgroundColor:e.badge.bgColor})},k(e.badge.text),5)):y("",!0),e.iconUrl?(t(),c(u,{key:1,src:e.iconUrl,class:"h-full w-full"},null,8,["src"])):y("",!0)],2),n.property.layout==="iconText"?(t(),l("span",{key:0,class:"text-12px",style:x({color:e.titleColor,height:"20px",lineHeight:"20px"})},k(e.title),5)):y("",!0)],4)}),128))])]),_:2},1024))),128))]),_:1},8,["height"])}}}),C=D(h,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/DiyEditor/components/mobile/MenuSwiper/index.vue"]])});export{B as __tla,C as default};
