import{d as D,I as E,n as X,r as d,f as Y,C as $,T as aa,o,c as v,i as a,w as l,a as t,U as ea,F as x,k as P,V as la,G as F,l as u,j as i,H as f,g as ta,t as S,Z as ra,L as sa,J as na,K as oa,x as ia,N as ua,O as ca,P as _a,ax as pa,Q as da,R as ma,_ as fa,__tla as ya}from"./index-Daqg4PFz.js";import{_ as ha,__tla as ga}from"./index-BBLwwrga.js";import{_ as ba,__tla as ka}from"./DictTag-BDZzHcIz.js";import{_ as wa,__tla as va}from"./ContentWrap-DZg14iby.js";import{_ as xa,__tla as Sa}from"./index-CmwFi8Xl.js";import{d as Ca,__tla as Ta}from"./formatTime-BCfRGyrF.js";import{C as Va,g as Oa,d as Ua,__tla as Na}from"./ClientForm-OmZEgiTy.js";import{__tla as za}from"./index-CS70nJJ8.js";import"./color-BN7ZL7BD.js";import{__tla as Ma}from"./el-card-Dvjjuipo.js";import{__tla as Aa}from"./Dialog-BjBBVYCI.js";import"./constants-WoCEnNvc.js";let G,Pa=Promise.all([(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Aa}catch{}})()]).then(async()=>{let C,T;C=["src"],T=D({name:"SystemOAuth2Client",__name:"index",setup(Fa){const V=E(),{t:I}=X(),y=d(!0),O=d(0),U=d([]),s=Y({pageNo:1,pageSize:10,name:null,status:void 0}),N=d(),c=async()=>{y.value=!0;try{const _=await Oa(s);U.value=_.list,O.value=_.total}finally{y.value=!1}},h=()=>{s.pageNo=1,c()},R=()=>{N.value.resetFields(),h()},z=d(),M=(_,r)=>{z.value.open(_,r)};return $(()=>{c()}),(_,r)=>{const J=xa,K=ra,g=sa,Q=na,j=oa,b=ia,p=ua,q=ca,A=wa,n=_a,H=ba,L=pa,W=da,Z=ha,k=aa("hasPermi"),B=ma;return o(),v(x,null,[a(J,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),a(A,null,{default:l(()=>[a(q,{class:"-mb-15px",model:t(s),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:l(()=>[a(g,{label:"\u5E94\u7528\u540D",prop:"name"},{default:l(()=>[a(K,{modelValue:t(s).name,"onUpdate:modelValue":r[0]||(r[0]=e=>t(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:ea(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(g,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[a(j,{modelValue:t(s).status,"onUpdate:modelValue":r[1]||(r[1]=e=>t(s).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(o(!0),v(x,null,P(t(la)(t(F).COMMON_STATUS),e=>(o(),u(Q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(p,{onClick:h},{default:l(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),i(" \u641C\u7D22")]),_:1}),a(p,{onClick:R},{default:l(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),i(" \u91CD\u7F6E")]),_:1}),f((o(),u(p,{plain:"",type:"primary",onClick:r[2]||(r[2]=e=>M("create"))},{default:l(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),i(" \u65B0\u589E ")]),_:1})),[[k,["system:oauth2-client:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:l(()=>[f((o(),u(W,{data:t(U)},{default:l(()=>[a(n,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",align:"center",prop:"clientId"}),a(n,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",align:"center",prop:"secret"}),a(n,{label:"\u5E94\u7528\u540D",align:"center",prop:"name"}),a(n,{label:"\u5E94\u7528\u56FE\u6807",align:"center",prop:"logo"},{default:l(e=>[ta("img",{width:"40px",height:"40px",src:e.row.logo},null,8,C)]),_:1}),a(n,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:l(e=>[a(H,{type:t(F).COMMON_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u8BBF\u95EE\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"accessTokenValiditySeconds"},{default:l(e=>[i(S(e.row.accessTokenValiditySeconds)+" \u79D2",1)]),_:1}),a(n,{label:"\u5237\u65B0\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"refreshTokenValiditySeconds"},{default:l(e=>[i(S(e.row.refreshTokenValiditySeconds)+" \u79D2",1)]),_:1}),a(n,{label:"\u6388\u6743\u7C7B\u578B",align:"center",prop:"authorizedGrantTypes"},{default:l(e=>[(o(!0),v(x,null,P(e.row.authorizedGrantTypes,(w,m)=>(o(),u(L,{"disable-transitions":!0,key:m,index:m,class:"mr-5px"},{default:l(()=>[i(S(w),1)]),_:2},1032,["index"]))),128))]),_:1}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(Ca)},null,8,["formatter"]),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[f((o(),u(p,{link:"",type:"primary",onClick:w=>M("update",e.row.id)},{default:l(()=>[i(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["system:oauth2-client:update"]]]),f((o(),u(p,{link:"",type:"danger",onClick:w=>(async m=>{try{await V.delConfirm(),await Ua(m),V.success(I("common.delSuccess")),await c()}catch{}})(e.row.id)},{default:l(()=>[i(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["system:oauth2-client:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,t(y)]]),a(Z,{total:t(O),page:t(s).pageNo,"onUpdate:page":r[3]||(r[3]=e=>t(s).pageNo=e),limit:t(s).pageSize,"onUpdate:limit":r[4]||(r[4]=e=>t(s).pageSize=e),onPagination:c},null,8,["total","page","limit"])]),_:1}),a(Va,{ref_key:"formRef",ref:z,onSuccess:c},null,512)],64)}}}),G=fa(T,[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/system/oauth2/client/index.vue"]])});export{Pa as __tla,G as default};
