import{d as B,I as D,dY as E,b as N,r as V,f as P,o as n,c as b,a as r,g as w,t as H,i as a,w as l,l as O,j as g,z as Q,s as S,x as W,N as Y,cJ as F,E as G,bD as K,a5 as L,a6 as R,_ as X,__tla as Z}from"./index-Daqg4PFz.js";import{W as $,__tla as aa}from"./main-BFIJAzpS.js";import ta,{__tla as ea}from"./main-D2WNvJUY.js";import{u as la,U as ra,__tla as sa}from"./useUpload-DvwaTvLo.js";import{__tla as oa}from"./index-BBLwwrga.js";import{__tla as ca}from"./index-CS70nJJ8.js";import{__tla as ua}from"./main-CZAPo5JB.js";import{__tla as _a}from"./el-image-Bn34T02c.js";import{__tla as ia}from"./main-tYLRPXX5.js";import{__tla as na}from"./index-C7JnLY69.js";import{__tla as da}from"./index-DC2RezQi.js";import{__tla as ma}from"./formatTime-BCfRGyrF.js";let k,pa=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ma}catch{}})()]).then(async()=>{let d,m,p;d={key:0,class:"select-item2"},m={class:"item-name"},p=(s=>(L("data-v-17bdaa79"),s=s(),R(),s))(()=>w("div",{class:"el-upload__tip"}," \u683C\u5F0F\u652F\u6301 mp3/wma/wav/amr\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M\uFF0C\u64AD\u653E\u957F\u5EA6\u4E0D\u8D85\u8FC7 60s ",-1)),k=X(B({__name:"TabVoice",props:{modelValue:{type:Object,required:!0}},emits:["update:modelValue"],setup(s,{emit:j}){const x=D(),U={Authorization:"Bearer "+E()},z=s,A=j,e=N({get:()=>z.modelValue,set:t=>A("update:modelValue",t)}),o=V(!1),f=V([]),u=P({accountId:e.value.accountId,type:"voice",title:"",introduction:""}),C=t=>la(ra.Voice,10)(t),J=t=>{if(t.code!==0)return x.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;f.value=[],u.title="",u.introduction="",y(t.data)},M=()=>{e.value.mediaId=null,e.value.url=null,e.value.name=null},y=t=>{o.value=!1,e.value.mediaId=t.mediaId,e.value.url=t.url,e.value.name=t.name};return(t,c)=>{const _=S,v=W,i=Y,T=F,h=G,q=K;return n(),b("div",null,[r(e).url?(n(),b("div",d,[w("p",m,H(r(e).name),1),a(_,{class:"ope-row",justify:"center"},{default:l(()=>[a(r(ta),{url:r(e).url},null,8,["url"])]),_:1}),a(_,{class:"ope-row",justify:"center"},{default:l(()=>[a(i,{type:"danger",circle:"",onClick:M},{default:l(()=>[a(v,{icon:"ep:delete"})]),_:1})]),_:1})])):(n(),O(_,{key:1,style:{"text-align":"center"}},{default:l(()=>[a(h,{span:12,class:"col-select"},{default:l(()=>[a(i,{type:"success",onClick:c[0]||(c[0]=I=>o.value=!0)},{default:l(()=>[g(" \u7D20\u6750\u5E93\u9009\u62E9"),a(v,{icon:"ep:circle-check"})]),_:1}),a(T,{title:"\u9009\u62E9\u8BED\u97F3",modelValue:r(o),"onUpdate:modelValue":c[1]||(c[1]=I=>Q(o)?o.value=I:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:l(()=>[a(r($),{type:"voice","account-id":r(e).accountId,onSelectMaterial:y},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(h,{span:12,class:"col-add"},{default:l(()=>[a(q,{action:"http://localhost:48080/admin-api/mp/material/upload-temporary",headers:U,multiple:"",limit:1,"file-list":r(f),data:r(u),"before-upload":C,"on-success":J},{tip:l(()=>[p]),default:l(()=>[a(i,{type:"primary"},{default:l(()=>[g("\u70B9\u51FB\u4E0A\u4F20")]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1}))])}}}),[["__scopeId","data-v-17bdaa79"],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/views/mp/components/wx-reply/components/TabVoice.vue"]])});export{pa as __tla,k as default};
