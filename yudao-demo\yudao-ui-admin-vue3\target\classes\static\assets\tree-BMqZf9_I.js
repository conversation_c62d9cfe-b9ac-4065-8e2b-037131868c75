const g={id:"id",children:"children",pid:"pid"},y={children:"children",label:"name",value:"id",isLeaf:"leaf",emitPath:!1},h=r=>Object.assign({},g,r),b=(r,t={})=>{t=h(t);const{children:i}=t,n=[...r];for(let e=0;e<n.length;e++)n[e][i]&&n.splice(e+1,0,...n[e][i]);return n},v=(r,t,i={})=>{i=h(i);const n=[],e=[...r],o=new Set,{children:a}=i;for(;e.length;){const s=e[0];if(o.has(s))n.pop(),e.shift();else if(o.add(s),s[a]&&e.unshift(...s[a]),n.push(s),t(s))return n}return null},A=(r,t,i={})=>{const n=(i=h(i)).children;return function e(o){return o.map(a=>({...a})).filter(a=>(a[n]=a[n]&&e(a[n]),t(a)||a[n]&&a[n].length))}(r)},w=(r,t)=>r.map(i=>p(i,t)),p=(r,{children:t="children",conversion:i})=>{const n=Array.isArray(r[t])&&r[t].length>0,e=i(r)||{};return n?{...e,[t]:r[t].map(o=>p(o,{children:t,conversion:i}))}:{...e}},m=(r,t,i={})=>{r.forEach(n=>{const e=t(n,i)||n;n.children&&m(n.children,t,e)})},O=(r,t,i,n)=>{if(!Array.isArray(r))return console.warn("data must be an array"),[];const e=t||"id",o=i||"parentId",a="children",s={},l={},f=[];for(const c of r){const d=c[o];s[d]==null&&(s[d]=[]),l[c[e]]=c,s[d].push(c)}for(const c of r)l[c[o]]==null&&f.push(c);for(const c of f)u(c);function u(c){if(s[c[e]]!==null&&(c[a]=s[c[e]]),c[a])for(const d of c[a])u(d)}return f},S=(r,t,i,n,e)=>{t=t||"id",i=i||"parentId",e=e||Math.min(...r.map(s=>s[i]))||0;const o=JSON.parse(JSON.stringify(r)),a=o.filter(s=>{const l=o.filter(f=>s[t]===f[i]);return l.length>0&&(s.children=l),s[i]===e});return a!==""?a:r},$=(r,t)=>{if(r===void 0||!Array.isArray(r)||r.length===0)return console.warn("tree must be an array"),"";const i=r.find(o=>o.id===t);if(i!==void 0)return i.name;let n="";function e(o){for(const a of o){if(a.id===t)return n+=` / ${a.name}`,!0;if(a.children!==void 0&&a.children.length!==0&&(n+=` / ${a.name}`,e(a.children)))return!0}return!1}for(const o of r)if(n=`${o.name}`,e(o.children))break;return n};export{S as a,w as b,v as c,y as d,m as e,A as f,b as g,O as h,$ as t};
