import{bE as e,__tla as d}from"./index-Daqg4PFz.js";let r,t,l,c,s,i,n,p,m,b=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{s=async a=>await e.get({url:"/crm/receivable-plan/page",params:a}),n=async a=>await e.get({url:"/crm/receivable-plan/page-by-customer",params:a}),t=async a=>await e.get({url:"/crm/receivable-plan/get?id="+a}),r=async(a,u)=>await e.get({url:`/crm/receivable-plan/simple-list?customerId=${a}&contractId=${u}`}),l=async a=>await e.post({url:"/crm/receivable-plan/create",data:a}),m=async a=>await e.put({url:"/crm/receivable-plan/update",data:a}),c=async a=>await e.delete({url:"/crm/receivable-plan/delete?id="+a}),p=async a=>await e.download({url:"/crm/receivable-plan/export-excel",params:a}),i=async()=>await e.get({url:"/crm/receivable-plan/remind-count"})});export{b as __tla,r as a,t as b,l as c,c as d,s as e,i as f,n as g,p as h,m as u};
