import{dr as U,_ as ee,n as te,cc as ae,as as re,r as t,f as D,b as oe,at as $e,C as ue,o as W,c as _,a as e,av as w,g as s,H as ie,a8 as le,i as Oe,w as _e,a0 as Y,t as L,a9 as R,a3 as Ve,az as ce,ds as pe,dt as ve,F as Ce,k as Be,bt as Ne,j as Je,l as je,b1 as Ee,__tla as De}from"./index-Daqg4PFz.js";let he,<PERSON>=Promise.all([(()=>{try{return De}catch{}})()]).then(async()=>{function q(a,f="XwKsGlMcdPMEhR1B"){const l=U.enc.Utf8.parse(f),o=U.enc.Utf8.parse(a);return U.AES.encrypt(o,l,{mode:U.mode.ECB,padding:U.pad.Pkcs7}).toString()}function se(a){let f,l,o,g;const r=window,u=a.$el.parentNode.offsetWidth||r.offsetWidth,y=a.$el.parentNode.offsetHeight||r.offsetHeight;return f=a.imgSize.width.indexOf("%")!=-1?parseInt(a.imgSize.width)/100*u+"px":a.imgSize.width,l=a.imgSize.height.indexOf("%")!=-1?parseInt(a.imgSize.height)/100*y+"px":a.imgSize.height,o=a.barSize.width.indexOf("%")!=-1?parseInt(a.barSize.width)/100*u+"px":a.barSize.width,g=a.barSize.height.indexOf("%")!=-1?parseInt(a.barSize.height)/100*y+"px":a.barSize.height,{imgWidth:f,imgHeight:l,barWidth:o,barHeight:g}}const de={style:{position:"relative"}},ge=["src"],fe=[s("i",{class:"iconfont icon-refresh"},null,-1)],ye=["textContent"],me=["textContent"],be=["src"],Se=ee({__name:"VerifySlide",props:{captchaType:{type:String},type:{type:String,default:"1"},mode:{type:String,default:"fixed"},vSpace:{type:Number,default:5},explain:{type:String,default:""},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object,default:()=>({width:"50px",height:"50px"})},barSize:{type:Object,default:()=>({width:"310px",height:"30px"})}},setup(a){const f=a,{t:l}=te(),{mode:o,captchaType:g,type:r,blockSize:u,explain:y}=ae(f),{proxy:p}=re();let c=t(""),z=t(""),M=t(""),P=t(""),H=t(""),m=t(""),B=t(""),b=t(""),k=t(""),N=t(""),v=D({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),V=t(void 0),I=t(void 0),$=t(void 0),C=t("#ddd"),O=t(void 0),J=t("icon-right"),j=t(!1),d=t(!1),n=t(!0),S=t(""),x=t(""),X=t(0);const E=oe(()=>p.$el.querySelector(".verify-bar-area")),A=()=>{y.value===""?k.value=l("captcha.slide"):k.value=y.value,ne(),ce(()=>{let{imgHeight:i,imgWidth:h,barHeight:T,barWidth:Z}=se(p);v.imgHeight=i,v.imgWidth=h,v.barHeight=T,v.barWidth=Z,p.$parent.$emit("ready",p)}),window.removeEventListener("touchmove",function(i){K(i)}),window.removeEventListener("mousemove",function(i){K(i)}),window.removeEventListener("touchend",function(){G()}),window.removeEventListener("mouseup",function(){G()}),window.addEventListener("touchmove",function(i){K(i)}),window.addEventListener("mousemove",function(i){K(i)}),window.addEventListener("touchend",function(){G()}),window.addEventListener("mouseup",function(){G()})};$e(r,()=>{A()}),ue(()=>{A(),p.$el.onselectstart=function(){return!1}});const F=i=>{if((i=i||window.event).touches)h=i.touches[0].pageX;else var h=i.clientX;X.value=Math.floor(h-E.value.getBoundingClientRect().left),m.value=+new Date,d.value==0&&(k.value="",$.value="#337ab7",C.value="#337AB7",O.value="#fff",i.stopPropagation(),j.value=!0)},K=i=>{if(i=i||window.event,j.value&&d.value==0){if(i.touches)h=i.touches[0].pageX;else var h=i.clientX;var T=h-E.value.getBoundingClientRect().left;T>=E.value.offsetWidth-parseInt(parseInt(u.value.width)/2)-2&&(T=E.value.offsetWidth-parseInt(parseInt(u.value.width)/2)-2),T<=0&&(T=parseInt(parseInt(u.value.width)/2)),V.value=T-X.value+"px",I.value=T-X.value+"px"}},G=()=>{if(B.value=+new Date,j.value&&d.value==0){var i=parseInt((V.value||"0").replace("px",""));i=310*i/parseInt(v.imgWidth);let h={captchaType:g.value,pointJson:c.value?q(JSON.stringify({x:i,y:5}),c.value):JSON.stringify({x:i,y:5}),token:H.value};pe(h).then(T=>{if(T.repCode=="0000"){$.value="#5cb85c",C.value="#5cb85c",O.value="#fff",J.value="icon-check",n.value=!1,d.value=!0,o.value=="pop"&&setTimeout(()=>{p.$parent.clickShow=!1,Q()},1500),z.value=!0,b.value=`${((B.value-m.value)/1e3).toFixed(2)}s
            ${l("captcha.success")}`;var Z=c.value?q(H.value+"---"+JSON.stringify({x:i,y:5}),c.value):H.value+"---"+JSON.stringify({x:i,y:5});setTimeout(()=>{b.value="",p.$parent.closeBox(),p.$parent.$emit("success",{captchaVerification:Z})},1e3)}else $.value="#d9534f",C.value="#d9534f",O.value="#fff",J.value="icon-close",z.value=!1,setTimeout(function(){Q()},1e3),p.$parent.$emit("error",p),b.value=l("captcha.fail"),setTimeout(()=>{b.value=""},1e3)}),j.value=!1}},Q=async()=>{n.value=!0,N.value="",S.value="left .3s",V.value=0,I.value=void 0,x.value="width .3s",C.value="#ddd",$.value="#fff",O.value="#000",J.value="icon-right",d.value=!1,await ne(),setTimeout(()=>{x.value="",S.value="",k.value=y.value},300)},ne=async()=>{let i={captchaType:g.value};const h=await ve(i);h.repCode=="0000"?(M.value=h.repData.originalImageBase64,P.value=h.repData.jigsawImageBase64,H.value=h.repData.token,c.value=h.repData.secretKey):b.value=h.repMsg};return(i,h)=>(W(),_("div",de,[e(r)==="2"?(W(),_("div",{key:0,style:w({height:parseInt(e(v).imgHeight)+a.vSpace+"px"}),class:"verify-img-out"},[s("div",{style:w({width:e(v).imgWidth,height:e(v).imgHeight}),class:"verify-img-panel"},[s("img",{src:"data:image/png;base64,"+e(M),alt:"",style:{display:"block",width:"100%",height:"100%"}},null,8,ge),ie(s("div",{class:"verify-refresh",onClick:Q},[...fe],512),[[le,e(n)]]),Oe(Ve,{name:"tips"},{default:_e(()=>[e(b)?(W(),_("span",{key:0,class:Y([e(z)?"suc-bg":"err-bg","verify-tips"])},L(e(b)),3)):R("",!0)]),_:1})],4)],4)):R("",!0),s("div",{style:w({width:e(v).imgWidth,height:a.barSize.height,"line-height":a.barSize.height}),class:"verify-bar-area"},[s("span",{class:"verify-msg",textContent:L(e(k))},null,8,ye),s("div",{style:w({width:e(I)!==void 0?e(I):a.barSize.height,height:a.barSize.height,"border-color":e(C),transaction:e(x)}),class:"verify-left-bar"},[s("span",{class:"verify-msg",textContent:L(e(N))},null,8,me),s("div",{style:w({width:a.barSize.height,height:a.barSize.height,"background-color":e($),left:e(V),transition:e(S)}),class:"verify-move-block",onMousedown:F,onTouchstart:F},[s("i",{class:Y(["verify-icon iconfont",e(J)]),style:w({color:e(O)})},null,6),e(r)==="2"?(W(),_("div",{key:0,style:w({width:Math.floor(47*parseInt(e(v).imgWidth)/310)+"px",height:e(v).imgHeight,top:"-"+(parseInt(e(v).imgHeight)+a.vSpace)+"px","background-size":e(v).imgWidth+" "+e(v).imgHeight}),class:"verify-sub-block"},[s("img",{src:"data:image/png;base64,"+e(P),alt:"",style:{display:"block",width:"100%",height:"100%","-webkit-user-drag":"none"}},null,8,be)],4)):R("",!0)],36)],4)],4)]))}},[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Verifition/src/Verify/VerifySlide.vue"]]),xe={style:{position:"relative"}},we={class:"verify-img-out"},ze=[s("i",{class:"iconfont icon-refresh"},null,-1)],ke=["src"],Te={class:"verify-msg"},We={name:"Vue3Verify",components:{VerifySlide:Se,VerifyPoints:ee({__name:"VerifyPoints",props:{mode:{type:String,default:"fixed"},captchaType:{type:String},vSpace:{type:Number,default:5},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},barSize:{type:Object,default:()=>({width:"310px",height:"40px"})}},setup(a){const f=a,{t:l}=te(),{mode:o,captchaType:g}=ae(f),{proxy:r}=re();let u=t(""),y=t(3),p=D([]),c=D([]),z=t(1),M=t(""),P=D([]),H=t(""),m=D({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),B=D([]),b=t(""),k=t(void 0),N=t(void 0),v=t(!0),V=t(!0);ue(()=>{p.splice(0,p.length),c.splice(0,c.length),z.value=1,J(),ce(()=>{let{imgHeight:d,imgWidth:n,barHeight:S,barWidth:x}=se(r);m.imgHeight=d,m.imgWidth=n,m.barHeight=S,m.barWidth=x,r.$parent.$emit("ready",r)}),r.$el.onselectstart=function(){return!1}});const I=t(null),$=function(d,n){return{x:n.offsetX,y:n.offsetY}},C=function(d){return B.push(Object.assign({},d)),z.value+1},O=async function(){B.splice(0,B.length),k.value="#000",N.value="#ddd",V.value=!0,p.splice(0,p.length),c.splice(0,c.length),z.value=1,await J(),v.value=!0},J=async()=>{let d={captchaType:g.value};const n=await ve(d);n.repCode=="0000"?(M.value=n.repData.originalImageBase64,H.value=n.repData.token,u.value=n.repData.secretKey,P.value=n.repData.wordList,b.value=l("captcha.point")+"\u3010"+P.value.join(",")+"\u3011"):b.value=n.repMsg},j=function(d,n){return d.map(S=>({x:Math.round(310*S.x/parseInt(n.imgWidth)),y:Math.round(155*S.y/parseInt(n.imgHeight))}))};return(d,n)=>(W(),_("div",xe,[s("div",we,[s("div",{style:w({width:e(m).imgWidth,height:e(m).imgHeight,"background-size":e(m).imgWidth+" "+e(m).imgHeight,"margin-bottom":a.vSpace+"px"}),class:"verify-img-panel"},[ie(s("div",{class:"verify-refresh",style:{"z-index":"3"},onClick:O},[...ze],512),[[le,e(v)]]),s("img",{ref_key:"canvas",ref:I,src:"data:image/png;base64,"+e(M),alt:"",style:{display:"block",width:"100%",height:"100%"},onClick:n[0]||(n[0]=S=>e(V)?(x=>{if(c.push($(I,x)),z.value==y.value){z.value=C($(I,x));let X=j(c,m);c.length=0,c.push(...X),setTimeout(()=>{var E=u.value?q(H.value+"---"+JSON.stringify(c),u.value):H.value+"---"+JSON.stringify(c);let A={captchaType:g.value,pointJson:u.value?q(JSON.stringify(c),u.value):JSON.stringify(c),token:H.value};pe(A).then(F=>{F.repCode=="0000"?(k.value="#4cae4c",N.value="#5cb85c",b.value=l("captcha.success"),V.value=!1,o.value=="pop"&&setTimeout(()=>{r.$parent.clickShow=!1,O()},1500),r.$parent.$emit("success",{captchaVerification:E})):(r.$parent.$emit("error",r),k.value="#d9534f",N.value="#d9534f",b.value=l("captcha.fail"),setTimeout(()=>{O()},700))})},400)}z.value<y.value&&(z.value=C($(I,x)))})(S):void 0)},null,8,ke),(W(!0),_(Ce,null,Be(e(B),(S,x)=>(W(),_("div",{key:x,style:w({"background-color":"#1abd6c",color:"#fff","z-index":9999,width:"20px",height:"20px","text-align":"center","line-height":"20px","border-radius":"50%",position:"absolute",top:parseInt(S.y-10)+"px",left:parseInt(S.x-10)+"px"}),class:"point-area"},L(x+1),5))),128))],4)]),s("div",{style:w({width:e(m).imgWidth,color:e(k),"border-color":e(N),"line-height":a.barSize.height}),class:"verify-bar-area"},[s("span",Te,L(e(b)),1)],4)]))}},[["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Verifition/src/Verify/VerifyPoints.vue"]])},props:{captchaType:{type:String,required:!0},figure:{type:Number},arith:{type:Number},mode:{type:String,default:"pop"},vSpace:{type:Number},explain:{type:String},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object},barSize:{type:Object}},setup(a){const{t:f}=te(),{captchaType:l,mode:o}=ae(a),g=t(!1),r=t(void 0),u=t(void 0),y=t({}),p=oe(()=>o.value!="pop"||g.value);return Ne(()=>{switch(l.value){case"blockPuzzle":r.value="2",u.value="VerifySlide";break;case"clickWord":r.value="",u.value="VerifyPoints"}}),{t:f,clickShow:g,verifyType:r,componentType:u,instance:y,showBox:p,closeBox:()=>{g.value=!1,y.value.refresh&&y.value.refresh()},show:()=>{o.value=="pop"&&(g.value=!0)}}}},He={key:0,class:"verifybox-top"},Ie=[s("i",{class:"iconfont icon-close"},null,-1)];he=ee(We,[["render",function(a,f,l,o,g,r){return ie((W(),_("div",{class:Y(l.mode=="pop"?"mask":"")},[s("div",{class:Y(l.mode=="pop"?"verifybox":""),style:w({"max-width":parseInt(l.imgSize.width)+20+"px"})},[l.mode=="pop"?(W(),_("div",He,[Je(L(o.t("captcha.verification"))+" ",1),s("span",{class:"verifybox-close",onClick:f[0]||(f[0]=(...u)=>o.closeBox&&o.closeBox(...u))},[...Ie])])):R("",!0),s("div",{style:w({padding:l.mode=="pop"?"10px":"0"}),class:"verifybox-bottom"},[o.componentType?(W(),je(Ee(o.componentType),{key:0,ref:"instance",arith:l.arith,barSize:l.barSize,blockSize:l.blockSize,captchaType:l.captchaType,explain:l.explain,figure:l.figure,imgSize:l.imgSize,mode:l.mode,type:o.verifyType,vSpace:l.vSpace},null,8,["arith","barSize","blockSize","captchaType","explain","figure","imgSize","mode","type","vSpace"])):R("",!0)],4)],6)],2)),[[le,o.showBox]])}],["__file","/Users/<USER>/Java/yudao-ui-admin-vue3/src/components/Verifition/src/Verify.vue"]])});export{he as _,Le as __tla};
